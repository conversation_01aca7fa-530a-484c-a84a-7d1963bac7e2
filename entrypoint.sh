#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
JSON_SECRETS_FILE_PATH="${PATH_TO_SECRET_FILE}" # Provided by Docker environment variable
CONFIG_TEMPLATE_PATH="/app/manifest/config/config.yaml.template" # Absolute path in container
CONFIG_OUTPUT_PATH="/app/manifest/config/config.yaml"   # Absolute path in container

# --- Helper Functions ---
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

# --- Main Script ---

log_info "Entrypoint script started."

# 1. Check if the JSON secrets file exists
if [ ! -f "$JSON_SECRETS_FILE_PATH" ]; then
    log_error "JSON secrets file not found at '$JSON_SECRETS_FILE_PATH'."
    exit 1
fi
log_info "JSON secrets file found at '$JSON_SECRETS_FILE_PATH'."

# 2. Create a temporary file for exported variables
# Using mktemp for secure temporary file creation
temp_env_file=$(mktemp)
if [ -z "$temp_env_file" ]; then
    log_error "Failed to create temporary file for environment variables."
    exit 1
fi
# Ensure the temporary file is removed on script exit (success or failure)
trap 'rm -f "$temp_env_file"' EXIT

log_info "Temporary environment file created at '$temp_env_file'."

# 3. Extract key-value pairs from JSON and prepare for export
# This jq command handles strings, numbers, and booleans correctly for env var export.
# It outputs lines in KEY=VALUE format.
if ! jq -r 'to_entries[] | .key + "=" + (.value | if type == "string" then . else tostring end)' "$JSON_SECRETS_FILE_PATH" > "$temp_env_file"; then
    log_error "Failed to parse JSON secrets file '$JSON_SECRETS_FILE_PATH' with jq."
    exit 1
fi

log_info "Successfully parsed JSON secrets. Variables prepared for export:"
# Debug: Show what will be exported (consider removing or reducing verbosity in production)
# cat "$temp_env_file"

# 4. Export variables from the temporary file
while IFS='=' read -r key value; do
    if [ -n "$key" ]; then # Ensure key is not empty
        export "$key"="$value"
        # log_info "Exported: $key" # Verbose: logs every exported variable
    fi
done < "$temp_env_file"
# The temp_env_file will be removed by the trap on EXIT.
log_info "Environment variables exported."

# 5. Check if the configuration template file exists
if [ ! -f "$CONFIG_TEMPLATE_PATH" ]; then
    log_error "Configuration template file not found at '$CONFIG_TEMPLATE_PATH'."
    log_info "Contents of /app/manifest/config/:"
    ls -la /app/manifest/config/
    exit 1
fi
log_info "Configuration template file found at '$CONFIG_TEMPLATE_PATH'."

# 6. Generate the configuration file using envsubst
log_info "Generating config file '$CONFIG_OUTPUT_PATH' from '$CONFIG_TEMPLATE_PATH'..."
# envsubst will substitute all exported environment variables found in the template (e.g., ${VAR} or $VAR)
if ! envsubst < "$CONFIG_TEMPLATE_PATH" > "$CONFIG_OUTPUT_PATH"; then
    log_error "envsubst command failed to generate '$CONFIG_OUTPUT_PATH'."
    # Check if output file was created or partially written
    if [ -f "$CONFIG_OUTPUT_PATH" ]; then
        log_info "--- Content of (potentially incomplete) $CONFIG_OUTPUT_PATH ---"
        cat "$CONFIG_OUTPUT_PATH"
        log_info "--- End of content ---"
    fi
    exit 1
fi

log_info "Config file '$CONFIG_OUTPUT_PATH' generated successfully."
# Debug: Show generated config (consider removing or reducing verbosity in production)
# log_info "--- Content of generated $CONFIG_OUTPUT_PATH ---"
# cat "$CONFIG_OUTPUT_PATH"
# log_info "--- End of $CONFIG_OUTPUT_PATH content ---"

# 7. Execute the main application command (passed as CMD in Dockerfile)
log_info "Executing command: $@"
exec "$@"
