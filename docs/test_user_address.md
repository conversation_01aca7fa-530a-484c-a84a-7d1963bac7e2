# 用户地址接口改造测试文档

## 改造完成内容

### 1. 通用查询工具模块
- ✅ 创建 `/internal/utility/agent_query.go`
- ✅ 提供三级代理查询条件函数
- ✅ 提供telegram查询条件函数
- ✅ 提供统一的表关联函数

### 2. API结构扩展
- ✅ 新增三级代理查询条件：FirstAgentName, SecondAgentName, ThirdAgentName
- ✅ 新增telegram查询条件：TelegramId, TelegramUsername, FirstName
- ✅ 新增返回字段：三级代理信息 + telegram信息

### 3. Repository查询逻辑
- ✅ 集成通用查询工具
- ✅ 添加复杂关联查询（users + agents + user_backup_accounts）
- ✅ 实现新增查询条件的处理
- ✅ 修改返回数据映射

## 测试用例

### API请求示例
```http
GET /user-addresses?page=1&pageSize=10&firstAgentName=代理1&telegramId=123456
```

### 预期返回示例
```json
{
  "page": {
    "currentPage": 1,
    "pageSize": 10,
    "totalSize": 100,
    "totalPage": 10
  },
  "data": [
    {
      "userAddressId": 1,
      "userId": 123,
      "account": "user123",
      "username": "用户昵称",
      "address": "1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa",
      "firstAgentName": "代理1",
      "secondAgentName": "代理2", 
      "thirdAgentName": "代理3",
      "telegramId": "123456",
      "telegramUsername": "@user123",
      "firstName": "张三"
    }
  ]
}
```

## 后续计划

此用户地址接口改造为其他12个用户相关接口提供了标准模板：

1. **用户充值记录** - ListUserRechargesReq
2. **用户提现记录** - ListUserWithdrawsReq  
3. **用户交易记录** - ListAdminTransactionsReq
4. **用户转账记录** - ListAdminTransfersReq
5. **用户钱包** - ListWalletsReq
6. **红包相关** - 3个红包接口
7. **收款相关** - ListPaymentRequestReq
8. **推荐佣金** - GetReferralCommissionListReq
9. **登录日志** - GetLoginLogListReq

每个接口都可以复用 `/internal/utility/agent_query.go` 中的通用函数。