# Red Packet DAO Fixes Summary

## Database Column Name Mismatches Fixed

### 1. red_packet_claims table
- Changed `rpc.claimed_amount` to `rpc.amount as claimed_amount` (actual column is `amount`)
- Moved `message_id` and `chat_id` from `rpc` to `rp` table (these columns exist in red_packets, not red_packet_claims)
- Removed `chat_id` entirely (doesn't exist in either table)

### 2. red_packets table  
- Changed `rp.total_quantity` to `rp.quantity as total_quantity` (actual column is `quantity`)
- Changed `rp.image_id` to `rp.red_packet_images_id as image_id`
- Changed `rp.image_url` to `rp.thumb_url as image_url`
- Removed `rp.chat_id` (column doesn't exist)
- Removed `rp.updated_at` (column doesn't exist)

### 3. Model Updates
- Updated `RedPacketAdminInfo` to remove `ChatId` and `UpdatedAt` fields
- Updated `RedPacketClaimAdminInfo` to remove `ChatId` field
- Changed `MessageId` type from `int64` to `string` to match database

### 4. DAO Cleanup
- Removed old `ListAdminRedPackets` method from red_packets.go
- Removed old `ListAdminRedPacketClaims` method from red_packet_claims.go
- Updated return types from `v1.RedPacketAdminInfoType` to `model.RedPacketAdminInfo`
- Removed unused v1 imports

## Column Mapping Reference

### red_packets table actual columns:
- `quantity` (not `total_quantity`)
- `red_packet_images_id` (not `image_id`)
- `thumb_url` (not `image_url`)
- `message_id` (type: string)
- No `chat_id` column
- No `updated_at` column

### red_packet_claims table actual columns:
- `amount` (not `claimed_amount`)
- No `message_id` column
- No `chat_id` column

## Testing Notes
After these fixes, the red packet list APIs should work correctly:
- `/api/system/red-packets`
- `/api/system/red-packet-claims`
- `/api/system/red-packet-images`