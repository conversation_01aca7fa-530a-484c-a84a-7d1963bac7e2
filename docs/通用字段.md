# Repository 模式重构为直接 DAO 查询 - 通用操作模板

## 操作概述
将使用 Repository 模式的列表查询方法重构为直接调用 DAO 的查询方法，实现一次性关联查询所有需要的数据。

## 步骤详解

### 1. 分析需求和现有代码结构

```bash
# 1.1 检查 API 定义，了解需要返回的字段
cat api/system/v1/[module_name].go

# 1.2 查看现有的 logic 实现
cat internal/logic/system/v1/[module_name].go

# 1.3 参考已实现的模块（如 transfer.go）
cat internal/logic/system/v1/transfer.go
```

### 2. 创建 Model 结构体

创建文件：`internal/model/[module_name].go`

```go
package model

import (
    "github.com/gogf/gf/v2/os/gtime"
    "github.com/shopspring/decimal"  // 如果有金额字段
)

// [ModuleName]AdminInfo 管理后台信息模型
type [ModuleName]AdminInfo struct {
    // 基础字段
    Id          int64       `json:"id" orm:"id" description:"记录ID"`
    UserId      int64       `json:"userId" orm:"user_id" description:"用户ID"`
    UserAccount string      `json:"userAccount" orm:"user_account" description:"用户账号"`
    TokenId     int         `json:"tokenId" orm:"token_id" description:"代币ID"`
    TokenSymbol string      `json:"tokenSymbol" orm:"token_symbol" description:"代币符号"`
    TokenName   string      `json:"tokenName" orm:"token_name" description:"代币名称"`
    Amount      decimal.Decimal `json:"amount" orm:"amount" description:"金额"`
    Status      int         `json:"status" orm:"status" description:"状态"`
    CreatedAt   *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
    UpdatedAt   *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`
    
    // 关联用户代理信息（默认用户不加前缀）
    FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name" description:"一级代理名称"`
    SecondAgentName string `json:"secondAgentName" orm:"second_agent_name" description:"二级代理名称"`
    ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name" description:"三级代理名称"`
    
    // Telegram信息（默认用户不加前缀）
    TelegramId       string `json:"telegramId" orm:"telegram_id" description:"Telegram ID"`
    TelegramUsername string `json:"telegramUsername" orm:"telegram_username" description:"Telegram用户名"`
    FirstName        string `json:"firstName" orm:"first_name" description:"真实姓名"`
    
    // 如果有第二个用户，添加对应字段（需要加前缀）
    OtherUserId      int64  `json:"otherUserId" orm:"other_user_id" description:"另一个用户ID"`
    OtherUserAccount string `json:"otherUserAccount" orm:"other_user_account" description:"另一个用户账号"`
    
    OtherUserFirstAgentName  string `json:"otherUserFirstAgentName" orm:"other_user_first_agent_name" description:"另一用户一级代理"`
    OtherUserSecondAgentName string `json:"otherUserSecondAgentName" orm:"other_user_second_agent_name" description:"另一用户二级代理"`
    OtherUserThirdAgentName  string `json:"otherUserThirdAgentName" orm:"other_user_third_agent_name" description:"另一用户三级代理"`
    
    OtherUserTelegramId       string `json:"otherUserTelegramId" orm:"other_user_telegram_id" description:"另一用户Telegram ID"`
    OtherUserTelegramUsername string `json:"otherUserTelegramUsername" orm:"other_user_telegram_username" description:"另一用户Telegram用户名"`
    OtherUserFirstName        string `json:"otherUserFirstName" orm:"other_user_first_name" description:"另一用户真实姓名"`
}
```

### 3. 实现 DAO 查询方法

在 `internal/dao/[module_name].go` 中添加：

```go
// 添加必要的导入
import (
    "admin-api/internal/model"
    "github.com/gogf/gf/v2/text/gstr"
)

// ListAdmin[ModuleName]WithFullInfo 查询后台列表（含完整信息）
func (d *[moduleName]Dao) ListAdmin[ModuleName]WithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.[ModuleName]AdminInfo, total int, err error) {
    // 初始化返回列表
    list = make([]*model.[ModuleName]AdminInfo, 0)

    // 构建基础查询，使用别名避免字段冲突
    m := d.Ctx(ctx).As("t").
        LeftJoin("users u1", "t.user_id = u1.id").
        LeftJoin("tokens token", "t.token_id = token.token_id")
    
    // 添加默认用户的代理关联
    m = m.LeftJoin("users u1_first_agent", "u1.first_id = u1_first_agent.id")
    m = m.LeftJoin("users u1_second_agent", "u1.second_id = u1_second_agent.id")
    m = m.LeftJoin("users u1_third_agent", "u1.third_id = u1_third_agent.id")
    
    // 添加默认用户的 Telegram 信息关联
    m = m.LeftJoin("user_backup_accounts u1_uba", "u1.id = u1_uba.user_id AND u1_uba.is_master = 1")

    // 如果有第二个用户
    m = m.LeftJoin("users u2", "t.other_user_id = u2.id")
    m = m.LeftJoin("users u2_first_agent", "u2.first_id = u2_first_agent.id")
    m = m.LeftJoin("users u2_second_agent", "u2.second_id = u2_second_agent.id")
    m = m.LeftJoin("users u2_third_agent", "u2.third_id = u2_third_agent.id")
    m = m.LeftJoin("user_backup_accounts u2_uba", "u2.id = u2_uba.user_id AND u2_uba.is_master = 1")

    // 添加软删除条件
    m = m.WhereNull("t.deleted_at")

    // 处理查询条件（分类处理 LIKE、BETWEEN、精确匹配）
    query := m
    var betweenConditions = make(map[string]g.Slice)
    var likeConditions = make(map[string]string)
    var exactConditions = make(map[string]interface{})
    
    // 将条件按类型分类
    for key, value := range condition {
        if gstr.Contains(key, " BETWEEN ? AND ?") {
            fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
            if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
                betweenConditions[fieldName] = timeSlice
            }
        } else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
            fieldName := gstr.Replace(key, " LIKE ?", "")
            fieldName = gstr.Replace(fieldName, " LIKE", "")
            if valueStr, ok := value.(string); ok {
                likeConditions[fieldName] = valueStr
            }
        } else if gstr.Contains(key, " >=") {
            query = query.Where(key, value)
        } else if gstr.Contains(key, " <=") {
            query = query.Where(key, value)
        } else {
            exactConditions[key] = value
        }
    }
    
    // 按顺序应用条件
    // 1. 先应用精确匹配条件
    for key, value := range exactConditions {
        query = query.Where(key, value)
    }
    
    // 2. 再应用LIKE条件
    for fieldName, pattern := range likeConditions {
        query = query.WhereLike(fieldName, pattern)
    }
    
    // 3. 最后应用BETWEEN条件
    for fieldName, timeSlice := range betweenConditions {
        query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
    }

    // 克隆查询用于计算总数
    countQuery := query.Clone()
    total, err = countQuery.Count()
    if err != nil {
        return nil, 0, gerror.Wrap(err, "查询总数失败")
    }

    if total == 0 {
        return list, 0, nil
    }

    // 定义查询字段
    baseFields := []string{
        "t.id",
        "t.user_id",
        "u1.account as user_account",
        "t.other_user_id",
        "u2.account as other_user_account",
        "t.token_id",
        "COALESCE(token.symbol, t.symbol) as token_symbol",
        "token.name as token_name",
        "t.amount",
        "t.status",
        "t.created_at",
        "t.updated_at",
    }
    
    // 默认用户的代理和Telegram字段
    userAgentAndTelegramFields := []string{
        "u1_first_agent.account as first_agent_name",
        "u1_second_agent.account as second_agent_name",
        "u1_third_agent.account as third_agent_name",
        "u1_uba.telegram_id",
        "u1_uba.telegram_username",
        "u1_uba.first_name",
    }
    
    // 第二个用户的代理和Telegram字段
    otherUserAgentAndTelegramFields := []string{
        "u2_first_agent.account as other_user_first_agent_name",
        "u2_second_agent.account as other_user_second_agent_name",
        "u2_third_agent.account as other_user_third_agent_name",
        "u2_uba.telegram_id as other_user_telegram_id",
        "u2_uba.telegram_username as other_user_telegram_username",
        "u2_uba.first_name as other_user_first_name",
    }
    
    allFields := append(baseFields, userAgentAndTelegramFields...)
    allFields = append(allFields, otherUserAgentAndTelegramFields...)
    
    // 转换为interface{}类型
    fieldInterfaces := make([]interface{}, len(allFields))
    for i, field := range allFields {
        fieldInterfaces[i] = field
    }
    
    // 执行查询
    err = query.Page(page, pageSize).Order("t.id DESC").Fields(fieldInterfaces...).Scan(&list)
    if err != nil {
        return nil, 0, gerror.Wrap(err, "查询列表失败")
    }

    return list, total, nil
}
```

### 4. 重构 Logic 层

修改 `internal/logic/system/v1/[module_name].go`：

```go
// 4.1 添加必要的导入
import (
    "admin-api/api/common"
    v1 "admin-api/api/system/v1"
    "admin-api/internal/codes"
    "admin-api/internal/consts"
    "admin-api/internal/dao"
    "admin-api/utility/excel"
    "context"
    "math"
    "strings"
    
    "admin-api/internal/model/entity" // 如果需要

    "github.com/gogf/gf/v2/errors/gerror"
    "github.com/gogf/gf/v2/frame/g"
)

// 4.2 重写 List 方法
func (s *sSystemLogic) List[ModuleName](ctx context.Context, req *v1.List[ModuleName]Req) (res *v1.List[ModuleName]Res, err error) {
    // 初始化返回结果
    res = &v1.List[ModuleName]Res{
        Page: common.PageResponse{
            CurrentPage: req.Page,
            PageSize:    req.PageSize,
            TotalSize:   0,
            TotalPage:   0,
        },
        Data: make([]*v1.[ModuleName]ListItem, 0),
    }

    // 构建查询条件
    condition := g.Map{}
    
    // 添加筛选条件（根据实际需求）
    if req.Id != 0 {
        condition["t.id"] = req.Id
    }
    if req.Username != "" {
        condition["u1.account LIKE"] = "%" + req.Username + "%"
    }
    if req.Status != nil {
        condition["t.status"] = *req.Status
    }
    
    // 处理日期范围
    if req.DateRange != "" {
        dateRange := strings.Split(req.DateRange, ",")
        if len(dateRange) == 2 {
            condition["t.created_at >="] = dateRange[0] + " 00:00:00"
            condition["t.created_at <="] = dateRange[1] + " 23:59:59"
        }
    }
    
    // 代理查询条件（默认用户）
    if req.FirstAgentName != "" {
        condition["u1_first_agent.account LIKE"] = "%" + req.FirstAgentName + "%"
    }
    if req.SecondAgentName != "" {
        condition["u1_second_agent.account LIKE"] = "%" + req.SecondAgentName + "%"
    }
    if req.ThirdAgentName != "" {
        condition["u1_third_agent.account LIKE"] = "%" + req.ThirdAgentName + "%"
    }
    
    // Telegram查询条件（默认用户）
    if req.TelegramId != "" {
        condition["u1_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
    }
    if req.TelegramUsername != "" {
        condition["u1_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
    }
    if req.FirstName != "" {
        condition["u1_uba.first_name LIKE"] = "%" + req.FirstName + "%"
    }

    // 处理导出
    if req.Export == 1 {
        // 导出时不进行分页限制
        list, _, err := dao.[ModuleName].ListAdmin[ModuleName]WithFullInfo(ctx, 1, 9999999, condition)
        if err != nil {
            return nil, gerror.Wrap(err, "导出查询失败")
        }
        
        // 转换为导出格式
        exportData := make([]interface{}, len(list))
        for i, item := range list {
            createdAt := ""
            if item.CreatedAt != nil {
                createdAt = item.CreatedAt.String()
            }
            
            exportData[i] = struct {
                Id               int64  `json:"id" excel:"ID"`
                UserAccount      string `json:"userAccount" excel:"用户账号"`
                TokenSymbol      string `json:"tokenSymbol" excel:"代币符号"`
                Amount           string `json:"amount" excel:"金额"`
                StatusText       string `json:"statusText" excel:"状态"`
                CreatedAt        string `json:"createdAt" excel:"创建时间"`
                FirstAgentName   string `json:"firstAgentName" excel:"一级代理"`
                SecondAgentName  string `json:"secondAgentName" excel:"二级代理"`
                ThirdAgentName   string `json:"thirdAgentName" excel:"三级代理"`
                TelegramId       string `json:"telegramId" excel:"Telegram ID"`
                TelegramUsername string `json:"telegramUsername" excel:"Telegram用户名"`
                FirstName        string `json:"firstName" excel:"真实姓名"`
                // 第二个用户的字段（如果有）
                OtherUserAccount          string `json:"otherUserAccount" excel:"另一用户账号"`
                OtherUserFirstAgentName   string `json:"otherUserFirstAgentName" excel:"另一用户一级代理"`
                OtherUserSecondAgentName  string `json:"otherUserSecondAgentName" excel:"另一用户二级代理"`
                OtherUserThirdAgentName   string `json:"otherUserThirdAgentName" excel:"另一用户三级代理"`
                OtherUserTelegramId       string `json:"otherUserTelegramId" excel:"另一用户Telegram ID"`
                OtherUserTelegramUsername string `json:"otherUserTelegramUsername" excel:"另一用户Telegram用户名"`
                OtherUserFirstName        string `json:"otherUserFirstName" excel:"另一用户真实姓名"`
            }{
                Id:               item.Id,
                UserAccount:      item.UserAccount,
                TokenSymbol:      item.TokenSymbol,
                Amount:           item.Amount.String(),
                StatusText:       consts.[ModuleName]StatusText[item.Status],
                CreatedAt:        createdAt,
                FirstAgentName:   item.FirstAgentName,
                SecondAgentName:  item.SecondAgentName,
                ThirdAgentName:   item.ThirdAgentName,
                TelegramId:       item.TelegramId,
                TelegramUsername: item.TelegramUsername,
                FirstName:        item.FirstName,
                // 第二个用户
                OtherUserAccount:          item.OtherUserAccount,
                OtherUserFirstAgentName:   item.OtherUserFirstAgentName,
                OtherUserSecondAgentName:  item.OtherUserSecondAgentName,
                OtherUserThirdAgentName:   item.OtherUserThirdAgentName,
                OtherUserTelegramId:       item.OtherUserTelegramId,
                OtherUserTelegramUsername: item.OtherUserTelegramUsername,
                OtherUserFirstName:        item.OtherUserFirstName,
            }
        }
        
        // 定义Excel表头
        excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取
        
        // 调用Excel导出工具
        return res, excel.ExportByStructs(ctx, excelTags, exportData, "[模块名]", "[模块名]列表")
    }

    // 查询分页数据
    list, total, err := dao.[ModuleName].ListAdmin[ModuleName]WithFullInfo(ctx, req.Page, req.PageSize, condition)
    if err != nil {
        return nil, gerror.Wrap(err, "查询列表失败")
    }

    // 设置分页信息
    res.Page.TotalSize = total
    res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

    // 转换DAO结果到API响应格式
    res.Data = make([]*v1.[ModuleName]ListItem, len(list))
    for i, item := range list {
        createdAt := ""
        if item.CreatedAt != nil {
            createdAt = item.CreatedAt.String()
        }
        updatedAt := ""
        if item.UpdatedAt != nil {
            updatedAt = item.UpdatedAt.String()
        }

        res.Data[i] = &v1.[ModuleName]ListItem{
            Id:               item.Id,
            UserId:           item.UserId,
            UserAccount:      item.UserAccount,
            TokenId:          item.TokenId,
            TokenSymbol:      item.TokenSymbol,
            TokenName:        item.TokenName,
            Amount:           item.Amount.String(),
            Status:           item.Status,
            StatusText:       consts.[ModuleName]StatusText[item.Status],
            CreatedAt:        createdAt,
            UpdatedAt:        updatedAt,
            // 默认用户的代理和Telegram信息（不加前缀）
            FirstAgentName:   item.FirstAgentName,
            SecondAgentName:  item.SecondAgentName,
            ThirdAgentName:   item.ThirdAgentName,
            TelegramId:       item.TelegramId,
            TelegramUsername: item.TelegramUsername,
            FirstName:        item.FirstName,
            // 第二个用户的信息（加前缀）
            OtherUserId:               item.OtherUserId,
            OtherUserAccount:          item.OtherUserAccount,
            OtherUserFirstAgentName:   item.OtherUserFirstAgentName,
            OtherUserSecondAgentName:  item.OtherUserSecondAgentName,
            OtherUserThirdAgentName:   item.OtherUserThirdAgentName,
            OtherUserTelegramId:       item.OtherUserTelegramId,
            OtherUserTelegramUsername: item.OtherUserTelegramUsername,
            OtherUserFirstName:        item.OtherUserFirstName,
        }
    }

    return res, nil
}
```

### 5. 清理代码

#### 5.1 移除不再需要的 Repository 相关代码

1. **Logic 层清理**：
   - 移除对 repository 的调用
   - 移除 repository 相关的导入

2. **Repository 层处理**：
   ```go
   // 如果接口需要 List 方法，保留一个空实现
   func (r *[moduleName]Repository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*[module].ListItemDTO, total int, err error) {
       // 临时返回空数组
       return make([]*[module].ListItemDTO, 0), 0, nil
   }
   ```

3. **DAO 层清理**：
   - 移除旧的列表查询方法（如果有）

#### 5.2 移除未使用的导入

常见需要移除的导入：
- `"admin-api/internal/utility"` （如果不再使用 utility.AddDateRangeCondition）
- `"github.com/gogf/gf/v2/util/gconv"` （如果不再需要类型转换）

### 6. 测试编译

```bash
# 测试编译
go build -o /tmp/test-build ./cmd/main.go

# 查看编译错误（如果有）
go build -o /tmp/test-build ./cmd/main.go 2>&1 | head -20
```

## 注意事项

### 1. 字段命名规范
- **默认用户**（发起者/发送方）不加前缀，直接使用：
  - `FirstAgentName`、`SecondAgentName`、`ThirdAgentName`
  - `TelegramId`、`TelegramUsername`、`FirstName`
- **第二个用户**需要加前缀（如 Receiver、Payer、OtherUser）：
  - `PayerFirstAgentName`、`ReceiverFirstAgentName`
  - `PayerTelegramId`、`ReceiverTelegramId`
- JSON tag 要符合前端约定

### 2. 查询优化
- 使用表别名避免字段冲突（t、u1、u2 等）
- LEFT JOIN 确保没有关联数据时也能返回主记录
- 软删除条件要加在主表上：`WhereNull("t.deleted_at")`
- 使用 COALESCE 处理可能为空的字段：`COALESCE(token.symbol, t.symbol) as token_symbol`

### 3. 数据处理
- **时间字段**：需要判空并转字符串
  ```go
  if item.CreatedAt != nil {
      res.Data[i].CreatedAt = item.CreatedAt.String()
  }
  ```
- **金额字段**：使用 decimal 类型并转字符串
  ```go
  Amount: item.Amount.String()
  ```
- **状态文本**：使用常量映射
  ```go
  StatusText: consts.[ModuleName]StatusText[item.Status]
  ```

### 4. 导出功能
- 导出时查询所有数据（pageSize 设置为 9999999）
- 定义专门的导出结构体
- 使用 excel tag 定义表头
- 调用统一的导出工具

### 5. 条件处理顺序
为避免 SQL 参数顺序混乱，按以下顺序处理条件：
1. 精确匹配条件
2. LIKE 条件
3. BETWEEN 条件

## 常见问题

### Q1: 如何处理多个用户的情况？
A: 为每个用户使用不同的表别名，如 u1、u2、u3，并在字段中相应命名。

### Q2: 如何处理复杂的查询条件？
A: 在 DAO 层的条件分类处理中添加对应的处理逻辑。

### Q3: 如何优化查询性能？
A: 
- 确保关联字段有索引
- 只查询需要的字段
- 考虑添加查询缓存

## 完整操作流程总结

1. **创建 Model** → 定义完整的数据结构
2. **实现 DAO 方法** → 编写关联查询
3. **重构 Logic** → 调用 DAO 并转换数据
4. **清理代码** → 移除未使用的代码和导入
5. **测试编译** → 确保代码正确

这个模板可以用于任何需要从 Repository 模式迁移到直接 DAO 查询的模块，只需要根据具体业务调整字段和查询条件即可。