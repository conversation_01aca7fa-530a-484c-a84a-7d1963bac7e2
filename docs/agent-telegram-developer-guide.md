# Agent and Telegram Information - Developer Guide

## Quick Start

This guide shows developers how to add agent hierarchy and Telegram information to new API endpoints.

## Standard Implementation Pattern

### 1. API Definition

Add these fields to your request and response structures:

```go
// Request structure - for search functionality
type YourListReq struct {
    // ... existing fields ...
    
    // Agent search fields
    FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
    SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
    ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`
    
    // Telegram search fields
    TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
    TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
    FirstName        string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// Response structure - for data display
type YourListItem struct {
    // ... existing fields ...
    
    // Agent information
    FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
    SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
    ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`
    
    // Telegram information
    TelegramId       string `json:"telegramId" dc:"Telegram ID"`
    TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名"`
    FirstName        string `json:"firstName" dc:"真实姓名"`
}
```

### 2. Repository Implementation

Use the utility functions for consistent implementation:

```go
package your_module

import (
    "admin-api/internal/utility"
    "admin-api/api/system/v1"
)

func (r *yourRepository) ListWithAgentInfo(ctx context.Context, page, pageSize int, condition map[string]interface{}) ([]*v1.YourListItem, int64, error) {
    // Build base query
    model := dao.YourTable.Ctx(ctx).As("yt").
        LeftJoin(dao.Users.Table()+" u", "yt.user_id = u.id")
    
    // Add agent and telegram joins using utility
    model = utility.AddAgentAndTelegramJoins(model, dao.Users.Table(), dao.UserBackupAccounts.Table())
    
    // Add search conditions using utility
    model = utility.AddAgentAndTelegramSearchConditions(model, condition)
    
    // Get total count
    total, err := model.Count()
    if err != nil {
        return nil, 0, gerror.Wrap(err, "查询总数失败")
    }
    
    // Build field list
    baseFields := []string{
        "yt.id",
        "yt.user_id", 
        // ... other base fields ...
        "u.account",
        "u.nickname",
    }
    
    // Add agent and telegram fields using utility
    agentAndTelegramFields := utility.GetAgentAndTelegramFields()
    allFields := append(baseFields, agentAndTelegramFields...)
    
    // Convert to interface{} for GoFrame
    fieldInterfaces := make([]interface{}, len(allFields))
    for i, field := range allFields {
        fieldInterfaces[i] = field
    }
    
    // Execute query with pagination
    var results []map[string]interface{}
    err = model.Fields(fieldInterfaces...).
        Page(page, pageSize).
        OrderDesc("yt.created_at").
        Scan(&results)
    
    if err != nil {
        return nil, 0, gerror.Wrap(err, "查询列表失败")
    }
    
    // Convert to response format
    list := make([]*v1.YourListItem, len(results))
    for i, result := range results {
        list[i] = &v1.YourListItem{
            // Map base fields
            Id:       gconv.Uint64(result["id"]),
            UserId:   gconv.Uint64(result["user_id"]),
            Account:  gconv.String(result["account"]),
            Nickname: gconv.String(result["nickname"]),
            
            // Map agent fields
            FirstAgentName:  gconv.String(result["first_agent_name"]),
            SecondAgentName: gconv.String(result["second_agent_name"]),
            ThirdAgentName:  gconv.String(result["third_agent_name"]),
            
            // Map telegram fields
            TelegramId:       gconv.String(result["telegram_id"]),
            TelegramUsername: gconv.String(result["telegram_username"]),
            FirstName:        gconv.String(result["first_name"]),
        }
    }
    
    return list, total, nil
}
```

### 3. Service Layer

Update your service to use the new repository method:

```go
func (s *sSystemLogic) GetYourList(ctx context.Context, req *v1.GetYourListReq) (res *v1.GetYourListRes, err error) {
    // Build search conditions
    condition := make(map[string]interface{})
    
    // Add standard search conditions
    if req.UserId > 0 {
        condition["user_id"] = req.UserId
    }
    
    // Add agent search conditions (handled by utility)
    if req.FirstAgentName != "" {
        condition["first_agent_name"] = req.FirstAgentName
    }
    if req.SecondAgentName != "" {
        condition["second_agent_name"] = req.SecondAgentName
    }
    if req.ThirdAgentName != "" {
        condition["third_agent_name"] = req.ThirdAgentName
    }
    
    // Add telegram search conditions (handled by utility)
    if req.TelegramId != "" {
        condition["telegram_id"] = req.TelegramId
    }
    if req.TelegramUsername != "" {
        condition["telegram_username"] = req.TelegramUsername
    }
    if req.FirstName != "" {
        condition["first_name"] = req.FirstName
    }
    
    // Call repository with agent info
    list, total, err := s.yourRepo.ListWithAgentInfo(ctx, req.Page, req.PageSize, condition)
    if err != nil {
        return nil, err
    }
    
    res = &v1.GetYourListRes{
        List:  list,
        Total: total,
    }
    
    return res, nil
}
```

## Utility Functions Reference

### Available Functions

```go
// Add standard JOIN clauses for agent and telegram info
func AddAgentAndTelegramJoins(model *gdb.Model, usersTable, userBackupAccountsTable string) *gdb.Model

// Get standard field list for agent and telegram info
func GetAgentAndTelegramFields() []string

// Add search conditions for agent and telegram fields
func AddAgentAndTelegramSearchConditions(model *gdb.Model, condition map[string]interface{}) *gdb.Model
```

### Field Names Returned

The utility functions return these standardized field names:

- `first_agent_name` - First level agent account
- `second_agent_name` - Second level agent account  
- `third_agent_name` - Third level agent account
- `telegram_id` - User's Telegram ID
- `telegram_username` - User's Telegram username
- `first_name` - User's real name

## Database Requirements

### Required Indexes

Ensure these indexes exist for optimal performance:

```sql
-- Agent relationship indexes
CREATE INDEX idx_users_first_id ON users(first_id);
CREATE INDEX idx_users_second_id ON users(second_id);
CREATE INDEX idx_users_third_id ON users(third_id);

-- Telegram info index
CREATE INDEX idx_user_backup_accounts_user_master 
ON user_backup_accounts(user_id, is_master, deleted_at);

-- Your table user_id index
CREATE INDEX idx_your_table_user_id ON your_table(user_id);
```

## Common Patterns

### 1. Dual User Information (e.g., Payment Requests)

For endpoints that involve two users (requester and payer), implement separate joins:

```go
// Add joins for both requester and payer
model = model.
    LeftJoin("users requester", "pr.requester_user_id = requester.id").
    LeftJoin("users req_first_agent", "requester.first_id = req_first_agent.id").
    // ... more requester agent joins ...
    LeftJoin("user_backup_accounts req_uba", "requester.id = req_uba.user_id AND req_uba.is_master = 1").
    LeftJoin("users payer", "pr.payer_user_id = payer.id").
    LeftJoin("users payer_first_agent", "payer.first_id = payer_first_agent.id")
    // ... more payer agent joins ...
```

### 2. Detail Endpoints

For detail endpoints, create separate methods that include agent info:

```go
func (r *yourRepository) GetDetailWithAgentInfo(ctx context.Context, id uint) (*v1.YourDetailItem, error) {
    // Similar pattern to list but for single record
    // Use the same utility functions
}
```

### 3. Search Optimization

The utility functions automatically handle LIKE searches for agent and telegram fields:

```go
// These conditions are automatically converted to LIKE searches
condition["first_agent_name"] = "test"     // becomes: first_agent.account LIKE '%test%'
condition["telegram_username"] = "user"   // becomes: uba.telegram_username LIKE '%user%'
```

## Testing

### 1. Verify Field Population

Test that all fields are properly populated:

```bash
curl "http://localhost:7999/api/system/your-endpoint?page=1&pageSize=10"
```

Check response includes:
- `firstAgentName`, `secondAgentName`, `thirdAgentName`
- `telegramId`, `telegramUsername`, `firstName`

### 2. Test Search Functionality

```bash
curl "http://localhost:7999/api/system/your-endpoint?firstAgentName=test&telegramUsername=user"
```

### 3. Performance Testing

Use the benchmark script to verify performance:

```bash
mysql -u root -p xpayapi < scripts/performance_benchmark.sql
```

## Troubleshooting

### Common Issues

1. **Missing Fields**: Ensure utility functions are used correctly
2. **Performance Issues**: Check that required indexes are created
3. **Search Not Working**: Verify search conditions are passed to utility function
4. **Null Values**: Check that LEFT JOINs include proper NULL handling

### Debug Queries

Enable database debugging to see generated SQL:

```yaml
# config.yaml
database:
  default:
    debug: true
```

## Best Practices

1. **Always use utility functions** for consistency
2. **Include proper error handling** in repository methods
3. **Add appropriate indexes** for your main table
4. **Test search functionality** thoroughly
5. **Monitor query performance** after implementation

This pattern ensures consistent implementation across all endpoints while maintaining optimal performance.
