-- Performance Benchmark Script for Agent and Telegram Information Queries
-- Created: 2025-06-18
-- Usage: Run this script before and after applying the performance indexes to compare results

-- ============================================================================
-- Enable Query Profiling
-- ============================================================================
SET profiling = 1;
SET profiling_history_size = 100;

-- ============================================================================
-- Test Queries - User Recharges with Agent Info
-- ============================================================================

-- Test 1: User Recharges List Query
SELECT 
    ur.user_recharges_id,
    ur.user_id,
    ur.amount,
    ur.created_at,
    u.account,
    u.nickname,
    first_agent.account as first_agent_name,
    second_agent.account as second_agent_name,
    third_agent.account as third_agent_name,
    uba.telegram_id,
    uba.telegram_username,
    uba.first_name
FROM user_recharges ur
LEFT JOIN users u ON ur.user_id = u.id
LEFT JOIN users first_agent ON u.first_id = first_agent.id
LEFT JOIN users second_agent ON u.second_id = second_agent.id
LEFT JOIN users third_agent ON u.third_id = third_agent.id
LEFT JOIN user_backup_accounts uba ON u.id = uba.user_id AND uba.is_master = 1
WHERE u.deleted_at IS NULL
  AND first_agent.deleted_at IS NULL
  AND second_agent.deleted_at IS NULL
  AND third_agent.deleted_at IS NULL
  AND uba.deleted_at IS NULL
ORDER BY ur.created_at DESC
LIMIT 20;

-- ============================================================================
-- Test Queries - User Withdraws with Agent Info
-- ============================================================================

-- Test 2: User Withdraws List Query
SELECT 
    uw.user_withdraws_id,
    uw.user_id,
    uw.amount,
    uw.state,
    uw.created_at,
    u.account,
    u.nickname,
    first_agent.account as first_agent_name,
    second_agent.account as second_agent_name,
    third_agent.account as third_agent_name,
    uba.telegram_id,
    uba.telegram_username,
    uba.first_name
FROM user_withdraws uw
LEFT JOIN users u ON uw.user_id = u.id
LEFT JOIN users first_agent ON u.first_id = first_agent.id
LEFT JOIN users second_agent ON u.second_id = second_agent.id
LEFT JOIN users third_agent ON u.third_id = third_agent.id
LEFT JOIN user_backup_accounts uba ON u.id = uba.user_id AND uba.is_master = 1
WHERE u.deleted_at IS NULL
  AND first_agent.deleted_at IS NULL
  AND second_agent.deleted_at IS NULL
  AND third_agent.deleted_at IS NULL
  AND uba.deleted_at IS NULL
ORDER BY uw.created_at DESC
LIMIT 20;

-- ============================================================================
-- Test Queries - Wallets with Agent Info
-- ============================================================================

-- Test 3: Wallets List Query
SELECT 
    w.wallet_id,
    w.user_id,
    w.available_balance,
    w.frozen_balance,
    w.symbol,
    u.account,
    u.nickname,
    first_agent.account as first_agent_name,
    second_agent.account as second_agent_name,
    third_agent.account as third_agent_name,
    uba.telegram_id,
    uba.telegram_username,
    uba.first_name
FROM wallets w
LEFT JOIN users u ON w.user_id = u.id
LEFT JOIN users first_agent ON u.first_id = first_agent.id
LEFT JOIN users second_agent ON u.second_id = second_agent.id
LEFT JOIN users third_agent ON u.third_id = third_agent.id
LEFT JOIN user_backup_accounts uba ON u.id = uba.user_id AND uba.is_master = 1
WHERE u.deleted_at IS NULL
  AND first_agent.deleted_at IS NULL
  AND second_agent.deleted_at IS NULL
  AND third_agent.deleted_at IS NULL
  AND uba.deleted_at IS NULL
ORDER BY w.created_at DESC
LIMIT 20;

-- ============================================================================
-- Test Queries - Payment Requests with Dual User Info
-- ============================================================================

-- Test 4: Payment Requests List Query (Dual User Info)
SELECT 
    pr.payment_request_id,
    pr.requester_user_id,
    pr.payer_user_id,
    pr.amount,
    pr.status,
    pr.created_at,
    requester.account as requester_account,
    requester.nickname as requester_nickname,
    req_first_agent.account as requester_first_agent_name,
    req_second_agent.account as requester_second_agent_name,
    req_third_agent.account as requester_third_agent_name,
    req_uba.telegram_id as requester_telegram_id,
    req_uba.telegram_username as requester_telegram_username,
    req_uba.first_name as requester_first_name,
    payer.account as payer_account,
    payer.nickname as payer_nickname,
    payer_first_agent.account as payer_first_agent_name,
    payer_second_agent.account as payer_second_agent_name,
    payer_third_agent.account as payer_third_agent_name,
    payer_uba.telegram_id as payer_telegram_id,
    payer_uba.telegram_username as payer_telegram_username,
    payer_uba.first_name as payer_first_name
FROM payment_requests pr
LEFT JOIN users requester ON pr.requester_user_id = requester.id
LEFT JOIN users req_first_agent ON requester.first_id = req_first_agent.id
LEFT JOIN users req_second_agent ON requester.second_id = req_second_agent.id
LEFT JOIN users req_third_agent ON requester.third_id = req_third_agent.id
LEFT JOIN user_backup_accounts req_uba ON requester.id = req_uba.user_id AND req_uba.is_master = 1
LEFT JOIN users payer ON pr.payer_user_id = payer.id
LEFT JOIN users payer_first_agent ON payer.first_id = payer_first_agent.id
LEFT JOIN users payer_second_agent ON payer.second_id = payer_second_agent.id
LEFT JOIN users payer_third_agent ON payer.third_id = payer_third_agent.id
LEFT JOIN user_backup_accounts payer_uba ON payer.id = payer_uba.user_id AND payer_uba.is_master = 1
WHERE requester.deleted_at IS NULL
  AND payer.deleted_at IS NULL
ORDER BY pr.created_at DESC
LIMIT 20;

-- ============================================================================
-- Test Queries - Search by Agent Names
-- ============================================================================

-- Test 5: Search by First Agent Name
SELECT 
    ur.user_recharges_id,
    ur.user_id,
    ur.amount,
    first_agent.account as first_agent_name
FROM user_recharges ur
LEFT JOIN users u ON ur.user_id = u.id
LEFT JOIN users first_agent ON u.first_id = first_agent.id
WHERE first_agent.account LIKE '%test%'
  AND u.deleted_at IS NULL
  AND first_agent.deleted_at IS NULL
LIMIT 10;

-- Test 6: Search by Telegram Username
SELECT 
    ur.user_recharges_id,
    ur.user_id,
    ur.amount,
    uba.telegram_username
FROM user_recharges ur
LEFT JOIN users u ON ur.user_id = u.id
LEFT JOIN user_backup_accounts uba ON u.id = uba.user_id AND uba.is_master = 1
WHERE uba.telegram_username LIKE '%test%'
  AND u.deleted_at IS NULL
  AND uba.deleted_at IS NULL
LIMIT 10;

-- ============================================================================
-- Show Query Profiles
-- ============================================================================

-- Show all query execution times
SHOW PROFILES;

-- Show detailed profile for the last query (change query_id as needed)
-- SHOW PROFILE FOR QUERY 1;

-- ============================================================================
-- Explain Query Plans
-- ============================================================================

-- Explain the most complex query (payment requests with dual user info)
EXPLAIN FORMAT=JSON
SELECT 
    pr.payment_request_id,
    pr.requester_user_id,
    pr.payer_user_id,
    requester.account as requester_account,
    req_first_agent.account as requester_first_agent_name,
    req_uba.telegram_id as requester_telegram_id,
    payer.account as payer_account,
    payer_first_agent.account as payer_first_agent_name,
    payer_uba.telegram_id as payer_telegram_id
FROM payment_requests pr
LEFT JOIN users requester ON pr.requester_user_id = requester.id
LEFT JOIN users req_first_agent ON requester.first_id = req_first_agent.id
LEFT JOIN user_backup_accounts req_uba ON requester.id = req_uba.user_id AND req_uba.is_master = 1
LEFT JOIN users payer ON pr.payer_user_id = payer.id
LEFT JOIN users payer_first_agent ON payer.first_id = payer_first_agent.id
LEFT JOIN user_backup_accounts payer_uba ON payer.id = payer_uba.user_id AND payer_uba.is_master = 1
WHERE requester.deleted_at IS NULL
  AND payer.deleted_at IS NULL
ORDER BY pr.created_at DESC
LIMIT 20;

-- ============================================================================
-- Index Usage Analysis
-- ============================================================================

-- Check if indexes are being used
SHOW INDEX FROM users;
SHOW INDEX FROM user_backup_accounts;
SHOW INDEX FROM user_recharges;
SHOW INDEX FROM user_withdraws;
SHOW INDEX FROM payment_requests;

-- Disable profiling
SET profiling = 0;
