// Package encrypt

package encrypt

import (
	"crypto/md5"
	"fmt"
	"hash/fnv"

	"github.com/gogf/gf/v2/errors/gerror"
	"golang.org/x/crypto/bcrypt"
)

// Md5ToString 生成md5
func Md5ToString(str string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(str)))
}

// Md5 生成md5
func Md5(b []byte) string {
	return fmt.Sprintf("%x", md5.Sum(b))
}

func Hash32(b []byte) uint32 {
	h := fnv.New32a()
	h.Write(b)
	return h.Sum32()
}

// BcryptHash 使用bcrypt算法对密码进行加密
func BcryptHash(password string) (string, error) {
	// 检查密码长度，bcrypt对输入长度有限制（最大72字节）
	if len(password) > 72 {
		// 如果过长，截取前72字节
		password = password[:72]
	}

	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", gerror.New("密码加密失败")
	}
	return string(bytes), nil
}

// BcryptVerify 验证bcrypt加密的密码是否匹配
func BcryptVerify(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
