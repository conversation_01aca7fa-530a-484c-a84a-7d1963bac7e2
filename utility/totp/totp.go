package totp

import (
	"crypto/rand"
	"encoding/base32"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/util/grand"
)

// GenerateTOTPSecret 生成 TOTP Secret
func GenerateTOTPSecret() (string, error) {
	// 生成 20 字节的随机数据
	secret := make([]byte, 20)
	_, err := rand.Read(secret)
	if err != nil {
		return "", err
	}

	// 编码为 base32，这是 TOTP 的标准格式
	encodedSecret := base32.StdEncoding.WithPadding(base32.NoPadding).EncodeToString(secret)
	return encodedSecret, nil
}

// GenerateRecoveryCodes 生成恢复码
func GenerateRecoveryCodes() []string {
	codes := make([]string, 10) // 生成 10 个恢复码

	for i := 0; i < 10; i++ {
		// 生成 8 位数字的恢复码
		code := grand.Digits(8)
		// 格式化为 xxxx-xxxx 的形式
		codes[i] = fmt.Sprintf("%s-%s", code[:4], code[4:])
	}

	return codes
}

// RecoveryCodesToJSON 将恢复码转换为 JSON 字符串
func RecoveryCodesToJSON(codes []string) (string, error) {
	jsonData, err := json.Marshal(codes)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

// RecoveryCodesFromJSON 从 JSON 字符串解析恢复码
func RecoveryCodesFromJSON(jsonStr string) ([]string, error) {
	var codes []string
	if jsonStr == "" {
		return codes, nil
	}
	err := json.Unmarshal([]byte(jsonStr), &codes)
	if err != nil {
		return nil, err
	}
	return codes, nil
}