// Package csv provides CSV export functionality
package csv

import (
	"context"
	"encoding/csv"
	"fmt"
	"net/url"
	"reflect"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// ExportByStructs 导出切片结构体到CSV文件
func ExportByStructs(ctx context.Context, tags []string, list interface{}, fileName string, sheetName string) (err error) {
	// 如果tags为空或nil，从struct的excel tag中自动提取
	if len(tags) == 0 {
		tags, err = extractCSVTagsFromStruct(list)
		if err != nil {
			return gerror.Wrap(err, "提取CSV列头失败")
		}
	}

	r := ghttp.RequestFromCtx(ctx)
	if r == nil {
		err = gerror.New("ctx not http request")
		return
	}

	writer := r.Response.Writer
	
	// 设置CSV响应头
	writer.Header().Set("Content-Type", "text/csv; charset=utf-8")
	writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s.csv", url.QueryEscape(fileName)))
	writer.Header().Set("Content-Transfer-Encoding", "binary")
	writer.Header().Set("Access-Control-Expose-Headers", "Content-Disposition")

	// 写入UTF-8 BOM以确保Excel正确显示中文
	_, err = writer.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		return gerror.Wrap(err, "写入BOM失败")
	}

	// 创建CSV writer
	csvWriter := csv.NewWriter(writer)
	defer csvWriter.Flush()

	// 写入表头
	if err = csvWriter.Write(tags); err != nil {
		return gerror.Wrap(err, "写入CSV表头失败")
	}

	// 写入数据行
	for _, v := range gconv.Interfaces(list) {
		t := reflect.TypeOf(v)
		value := reflect.ValueOf(v)
		row := make([]string, 0, t.NumField())
		
		for i := 0; i < t.NumField(); i++ {
			val := value.Field(i).Interface()
			// 将所有字段转换为字符串
			strVal := gconv.String(val)
			row = append(row, strVal)
		}
		
		if err = csvWriter.Write(row); err != nil {
			return gerror.Wrap(err, "写入CSV数据行失败")
		}
	}

	return nil
}

// extractCSVTagsFromStruct 从struct的excel tag中提取列头（兼容现有代码）
func extractCSVTagsFromStruct(list interface{}) ([]string, error) {
	var tags []string
	
	// 获取切片的元素类型
	sliceValue := reflect.ValueOf(list)
	if sliceValue.Kind() != reflect.Slice || sliceValue.Len() == 0 {
		return nil, gerror.New("list必须是非空切片")
	}
	
	// 获取第一个元素的类型
	firstElement := sliceValue.Index(0)
	elementType := firstElement.Type()
	
	// 遍历struct字段，提取excel tag（保持兼容性）
	for i := 0; i < elementType.NumField(); i++ {
		field := elementType.Field(i)
		
		// 优先使用csv tag，其次使用excel tag，最后使用字段名
		csvTag := field.Tag.Get("csv")
		if csvTag != "" {
			tags = append(tags, csvTag)
		} else {
			excelTag := field.Tag.Get("excel")
			if excelTag != "" {
				tags = append(tags, excelTag)
			} else {
				// 如果没有tag，使用字段名
				tags = append(tags, field.Name)
			}
		}
	}
	
	if len(tags) == 0 {
		return nil, gerror.New("未找到有效的CSV标签")
	}
	
	return tags, nil
}

// escapeCSVField 转义CSV字段中的特殊字符
func escapeCSVField(field string) string {
	// 如果字段包含逗号、双引号或换行符，需要用双引号包围并转义内部的双引号
	if strings.Contains(field, ",") || strings.Contains(field, "\"") || strings.Contains(field, "\n") || strings.Contains(field, "\r") {
		// 将字段中的双引号转义为两个双引号
		escaped := strings.ReplaceAll(field, "\"", "\"\"")
		return fmt.Sprintf("\"%s\"", escaped)
	}
	return field
}