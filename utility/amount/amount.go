package amount

import (
	"fmt"
	"math"
	"math/big"
	"strconv"
	"strings"

	"admin-api/internal/codes"

	"github.com/gogf/gf/v2/errors/gerror"
)

// tenToPower computes 10^n using big.Int
func tenToPower(n uint8) *big.Int {
	return new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(n)), nil)
}

// FormatBalance converts a big.Int balance (smallest unit) to a human-readable decimal string.
// balance: The balance in the smallest unit (e.g., cents for USD with 2 decimals).
// decimals: The number of decimal places for the currency.
func FormatBalance(balance *big.Int, decimals uint8) string {
	if balance == nil {
		// Consider returning an error or a specific indicator for nil balance
		return "0" // Defaulting to "0" for nil input
	}

	// Handle zero decimals case
	if decimals == 0 {
		return balance.String()
	}

	divisor := tenToPower(decimals)
	absBalance := new(big.Int).Abs(balance) // Work with absolute value for formatting

	// Calculate integer part and remainder part
	integerPart := new(big.Int).Div(absBalance, divisor)
	remainderPart := new(big.Int).Mod(absBalance, divisor)

	// Format remainder part with leading zeros if necessary
	remainderStr := remainderPart.String()
	remainderLen := len(remainderStr)
	if remainderLen < int(decimals) {
		remainderStr = strings.Repeat("0", int(decimals)-remainderLen) + remainderStr
	}

	// Combine integer and remainder parts
	formatted := integerPart.String() + "." + remainderStr

	// Add negative sign if original balance was negative
	if balance.Sign() < 0 {
		formatted = "-" + formatted
	}

	return formatted
}

// ParseAmount converts a human-readable decimal string amount to a big.Int (smallest unit).
// amountStr: The decimal string representation of the amount (e.g., "123.45").
// decimals: The number of decimal places for the currency.
func ParseAmount(amountStr string, decimals uint8) (*big.Int, error) {
	amountStr = strings.TrimSpace(amountStr)
	if amountStr == "" {
		return nil, gerror.NewCodef(codes.CodeInvalidParameter, "金额字符串不能为空")
	}

	// Use big.Float for parsing, as it handles decimal strings directly.
	// Use high precision to avoid premature rounding during parsing. 256 bits should suffice.
	floatAmount, _, err := big.ParseFloat(amountStr, 10, 256, big.RoundingMode(0)) // Use ToNearestEven or other appropriate mode if needed
	if err != nil {
		return nil, gerror.NewCodef(codes.CodeInvalidAmountFormat, "无效的金额格式: %s", err.Error())
	}

	// Multiplier = 10 ^ decimals
	multiplier := new(big.Float).SetInt(tenToPower(decimals))

	// Scale the amount: amount * (10 ^ decimals)
	scaledAmount := new(big.Float).Mul(floatAmount, multiplier)

	// Convert the scaled amount to big.Int.
	// We need to check if the conversion is exact, especially after multiplication.
	// Add a small epsilon (0.5) before converting to Int for rounding to the nearest integer.
	// epsilon := new(big.Float).SetFloat64(0.5)
	// if scaledAmount.Sign() < 0 {
	// 	epsilon.Neg(epsilon)
	// }
	// scaledAmount.Add(scaledAmount, epsilon) // Apply rounding adjustment - Be cautious with this approach

	// Convert to big.Int. Int() truncates towards zero.
	intResult, accuracy := scaledAmount.Int(nil)

	// Check if the original string had more decimal places than allowed
	parts := strings.Split(amountStr, ".")

	if len(parts) == 2 && len(parts[1]) > int(decimals) {
		// return nil, gerror.NewCodef(codes.CodeInvalidAmountFormat, "金额的小数位数 (%d) 超过允许的最大位数 (%d)", len(parts[1]), decimals)
	}

	// Check accuracy. If not Exact, it means the original number wasn't perfectly representable
	// or the scaling resulted in a non-integer value *before* truncation.
	// For currency, we usually expect exact amounts or amounts within the allowed decimal places.
	// If accuracy is Below or Above, it might indicate an issue, but Int() truncates.
	// A more robust check might involve comparing the Int result back to the scaled Float.
	// However, given we checked decimal places earlier, truncation by Int() should be acceptable here.
	if accuracy != big.Exact {
		// This might happen if the input had too many *significant* digits before scaling,
		// even if decimal places were okay. Log a warning?
		// fmt.Printf("Warning: ParseAmount conversion accuracy for %s was %v\n", amountStr, accuracy)
	}

	return intResult, nil
}

// ValidateStringDecimalPlaces checks if a string representation of a number exceeds the specified decimal places.
// It handles various edge cases like scientific notation, empty strings, and non-numeric values.
// Returns true if the string has valid decimal places (not exceeding maxDecimals), false otherwise.
// An error is returned for invalid number formats.
func ValidateStringDecimalPlaces(s string, maxDecimals uint8) (bool, error) {
	s = strings.TrimSpace(s)
	if s == "" {
		return false, gerror.NewCodef(codes.CodeInvalidParameter, "数值字符串不能为空")
	}

	// Check if the string is in scientific notation (contains 'e' or 'E')
	if strings.ContainsAny(s, "eE") {
		// Convert scientific notation to a regular decimal
		f, err := strconv.ParseFloat(s, 64)
		if err != nil {
			return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "无效的数值格式: %s", err.Error())
		}

		// Convert float to string with enough precision to not lose information
		// and then validate the resulting string
		fStr := fmt.Sprintf("%g", f)
		return ValidateStringDecimalPlaces(fStr, maxDecimals)
	}

	// Split by decimal point
	parts := strings.Split(s, ".")
	if len(parts) == 1 {
		// No decimal point, so no decimal places
		return true, nil
	}

	// More than one decimal point is invalid
	if len(parts) > 2 {
		return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "无效的数值格式，含有多个小数点")
	}

	// Check if the integer part is a valid number
	_, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil && parts[0] != "-" && parts[0] != "+" && parts[0] != "" {
		return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "无效的整数部分格式: %s", err.Error())
	}

	// Check if the decimal part is a valid number
	decimalPart := parts[1]
	_, err = strconv.ParseUint(decimalPart, 10, 64)
	if err != nil {
		return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "无效的小数部分格式: %s", err.Error())
	}

	// Trim trailing zeros as they don't contribute to decimal precision
	decimalPart = strings.TrimRight(decimalPart, "0")
	if len(decimalPart) > int(maxDecimals) {
		return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "小数位数 (%d) 超过允许的最大位数 (%d)", len(decimalPart), maxDecimals)
	}

	return true, nil
}

// ValidateFloat64DecimalPlaces checks if a float64 value exceeds the specified decimal places.
// This handles floating-point precision issues by converting the float to a string and then checking.
// Returns true if the float has valid decimal places (not exceeding maxDecimals), false otherwise.
func ValidateFloat64DecimalPlaces(f float64, maxDecimals uint8) (bool, error) {
	// Special case handling for zero, infinity and NaN
	if f == float64(0) {
		return true, nil
	}
	if f != f { // NaN check
		return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "NaN 不是有效的数值")
	}
	// Check for infinity using math.IsInf
	if math.IsInf(f, 0) { // Infinity check, 0 means both positive and negative infinity
		return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "无穷大不是有效的数值")
	}
	// Convert to a scientific notation string to avoid precision issues
	fStr := fmt.Sprintf("%e", f)

	// Convert this to a standard format for validation
	parsed, err := strconv.ParseFloat(fStr, 64)
	if err != nil {
		return false, gerror.NewCodef(codes.CodeInvalidAmountFormat, "无效的数值格式: %s", err.Error())
	}

	// Now convert to a decimal string with full precision
	// This string format is best for checking the actual decimal places
	decimalStr := strconv.FormatFloat(parsed, 'f', -1, 64)

	// Trim trailing zeros after decimal point
	if strings.Contains(decimalStr, ".") {
		decimalStr = strings.TrimRight(strings.TrimRight(decimalStr, "0"), ".")
	}

	return ValidateStringDecimalPlaces(decimalStr, maxDecimals)
}
