services:
  admin-api:
    image: admin-api:latest
    build: ./
    restart: unless-stopped
    volumes:
      - ./config_variables.json:/home/<USER>/home
      - ./logs:/home/<USER>/home
    environment:
      # entrypoint.sh 需要这个变量来找到变量文件
      - PATH_TO_SECRET_FILE=/home/<USER>
    ports:
      # 映射 config.yaml 中定义的 eserver 和 healthCheck 端口
      - "7999:7999"
    networks:
      - xpay_app-network

networks:
  xpay_app-network:
    external: true
