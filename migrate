#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# The directory containing your SQL migration files.
MIGRATIONS_DIR="migrations/mysql"
# The database connection URL is expected in the environment variable DATABASE_URL.
# Example: export DATABASE_URL="mysql://user:password@tcp(127.0.0.1:3306)/database_name?multiStatements=true"
DATABASE_URL="mysql://root:root@tcp(127.0.0.1:3306)/admin-api?multiStatements=true"

# --- Helper Functions ---
usage() {
  echo "Usage: $0 <command> [arguments]"
  echo ""
  echo "A wrapper script for golang-migrate targeting '$MIGRATIONS_DIR'."
  echo ""
  echo "Commands:"
  echo "  create <name>      Create a new migration file pair (up/down) with sequence numbers."
  echo "                     Example: $0 create add_users_table"
  echo "  up [n]             Apply all or N up migrations."
  echo "                     Example: $0 up       (apply all)"
  echo "                     Example: $0 up 2     (apply next 2)"
  echo "  down [n]           Revert all or N down migrations (prompts if N is not specified)."
  echo "                     Example: $0 down     (revert all - asks confirmation)"
  echo "                     Example: $0 down 1   (revert last 1)"
  echo "  goto <version>     Migrate to a specific version."
  echo "                     Example: $0 goto 20230101..."
  echo "  version            Show the current migration version and dirty state."
  echo "  force <version>    Force set the database version (use with caution!)."
  echo "                     Example: $0 force 20230101..."
  echo "  drop               Drop everything in the database (use with extreme caution!)."
  echo ""
  echo "Environment Variables:"
  echo "  DATABASE_URL       Required. The database connection string."
  echo "                     Example: mysql://user:password@tcp(host:port)/database_name"
}

# --- Input Validation ---
# Check if migrate command exists
if ! command -v migrate &> /dev/null; then
    echo "Error: 'migrate' command not found."
    echo "Please install golang-migrate CLI: https://github.com/golang-migrate/migrate/tree/master/cmd/migrate"
    exit 1
fi


# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
  echo "Error: DATABASE_URL environment variable is not set."
  echo "Please set it before running this script."
  echo "Example: export DATABASE_URL=\"mysql://root:root@tcp(127.0.0.1:3306)/admin-api\""
  exit 1
fi

# Check if a command was provided
if [ $# -eq 0 ]; then
  usage
  exit 1
fi

# Check if migration directory exists
if [ ! -d "$MIGRATIONS_DIR" ]; then
    echo "Error: Migrations directory '$MIGRATIONS_DIR' not found."
    echo "Please create it or run the script from the correct project root directory."
    exit 1
fi


# --- Command Execution ---
COMMAND=$1
shift # Remove the command name from the list of arguments ($@)

echo "--- Running migrate command '$COMMAND' on '$MIGRATIONS_DIR' ---"

case $COMMAND in
  create)
    if [ -z "$1" ]; then
      echo "Error: Migration name is required for 'create'."
      usage
      exit 1
    fi
    MIGRATION_NAME=$1
    echo "Creating migration: $MIGRATION_NAME"
    # Use -seq to prefix with a timestamp for ordering
    migrate create -ext sql -dir "$MIGRATIONS_DIR" -seq "$MIGRATION_NAME"
    ;;

  up)
    echo "Applying up migrations..."
    migrate -database "$DATABASE_URL" -path "$MIGRATIONS_DIR" up "$@" # Pass any remaining arguments (like count)
    ;;

  down)
    # Add confirmation for potentially destructive 'down all'
    if [ -z "$1" ]; then
        read -p "Apply ALL down migrations? This can lead to data loss! (y/N): " confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            echo "Aborted."
            exit 0
        fi
        echo "Applying ALL down migrations..."
    else
        echo "Applying $1 down migration(s)..."
    fi
    migrate -database "$DATABASE_URL" -path "$MIGRATIONS_DIR" down "$@" # Pass any remaining arguments (like count)
    ;;

  goto)
    if [ -z "$1" ]; then
        echo "Error: Version number is required for 'goto'."
        usage
        exit 1
    fi
    VERSION=$1
    echo "Migrating to version $VERSION..."
    migrate -database "$DATABASE_URL" -path "$MIGRATIONS_DIR" goto "$VERSION"
    ;;

  version)
    echo "Checking migration version..."
    migrate -database "$DATABASE_URL" -path "$MIGRATIONS_DIR" version
    ;;

  force)
    if [ -z "$1" ]; then
      echo "Error: Version number is required for 'force'."
      usage
      exit 1
    fi
    VERSION=$1
    read -p "WARNING: Forcing version to '$VERSION'. This DOES NOT run migrations. Fixes dirty state. Are you sure? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 0
    fi
    echo "Forcing version to $VERSION..."
    migrate -database "$DATABASE_URL" -path "$MIGRATIONS_DIR" force "$VERSION"
    ;;

  drop)
    read -p "WARNING: This will drop EVERYTHING in the database specified by DATABASE_URL! Are you absolutely sure? (Type 'yes' to proceed): " confirm
    if [[ "$confirm" != "yes" ]]; then
        echo "Aborted."
        exit 0
    fi
    echo "Dropping database..."
    # We use -f here because we already added a strong confirmation prompt above
    migrate -database "$DATABASE_URL" -path "$MIGRATIONS_DIR" drop -f
    ;;

  *)
    echo "Error: Unknown command '$COMMAND'"
    usage
    exit 1
    ;;
esac

echo "--- Done ---"
exit 0