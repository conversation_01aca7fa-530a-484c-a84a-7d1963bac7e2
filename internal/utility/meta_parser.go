package utility

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/text/gregex"
)

// MetaInfo 存储从 g.Meta 解析出的信息
type MetaInfo struct {
	Path    string            // API 路径
	Method  string            // HTTP 方法
	Tags    []string          // API 标签
	Summary string            // API 摘要
	Extra   map[string]string // 其他元数据
}

// ParsedAPI 存储解析出的 API 信息
type ParsedAPI struct {
	Package    string    // 包名
	StructName string    // 结构体名称
	Meta       *MetaInfo // 元数据信息
	Fields     []string  // 字段信息（可选）
}

// ParseAllMeta 解析指定目录下所有文件中的 g.Meta 标签
func ParseAllMeta(apiDir string) ([]*ParsedAPI, error) {
	var result []*ParsedAPI

	// 递归遍历目录
	err := filepath.Walk(apiDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理 .go 文件
		if !info.IsDir() && strings.HasSuffix(path, ".go") {
			apis, err := parseFileForMeta(path)
			if err != nil {
				g.Log().Errorf(nil, "解析文件 %s 失败: %v", path, err)
				return nil // 继续处理其他文件
			}
			result = append(result, apis...)
		}
		return nil
	})

	return result, err
}

// parseFileForMeta 解析单个文件中的 g.Meta 标签
func parseFileForMeta(filePath string) ([]*ParsedAPI, error) {
	var result []*ParsedAPI

	// 创建文件集合
	fset := token.NewFileSet()

	// 解析文件
	file, err := parser.ParseFile(fset, filePath, nil, parser.ParseComments)
	if err != nil {
		return nil, fmt.Errorf("解析文件失败: %v", err)
	}

	// 获取包名
	packageName := file.Name.Name

	// 遍历所有声明
	for _, decl := range file.Decls {
		// 只处理通用声明（类型声明）
		genDecl, ok := decl.(*ast.GenDecl)
		if !ok || genDecl.Tok != token.TYPE {
			continue
		}

		// 遍历所有类型规范
		for _, spec := range genDecl.Specs {
			typeSpec, ok := spec.(*ast.TypeSpec)
			if !ok {
				continue
			}

			// 只处理结构体类型
			structType, ok := typeSpec.Type.(*ast.StructType)
			if !ok {
				continue
			}

			// 检查是否包含 g.Meta 字段
			for _, field := range structType.Fields.List {
				if len(field.Names) == 0 {
					// 匿名字段，检查是否为 g.Meta
					selectorExpr, ok := field.Type.(*ast.SelectorExpr)
					if !ok {
						continue
					}

					ident, ok := selectorExpr.X.(*ast.Ident)
					if !ok || ident.Name != "g" || selectorExpr.Sel.Name != "Meta" {
						continue
					}

					// 找到 g.Meta 字段，解析其标签
					if field.Tag != nil {
						tagValue := field.Tag.Value
						// 去除首尾的反引号
						tagValue = strings.Trim(tagValue, "`")

						meta := parseMeta(tagValue)
						api := &ParsedAPI{
							Package:    packageName,
							StructName: typeSpec.Name.Name,
							Meta:       meta,
						}
						result = append(result, api)
					}
				}
			}
		}
	}

	return result, nil
}

// parseMeta 解析 g.Meta 标签值
func parseMeta(tagValue string) *MetaInfo {
	meta := &MetaInfo{
		Extra: make(map[string]string),
	}

	// 使用正则表达式提取标签中的键值对
	matches, _ := gregex.MatchAllString(`(\w+):"([^"]*)"`, tagValue)
	for _, match := range matches {
		if len(match) >= 3 {
			key := match[1]
			value := match[2]

			switch key {
			case "path":
				meta.Path = value
			case "method":
				meta.Method = value
			case "tags":
				meta.Tags = strings.Split(value, ",")
			case "summary":
				meta.Summary = value
			default:
				meta.Extra[key] = value
			}
		}
	}

	return meta
}

// GetAllAPIs 获取所有 API 信息并返回
func GetAllAPIs() ([]*ParsedAPI, error) {
	// 获取 API 目录路径
	apiDir := "api"

	// 确保目录存在
	if !gfile.Exists(apiDir) {
		return nil, fmt.Errorf("API 目录不存在: %s", apiDir)
	}

	// 解析所有 Meta 数据
	return ParseAllMeta(apiDir)
}

// PrintAllAPIs 打印所有 API 信息（用于调试）
func PrintAllAPIs() {
	apis, err := GetAllAPIs()
	if err != nil {
		g.Log().Error(nil, err)
		return
	}

	for _, api := range apis {
		fmt.Printf("包: %s, 结构体: %s\n", api.Package, api.StructName)
		fmt.Printf("  路径: %s\n", api.Meta.Path)
		fmt.Printf("  方法: %s\n", api.Meta.Method)
		fmt.Printf("  标签: %v\n", api.Meta.Tags)
		fmt.Printf("  摘要: %s\n", api.Meta.Summary)
		fmt.Println("----------------------------")
	}

	fmt.Printf("总计: %d 个 API\n", len(apis))
}
