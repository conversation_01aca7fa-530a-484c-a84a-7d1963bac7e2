package utility

import (
	"net"
	"strings"
)

// IsValidIP checks if the given string is a valid IP address (IPv4 or IPv6)
func IsValidIP(ip string) bool {
	// Parse IP address
	parsedIP := net.ParseIP(strings.TrimSpace(ip))
	return parsedIP != nil
}

// IsValidCIDR checks if the given string is a valid CIDR notation
func IsValidCIDR(cidr string) bool {
	// Parse CIDR
	_, _, err := net.ParseCIDR(strings.TrimSpace(cidr))
	return err == nil
}

// IsValidIPOrCIDR checks if the given string is either a valid IP or CIDR
func IsValidIPOrCIDR(value string) bool {
	value = strings.TrimSpace(value)
	return IsValidIP(value) || IsValidCIDR(value)
}

// ValidateIPWhitelist validates a comma-separated list of IPs/CIDRs
func ValidateIPWhitelist(whitelist string) (bool, string) {
	if whitelist == "" {
		return true, ""
	}

	ips := strings.Split(whitelist, ",")
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip != "" && !IsValidIPOrCIDR(ip) {
			return false, ip
		}
	}

	return true, ""
}