package utility

import (
	"crypto/rand"
	"encoding/base32"
	"encoding/base64" // 新增：导入base64包
	"fmt"
	"net/url"
	"strings"

	"github.com/pquerna/otp/totp"
	"golang.org/x/crypto/bcrypt"
	qrcode "github.com/skip2/go-qrcode" // 导入go-qrcode库
)

// HashPassword 对密码进行哈希处理
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// VerifyPassword 验证密码是否正确
func VerifyPassword(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

// GenerateGoogle2FASecret 生成Google 2FA密钥
func GenerateGoogle2FASecret() string {
	// 生成32字节的随机数据
	secret := make([]byte, 20)
	rand.Read(secret)
	
	// 转换为base32编码
	encodedSecret := base32.StdEncoding.EncodeToString(secret)
	// 移除填充字符
	return strings.TrimRight(encodedSecret, "=")
}

// VerifyGoogle2FACode 验证Google 2FA验证码
func VerifyGoogle2FACode(secret, code string) bool {
	// 使用TOTP验证
	valid := totp.Validate(code, secret)
	return valid
}

// GenerateGoogle2FAQRCodeBase64 生成Google 2FA二维码Base64数据
func GenerateGoogle2FAQRCodeBase64(email, secret string) (string, error) {
	// 构建otpauth URL
	issuer := "Merchant API"
	authURL := fmt.Sprintf("otpauth://totp/%s:%s?secret=%s&issuer=%s",
		url.QueryEscape(issuer),
		url.QueryEscape(email),
		secret,
		url.QueryEscape(issuer))
	
	// 生成二维码并编码为Base64
	// qrcode.Medium 表示中等纠错级别
	// 256 表示图片大小为256x256像素
	png, err := qrcode.Encode(authURL, qrcode.Medium, 256)
	if err != nil {
		return "", err
	}
	
	// 返回Base64编码的PNG图片数据
	return "data:image/png;base64," + base64.StdEncoding.EncodeToString(png), nil
}