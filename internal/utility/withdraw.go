package utility

import (
	"strings"
)

// GetWithdrawStateText 获取提现状态文本描述
func GetWithdrawStateText(state uint) string {
	switch state {
	case 1:
		return "待审核"
	case 2:
		return "处理中"
	case 3:
		return "已拒绝"
	case 4:
		return "已完成"
	case 5:
		return "失败"
	case 6:
		return "已撤销"
	default:
		return "未知状态"
	}
}

// CanCancelWithdraw 判断提现记录是否可以撤销
func CanCancelWithdraw(state uint) bool {
	// 只有待审核状态(1)的提现申请可以撤销
	return state == 1
}

// GetEstimatedTime 获取预计到账时间
func GetEstimatedTime(chain string) string {
	switch strings.ToLower(chain) {
	case "tron", "trc20":
		return "5-30分钟"
	case "ethereum", "eth", "erc20":
		return "10-60分钟"
	case "bsc":
		return "3-15分钟"
	default:
		return "1-24小时"
	}
}