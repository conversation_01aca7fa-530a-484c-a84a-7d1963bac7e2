package router

import (
	"admin-api/internal/controller/system"
	"admin-api/internal/middleware"
	"admin-api/internal/packed"
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
)

func AdminV1(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/api", func(group *ghttp.RouterGroup) {
		// 全局中间件，应用于所有 /api 路由
		group.Middleware(
			// CORS 中间件放在最前面，确保所有请求都经过 CORS 处理
			middleware.CORS,
			// 请求日志
			middleware.RequestLogger,
		)

		// 需要登录的API路由组
		group.Group("/system", func(group *ghttp.RouterGroup) {
			// 注册需要认证的中间件
			group.Middleware(
				// auth认证
				middleware.AuthMiddleware,
				// 操作日志
				middleware.OperationLoggerMiddleware,
				// 响应处理
				packed.ResponseMiddleware,
			)
			// 绑定控制器
			group.Bind(
				// 系统功能控制器 (包含所有系统功能，包括兑换管理)
				system.NewV1(),
				// Add other system controllers here if needed
			)
		})

	})
}
