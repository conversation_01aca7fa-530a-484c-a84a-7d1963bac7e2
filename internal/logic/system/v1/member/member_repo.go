package member

import (
	v1 "admin-api/api/system/v1" // Import v1 for UserInfo type
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/member"
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type memberRepository struct{}

// NewMemberRepository creates and returns a new instance of IMemberRepository.
func NewMemberRepository() member.IMemberRepository {
	return &memberRepository{}
}

// Create inserts a new admin member record.
func (r *memberRepository) Create(ctx context.Context, memberDo *do.AdminMember) (id int64, err error) {
	// Ensure timestamps are set if not provided
	if memberDo.CreatedAt == nil {
		memberDo.CreatedAt = gtime.Now()
	}
	if memberDo.UpdatedAt == nil {
		memberDo.UpdatedAt = gtime.Now()
	}
	// Tree is set later in the transaction
	memberDo.Tree = ""

	result, err := dao.AdminMember.Ctx(ctx).Data(memberDo).OmitEmpty().Insert()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "创建用户失败")
	}
	id, err = result.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新用户ID失败")
	}
	return id, nil
}

// UpdateFields updates specific fields for a member, typically within a transaction.
func (r *memberRepository) UpdateFields(ctx context.Context, tx gdb.TX, id int64, data g.Map) error {
	if len(data) == 0 {
		return nil // Nothing to update
	}
	// Ensure UpdatedAt is always updated
	data[dao.AdminMember.Columns().UpdatedAt] = gtime.Now()

	_, err := tx.Model(dao.AdminMember.Table()).
		Data(data).
		Where(dao.AdminMember.Columns().Id, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新用户字段失败 (ID: %d)", id)
	}
	return nil
}

// Delete performs a soft delete on members within a transaction.
func (r *memberRepository) Delete(ctx context.Context, tx gdb.TX, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	_, err := tx.Model(dao.AdminMember.Table()).
		Data(g.Map{dao.AdminMember.Columns().DeletedAt: gtime.Now()}).
		WhereIn(dao.AdminMember.Columns().Id, ids).
		WhereNull(dao.AdminMember.Columns().DeletedAt). // Only soft delete active records
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "软删除用户失败 (IDs: %v)", ids)
	}
	return nil
}

// GetByID retrieves a single admin member by ID.
func (r *memberRepository) GetByID(ctx context.Context, id int64) (*entity.AdminMember, error) {
	var memberEntity *entity.AdminMember
	err := dao.AdminMember.Ctx(ctx).
		Where(dao.AdminMember.Columns().Id, id).
		WhereNull(dao.AdminMember.Columns().DeletedAt).
		Scan(&memberEntity)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeMemberNotFound, "用户不存在 (ID: %d)", id)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询用户失败 (ID: %d)", id)
	}
	return memberEntity, nil
}

// GetByUsername retrieves a single admin member by username.
func (r *memberRepository) GetByUsername(ctx context.Context, username string) (*entity.AdminMember, error) {
	var memberEntity *entity.AdminMember
	err := dao.AdminMember.Ctx(ctx).
		Where(dao.AdminMember.Columns().Username, username).
		WhereNull(dao.AdminMember.Columns().DeletedAt).
		Scan(&memberEntity)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // Return nil, nil if not found, let service layer decide error code
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询用户失败 (Username: %s)", username)
	}
	return memberEntity, nil
}

// ExistsByEmail checks if an email exists, optionally excluding an ID.
func (r *memberRepository) ExistsByEmail(ctx context.Context, email string, excludeId ...int64) (bool, error) {
	m := dao.AdminMember.Ctx(ctx).
		Where(dao.AdminMember.Columns().Email, email).
		WhereNull(dao.AdminMember.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.AdminMember.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查邮箱是否存在失败 (Email: %s)", email)
	}
	return count > 0, nil
}

// ExistsByMobile checks if a mobile number exists, optionally excluding an ID.
func (r *memberRepository) ExistsByMobile(ctx context.Context, mobile string, excludeId ...int64) (bool, error) {
	m := dao.AdminMember.Ctx(ctx).
		Where(dao.AdminMember.Columns().Mobile, mobile).
		WhereNull(dao.AdminMember.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.AdminMember.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查手机号是否存在失败 (Mobile: %s)", mobile)
	}
	return count > 0, nil
}

// ExistsByInviteCode checks if an invite code exists, optionally excluding an ID.
func (r *memberRepository) ExistsByInviteCode(ctx context.Context, code string, excludeId ...int64) (bool, error) {
	if code == "" { // Invite code can be empty
		return false, nil
	}
	m := dao.AdminMember.Ctx(ctx).
		Where(dao.AdminMember.Columns().InviteCode, code).
		WhereNull(dao.AdminMember.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.AdminMember.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查邀请码是否存在失败 (Code: %s)", code)
	}
	return count > 0, nil
}

// List retrieves a paginated list of admin members based on conditions.
func (r *memberRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminMember, total int, err error) {
	m := dao.AdminMember.Ctx(ctx).WhereNull(dao.AdminMember.Columns().DeletedAt)

	// Apply conditions dynamically
	if username, ok := condition["username"]; ok && gconv.String(username) != "" {
		m = m.WhereLike(dao.AdminMember.Columns().Username, "%"+gconv.String(username)+"%")
	}
	if realName, ok := condition["realName"]; ok && gconv.String(realName) != "" {
		m = m.WhereLike(dao.AdminMember.Columns().RealName, "%"+gconv.String(realName)+"%")
	}
	if mobile, ok := condition["mobile"]; ok && gconv.String(mobile) != "" {
		m = m.Where(dao.AdminMember.Columns().Mobile, gconv.String(mobile))
	}
	if status, ok := condition["status"]; ok {
		// Ensure status is treated correctly (e.g., as *int)
		statusVal := gconv.Int(status)
		if statusVal == 0 || statusVal == 1 { // Assuming 0 and 1 are valid statuses
			m = m.Where(dao.AdminMember.Columns().Status, statusVal)
		}
	}
	// Filtering by roleId and postId requires joining or subqueries, handle in service layer if needed complexly.

	total, err = m.Count()
	if err != nil || total == 0 {
		// Wrap error only if it's not sql.ErrNoRows (or if total is 0)
		if err != nil && err != sql.ErrNoRows {
			return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "获取用户总数失败")
		}
		return []*entity.AdminMember{}, 0, nil // Return empty list if no records
	}

	list = make([]*entity.AdminMember, 0)
	err = m.Page(page, pageSize).OrderDesc(dao.AdminMember.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户列表失败")
	}

	return list, total, nil
}

// UpdateStatus updates the status of an admin member.
func (r *memberRepository) UpdateStatus(ctx context.Context, id int64, status int) error {
	_, err := dao.AdminMember.Ctx(ctx).
		Data(g.Map{
			dao.AdminMember.Columns().Status:    status,
			dao.AdminMember.Columns().UpdatedAt: gtime.Now(),
		}).
		Where(dao.AdminMember.Columns().Id, id).
		WhereNull(dao.AdminMember.Columns().DeletedAt).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新用户状态失败 (ID: %d)", id)
	}
	return nil
}

// UpdatePassword updates the password hash for an admin member.
func (r *memberRepository) UpdatePassword(ctx context.Context, id int64, passwordHash string) error {
	_, err := dao.AdminMember.Ctx(ctx).
		Data(g.Map{
			dao.AdminMember.Columns().PasswordHash: passwordHash,
			dao.AdminMember.Columns().Salt:         "", // Salt is included in bcrypt hash
			dao.AdminMember.Columns().UpdatedAt:    gtime.Now(),
		}).
		Where(dao.AdminMember.Columns().Id, id).
		WhereNull(dao.AdminMember.Columns().DeletedAt).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新用户密码失败 (ID: %d)", id)
	}
	return nil
}

// GetDirectSubordinatesCount counts the number of direct subordinates for a member.
func (r *memberRepository) GetDirectSubordinatesCount(ctx context.Context, id int64) (int, error) {
	count, err := dao.AdminMember.Ctx(ctx).
		Where(dao.AdminMember.Columns().Pid, id).
		WhereNull(dao.AdminMember.Columns().DeletedAt).
		Count()
	if err != nil {
		return 0, gerror.WrapCodef(codes.CodeInternalError, err, "查询直接下级数量失败 (ID: %d)", id)
	}
	return count, nil
}

// FindDescendantsByTree finds all descendants of a member based on the tree path prefix.
func (r *memberRepository) FindDescendantsByTree(ctx context.Context, treePrefix string) ([]*entity.AdminMember, error) {
	if !strings.HasSuffix(treePrefix, "/") {
		treePrefix += "/" // Ensure the prefix ends with a slash for correct matching
	}
	var descendants []*entity.AdminMember
	// Use LIKE pattern: treePrefix + ID + /%
	// We need to exclude the member itself, so we check tree != treePrefix
	err := dao.AdminMember.Ctx(ctx).
		WhereLike(dao.AdminMember.Columns().Tree, treePrefix+"%"). // Find all nodes starting with the prefix
		WhereNot(dao.AdminMember.Columns().Tree, treePrefix).      // Exclude the node itself
		WhereNull(dao.AdminMember.Columns().DeletedAt).
		Scan(&descendants)
	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询子孙节点失败 (TreePrefix: %s)", treePrefix)
	}
	return descendants, nil
}

// BatchUpdateLevelTree updates the level and tree path for multiple members within a transaction.
func (r *memberRepository) BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error {
	if len(updates) == 0 {
		return nil
	}
	// GoFrame doesn't directly support batch update with different values per row easily.
	// We iterate and update one by one within the transaction.
	for id, data := range updates {
		// Ensure UpdatedAt is set for each update
		data[dao.AdminMember.Columns().UpdatedAt] = gtime.Now()
		_, err := tx.Model(dao.AdminMember.Table()).
			Data(data).
			Where(dao.AdminMember.Columns().Id, id).
			Update()
		if err != nil {
			// Rollback will be handled by the caller managing the transaction
			return gerror.WrapCodef(codes.CodeInternalError, err, "批量更新 Level/Tree 失败 (ID: %d)", id)
		}
	}
	return nil
}

// BuildMemberTree is a utility function to build the tree path.
// Moved from DAO as it's more of a business logic utility.
func BuildMemberTree(parentTree string, memberId int64) string {
	if !strings.HasSuffix(parentTree, "/") {
		parentTree += "/"
	}
	return fmt.Sprintf("%s%d/", parentTree, memberId)
}

// ListForNotice retrieves a paginated list of users formatted for notice selection/status display.
func (r *memberRepository) ListForNotice(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.UserInfo, total int, err error) {
	// Build the query using only member table
	m := dao.AdminMember.Ctx(ctx).As("m").
		Where(fmt.Sprintf("m.%s", dao.AdminMember.Columns().DeletedAt), nil)

	// Apply conditions dynamically
	if username, ok := condition["username"]; ok && gconv.String(username) != "" {
		m = m.Where(fmt.Sprintf("m.%s LIKE ?", dao.AdminMember.Columns().Username), "%"+gconv.String(username)+"%")
	}
	// Removed deptId condition
	// Add filtering for roleId and postId if needed (requires more joins)

	total, err = m.Count()
	if err != nil || total == 0 {
		if err != nil && err != sql.ErrNoRows {
			return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "获取公告用户列表总数失败")
		}
		return []*v1.UserInfo{}, 0, nil
	}

	list = make([]*v1.UserInfo, 0)

	// Select fields only from member table
	fields := fmt.Sprintf("m.%s as id, m.%s as username, m.%s as avatar, '' as dept_name", // Always return empty dept_name
		dao.AdminMember.Columns().Id, dao.AdminMember.Columns().Username, dao.AdminMember.Columns().Avatar)

	err = m.Fields(fields).Page(page, pageSize).OrderAsc(fmt.Sprintf("m.%s", dao.AdminMember.Columns().Id)).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询公告用户列表失败")
	}

	return list, total, nil
}

// GetAllActiveUserIDs retrieves IDs of all active users.
func (r *memberRepository) GetAllActiveUserIDs(ctx context.Context) ([]int64, error) {
	ids, err := dao.AdminMember.Ctx(ctx).
		Where(dao.AdminMember.Columns().Status, 1). // Assuming 1 means active
		WhereNull(dao.AdminMember.Columns().DeletedAt).
		Array(dao.AdminMember.Columns().Id)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取所有活动用户ID失败")
	}
	return gconv.Int64s(ids), nil // Convert []gdb.Value to []int64
}

// GetUserInfoMap retrieves a map of user IDs to UserInfo structs.
func (r *memberRepository) GetUserInfoMap(ctx context.Context, ids []int64) (map[int64]*v1.UserInfo, error) {
	userMap := make(map[int64]*v1.UserInfo)
	if len(ids) == 0 {
		return userMap, nil
	}

	var userList []*v1.UserInfo
	// Build the query using only member table
	m := dao.AdminMember.Ctx(ctx).As("m").
		WhereIn(fmt.Sprintf("m.%s", dao.AdminMember.Columns().Id), ids)

	// Select fields only from member table
	fields := fmt.Sprintf("m.%s as id, m.%s as username, m.%s as avatar, '' as dept_name", // Always return empty dept_name
		dao.AdminMember.Columns().Id, dao.AdminMember.Columns().Username, dao.AdminMember.Columns().Avatar)

	err := m.Fields(fields).Scan(&userList)
	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "批量查询用户信息失败 (IDs: %v)", ids)
	}

	for _, userInfo := range userList {
		if userInfo != nil { // Add nil check
			userMap[userInfo.Id] = userInfo
		}
	}

	return userMap, nil
}
