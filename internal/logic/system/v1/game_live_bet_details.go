package v1

import (
	"context"
	"strings"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
)

// GetGameLiveBetDetailsList 获取Live游戏投注详情列表
func (s *sSystemLogic) GetGameLiveBetDetailsList(ctx context.Context, req *v1.GetGameLiveBetDetailsListReq) (res *v1.GetGameLiveBetDetailsListRes, err error) {
	// 使用原生SQL查询以支持多表JOIN
	db := g.DB()
	
	// 构建基础查询
	query := `
		SELECT 
			glbd.*,
			uba.telegram_id,
			uba.telegram_username,
			uba.first_name,
			t.username as tenant_username
		FROM game_live_bet_details glbd
		LEFT JOIN user_backup_accounts uba ON glbd.user_id = uba.user_id AND uba.is_master = 1 AND uba.deleted_at IS NULL
		LEFT JOIN tenants t ON glbd.tenant_id = t.tenant_id AND t.deleted_at IS NULL
		WHERE 1=1
	`
	
	// 构建WHERE条件和参数
	var conditions []string
	var params []interface{}
	
	// 原有条件
	if req.Username != "" {
		conditions = append(conditions, "glbd.username LIKE ?")
		params = append(params, "%"+req.Username+"%")
	}
	if req.GameCode != "" {
		conditions = append(conditions, "glbd.game_code = ?")
		params = append(params, req.GameCode)
	}
	if req.GameCategory != "" {
		conditions = append(conditions, "glbd.game_category = ?")
		params = append(params, req.GameCategory)
	}
	if req.BetOrderNo != "" {
		conditions = append(conditions, "glbd.bet_order_no = ?")
		params = append(params, req.BetOrderNo)
	}
	if req.SessionId != "" {
		conditions = append(conditions, "glbd.session_id = ?")
		params = append(params, req.SessionId)
	}
	if req.Currency != "" {
		conditions = append(conditions, "glbd.currency = ?")
		params = append(params, req.Currency)
	}
	if req.ProductType != nil {
		conditions = append(conditions, "glbd.product_type = ?")
		params = append(params, *req.ProductType)
	}
	if req.ApiStatus != nil {
		conditions = append(conditions, "glbd.api_status = ?")
		params = append(params, *req.ApiStatus)
	}
	
	// 时间范围过滤
	if req.BetTimeStart != "" {
		conditions = append(conditions, "glbd.bet_time >= ?")
		params = append(params, req.BetTimeStart)
	}
	if req.BetTimeEnd != "" {
		conditions = append(conditions, "glbd.bet_time <= ?")
		params = append(params, req.BetTimeEnd)
	}
	
	// 金额范围过滤
	if req.MinBetAmount != "" {
		conditions = append(conditions, "glbd.bet_amount >= ?")
		params = append(params, req.MinBetAmount)
	}
	if req.MaxBetAmount != "" {
		conditions = append(conditions, "glbd.bet_amount <= ?")
		params = append(params, req.MaxBetAmount)
	}
	if req.MinWinAmount != "" {
		conditions = append(conditions, "glbd.win_amount >= ?")
		params = append(params, req.MinWinAmount)
	}
	if req.MaxWinAmount != "" {
		conditions = append(conditions, "glbd.win_amount <= ?")
		params = append(params, req.MaxWinAmount)
	}
	
	// 新增Telegram字段过滤
	if req.TelegramId != nil {
		conditions = append(conditions, "uba.telegram_id = ?")
		params = append(params, *req.TelegramId)
	}
	if req.TelegramUsername != "" {
		conditions = append(conditions, "uba.telegram_username LIKE ?")
		params = append(params, "%"+req.TelegramUsername+"%")
	}
	if req.FirstName != "" {
		conditions = append(conditions, "uba.first_name LIKE ?")
		params = append(params, "%"+req.FirstName+"%")
	}
	
	// 租户用户名过滤
	if req.TenantUsername != "" {
		conditions = append(conditions, "t.username LIKE ?")
		params = append(params, "%"+req.TenantUsername+"%")
	}
	
	// 添加WHERE条件
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}
	
	// 获取总数
	countQuery := strings.Replace(query, "SELECT glbd.*, uba.telegram_id, uba.telegram_username, uba.first_name, t.username as tenant_username", "SELECT COUNT(*) as total", 1)
	var total int
	result, err := db.Ctx(ctx).Raw(countQuery, params...).Value()
	if err != nil {
		return nil, gerror.Wrap(err, "count game live bet details failed")
	}
	if result != nil {
		total = gconv.Int(result)
	}
	
	// 添加排序和分页
	query += " ORDER BY glbd.bet_time DESC, glbd.id DESC"
	query += " LIMIT ? OFFSET ?"
	params = append(params, req.PageSize, (req.Page-1)*req.PageSize)
	
	// 执行查询
	type GameLiveBetDetailsWithJoin struct {
		entity.GameLiveBetDetails
		TelegramId       *int64  `json:"telegram_id"`
		TelegramUsername *string `json:"telegram_username"`
		FirstName        *string `json:"first_name"`
		TenantUsername   *string `json:"tenant_username"`
	}
	
	var list []GameLiveBetDetailsWithJoin
	err = db.Ctx(ctx).Raw(query, params...).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "get game live bet details list failed")
	}
	
	// 转换为响应结构
	listItems := make([]*v1.GameLiveBetDetailsListItem, len(list))
	for i, item := range list {
		listItems[i] = &v1.GameLiveBetDetailsListItem{
			Id:                item.Id,
			Username:          item.Username,
			BetAmount:         item.BetAmount,
			ValidBetAmount:    item.ValidBetAmount,
			WinAmount:         item.WinAmount,
			NetPnl:            item.NetPnl,
			Currency:          item.Currency,
			GameCode:          item.GameCode,
			ProductType:       item.ProductType,
			GameCategory:      item.GameCategory,
			BetOrderNo:        item.BetOrderNo,
			SessionId:         item.SessionId,
			BetTime:           item.BetTime,
			TransactionTime:   item.TransactionTime,
			AdditionalDetails: item.AdditionalDetails,
			ApiStatus:         item.ApiStatus,
			ApiErrorDesc:      item.ApiErrorDesc,
			CreatedAt:         item.CreatedAt,
			UpdatedAt:         item.UpdatedAt,
			TelegramId:        item.TelegramId,
			TelegramUsername:  item.TelegramUsername,
			FirstName:         item.FirstName,
			TenantUsername:    item.TenantUsername,
		}
	}
	
	res = &v1.GetGameLiveBetDetailsListRes{
		Total: total,
		List:  listItems,
	}
	return res, nil
}

// GetGameLiveBetDetailsDetail 获取Live游戏投注详情详情
func (s *sSystemLogic) GetGameLiveBetDetailsDetail(ctx context.Context, req *v1.GetGameLiveBetDetailsDetailReq) (res *v1.GetGameLiveBetDetailsDetailRes, err error) {
	// 使用原生SQL查询以支持多表JOIN
	db := g.DB()
	
	query := `
		SELECT 
			glbd.*,
			uba.telegram_id,
			uba.telegram_username,
			uba.first_name,
			t.username as tenant_username
		FROM game_live_bet_details glbd
		LEFT JOIN user_backup_accounts uba ON glbd.user_id = uba.user_id AND uba.is_master = 1 AND uba.deleted_at IS NULL
		LEFT JOIN tenants t ON glbd.tenant_id = t.tenant_id AND t.deleted_at IS NULL
		WHERE glbd.id = ?
	`
	
	type GameLiveBetDetailsWithJoin struct {
		entity.GameLiveBetDetails
		TelegramId       *int64  `json:"telegram_id"`
		TelegramUsername *string `json:"telegram_username"`
		FirstName        *string `json:"first_name"`
		TenantUsername   *string `json:"tenant_username"`
	}
	
	var betDetails GameLiveBetDetailsWithJoin
	err = db.Ctx(ctx).Raw(query, req.Id).Scan(&betDetails)
	if err != nil {
		return nil, gerror.Wrap(err, "get game live bet details failed")
	}
	if betDetails.Id == 0 {
		return nil, gerror.New("game live bet details not found")
	}

	res = &v1.GetGameLiveBetDetailsDetailRes{
		GameLiveBetDetailsListItem: &v1.GameLiveBetDetailsListItem{
			Id:                betDetails.Id,
			Username:          betDetails.Username,
			BetAmount:         betDetails.BetAmount,
			ValidBetAmount:    betDetails.ValidBetAmount,
			WinAmount:         betDetails.WinAmount,
			NetPnl:            betDetails.NetPnl,
			Currency:          betDetails.Currency,
			GameCode:          betDetails.GameCode,
			ProductType:       betDetails.ProductType,
			GameCategory:      betDetails.GameCategory,
			BetOrderNo:        betDetails.BetOrderNo,
			SessionId:         betDetails.SessionId,
			BetTime:           betDetails.BetTime,
			TransactionTime:   betDetails.TransactionTime,
			AdditionalDetails: betDetails.AdditionalDetails,
			ApiStatus:         betDetails.ApiStatus,
			ApiErrorDesc:      betDetails.ApiErrorDesc,
			CreatedAt:         betDetails.CreatedAt,
			UpdatedAt:         betDetails.UpdatedAt,
			TelegramId:        betDetails.TelegramId,
			TelegramUsername:  betDetails.TelegramUsername,
			FirstName:         betDetails.FirstName,
			TenantUsername:    betDetails.TenantUsername,
		},
	}
	return res, nil
}

// GetGameLiveBetDetailsStats 获取Live游戏投注详情统计
func (s *sSystemLogic) GetGameLiveBetDetailsStats(ctx context.Context, req *v1.GetGameLiveBetDetailsStatsReq) (res *v1.GetGameLiveBetDetailsStatsRes, err error) {
	var (
		m = dao.GameLiveBetDetails.Ctx(ctx)
	)

	// 构建查询条件
	if req.Username != "" {
		m = m.WhereLike("username", "%"+req.Username+"%")
	}
	if req.GameCode != "" {
		m = m.Where("game_code", req.GameCode)
	}
	if req.GameCategory != "" {
		m = m.Where("game_category", req.GameCategory)
	}
	if req.Currency != "" {
		m = m.Where("currency", req.Currency)
	}
	if req.ProductType != nil {
		m = m.Where("product_type", *req.ProductType)
	}

	// 时间范围过滤
	if req.BetTimeStart != "" {
		m = m.WhereGTE("bet_time", req.BetTimeStart)
	}
	if req.BetTimeEnd != "" {
		m = m.WhereLTE("bet_time", req.BetTimeEnd)
	}

	// 执行统计查询
	var statsResult struct {
		TotalRecords   int             `json:"total_records"`
		TotalBetAmount decimal.Decimal `json:"total_bet_amount"`
		TotalValidBet  decimal.Decimal `json:"total_valid_bet"`
		TotalWinAmount decimal.Decimal `json:"total_win_amount"`
		TotalNetPnl    decimal.Decimal `json:"total_net_pnl"`
		UniqueUsers    int             `json:"unique_users"`
		UniqueGames    int             `json:"unique_games"`
		WinRecords     int             `json:"win_records"`
	}

	err = m.Fields(
		"COUNT(*) as total_records",
		"COALESCE(SUM(bet_amount), 0) as total_bet_amount",
		"COALESCE(SUM(valid_bet_amount), 0) as total_valid_bet",
		"COALESCE(SUM(win_amount), 0) as total_win_amount",
		"COALESCE(SUM(net_pnl), 0) as total_net_pnl",
		"COUNT(DISTINCT username) as unique_users",
		"COUNT(DISTINCT game_code) as unique_games",
		"SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as win_records",
	).Scan(&statsResult)

	if err != nil {
		return nil, gerror.Wrap(err, "get game live bet details stats failed")
	}

	// 计算胜率
	var winRate float64
	if statsResult.TotalRecords > 0 {
		winRate = float64(statsResult.WinRecords) / float64(statsResult.TotalRecords) * 100
	}

	// 计算平均投注金额和平均赢金额
	var avgBetAmount, avgWinAmount decimal.Decimal
	if statsResult.TotalRecords > 0 {
		avgBetAmount = statsResult.TotalBetAmount.Div(decimal.NewFromInt(int64(statsResult.TotalRecords)))
		avgWinAmount = statsResult.TotalWinAmount.Div(decimal.NewFromInt(int64(statsResult.TotalRecords)))
	}

	res = &v1.GetGameLiveBetDetailsStatsRes{
		TotalRecords:   statsResult.TotalRecords,
		TotalBetAmount: statsResult.TotalBetAmount,
		TotalValidBet:  statsResult.TotalValidBet,
		TotalWinAmount: statsResult.TotalWinAmount,
		TotalNetPnl:    statsResult.TotalNetPnl,
		UniqueUsers:    statsResult.UniqueUsers,
		UniqueGames:    statsResult.UniqueGames,
		WinRate:        winRate,
		AvgBetAmount:   avgBetAmount,
		AvgWinAmount:   avgWinAmount,
	}

	return res, nil
}

// ExportGameLiveBetDetails 导出Live游戏投注详情
func (s *sSystemLogic) ExportGameLiveBetDetails(ctx context.Context, req *v1.ExportGameLiveBetDetailsReq) (res *v1.ExportGameLiveBetDetailsRes, err error) {
	var (
		m    = dao.GameLiveBetDetails.Ctx(ctx)
		list []*entity.GameLiveBetDetails
	)

	// 构建查询条件（与列表查询相同）
	if req.Username != "" {
		m = m.WhereLike("username", "%"+req.Username+"%")
	}
	if req.GameCode != "" {
		m = m.Where("game_code", req.GameCode)
	}
	if req.GameCategory != "" {
		m = m.Where("game_category", req.GameCategory)
	}
	if req.BetOrderNo != "" {
		m = m.Where("bet_order_no", req.BetOrderNo)
	}
	if req.SessionId != "" {
		m = m.Where("session_id", req.SessionId)
	}
	if req.Currency != "" {
		m = m.Where("currency", req.Currency)
	}
	if req.ProductType != nil {
		m = m.Where("product_type", *req.ProductType)
	}
	if req.ApiStatus != nil {
		m = m.Where("api_status", *req.ApiStatus)
	}

	// 时间范围过滤
	if req.BetTimeStart != "" {
		m = m.WhereGTE("bet_time", req.BetTimeStart)
	}
	if req.BetTimeEnd != "" {
		m = m.WhereLTE("bet_time", req.BetTimeEnd)
	}

	// 金额范围过滤
	if req.MinBetAmount != "" {
		if minAmount, err := decimal.NewFromString(req.MinBetAmount); err == nil {
			m = m.WhereGTE("bet_amount", minAmount)
		}
	}
	if req.MaxBetAmount != "" {
		if maxAmount, err := decimal.NewFromString(req.MaxBetAmount); err == nil {
			m = m.WhereLTE("bet_amount", maxAmount)
		}
	}
	if req.MinWinAmount != "" {
		if minWin, err := decimal.NewFromString(req.MinWinAmount); err == nil {
			m = m.WhereGTE("win_amount", minWin)
		}
	}
	if req.MaxWinAmount != "" {
		if maxWin, err := decimal.NewFromString(req.MaxWinAmount); err == nil {
			m = m.WhereLTE("win_amount", maxWin)
		}
	}

	// 获取导出数据（限制最大导出数量，避免内存溢出）
	err = m.Limit(50000). // 最大导出5万条记录
		OrderDesc("bet_time").
		OrderDesc("id").
		Scan(&list)
	
	if err != nil {
		return nil, gerror.Wrap(err, "get export data failed")
	}

	// 生成导出文件
	fileName := "game_live_bet_details_" + gconv.String(gtime.Timestamp()) + "." + req.ExportFormat
	
	// 这里应该根据具体的文件存储服务生成文件和下载链接
	// 示例实现，实际应该调用具体的导出服务
	downloadUrl := "/exports/" + fileName
	
	// TODO: 实际的文件生成逻辑
	// 1. 根据格式生成Excel或CSV文件
	// 2. 保存到文件存储服务
	// 3. 返回下载链接

	res = &v1.ExportGameLiveBetDetailsRes{
		DownloadUrl: downloadUrl,
		FileName:    fileName,
	}

	return res, nil
}

// buildGameLiveBetDetailsWhereConditions 构建Live游戏投注详情查询条件的辅助函数
func (s *sSystemLogic) buildGameLiveBetDetailsWhereConditions(m *gdb.Model, conditions map[string]interface{}) *gdb.Model {
	for field, value := range conditions {
		if value == nil || value == "" {
			continue
		}
		
		switch field {
		case "username":
			if username, ok := value.(string); ok && username != "" {
				m = m.WhereLike("username", "%"+username+"%")
			}
		case "game_code":
			m = m.Where("game_code", value)
		case "game_category":
			m = m.Where("game_category", value)
		case "bet_order_no":
			m = m.Where("bet_order_no", value)
		case "session_id":
			m = m.Where("session_id", value)
		case "currency":
			m = m.Where("currency", value)
		case "product_type":
			m = m.Where("product_type", value)
		case "api_status":
			m = m.Where("api_status", value)
		case "bet_time_start":
			m = m.WhereGTE("bet_time", value)
		case "bet_time_end":
			m = m.WhereLTE("bet_time", value)
		case "min_bet_amount":
			if amountStr, ok := value.(string); ok && amountStr != "" {
				if amount, err := decimal.NewFromString(amountStr); err == nil {
					m = m.WhereGTE("bet_amount", amount)
				}
			}
		case "max_bet_amount":
			if amountStr, ok := value.(string); ok && amountStr != "" {
				if amount, err := decimal.NewFromString(amountStr); err == nil {
					m = m.WhereLTE("bet_amount", amount)
				}
			}
		case "min_win_amount":
			if amountStr, ok := value.(string); ok && amountStr != "" {
				if amount, err := decimal.NewFromString(amountStr); err == nil {
					m = m.WhereGTE("win_amount", amount)
				}
			}
		case "max_win_amount":
			if amountStr, ok := value.(string); ok && amountStr != "" {
				if amount, err := decimal.NewFromString(amountStr); err == nil {
					m = m.WhereLTE("win_amount", amount)
				}
			}
		}
	}
	return m
}