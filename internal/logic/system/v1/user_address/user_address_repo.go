package user_address

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/user_address"
	"context"
	"database/sql"

	"github.com/gogf/gf/v2/errors/gerror"
)

// userAddressRepository implements the IUserAddressRepository interface.
type userAddressRepository struct{}

// NewUserAddressRepository creates and returns a new instance of userAddressRepository.
func NewUserAddressRepository() user_address.IUserAddressRepository {
	return &userAddressRepository{}
}

// List retrieves a paginated list of user addresses based on the provided criteria.
// Deprecated: 此方法已废弃，仅保留空实现以保持接口兼容性
func (r *userAddressRepository) List(ctx context.Context, req *v1.ListUserAddressesReq) (list []*v1.UserAddressListItem, total int, err error) {
	// 临时返回空数组，实际功能已迁移到Logic层直接调用DAO
	return make([]*v1.UserAddressListItem, 0), 0, nil
}

// GetByID retrieves a single user address by its ID.
func (r *userAddressRepository) GetByID(ctx context.Context, id uint) (*entity.UserAddress, error) {
	var address *entity.UserAddress
	err := dao.UserAddress.Ctx(ctx).Where(dao.UserAddress.Columns().UserAddressId, id).Scan(&address)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeUserRechargeNotFound, "充值地址不存在")
		}
		return nil, gerror.Wrap(err, "查询充值地址失败")
	}
	return address, nil
}
