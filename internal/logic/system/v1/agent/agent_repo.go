package agent

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/agent" // Import the interface package
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type agentRepository struct{}

// NewAgentRepository creates and returns a new instance of IAgentRepository.
func NewAgentRepository() agent.IAgentRepository {
	return &agentRepository{}
}

// Create creates a new agent record in the database and returns the new ID.
func (r *agentRepository) Create(ctx context.Context, data *entity.Agents) (agentId int64, err error) {
	result, err := dao.Agents.Ctx(ctx).Data(data).OmitEmpty().Insert() // Use OmitEmpty if appropriate
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to create agent")
	}
	newId, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to get last insert ID for agent")
	}
	return newId, nil
}

// GetByID retrieves agent details by ID from the database.
func (r *agentRepository) GetByID(ctx context.Context, agentId int64) (*entity.Agents, error) {
	var agentEntity *entity.Agents
	err := dao.Agents.Ctx(ctx).Where(dao.Agents.Columns().AgentId, agentId).WhereNull(dao.Agents.Columns().DeletedAt).Scan(&agentEntity) // Ensure not deleted
	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to get agent by ID: %d", agentId)
	}
	if agentEntity == nil {
		// Return nil, nil if not found, consistent with GetByUsername etc.
		return nil, nil
	}
	return agentEntity, nil
}

// GetByIDs retrieves multiple agents by their IDs.
func (r *agentRepository) GetByIDs(ctx context.Context, agentIds []int64) ([]*entity.Agents, error) {
	var list []*entity.Agents
	err := dao.Agents.Ctx(ctx).WhereIn(dao.Agents.Columns().AgentId, agentIds).WhereNull(dao.Agents.Columns().DeletedAt).Scan(&list)
	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to get agents by IDs")
	}
	return list, nil
}

// GetByUsername retrieves agent details by username from the database.
func (r *agentRepository) GetByUsername(ctx context.Context, username string) (*entity.Agents, error) {
	var agentEntity *entity.Agents
	err := dao.Agents.Ctx(ctx).Where(dao.Agents.Columns().Username, username).WhereNull(dao.Agents.Columns().DeletedAt).Scan(&agentEntity) // Ensure not deleted
	// Allow returning nil entity if not found, error only for DB issues
	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to get agent by username: %s", username)
	}
	return agentEntity, nil
}

// GetByEmail retrieves agent details by email, optionally excluding an ID.
func (r *agentRepository) GetByEmail(ctx context.Context, email string, excludeAgentId ...int64) (*entity.Agents, error) {
	var agentEntity *entity.Agents
	m := dao.Agents.Ctx(ctx).Where(dao.Agents.Columns().Email, email).WhereNull(dao.Agents.Columns().DeletedAt)
	if len(excludeAgentId) > 0 {
		m = m.WhereNot(dao.Agents.Columns().AgentId, excludeAgentId[0])
	}
	err := m.Scan(&agentEntity)
	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to get agent by email: %s", email)
	}
	return agentEntity, nil
}

// GetByPhoneNumber retrieves agent details by phone number, optionally excluding an ID.
// func (r *agentRepository) GetByPhoneNumber(ctx context.Context, phoneNumber string, excludeAgentId ...int64) (*entity.Agents, error) {
// 	var agentEntity *entity.Agents
// 	m := dao.Agents.Ctx(ctx).WhereNull(dao.Agents.Columns().DeletedAt)
// 	if len(excludeAgentId) > 0 {
// 		m = m.WhereNot(dao.Agents.Columns().AgentId, excludeAgentId[0])
// 	}
// 	err := m.Scan(&agentEntity)
// 	if err != nil {
// 		return nil, gerror.Wrapf(err, "Failed to get agent by phone number: %s", phoneNumber)
// 	}
// 	return agentEntity, nil
// }

// GetByInvitationCode retrieves agent details by invitation code.
func (r *agentRepository) GetByInvitationCode(ctx context.Context, code string) (*entity.Agents, error) {
	var agentEntity *entity.Agents
	err := dao.Agents.Ctx(ctx).Where(dao.Agents.Columns().InvitationCode, code).WhereNull(dao.Agents.Columns().DeletedAt).Scan(&agentEntity)
	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to get agent by invitation code: %s", code)
	}
	return agentEntity, nil
}

// List retrieves a list of agents based on criteria from the database.
func (r *agentRepository) List(ctx context.Context, req *v1.GetAgentListReq) (list []*entity.Agents, total int, err error) {
	m := dao.Agents.Ctx(ctx).WhereNull(dao.Agents.Columns().DeletedAt) // Ensure not deleted

	// Apply filters
	if req.Username != "" {
		m = m.WhereLike(dao.Agents.Columns().Username, "%"+req.Username+"%")
	}
	if req.AgentName != "" {
		m = m.WhereLike(dao.Agents.Columns().AgentName, "%"+req.AgentName+"%")
	}
	// if req.PhoneNumber != "" {
	// 	m = m.Where(dao.Agents.Columns().PhoneNumber, req.PhoneNumber)
	// }
	if req.Email != "" {
		m = m.Where(dao.Agents.Columns().Email, req.Email)
	}
	if req.Level != nil {
		m = m.Where(dao.Agents.Columns().Level, *req.Level)
	}
	if req.Status != nil {
		m = m.Where(dao.Agents.Columns().Status, *req.Status)
	}

	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to count agents")
	}
	if total == 0 {
		return []*entity.Agents{}, 0, nil
	}

	// Apply pagination
	err = m.Page(req.Page, req.PageSize).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to retrieve agent list")
	}

	return list, total, nil
}

// ListAll retrieves all agents matching criteria, without pagination.
func (r *agentRepository) ListAll(ctx context.Context, req *v1.GetAgentListReq) ([]*entity.Agents, error) {
	m := dao.Agents.Ctx(ctx).WhereNull(dao.Agents.Columns().DeletedAt) // Ensure not deleted

	// Apply filters (same as List method)
	if req.Username != "" {
		m = m.WhereLike(dao.Agents.Columns().Username, "%"+req.Username+"%")
	}
	if req.AgentName != "" {
		m = m.WhereLike(dao.Agents.Columns().AgentName, "%"+req.AgentName+"%")
	}
	// if req.PhoneNumber != "" {
	// 	m = m.Where(dao.Agents.Columns().PhoneNumber, req.PhoneNumber)
	// }
	if req.Email != "" {
		m = m.Where(dao.Agents.Columns().Email, req.Email)
	}
	if req.Level != nil {
		m = m.Where(dao.Agents.Columns().Level, *req.Level)
	}
	if req.Status != nil {
		m = m.Where(dao.Agents.Columns().Status, *req.Status)
	}

	var list []*entity.Agents
	err := m.OrderDesc(dao.Agents.Columns().AgentId).Scan(&list) // Example ordering
	if err != nil {
		return nil, gerror.Wrap(err, "Failed to retrieve all matching agents")
	}
	return list, nil
}

// Update updates basic agent information in the database.
func (r *agentRepository) Update(ctx context.Context, data *entity.Agents) error {
	// Ensure only updatable fields are included in the data map
	updateData := g.Map{
		dao.Agents.Columns().AgentName: data.AgentName,
		dao.Agents.Columns().Email:     data.Email,
		// dao.Agents.Columns().PhoneNumber: data.PhoneNumber,
		dao.Agents.Columns().Status:          data.Status,
		dao.Agents.Columns().BusinessName:    data.BusinessName,
		dao.Agents.Columns().TelegramAccount: data.TelegramAccount,
		dao.Agents.Columns().TelegramBotName: data.TelegramBotName,
		dao.Agents.Columns().UpdatedAt:       data.UpdatedAt,
	}

	// 只有在 TelegramBotToken 不为空时才更新
	if data.TelegramBotToken != "" {
		updateData[dao.Agents.Columns().TelegramBotToken] = data.TelegramBotToken
	}

	_, err := dao.Agents.Ctx(ctx).Data(updateData).Where(dao.Agents.Columns().AgentId, data.AgentId).WhereNull(dao.Agents.Columns().DeletedAt).Update() // Ensure not deleted
	if err != nil {
		return gerror.Wrapf(err, "Failed to update agent with ID: %d", data.AgentId)
	}
	return nil
}

// UpdateRelationship updates the relationship path for a specific agent.
func (r *agentRepository) UpdateRelationship(ctx context.Context, agentId int64, relationship string) error {
	_, err := dao.Agents.Ctx(ctx).Data(g.Map{
		dao.Agents.Columns().Relationship: relationship,
	}).Where(dao.Agents.Columns().AgentId, agentId).WhereNull(dao.Agents.Columns().DeletedAt).Update() // Ensure not deleted
	if err != nil {
		return gerror.Wrapf(err, "Failed to update relationship for agent ID: %d", agentId)
	}
	return nil
}

// DeleteSoft performs soft deletion for agents by IDs in the database.
func (r *agentRepository) DeleteSoft(ctx context.Context, agentIds []int64) error {
	// Assuming soft delete is handled by gogf model hooks or default behavior
	// If not, you need to update the deleted_at column manually.
	// _, err := dao.Agents.Ctx(ctx).Data(g.Map{dao.Agents.Columns().DeletedAt: gtime.Now()}).WhereIn(dao.Agents.Columns().AgentId, agentIds).Update()
	_, err := dao.Agents.Ctx(ctx).WhereIn(dao.Agents.Columns().AgentId, agentIds).Delete() // Use Delete for soft delete if configured
	// Assuming soft delete is handled by gogf model hooks or default behavior
	if err != nil {
		return gerror.Wrap(err, "Failed to soft delete agents")
	}
	return nil
}

// UpdateStatus updates the status for agents by IDs in the database.
func (r *agentRepository) UpdateStatus(ctx context.Context, agentIds []int64, status int) error {
	_, err := dao.Agents.Ctx(ctx).Data(g.Map{dao.Agents.Columns().Status: status}).WhereIn(dao.Agents.Columns().AgentId, agentIds).WhereNull(dao.Agents.Columns().DeletedAt).Update() // Ensure not deleted
	if err != nil {
		return gerror.Wrap(err, "Failed to update agent statuses")
	}
	return nil
}

// FindDescendantIDsByRelationship finds all descendant agent IDs based on a relationship prefix.
func (r *agentRepository) FindDescendantIDsByRelationship(ctx context.Context, relationshipPrefix string) ([]int64, error) {
	var descendantAgents []*entity.Agents
	// Find agents whose relationship path starts with the given prefix, excluding the prefix itself if needed.
	// Ensure the LIKE pattern correctly matches descendants.
	// Example: If prefix is "/1/", find "/1/2/", "/1/3/4/", etc.
	// Need to be careful not to match the parent itself if prefix ends with '/'
	likePattern := relationshipPrefix + "%"
	err := dao.Agents.Ctx(ctx).
		WhereLike(dao.Agents.Columns().Relationship, likePattern).       // Find paths starting with the prefix
		WhereNot(dao.Agents.Columns().Relationship, relationshipPrefix). // Exclude the parent itself
		WhereNull(dao.Agents.Columns().DeletedAt).                       // Ensure descendants are not deleted
		Fields(dao.Agents.Columns().AgentId).                            // Select only the ID
		Scan(&descendantAgents)

	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to find descendants for relationship prefix: %s", relationshipPrefix)
	}

	ids := make([]int64, len(descendantAgents))
	for i, agent := range descendantAgents {
		ids[i] = int64(agent.AgentId) // Convert uint to int64
	}
	return ids, nil
}

// UpdatePassword updates the password for a specific agent in the database.
func (r *agentRepository) UpdatePassword(ctx context.Context, agentId int64, hashedPassword string) error {
	_, err := dao.Agents.Ctx(ctx).Data(g.Map{dao.Agents.Columns().PasswordHash: hashedPassword}).Where(dao.Agents.Columns().AgentId, agentId).WhereNull(dao.Agents.Columns().DeletedAt).Update() // Ensure not deleted
	if err != nil {
		return gerror.Wrapf(err, "Failed to update password for agent ID: %d", agentId)
	}
	return nil
}

// Reset2FASecret resets the 2FA secret for a specific agent in the database.
// This implementation assumes clearing the secret field. Adjust if needed.
func (r *agentRepository) Reset2FASecret(ctx context.Context, agentId int64) error {
	updateData := g.Map{
		dao.Agents.Columns().GoogleAuthenticatorSecret: "", // Clear the secret
	}
	_, err := dao.Agents.Ctx(ctx).Data(updateData).Where(dao.Agents.Columns().AgentId, agentId).WhereNull(dao.Agents.Columns().DeletedAt).Update() // Ensure not deleted
	if err != nil {
		return gerror.Wrapf(err, "Failed to reset 2FA for agent ID: %d", agentId)
	}
	return nil
}
