package agent

import (
	"admin-api/internal/service/system/agent" // Import the interface package
	"context"

	"github.com/gogf/gf/v2/crypto/gmd5" // Using gmd5 for example, consider bcrypt or scrypt for better security
	"github.com/gogf/gf/v2/errors/gerror"
)

type agentAuthService struct {
	agentRepo agent.IAgentRepository // Inject repository for Reset2FA
}

// NewAgentAuthService creates and returns a new instance of IAgentAuthService.
// It requires an IAgentRepository implementation for Reset2FA functionality.
func NewAgentAuthService(repo agent.IAgentRepository) agent.IAgentAuthService {
	return &agentAuthService{
		agentRepo: repo,
	}
}

// HashPassword hashes the provided password using MD5.
// WARNING: MD5 is NOT considered secure for password hashing.
// Consider using bcrypt or scrypt via libraries like golang.org/x/crypto/bcrypt.
// This implementation uses gmd5 as a placeholder based on GoFrame components.
func (s *agentAuthService) HashPassword(ctx context.Context, password string) (string, error) {
	// Example using gmd5 - REPLACE WITH A SECURE HASHING ALGORITHM
	hashedPassword, err := gmd5.EncryptString(password)
	if err != nil {
		// Although gmd5.EncryptString rarely errors with string input, handle it just in case.
		return "", gerror.Wrap(err, "Failed to hash password using MD5")
	}
	// It's common practice to add a salt before hashing. gmd5 doesn't handle this automatically.
	// Example (needs a salt source): hashedPassword, _ = gmd5.EncryptString(password + salt)
	return hashedPassword, nil
}

// Reset2FA handles the logic for resetting an agent's 2FA.
// This implementation calls the repository to clear the 2FA secret.
func (s *agentAuthService) Reset2FA(ctx context.Context, agentId int64) error {
	// The core logic here is to update the database record.
	err := s.agentRepo.Reset2FASecret(ctx, agentId)
	if err != nil {
		// Wrap the error from the repository
		return gerror.Wrapf(err, "Failed to reset 2FA for agent ID %d in auth service", agentId)
	}
	// Potentially add logging or other actions here.
	return nil
}
