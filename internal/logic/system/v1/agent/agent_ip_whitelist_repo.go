package agent

import (
	"admin-api/api/common"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/agent" // Import the interface package
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

type agentIPWhitelistRepository struct{}

// NewAgentIPWhitelistRepository creates and returns a new instance of IAgentIPWhitelistRepository.
func NewAgentIPWhitelistRepository() agent.IAgentIPWhitelistRepository {
	return &agentIPWhitelistRepository{}
}

// Add adds an IP address to the agent's whitelist in the database.
func (r *agentIPWhitelistRepository) Add(ctx context.Context, data *entity.IpAccessList) error {
	// Ensure ListType is set correctly for agent whitelist.
	// The calling logic should ensure data.AgentId is populated.
	data.ListType = "whitelist"

	_, err := dao.IpAccessList.Ctx(ctx).Data(data).Insert()
	if err != nil {
		// Consider checking for duplicate entry errors specifically if needed
		return gerror.Wrap(err, "Failed to add IP whitelist entry")
	}
	return nil
}

// ListByAgentID retrieves the IP whitelist for a specific agent from the database.
func (r *agentIPWhitelistRepository) ListByAgentID(ctx context.Context, agentId int64, pageReq common.PageRequest) (list []*entity.IpAccessList, total int, err error) {
	m := dao.IpAccessList.Ctx(ctx).Where(dao.IpAccessList.Columns().AgentId, agentId).Where(dao.IpAccessList.Columns().ListType, "whitelist")

	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrapf(err, "Failed to count IP whitelist for agent ID: %d", agentId)
	}
	if total == 0 {
		return []*entity.IpAccessList{}, 0, nil
	}

	// Apply pagination
	err = m.Page(pageReq.Page, pageReq.PageSize).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrapf(err, "Failed to retrieve IP whitelist for agent ID: %d", agentId)
	}

	return list, total, nil
}

// DeleteByID deletes an IP whitelist entry by its ID from the database.
func (r *agentIPWhitelistRepository) DeleteByID(ctx context.Context, ipWhitelistId int64) error {
	// Perform a soft delete if the model supports it, otherwise a hard delete.
	// Check if the record exists and belongs to the correct type ('agent') before deleting if necessary.
	_, err := dao.IpAccessList.Ctx(ctx).Where(dao.IpAccessList.Columns().Id, ipWhitelistId).Delete()
	if err != nil {
		return gerror.Wrapf(err, "Failed to delete IP whitelist entry with ID: %d", ipWhitelistId)
	}
	return nil
}

// GetByAgentAndIP retrieves a whitelist entry by agent ID and IP address from the database.
func (r *agentIPWhitelistRepository) GetByAgentAndIP(ctx context.Context, agentId int64, ipAddress string) (*entity.IpAccessList, error) {
	var entry *entity.IpAccessList
	err := dao.IpAccessList.Ctx(ctx).
		Where(dao.IpAccessList.Columns().AgentId, agentId).
		Where(dao.IpAccessList.Columns().ListType, "whitelist"). // Ensure it's an agent whitelist
		Where(dao.IpAccessList.Columns().IpAddress, ipAddress).
		WhereNull(dao.IpAccessList.Columns().DeletedAt).
		Scan(&entry)

	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to get IP whitelist entry for agent %d and IP %s", agentId, ipAddress)
	}
	// Returns nil entry if not found, error only for DB issues.
	return entry, nil
}

// GetByID retrieves a whitelist entry by its ID from the database.
func (r *agentIPWhitelistRepository) GetByID(ctx context.Context, ipWhitelistId int64) (*entity.IpAccessList, error) {
	var entry *entity.IpAccessList
	err := dao.IpAccessList.Ctx(ctx).
		Where(dao.IpAccessList.Columns().Id, ipWhitelistId).
		WhereNull(dao.IpAccessList.Columns().DeletedAt).
		Scan(&entry)
	if err != nil {
		return nil, gerror.Wrapf(err, "Failed to get IP whitelist entry by ID: %d", ipWhitelistId)
	}
	if entry == nil {
		return nil, gerror.Newf("IP whitelist entry not found with ID: %d", ipWhitelistId)
	}
	// Ensure the found entry is indeed a whitelist entry
	if entry.ListType != "whitelist" {
		return nil, gerror.Newf("IP access list entry with ID %d is not a whitelist entry", ipWhitelistId)
	}
	return entry, nil
}
