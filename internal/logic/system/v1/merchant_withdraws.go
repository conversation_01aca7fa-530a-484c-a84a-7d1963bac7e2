package v1

import (
	"context"
	"fmt"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/constants"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service"
	"admin-api/internal/utility"
	"admin-api/internal/wallet"
	"admin-api/utility/csv"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GetMyWithdraws 获取我的提现记录列表
func (s *sSystemLogic) GetMyWithdraws(ctx context.Context, req *v1.GetMyWithdrawsReq) (res *v1.GetMyWithdrawsRes, err error) {
	res = &v1.GetMyWithdrawsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.MerchantWithdrawInfoType, 0),
	}

	// 构建查询条件
	condition := g.Map{}
	
	// 如果提供了商户ID，则筛选特定商户
	if req.MerchantId != nil && *req.MerchantId > 0 {
		condition["merchant_id"] = *req.MerchantId
	}

	// Apply additional filters
	if req.TokenId != nil {
		condition["token_id"] = *req.TokenId
	}
	if req.TokenName != "" {
		condition["name LIKE ?"] = "%" + req.TokenName + "%"
	}
	if req.State != nil {
		condition["state"] = *req.State
	}
	if req.OrderNo != "" {
		condition["order_no LIKE ?"] = "%" + req.OrderNo + "%"
	}
	if req.TxHash != "" {
		condition["tx_hash LIKE ?"] = "%" + req.TxHash + "%"
	}
	if req.Address != "" {
		condition["address LIKE ?"] = "%" + req.Address + "%"
	}

	// Amount range filtering
	if req.AmountMin != nil && *req.AmountMin != "" {
		if minAmount, parseErr := decimal.NewFromString(*req.AmountMin); parseErr == nil {
			condition["amount >= ?"] = minAmount
		}
	}
	if req.AmountMax != nil && *req.AmountMax != "" {
		if maxAmount, parseErr := decimal.NewFromString(*req.AmountMax); parseErr == nil {
			condition["amount <= ?"] = maxAmount
		}
	}

	// 使用灵活的日期范围处理，支持dateRange和createdAt数组两种格式
	utility.AddFlexibleDateRangeCondition(condition, req.DateRange, req.CreatedAt)

	// 如果是导出
	if req.Export {
		// 导出时不分页，查询所有符合条件的数据
		list, _, err := dao.MerchantWithdraws.List(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询提现记录失败")
		}

		if len(list) == 0 {
			return res, nil // 没有数据可导出
		}

		// 获取商户ID列表以批量查询商户名称
		merchantIds := make([]uint64, 0)
		merchantIdMap := make(map[uint64]bool)
		for _, withdraw := range list {
			if !merchantIdMap[withdraw.MerchantId] {
				merchantIds = append(merchantIds, withdraw.MerchantId)
				merchantIdMap[withdraw.MerchantId] = true
			}
		}
		
		// 批量查询商户信息
		merchantNameMap := make(map[uint64]string)
		if len(merchantIds) > 0 {
			var merchants []entity.Merchants
			err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
			if err != nil {
				g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
			} else {
				for _, merchant := range merchants {
					merchantNameMap[merchant.MerchantId] = merchant.MerchantName
				}
			}
		}

		// 准备导出数据
		exportData := make([]interface{}, 0, len(list))
		for _, withdraw := range list {
			// 状态文本
			stateText := utility.GetWithdrawStateText(withdraw.State)

			// 准备导出结构体
			exportItem := struct {
				WithdrawsId    int64  `json:"withdrawsId" excel:"提现记录ID"`
				MerchantId     uint64 `json:"merchantId" excel:"商户ID"`
				MerchantName   string `json:"merchantName" excel:"商户名称"`
				TokenName      string `json:"tokenName" excel:"币种名称"`
				OrderNo        string `json:"orderNo" excel:"订单号"`
				Amount         string `json:"amount" excel:"申请金额"`
				HandlingFee    string `json:"handlingFee" excel:"手续费"`
				ActualAmount   string `json:"actualAmount" excel:"实际到账金额"`
				Address        string `json:"address" excel:"提币地址"`
				RecipientName  string `json:"recipientName" excel:"收款人姓名"`
				State          string `json:"state" excel:"状态"`
				TxHash         string `json:"txHash" excel:"交易哈希"`
				UserRemark     string `json:"userRemark" excel:"用户备注"`
				RefuseReasonZh string `json:"refuseReasonZh" excel:"拒绝原因"`
				CreatedAt      string `json:"createdAt" excel:"创建时间"`
				CompletedAt    string `json:"completedAt" excel:"完成时间"`
			}{
				WithdrawsId:    int64(withdraw.WithdrawsId),
				MerchantId:     withdraw.MerchantId,
				MerchantName:   merchantNameMap[withdraw.MerchantId],
				TokenName:      withdraw.Name,
				OrderNo:        withdraw.OrderNo,
				Amount:         withdraw.Amount.String(),
				HandlingFee:    withdraw.HandlingFee.String(),
				ActualAmount:   withdraw.ActualAmount.String(),
				Address:        withdraw.Address,
				RecipientName:  withdraw.RecipientName,
				State:          stateText,
				TxHash:         withdraw.TxHash,
				UserRemark:     withdraw.UserRemark,
				RefuseReasonZh: withdraw.RefuseReasonZh,
				CreatedAt:      withdraw.CreatedAt.Format("Y-m-d H:i:s"),
			}
			if withdraw.CompletedAt != nil {
				exportItem.CompletedAt = withdraw.CompletedAt.Format("Y-m-d H:i:s")
			}
			exportData = append(exportData, exportItem)
		}

		// 定义Excel表头
		excelTags := []string{
			"提现记录ID", "商户ID", "商户名称", "币种名称", "订单号", "申请金额", "手续费", "实际到账金额",
			"提币地址", "收款人姓名", "状态", "交易哈希", "用户备注", "拒绝原因",
			"创建时间", "完成时间",
		}

		// 调用CSV导出工具
		fileName := "merchant_withdraws"
		if req.MerchantId != nil && *req.MerchantId > 0 {
			fileName = fmt.Sprintf("merchant_withdraws_%d", *req.MerchantId)
		}
		return res, csv.ExportByStructs(ctx, excelTags, exportData, fileName, "提现记录")
	}

	// 正常查询
	list, total, err := dao.MerchantWithdraws.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询提现记录失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = (total + req.PageSize - 1) / req.PageSize

	// 获取商户ID列表以批量查询商户名称
	merchantIds := make([]uint64, 0)
	merchantIdMap := make(map[uint64]bool)
	for _, withdraw := range list {
		if !merchantIdMap[withdraw.MerchantId] {
			merchantIds = append(merchantIds, withdraw.MerchantId)
			merchantIdMap[withdraw.MerchantId] = true
		}
	}
	
	// 批量查询商户信息
	merchantNameMap := make(map[uint64]string)
	if len(merchantIds) > 0 {
		var merchants []entity.Merchants
		err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
		if err != nil {
			g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
		} else {
			for _, merchant := range merchants {
				merchantNameMap[merchant.MerchantId] = merchant.MerchantName
			}
		}
	}

	// 转换数据格式
	for _, withdraw := range list {
		withdrawInfo := &v1.MerchantWithdrawInfoType{
			WithdrawsId:        withdraw.WithdrawsId,
			MerchantId:         withdraw.MerchantId,
			MerchantName:       merchantNameMap[withdraw.MerchantId],
			TokenId:            withdraw.TokenId,
			TokenName:          withdraw.Name,
			Chan:               withdraw.Chan,
			OrderNo:            withdraw.OrderNo,
			Address:            withdraw.Address,
			RecipientName:      withdraw.RecipientName,
			RecipientAccount:   withdraw.RecipientAccount,
			Amount:             withdraw.Amount,
			HandlingFee:        withdraw.HandlingFee,
			ActualAmount:       withdraw.ActualAmount,
			State:              withdraw.State,
			StateText:          utility.GetWithdrawStateText(withdraw.State),
			RefuseReasonZh:     withdraw.RefuseReasonZh,
			RefuseReasonEn:     withdraw.RefuseReasonEn,
			TxHash:             withdraw.TxHash,
			UserRemark:         withdraw.UserRemark,
			AdminRemark:        withdraw.AdminRemark,
			FiatType:           withdraw.FiatType,
			CreatedAt:          withdraw.CreatedAt,
			CheckedAt:          withdraw.CheckedAt,
			ProcessingAt:       withdraw.ProcessingAt,
			CompletedAt:        withdraw.CompletedAt,
			NotificationSent:   withdraw.NotificationSent,
			NotificationSentAt: withdraw.NotificationSentAt,
			CanCancel:          utility.CanCancelWithdraw(withdraw.State),
		}
		res.Data = append(res.Data, withdrawInfo)
	}

	return res, nil
}

// GetMyWithdrawDetail 获取我的提现记录详情
func (s *sSystemLogic) GetMyWithdrawDetail(ctx context.Context, req *v1.GetMyWithdrawDetailReq) (res *v1.GetMyWithdrawDetailRes, err error) {
	// 查询提现记录 - 管理员可以查看任何商户的提现记录
	var withdraw entity.MerchantWithdraws
	err = dao.MerchantWithdraws.Ctx(ctx).Where("withdraws_id", req.WithdrawsId).Scan(&withdraw)
	if err != nil {
		return nil, gerror.Wrap(err, "查询提现记录详情失败")
	}

	if withdraw.WithdrawsId == 0 {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 构建响应数据
	res = &v1.GetMyWithdrawDetailRes{
		Data: &v1.MerchantWithdrawDetailType{
			MerchantWithdraws: withdraw,
			StateText:         utility.GetWithdrawStateText(withdraw.State),
			CanCancel:         utility.CanCancelWithdraw(withdraw.State),
		},
	}

	return res, nil
}

// CancelMyWithdraw 撤销我的提现申请
func (s *sSystemLogic) CancelMyWithdraw(ctx context.Context, req *v1.CancelMyWithdrawReq) (res *v1.CancelMyWithdrawRes, err error) {
	// Initialize response with default values
	res = &v1.CancelMyWithdrawRes{
		Success: false,
		Message: "操作失败",
	}

	// 查询待审核的提现记录 - 管理员可以撤销任何商户的提现
	var withdraw entity.MerchantWithdraws
	err = dao.MerchantWithdraws.Ctx(ctx).
		Where("withdraws_id", req.WithdrawsId).
		Where("state", 1). // 只能撤销待审核状态
		Scan(&withdraw)
	if err != nil {
		// Return error for database issues
		return nil, gerror.Wrap(err, "查询提现记录失败")
	}

	if withdraw.WithdrawsId == 0 {
		// For business logic failures, return response object with success=false
		res.Message = "提现记录不存在或已无法撤销（只能撤销待审核状态的提现申请）"
		return res, nil
	}

	// 准备撤销原因
	cancelReason := "管理员撤销"
	if req.Reason != "" {
		cancelReason = fmt.Sprintf("管理员撤销: %s", req.Reason)
	}

	// 处理钱包余额操作逻辑
	// 1. 释放冻结的提现金额回到可用余额
	// 2. 记录财务操作日志
	// 3. 发送通知给用户
	err = s.handleWithdrawCancellation(ctx, &withdraw, cancelReason)
	if err != nil {
		// Return error for wallet operation failures
		return nil, gerror.Wrap(err, "处理提现撤销操作失败")
	}

	// 更新提现记录状态为已撤销
	_, err = dao.MerchantWithdraws.Ctx(ctx).
		Where("withdraws_id", req.WithdrawsId).
		Data(g.Map{
			"state": 6, // 已撤销状态
			"refuse_reason_zh": cancelReason,
			"refuse_reason_en": cancelReason,
			"updated_at": gtime.Now(),
		}).
		Update()
	if err != nil {
		// Return error for database update failures
		return nil, gerror.Wrap(err, "更新提现状态失败")
	}

	res.Success = true
	res.Message = "提现申请已成功撤销"
	return res, nil
}

// handleWithdrawCancellation 处理提现撤销的相关操作
func (s *sSystemLogic) handleWithdrawCancellation(ctx context.Context, withdraw *entity.MerchantWithdraws, cancelReason string) error {
	// 1. 获取钱包服务实例
	walletService := s.getWalletService()

	// 2. 构建撤销请求
	withdrawReq := &wallet.WithdrawRequest{
		MerchantID:    uint64(withdraw.MerchantId),
		TokenSymbol:   withdraw.Name, // 使用Name字段作为代币符号
		Amount:        withdraw.Amount,
		Address:       withdraw.Address, // 使用原始提现地址，满足验证要求
		OrderID:       withdraw.OrderNo,
		Memo:          cancelReason,
		RequestSource: "merchant_cancel",
		RequestIP:     utility.GetRequestIP(ctx),
	}

	// 3. 执行钱包撤销操作（将冻结金额释放回可用余额）
	results, err := walletService.CancelWithdraw(ctx, withdrawReq)
	if err != nil {
		g.Log().Errorf(ctx, "钱包撤销操作失败 - WithdrawsId: %d, MerchantId: %d, Amount: %s, Error: %v",
			withdraw.WithdrawsId, withdraw.MerchantId, withdraw.Amount.String(), err)
		return gerror.Wrap(err, "钱包撤销操作失败")
	}

	// 4. 记录操作日志，包含交易ID
	g.Log().Infof(ctx, "提现撤销操作成功 - WithdrawsId: %d, MerchantId: %d, Amount: %s %s, Reason: %s, 交易记录: [可用余额增加ID: %d, 冻结余额减少ID: %d]",
		withdraw.WithdrawsId, withdraw.MerchantId, withdraw.Amount.String(), withdraw.Name,
		cancelReason, results[0].TransactionID, results[1].TransactionID)

	// 5. 创建撤销回调记录
	withdrawService := service.GetWithdrawService()
	if err := withdrawService.CancelWithdraw(ctx, uint64(withdraw.WithdrawsId), cancelReason); err != nil {
		g.Log().Errorf(ctx, "创建提现撤销回调记录失败: withdrawsId=%d, error=%v", withdraw.WithdrawsId, err)
		// 不影响主流程，继续执行
	}

	return nil
}

// getWalletService 获取钱包服务实例
func (s *sSystemLogic) getWalletService() *wallet.Service {
	// 使用全局钱包服务实例，如果不存在则创建
	return wallet.GetWalletService()
}

// validateAmountRange 验证金额范围参数
func (s *sSystemLogic) validateAmountRange(amountMin, amountMax *string) error {
	if amountMin != nil && *amountMin != "" {
		if _, err := decimal.NewFromString(*amountMin); err != nil {
			return gerror.New("最小金额格式无效")
		}
	}
	if amountMax != nil && *amountMax != "" {
		if _, err := decimal.NewFromString(*amountMax); err != nil {
			return gerror.New("最大金额格式无效")
		}
	}

	// 如果两个都有值，验证最小值不能大于最大值
	if amountMin != nil && amountMax != nil && *amountMin != "" && *amountMax != "" {
		min, _ := decimal.NewFromString(*amountMin)
		max, _ := decimal.NewFromString(*amountMax)
		if min.GreaterThan(max) {
			return gerror.New("最小金额不能大于最大金额")
		}
	}

	return nil
}

// CreateMyWithdraw 创建我的提现申请
func (s *sSystemLogic) CreateMyWithdraw(ctx context.Context, req *v1.CreateMyWithdrawReq) (res *v1.CreateMyWithdrawRes, err error) {
	// 使用请求中的商户ID - 管理员必须指定为哪个商户创建提现
	merchantId := req.MerchantId
	if merchantId == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "商户ID不能为空")
	}

	// 1. 验证2FA代码 - 管理员操作可能需要不同的验证逻辑
	// 暂时跳过2FA验证，因为是管理员操作
	// TODO: 实现管理员的2FA验证逻辑
	
	// 2. 验证订单号是否已存在
	var existingCount int
	existingCount, err = dao.MerchantWithdraws.Ctx(ctx).
		Where("merchant_id", merchantId).
		Where("order_no", req.OrderNo).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查订单号失败")
	}
	if existingCount > 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "订单号已存在")
	}

	// 3. 验证提现金额格式
	amountDecimal, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "提现金额格式无效")
	}
	if amountDecimal.LessThanOrEqual(decimal.Zero) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "提现金额必须大于0")
	}

	// 4. 计算手续费（使用统一的配置驱动服务）
	withdrawFeeService := service.GetWithdrawFeeService()
	handlingFee, actualAmount, err := withdrawFeeService.CalculateFee(ctx, req.Chain, req.TokenName, req.Amount)
	if err != nil {
		return nil, gerror.Wrap(err, "计算手续费失败")
	}

	// 5. 验证提现限额和商户余额是否充足
	err = withdrawFeeService.ValidateWithdrawLimits(ctx, req.Chain, req.TokenName, req.Amount)
	if err != nil {
		return nil, gerror.Wrap(err, "提现限额验证失败")
	}

	err = s.validateMerchantBalance(ctx, merchantId, req.TokenName, req.Amount)
	if err != nil {
		return nil, gerror.Wrap(err, "余额验证失败")
	}

	// 6. 使用钱包服务准备提现（冻结余额）
	walletService := s.getWalletService()
	withdrawReq := &wallet.WithdrawRequest{
		MerchantID:    merchantId,
		TokenSymbol:   req.TokenName,
		Amount:        amountDecimal,
		Address:       req.Address,
		OrderID:       req.OrderNo,
		Memo:          req.UserRemark,
		RequestSource: "merchant_api",
		RequestIP:     utility.GetRequestIP(ctx),
	}

	// 准备提现（冻结余额）
	walletResult, err := walletService.PrepareWithdraw(ctx, withdrawReq)
	if err != nil {
		// Let the wallet service error propagate directly with its specific message
		return nil, err
	}

	// 7. 创建提现记录
	withdrawData := g.Map{
		"merchant_id":   merchantId,
		"token_id":      0, // 如果需要，可以通过token_name查询获得
		"name":          req.TokenName,
		"chan":          req.Chain,
		"order_no":      req.OrderNo,
		"address":       req.Address,
		"amount":        req.Amount,
		"handling_fee":  handlingFee,
		"actual_amount": actualAmount,
		"state":         1, // 1-待审核
		"user_remark":   req.UserRemark,
		"created_at":    gtime.Now(),
		"updated_at":    gtime.Now(),
	}

	withdrawsId, err := dao.MerchantWithdraws.CreateWithdraw(ctx, withdrawData)
	if err != nil {
		// 如果创建记录失败，需要取消余额冻结
		if _, cancelErr := walletService.CancelWithdraw(ctx, withdrawReq); cancelErr != nil {
			g.Log().Errorf(ctx, "取消提现冻结失败: %v", cancelErr)
		}
		return nil, gerror.Wrap(err, "创建提现记录失败")
	}

	// 记录钱包交易ID到日志
	g.Log().Infof(ctx, "提现申请创建成功, withdrawsId: %d, walletTransactionId: %d",
		withdrawsId, walletResult.TransactionID)

	// 7.1. 创建提现创建回调记录
	withdrawService := service.GetWithdrawService()
	if err := withdrawService.CreateWithdraw(ctx, uint64(withdrawsId)); err != nil {
		g.Log().Errorf(ctx, "创建提现创建回调记录失败: withdrawsId=%d, error=%v", withdrawsId, err)
		// 不影响主流程，继续执行
	}

	// 8. 构建响应数据
	res = &v1.CreateMyWithdrawRes{
		Data: &v1.CreateMyWithdrawData{
			WithdrawsId:   uint(withdrawsId),
			OrderNo:       req.OrderNo,
			TokenName:     req.TokenName,
			Chain:         req.Chain,
			Address:       req.Address,
			Amount:        req.Amount,
			HandlingFee:   handlingFee,
			ActualAmount:  actualAmount,
			Status:        "pending",
			EstimatedTime: utility.GetEstimatedTime(req.Chain),
		},
	}

	return res, nil
}

// GetMyWithdrawFee 获取我的提现手续费
func (s *sSystemLogic) GetMyWithdrawFee(ctx context.Context, req *v1.GetMyWithdrawFeeReq) (res *v1.GetMyWithdrawFeeRes, err error) {
	// 1. 验证提现金额格式
	amountDecimal, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "金额格式无效")
	}
	if amountDecimal.LessThanOrEqual(decimal.Zero) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "金额必须大于0")
	}

	// 2. 计算手续费（使用统一的配置驱动服务）
	withdrawFeeService := service.GetWithdrawFeeService()
	handlingFee, actualAmount, err := withdrawFeeService.CalculateFee(ctx, req.Chain, req.TokenName, req.Amount)
	if err != nil {
		return nil, gerror.Wrap(err, "计算手续费失败")
	}

	// 3. 手续费计算不依赖商户，所以不显示特定商户的可用余额
	// 管理员可以为任何商户计算手续费
	available := decimal.NewFromInt(0)

	// 4. 获取提现限额和费率信息（使用统一的配置驱动服务）
	minAmount, maxAmount, feeRate := withdrawFeeService.GetWithdrawLimitsAndRate(ctx, req.Chain, req.TokenName)

	// 5. 构建响应数据
	res = &v1.GetMyWithdrawFeeRes{
		Data: &v1.GetMyWithdrawFeeData{
			TokenName:     req.TokenName,
			Chain:         req.Chain,
			Amount:        req.Amount,
			HandlingFee:   handlingFee,
			ActualAmount:  actualAmount,
			FeeRate:       feeRate,
			MinAmount:     minAmount,
			MaxAmount:     maxAmount,
			Available:     available.String(),
			EstimatedTime: utility.GetEstimatedTime(req.Chain),
		},
	}

	return res, nil
}

// validateMerchantBalance 验证商户余额
func (s *sSystemLogic) validateMerchantBalance(ctx context.Context, merchantId uint64, tokenName, amount string) error {
	available, err := s.getMerchantAvailableBalance(ctx, merchantId, tokenName)
	if err != nil {
		return gerror.Wrap(err, "获取余额失败")
	}

	amountDecimal, err := decimal.NewFromString(amount)
	if err != nil {
		return gerror.Wrap(err, "金额格式错误")
	}

	if available.LessThan(amountDecimal) {
		return gerror.Newf("可用余额不足，当前可用: %s %s, 需要: %s %s",
			available.String(), tokenName, amount, tokenName)
	}

	return nil
}

// getMerchantAvailableBalance 获取商户可用余额
func (s *sSystemLogic) getMerchantAvailableBalance(ctx context.Context, merchantId uint64, tokenName string) (decimal.Decimal, error) {
	walletService := s.getWalletService()
	walletInfo, err := walletService.GetBalance(ctx, merchantId, tokenName)
	if err != nil {
		// 如果钱包不存在，返回0余额
		if walletErr := wallet.GetWalletError(err); walletErr != nil && walletErr.Code == wallet.ErrCodeWalletNotFound {
			return decimal.Zero, nil
		}
		return decimal.Zero, gerror.Wrap(err, "获取钱包余额失败")
	}

	return walletInfo.Available, nil
}

// ApproveWithdraw 审批通过提现申请
func (s *sSystemLogic) ApproveWithdraw(ctx context.Context, req *v1.ApproveWithdrawReq) (res *v1.ApproveWithdrawRes, err error) {
	res = &v1.ApproveWithdrawRes{}

	// 1. 获取当前用户信息（管理员）
	// 使用merchantId作为临时方案，实际应该是管理员ID
	// TODO: 实现真正的管理员ID获取逻辑
	currentUserId := utility.GetCurrentMerchantID(ctx)
	if currentUserId == 0 {
		// 如果不是商户用户，可能是管理员，使用固定ID
		currentUserId = 1 // 管理员默认ID
	}

	// 2. 验证权限（这里应该检查是否有管理员权限）
	// TODO: 添加权限检查逻辑

	// 3. 获取提现记录
	withdrawRecord, err := dao.MerchantWithdraws.Ctx(ctx).
		Where("withdraws_id", req.WithdrawsId).
		Where("state", constants.WithdrawStatusPending). // 只能审批待审核的
		One()

	if err != nil {
		return nil, gerror.Wrap(err, "查询提现记录失败")
	}
	if withdrawRecord.IsEmpty() {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在或状态不正确")
	}

	// 4. 解析提现记录数据
	var withdraw entity.MerchantWithdraws
	if err := withdrawRecord.Struct(&withdraw); err != nil {
		return nil, gerror.Wrap(err, "解析提现记录失败")
	}

	// 5. 使用钱包服务生成审批审计记录
	walletService := s.getWalletService()
	approveReq := &wallet.ApproveWithdrawRequest{
		MerchantID:    withdraw.MerchantId,
		TokenSymbol:   withdraw.Name,
		WithdrawID:    uint64(req.WithdrawsId),
		ApproverID:    currentUserId,
		ApprovalNotes: req.Notes,
	}

	// 生成两条审计记录
	transactionResults, err := walletService.ApproveWithdraw(ctx, approveReq)
	if err != nil {
		return nil, gerror.Wrap(err, "生成审批审计记录失败")
	}

	// 6. 更新提现状态为处理中
	updateData := g.Map{
		"checked_by":   currentUserId,
		"admin_remark": req.Notes,
	}
	err = dao.MerchantWithdraws.UpdateStatus(ctx, uint64(req.WithdrawsId), constants.WithdrawStatusProcessing, updateData)
	if err != nil {
		return nil, gerror.Wrap(err, "更新提现状态失败")
	}

	// 7. 记录操作日志
	g.Log().Infof(ctx, "提现审批通过: withdrawsId=%d, approver=%d, transactionIds=%v",
		req.WithdrawsId, currentUserId, []uint64{transactionResults[0].TransactionID, transactionResults[1].TransactionID})

	// 8. 构建响应
	res.Success = true
	res.Message = "提现申请已审批通过，进入处理中状态"
	res.TransactionIds = []uint64{
		transactionResults[0].TransactionID,
		transactionResults[1].TransactionID,
	}

	return res, nil
}

// RejectWithdraw 拒绝提现申请
func (s *sSystemLogic) RejectWithdraw(ctx context.Context, req *v1.RejectWithdrawReq) (res *v1.RejectWithdrawRes, err error) {
	res = &v1.RejectWithdrawRes{}

	// 1. 获取当前用户信息（管理员）
	// 使用merchantId作为临时方案，实际应该是管理员ID
	// TODO: 实现真正的管理员ID获取逻辑
	currentUserId := utility.GetCurrentMerchantID(ctx)
	if currentUserId == 0 {
		// 如果不是商户用户，可能是管理员，使用固定ID
		currentUserId = 1 // 管理员默认ID
	}

	// 2. 验证权限（这里应该检查是否有管理员权限）
	// TODO: 添加权限检查逻辑

	// 3. 获取提现记录
	withdrawRecord, err := dao.MerchantWithdraws.Ctx(ctx).
		Where("withdraws_id", req.WithdrawsId).
		Where("state", constants.WithdrawStatusPending). // 只能拒绝待审核的
		One()

	if err != nil {
		return nil, gerror.Wrap(err, "查询提现记录失败")
	}
	if withdrawRecord.IsEmpty() {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在或状态不正确")
	}

	// 4. 解析提现记录数据
	var withdraw entity.MerchantWithdraws
	if err := withdrawRecord.Struct(&withdraw); err != nil {
		return nil, gerror.Wrap(err, "解析提现记录失败")
	}

	// 5. 获取提现金额
	// withdraw.Amount 已经是decimal.Decimal类型，直接使用
	amountDecimal := withdraw.Amount

	// 6. 使用钱包服务解冻余额（生成两条记录）
	walletService := s.getWalletService()
	cancelReq := &wallet.WithdrawRequest{
		MerchantID:    withdraw.MerchantId,
		TokenSymbol:   withdraw.Name,
		Amount:        amountDecimal,
		OrderID:       withdraw.OrderNo,
		Memo:          fmt.Sprintf("提现拒绝: %s", req.Reason),
		RequestSource: "admin",
	}

	// 取消提现（解冻余额）
	transactionResults, err := walletService.CancelWithdraw(ctx, cancelReq)
	if err != nil {
		return nil, gerror.Wrap(err, "解冻余额失败")
	}

	// 7. 更新提现状态为已拒绝
	updateData := g.Map{
		"checked_by":       currentUserId,
		"admin_remark":     req.Reason,
		"refuse_reason_zh": req.Reason,
		"refuse_reason_en": req.Reason, // 可以后续添加翻译逻辑
	}
	err = dao.MerchantWithdraws.UpdateStatus(ctx, uint64(req.WithdrawsId), constants.WithdrawStatusRejected, updateData)
	if err != nil {
		return nil, gerror.Wrap(err, "更新提现状态失败")
	}

	// 8. 记录操作日志
	g.Log().Infof(ctx, "提现申请已拒绝: withdrawsId=%d, rejecter=%d, reason=%s, transactionIds=%v",
		req.WithdrawsId, currentUserId, req.Reason,
		[]uint64{transactionResults[0].TransactionID, transactionResults[1].TransactionID})

	// 9. 构建响应
	res.Success = true
	res.Message = fmt.Sprintf("提现申请已拒绝，资金已解冻。拒绝原因：%s", req.Reason)
	res.TransactionIds = []uint64{
		transactionResults[0].TransactionID,
		transactionResults[1].TransactionID,
	}

	return res, nil
}
