package exchange_order

import (
	"context"
	"strings"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/service"
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type sExchangeOrder struct{}

func init() {
	service.RegisterExchangeOrder(New())
}

func New() service.IExchangeOrder {
	return &sExchangeOrder{}
}

// GetOrderList 获取兑换订单列表
func (s *sExchangeOrder) GetOrderList(ctx context.Context, req *v1.ExchangeOrderListReq) (*v1.ExchangeOrderListRes, error) {
	// 构建查询条件
	condition := g.Map{}

	// 添加筛选条件
	if req.OrderSn != "" {
		condition["o.order_sn"] = req.OrderSn
	}
	if req.UserId > 0 {
		condition["o.user_id"] = req.UserId
	}
	if req.ProductId > 0 {
		condition["o.product_id"] = req.ProductId
	}
	if req.Symbol != "" {
		condition["o.symbol LIKE"] = "%" + req.Symbol + "%"
	}
	if req.TradeType != "" {
		condition["o.trade_type"] = req.TradeType
	}
	if req.Status != "" {
		condition["o.status"] = req.Status
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["o.created_at >="] = dateRange[0] + " 00:00:00"
			condition["o.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 代理查询条件
	if req.FirstAgentName != "" {
		condition["u_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["u_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["u_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// Telegram查询条件
	if req.TelegramId != "" {
		condition["u_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["u_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["u_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 处理导出
	if req.Export == 1 {
		// 导出时不进行分页限制
		list, _, err := dao.ExchangeOrders.ListAdminExchangeOrdersWithFullInfo(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询失败")
		}

		// 转换为导出格式
		exportData := make([]interface{}, len(list))
		for i, item := range list {
			createdAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.String()
			}
			executedAt := ""
			if item.ExecutedAt != nil {
				executedAt = item.ExecutedAt.String()
			}
			completedAt := ""
			if item.CompletedAt != nil {
				completedAt = item.CompletedAt.String()
			}

			exportData[i] = struct {
				OrderSn               string `json:"orderSn" excel:"订单号"`
				UserId                uint64 `json:"userId" excel:"用户ID"`
				Account               string `json:"account" excel:"用户账号"`
				Symbol                string `json:"symbol" excel:"交易对"`
				TradeType             string `json:"tradeType" excel:"交易类型"`
				AmountBase            string `json:"amountBase" excel:"基础代币数量"`
				AmountQuote           string `json:"amountQuote" excel:"计价代币数量"`
				Price                 string `json:"price" excel:"成交价格"`
				SpreadAmount          string `json:"spreadAmount" excel:"点差金额"`
				FeeAmount             string `json:"feeAmount" excel:"手续费金额"`
				OutputAmountBeforeFee string `json:"outputAmountBeforeFee" excel:"扣费前输出金额"`
				OutputAmountAfterFee  string `json:"outputAmountAfterFee" excel:"扣费后输出金额"`
				FeeCalculationMethod  string `json:"feeCalculationMethod" excel:"手续费计算方法"`
				Status                string `json:"status" excel:"状态"`
				CreatedAt             string `json:"createdAt" excel:"创建时间"`
				ExecutedAt            string `json:"executedAt" excel:"执行时间"`
				CompletedAt           string `json:"completedAt" excel:"完成时间"`
				FirstAgentName        string `json:"firstAgentName" excel:"一级代理"`
				SecondAgentName       string `json:"secondAgentName" excel:"二级代理"`
				ThirdAgentName        string `json:"thirdAgentName" excel:"三级代理"`
				TelegramId            string `json:"telegramId" excel:"Telegram ID"`
				TelegramUsername      string `json:"telegramUsername" excel:"Telegram用户名"`
				FirstName             string `json:"firstName" excel:"真实姓名"`
			}{
				OrderSn:               item.OrderSn,
				UserId:                item.UserId,
				Account:               item.Account,
				Symbol:                item.Symbol,
				TradeType:             item.TradeType,
				AmountBase:            item.AmountBase.String(),
				AmountQuote:           item.AmountQuote.String(),
				Price:                 item.Price.String(),
				SpreadAmount:          item.SpreadAmount.String(),
				FeeAmount:             item.FeeAmount.String(),
				OutputAmountBeforeFee: item.OutputAmountBeforeFee.String(),
				OutputAmountAfterFee:  item.OutputAmountAfterFee.String(),
				FeeCalculationMethod:  item.FeeCalculationMethod,
				Status:                item.Status,
				CreatedAt:             createdAt,
				ExecutedAt:            executedAt,
				CompletedAt:           completedAt,
				FirstAgentName:        item.FirstAgentName,
				SecondAgentName:       item.SecondAgentName,
				ThirdAgentName:        item.ThirdAgentName,
				TelegramId:            item.TelegramId,
				TelegramUsername:      item.TelegramUsername,
				FirstName:             item.FirstName,
			}
		}

		// 定义Excel表头
		excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取

		// 调用Excel导出工具
		err = excel.ExportByStructs(ctx, excelTags, exportData, "兑换订单", "兑换订单列表")
		return nil, err
	}

	// 查询分页数据
	list, total, err := dao.ExchangeOrders.ListAdminExchangeOrdersWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询兑换订单列表失败")
	}

	// 转换为API响应格式
	resultList := make([]*v1.ExchangeOrderItem, len(list))
	for i, item := range list {
		createdAt := ""
		if item.CreatedAt != nil {
			createdAt = item.CreatedAt.String()
		}
		executedAt := ""
		if item.ExecutedAt != nil {
			executedAt = item.ExecutedAt.String()
		}
		completedAt := ""
		if item.CompletedAt != nil {
			completedAt = item.CompletedAt.String()
		}
		updatedAt := ""
		if item.UpdatedAt != nil {
			updatedAt = item.UpdatedAt.String()
		}

		resultList[i] = &v1.ExchangeOrderItem{
			OrderId:               item.OrderId,
			OrderSn:               item.OrderSn,
			UserId:                item.UserId,
			ProductId:             item.ProductId,
			BaseToken:             item.BaseToken,
			QuoteToken:            item.QuoteToken,
			Symbol:                item.Symbol,
			TradeType:             item.TradeType,
			AmountBase:            item.AmountBase,
			AmountQuote:           item.AmountQuote,
			OriginalFromAmount:    item.OriginalFromAmount,
			OriginalToAmount:      item.OriginalToAmount,
			Price:                 item.Price,
			SpreadAmount:          item.SpreadAmount,
			SpreadRate:            item.SpreadRate,
			FeeAmount:             item.FeeAmount,
			FeeTokenId:            item.FeeTokenId,
			OutputAmountBeforeFee: item.OutputAmountBeforeFee,
			OutputAmountAfterFee:  item.OutputAmountAfterFee,
			FeeCalculationMethod:  item.FeeCalculationMethod,
			TransactionHash:       item.TransactionHash,
			QuoteId:               item.QuoteId,
			Status:                item.Status,
			ErrorMessage:          item.ErrorMessage,
			ClientOrderId:         item.ClientOrderId,
			CreatedAt:             createdAt,
			ExecutedAt:            executedAt,
			CompletedAt:           completedAt,
			UpdatedAt:             updatedAt,
			Account:               item.Account,
			FirstAgentName:        item.FirstAgentName,
			SecondAgentName:       item.SecondAgentName,
			ThirdAgentName:        item.ThirdAgentName,
			TelegramId:            item.TelegramId,
			TelegramUsername:      item.TelegramUsername,
			FirstName:             item.FirstName,
		}
	}

	return &v1.ExchangeOrderListRes{
		List:     resultList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetOrderDetail 获取兑换订单详情
func (s *sExchangeOrder) GetOrderDetail(ctx context.Context, req *v1.ExchangeOrderDetailReq) (*v1.ExchangeOrderDetailRes, error) {
	// 使用新的 DAO 方法获取包含完整信息的订单详情
	orderInfo, err := dao.ExchangeOrders.GetOrderDetailWithFullInfo(ctx, req.OrderId)
	if err != nil {
		return nil, err
	}
	
	// 转换时间字段为字符串格式
	createdAt := ""
	if orderInfo.CreatedAt != nil {
		createdAt = orderInfo.CreatedAt.String()
	}
	executedAt := ""
	if orderInfo.ExecutedAt != nil {
		executedAt = orderInfo.ExecutedAt.String()
	}
	completedAt := ""
	if orderInfo.CompletedAt != nil {
		completedAt = orderInfo.CompletedAt.String()
	}
	updatedAt := ""
	if orderInfo.UpdatedAt != nil {
		updatedAt = orderInfo.UpdatedAt.String()
	}
	
	// 构建响应
	order := &v1.ExchangeOrderItem{
		OrderId:               orderInfo.OrderId,
		OrderSn:               orderInfo.OrderSn,
		UserId:                orderInfo.UserId,
		ProductId:             orderInfo.ProductId,
		BaseToken:             orderInfo.BaseToken,
		QuoteToken:            orderInfo.QuoteToken,
		Symbol:                orderInfo.Symbol,
		TradeType:             orderInfo.TradeType,
		AmountBase:            orderInfo.AmountBase,
		AmountQuote:           orderInfo.AmountQuote,
		OriginalFromAmount:    orderInfo.OriginalFromAmount,
		OriginalToAmount:      orderInfo.OriginalToAmount,
		Price:                 orderInfo.Price,
		SpreadAmount:          orderInfo.SpreadAmount,
		SpreadRate:            orderInfo.SpreadRate,
		FeeAmount:             orderInfo.FeeAmount,
		FeeTokenId:            orderInfo.FeeTokenId,
		OutputAmountBeforeFee: orderInfo.OutputAmountBeforeFee,
		OutputAmountAfterFee:  orderInfo.OutputAmountAfterFee,
		FeeCalculationMethod:  orderInfo.FeeCalculationMethod,
		TransactionHash:       orderInfo.TransactionHash,
		QuoteId:               orderInfo.QuoteId,
		Status:                orderInfo.Status,
		ErrorMessage:          orderInfo.ErrorMessage,
		ClientOrderId:         orderInfo.ClientOrderId,
		CreatedAt:             createdAt,
		ExecutedAt:            executedAt,
		CompletedAt:           completedAt,
		UpdatedAt:             updatedAt,
		// 用户信息
		Account:               orderInfo.Account,
		// 三级代理信息
		FirstAgentName:        orderInfo.FirstAgentName,
		SecondAgentName:       orderInfo.SecondAgentName,
		ThirdAgentName:        orderInfo.ThirdAgentName,
		// Telegram信息
		TelegramId:            orderInfo.TelegramId,
		TelegramUsername:      orderInfo.TelegramUsername,
		FirstName:             orderInfo.FirstName,
	}

	return &v1.ExchangeOrderDetailRes{
		ExchangeOrderItem: order,
	}, nil
}
