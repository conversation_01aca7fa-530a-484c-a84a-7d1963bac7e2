package v1

import (
	"context"
	"fmt"
	"math"
	"math/big"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	adminConstants "admin-api/internal/constants"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service"
	"admin-api/internal/utils"
	amountUtil "admin-api/utility/amount"
	"admin-api/utility/excel"

	"github.com/yalks/wallet"
	"github.com/yalks/wallet/constants"
)

// GetOrCreateWallet 确保钱包存在，如果不存在则创建。返回钱包详情。
func (s *sSystemLogic) GetOrCreateWallet(ctx context.Context, userId uint32, tokenId uint32) (*entity.Wallets, error) {
	// 1. 尝试获取钱包 (使用 Repository)
	wallet, err := s.walletRepo.GetWalletInfo(ctx, userId, tokenId) // Use injected repo
	if err != nil {
		// Repository GetWalletInfo already wraps errors
		return nil, err
	}

	// 2. 如果钱包存在，直接返回
	if wallet != nil {
		return wallet, nil
	}

	// 3. 如果钱包不存在，获取代币信息以获取小数位数 (使用 Repository)
	token, err := s.tokenRepo.GetByID(ctx, uint(tokenId)) // Use injected tokenRepo
	if err != nil {
		return nil, gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", tokenId)
	}
	if token == nil {
		// 使用 token_codes.go 中定义的错误码
		return nil, gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", tokenId)
	}

	// 4. 创建钱包
	// 注意：这里可能存在并发创建的竞态条件。
	// 数据库的 (user_id, token_id) UNIQUE KEY 约束是最后的防线。
	// CreateWalletWithDefaults 应该能处理或返回可识别的重复错误。
	// 4. 创建钱包 (使用 Repository - GetOrCreate handles creation and race conditions)
	// The GetOrCreate method in the repository now handles the logic of checking existence and creating if needed.
	// We already called GetWalletInfo at the beginning. If it was nil, we proceed to call GetOrCreate.
	// However, the initial GetWalletInfo call might be redundant if GetOrCreate handles everything.
	// Let's assume GetOrCreate is the primary method to ensure a wallet exists.
	// We might need to refactor GetOrCreate in the repo slightly if it doesn't return the created wallet directly on creation race condition.
	// For now, let's call GetOrCreate again, assuming it handles the logic correctly.
	newWallet, err := s.walletRepo.GetOrCreate(ctx, userId, tokenId) // Use injected repo
	if err != nil {
		// GetOrCreate should return the wallet or an error
		return nil, gerror.Wrapf(err, "获取或创建钱包失败 (userId: %d, tokenId: %d)", userId, tokenId)
	}
	// No need to check for nil here, GetOrCreate guarantees a wallet or an error

	return newWallet, nil
}

// IncreaseBalance 增加可用余额 (使用存储的 int64 金额)
func (s *sSystemLogic) IncreaseBalance(ctx context.Context, userId uint32, tokenId uint32, amountDecimal decimal.Decimal, changeReason string) error {
	if amountDecimal.LessThan(decimal.Zero) {
		return gerror.NewCode(codes.CodeInvalidParameter, "增加的金额必须为正数")
	}

	// 确保钱包存在
	localWallet, err := s.GetOrCreateWallet(ctx, userId, tokenId)
	if err != nil {
		return err // Propagate error from GetOrCreateWallet
	}
	if localWallet == nil {
		return gerror.NewCodef(codes.CodeWalletNotFound, "钱包不存在，无法增加余额 (userId: %d, tokenId: %d)", userId, tokenId)
	}

	// 获取代币信息以获取符号 (Symbol) 和 精度 (Decimals)
	token, err := s.tokenRepo.GetByID(ctx, uint(tokenId))
	if err != nil {
		return gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", tokenId)
	}
	if token == nil {
		return gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", tokenId)
	}
	symbol := token.Symbol

	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

		// --- 事务开始 ---
		// 获取管理员名称
		adminName := ""
		if r := g.RequestFromCtx(ctx); r == nil {
			return codes.NewError(codes.CodeUnauthorized)
		} else {
			adminName = r.GetCtxVar("username").String()
		}

		// 使用新的资金操作描述器
		descriptor := utils.NewFundOperationDescriptor("zh")
		businessID := descriptor.GenerateBusinessID(adminConstants.FundOpAdminAdd, userId, gtime.Now().Unix())

		// 构建描述信息
		memo := fmt.Sprintf("%s-%s", adminName, changeReason)
		description := descriptor.FormatDescriptionWithMemo(adminConstants.FundOpAdminAdd, amountDecimal.String(), symbol, memo)

		// 使用新的请求构建方法
		walletReq := &constants.FundOperationRequest{
			UserID:      uint64(userId),
			TokenSymbol: symbol,
			Amount:      amountDecimal,
			BusinessID:  businessID,
			FundType:    constants.FundTypeAdminAdd,
			Description: description,
			Metadata: map[string]string{
				"type":         "admin_credit",
				"request_id":   fmt.Sprintf("%d", userId),
				"operation":    "credit",
				"changeReason": changeReason,
				"admin_name":   adminName,
			},
			RequestSource: "admin",
		}

		_, err := wallet.Manager().ProcessFundOperationInTx(ctx, tx, walletReq)
		if err != nil {
			g.Log().Errorf(ctx, "增加资金失败: %v", err)
			return gerror.Wrap(err, "增加资金失败")
		}

		// 获取用户信息用于记录日志
		user, err := dao.Users.Ctx(ctx).Where("id = ?", userId).One()
		if err == nil && !user.IsEmpty() {
			var userEntity entity.Users
			if err := user.Struct(&userEntity); err == nil {
				// 记录余额调整日志
				service.TelegramAdminLog().RecordBalanceAdjustmentLog(ctx, &userEntity, amountDecimal.String(), changeReason, true, false)
			}
		}

		return nil
	})
}

// DecreaseBalance 减少可用余额 (使用存储的 int64 金额)
func (s *sSystemLogic) DecreaseBalance(ctx context.Context, userId uint32, tokenId uint32, amountDecimal decimal.Decimal, changeReason string) error {
	if amountDecimal.LessThan(decimal.Zero) {
		return gerror.NewCode(codes.CodeInvalidParameter, "减少的金额必须为正数")
	}

	// 确保钱包存在 (读取操作，不需要在事务内)
	// 确保钱包存在 (使用 Repository)
	localWallet, err := s.walletRepo.GetWalletInfo(ctx, userId, tokenId) // Use injected repo
	if err != nil {
		return err // Repository already wraps error
	}
	if localWallet == nil {
		return gerror.NewCodef(codes.CodeWalletNotFound, "钱包不存在，无法减少余额 (userId: %d, tokenId: %d)", userId, tokenId)
	}

	// 获取代币信息以获取符号 (Symbol) 和 精度 (Decimals)
	token, err := s.tokenRepo.GetByID(ctx, uint(tokenId))
	if err != nil {
		return gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", tokenId)
	}
	if token == nil {
		return gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", tokenId)
	}
	symbol := token.Symbol

	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

		// --- 事务开始 ---
		// 获取管理员名称
		adminName := ""
		if r := g.RequestFromCtx(ctx); r == nil {
			return codes.NewError(codes.CodeUnauthorized)
		} else {
			adminName = r.GetCtxVar("username").String()
		}

		// 使用新的资金操作描述器
		descriptor := utils.NewFundOperationDescriptor("zh")
		businessID := descriptor.GenerateBusinessID(adminConstants.FundOpAdminDeduct, userId, gtime.Now().Unix())

		// 构建描述信息
		memo := fmt.Sprintf("%s-%s", adminName, changeReason)
		description := descriptor.FormatDescriptionWithMemo(adminConstants.FundOpAdminDeduct, amountDecimal.String(), symbol, memo)

		// 使用新的请求构建方法
		walletReq := &constants.FundOperationRequest{
			UserID:      uint64(userId),
			TokenSymbol: symbol,
			Amount:      amountDecimal,
			BusinessID:  businessID,
			FundType:    constants.FundTypeAdminDeduct,
			Description: description,
			Metadata: map[string]string{
				"type":         "admin_debit",
				"request_id":   fmt.Sprintf("%d", userId),
				"operation":    "debit",
				"changeReason": changeReason,
				"admin_name":   adminName,
			},
			RequestSource: "admin",
		}

		_, err := wallet.Manager().ProcessFundOperationInTx(ctx, tx, walletReq)
		if err != nil {
			g.Log().Errorf(ctx, "减少资金失败: %v", err)
			return gerror.Wrap(err, "减少资金失败")
		}

		// 获取用户信息用于记录日志
		user, err := dao.Users.Ctx(ctx).Where("id = ?", userId).One()
		if err == nil && !user.IsEmpty() {
			var userEntity entity.Users
			if err := user.Struct(&userEntity); err == nil {
				// 记录余额调整日志
				service.TelegramAdminLog().RecordBalanceAdjustmentLog(ctx, &userEntity, amountDecimal.String(), changeReason, false, false)
			}
		}

		return nil
	})
}

// ListWallets 获取钱包列表（带代理和telegram信息）
func (s *sSystemLogic) ListWallets(ctx context.Context, req *v1.ListWalletsReq) (res *v1.ListWalletsRes, err error) {
	// 初始化返回结果
	res = &v1.ListWalletsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.WalletListItem, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 用户账号（模糊查询）
	if req.Account != "" {
		condition["u.account LIKE"] = "%" + req.Account + "%"
	}

	// 币种符号（模糊查询）
	if req.Symbol != "" {
		condition["w.symbol LIKE"] = "%" + req.Symbol + "%"
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["w.created_at >="] = dateRange[0] + " 00:00:00"
			condition["w.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 代理查询条件
	if req.FirstAgentName != "" {
		condition["first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// Telegram查询条件
	if req.TelegramId != "" {
		condition["uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 处理导出
	if req.Export == 1 {
		// 导出时不进行分页限制
		list, _, err := dao.Wallets.ListAdminWalletsWithFullInfo(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询失败")
		}

		// 转换为导出格式
		exportData := make([]interface{}, len(list))
		for i, item := range list {
			createdAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.String()
			}

			// 格式化余额
			formattedAvailable := amountUtil.FormatBalance(big.NewInt(item.AvailableBalance), uint8(item.DecimalPlaces))
			formattedFrozen := amountUtil.FormatBalance(big.NewInt(item.FrozenBalance), uint8(item.DecimalPlaces))

			exportData[i] = struct {
				WalletId           int64  `json:"walletId" excel:"钱包ID"`
				UserAccount        string `json:"userAccount" excel:"用户账号"`
				Symbol             string `json:"symbol" excel:"代币符号"`
				Type               string `json:"type" excel:"钱包类型"`
				FormattedAvailable string `json:"formattedAvailable" excel:"可用余额"`
				FormattedFrozen    string `json:"formattedFrozen" excel:"冻结余额"`
				CreatedAt          string `json:"createdAt" excel:"创建时间"`
				FirstAgentName     string `json:"firstAgentName" excel:"一级代理"`
				SecondAgentName    string `json:"secondAgentName" excel:"二级代理"`
				ThirdAgentName     string `json:"thirdAgentName" excel:"三级代理"`
				TelegramId         string `json:"telegramId" excel:"Telegram ID"`
				TelegramUsername   string `json:"telegramUsername" excel:"Telegram用户名"`
				FirstName          string `json:"firstName" excel:"真实姓名"`
			}{
				WalletId:           item.WalletId,
				UserAccount:        item.UserAccount,
				Symbol:             item.Symbol,
				Type:               item.Type,
				FormattedAvailable: formattedAvailable,
				FormattedFrozen:    formattedFrozen,
				CreatedAt:          createdAt,
				FirstAgentName:     item.FirstAgentName,
				SecondAgentName:    item.SecondAgentName,
				ThirdAgentName:     item.ThirdAgentName,
				TelegramId:         item.TelegramId,
				TelegramUsername:   item.TelegramUsername,
				FirstName:          item.FirstName,
			}
		}

		// 定义Excel表头（通过struct tag自动获取）
		excelTags := []string{}

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "钱包管理", "钱包列表")
	}

	// 查询分页数据
	list, total, err := dao.Wallets.ListAdminWalletsWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询钱包列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 转换DAO结果到API响应格式
	res.Data = make([]*v1.WalletListItem, len(list))
	for i, item := range list {
		// 将entity转换为API响应对象
		res.Data[i] = &v1.WalletListItem{
			Wallets: &entity.Wallets{
				WalletId:         int(item.WalletId),
				UserId:           uint(item.UserId),
				TokenId:          uint(item.TokenId),
				Symbol:           item.Symbol,
				Type:             item.Type,
				AvailableBalance: item.AvailableBalance,
				FrozenBalance:    item.FrozenBalance,
				DecimalPlaces:    item.DecimalPlaces,
				CreatedAt:        item.CreatedAt,
				UpdatedAt:        item.UpdatedAt,
			},
			Account:                   item.UserAccount,
			FormattedAvailableBalance: amountUtil.FormatBalance(big.NewInt(item.AvailableBalance), uint8(item.DecimalPlaces)),
			FormattedFrozenBalance:    amountUtil.FormatBalance(big.NewInt(item.FrozenBalance), uint8(item.DecimalPlaces)),
			FirstAgentName:            item.FirstAgentName,
			SecondAgentName:           item.SecondAgentName,
			ThirdAgentName:            item.ThirdAgentName,
			TelegramId:                item.TelegramId,
			TelegramUsername:          item.TelegramUsername,
			FirstName:                 item.FirstName,
		}
	}

	return res, nil
}

// getTokenIdBySymbol 根据代币符号获取代币ID
func (s *sSystemLogic) getTokenIdBySymbol(ctx context.Context, symbol string) (uint32, error) {
	// 查询代币信息
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Status, 1). // 只查询状态为上架的代币
		WhereNull(dao.Tokens.Columns().DeletedAt).
		Scan(&token)

	if err != nil {
		return 0, gerror.Wrapf(err, "查询代币信息失败, Symbol: %s", symbol)
	}
	if token == nil {
		return 0, gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在或未上架 (Symbol: %s)", symbol)
	}

	return uint32(token.TokenId), nil
}

// AdjustBalance 调整钱包余额
func (s *sSystemLogic) AdjustBalance(ctx context.Context, req *v1.AdjustBalanceReq) (res *v1.AdjustBalanceRes, err error) {
	res = &v1.AdjustBalanceRes{
		Success: false,
	}

	// 获取钱包信息 - 使用现有的存储库方法
	// 先尝试通过walletId查询钱包记录
	// 通过DAO直接查询钱包信息
	wallet := &entity.Wallets{}
	err = dao.Wallets.Ctx(ctx).Where("wallet_id", req.WalletId).Scan(wallet)
	if err != nil {
		return nil, gerror.Wrapf(err, "获取钱包信息失败 (walletId: %d)", req.WalletId)
	}
	if wallet == nil || wallet.WalletId == 0 {
		return nil, gerror.NewCodef(codes.CodeWalletNotFound, "钱包不存在 (walletId: %d)", req.WalletId)
	}

	// // 从钱包信息中获取用户ID和代币ID
	userId := uint32(wallet.UserId)
	tokenId := uint32(wallet.TokenId)

	// // 根据类型调整余额
	// // 首先获取代币信息以计算正确的金额
	token, err := s.tokenRepo.GetByID(ctx, uint(tokenId))
	if err != nil {
		return nil, gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", tokenId)
	}
	if token == nil {
		return nil, gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", tokenId)
	}

	// // 将 float64 金额转换为字符串，然后解析为代币的最小单位 (big.Int)
	// amountStr := fmt.Sprintf("%f", req.Amount)
	// amountBigInt, err := amountUtil.ParseAmount(amountStr, uint8(token.Decimals))
	// if err != nil {
	// 	return nil, gerror.Wrapf(err, "解析金额失败: %s", amountStr)
	// }

	// // 将 big.Int 转换为 int64，用于内部操作
	// amountInt64 := amountBigInt.Int64()
	decimalAmount := decimal.NewFromFloat(req.Amount)

	if req.Type == 1 { // 增加余额
		if err := s.IncreaseBalance(ctx, userId, tokenId, decimalAmount, req.ChangeReason); err != nil {
			return nil, err
		}
	} else if req.Type == 2 { // 减少余额
		if err := s.DecreaseBalance(ctx, userId, tokenId, decimalAmount, req.ChangeReason); err != nil {
			return nil, err
		}
	} else {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "类型参数错误，只能是1(增加)或2(减少)")
	}

	// 获取最新余额 - 再次查询，获取最新状态
	updatedWallet := &entity.Wallets{}
	err = dao.Wallets.Ctx(ctx).Where("wallet_id", req.WalletId).Scan(updatedWallet)
	if err != nil {
		return nil, gerror.Wrapf(err, "获取最新钱包信息失败 (walletId: %d)", req.WalletId)
	}
	if updatedWallet == nil || updatedWallet.WalletId == 0 {
		return nil, gerror.NewCodef(codes.CodeWalletNotFound, "调整后钱包未找到 (walletId: %d)", req.WalletId)
	}

	// 返回成功和新余额
	res.Success = true
	res.NewBalance = updatedWallet.AvailableBalance
	return res, nil
}

// GetAvailableBalance 获取可用余额 (返回 big.Int 和小数位数)
func (s *sSystemLogic) GetAvailableBalance(ctx context.Context, userId uint32, tokenId uint32) (*big.Int, uint8, error) {
	// Use Repository
	wallet, err := s.walletRepo.GetWalletInfo(ctx, userId, tokenId) // Use injected repo
	if err != nil {
		return nil, 0, gerror.Wrapf(err, "获取钱包信息失败, UserID: %d, TokenID: %d", userId, tokenId)
	}
	if wallet == nil {
		return nil, 0, gerror.NewCodef(codes.CodeWalletNotFound, "钱包未找到 (userId: %d, tokenId: %d)", userId, tokenId)
	}

	// Use Repository
	token, err := s.tokenRepo.GetByID(ctx, uint(tokenId))
	if err != nil {
		return nil, 0, gerror.Wrapf(err, "获取代币信息失败, TokenID: %d", tokenId)
	}
	if token == nil {
		return nil, 0, gerror.NewCodef(codes.CodeTokenNotFound, "代币信息不存在, TokenID: %d", tokenId)
	}

	balanceBigInt := big.NewInt(wallet.AvailableBalance)
	return balanceBigInt, uint8(token.Decimals), nil // Cast decimals
}

// GetWalletBalance 获取钱包余额
func (s *sSystemLogic) GetWalletBalance(ctx context.Context, req *v1.GetWalletBalanceReq) (res *v1.GetWalletBalanceRes, err error) {
	// 根据Symbol获取TokenId
	tokenId, err := s.getTokenIdBySymbol(ctx, req.Symbol)
	if err != nil {
		return nil, err
	}

	// 获取余额
	balanceBigInt, decimals, err := s.GetAvailableBalance(ctx, req.UserId, tokenId)
	if err != nil {
		return nil, err
	}

	// 获取钱包信息以获取冻结余额
	wallet, err := s.walletRepo.GetWalletInfo(ctx, req.UserId, tokenId)
	if err != nil {
		return nil, err
	}
	if wallet == nil {
		return nil, gerror.NewCodef(codes.CodeWalletNotFound, "钱包未找到 (userId: %d, tokenId: %d)", req.UserId, tokenId)
	}

	// 格式化余额
	res = &v1.GetWalletBalanceRes{
		AvailableBalance: amountUtil.FormatBalance(balanceBigInt, decimals),
		FrozenBalance:    amountUtil.FormatBalance(big.NewInt(wallet.FrozenBalance), decimals),
		DecimalPlaces:    decimals,
	}
	return res, nil
}
