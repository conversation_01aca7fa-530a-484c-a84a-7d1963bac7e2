package user_withdraw

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/user_withdraw"
	"admin-api/internal/utility"
	"context"
	"database/sql"
	"errors"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// userWithdrawRepository implements the IUserWithdrawRepository interface
type userWithdrawRepository struct{}

// NewUserWithdrawRepository creates a new userWithdrawRepository instance
func NewUserWithdrawRepository() user_withdraw.IUserWithdrawRepository {
	return &userWithdrawRepository{}
}

// List retrieves a paginated list of user withdraws based on the provided conditions
func (r *userWithdrawRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*user_withdraw.UserWithdrawListItemDTO, total int, err error) {
	// Initialize the list
	list = make([]*user_withdraw.UserWithdrawListItemDTO, 0)

	// Build the query model
	model := dao.UserWithdraws.Ctx(ctx).As("uw")

	// Join with users table to get account and nickname
	model = model.LeftJoin(dao.Users.Table()+" u", "uw.user_id = u.id")

	// Join with tokens table to get symbol
	model = model.LeftJoin("tokens t", "uw.token_id = t.token_id")

	// Apply conditions
	if condition != nil {
		model = model.Where(condition)
		// Debug log the condition
		g.Log().Debugf(ctx, "User withdraws list query condition: %v", condition)
	}

	// Count total records
	count, err := model.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "统计提现记录总数失败")
	}
	total = count

	// If no records found, return empty list
	if total == 0 {
		return list, 0, nil
	}

	// Select fields for the result
	model = model.Fields(
		"uw.user_withdraws_id",
		"uw.user_id",
		"uw.token_id",
		"uw.wallet_id",
		"uw.order_no",
		"uw.address",
		"uw.recipient_name",
		"uw.recipient_account",
		"uw.recipient_qrcode",
		"uw.fiat_type",
		"uw.amount",
		"uw.handling_fee",
		"uw.actual_amount",
		"uw.conversion_token_symbol",
		"uw.conversion_rate",
		"uw.converted_amount",
		"uw.chan", // Note: column name in table is 'chan' (not 'chain')
		"uw.state",
		"uw.tx_hash",
		"uw.user_remark",
		"uw.admin_remark",
		"uw.created_at",
		"uw.checked_at",
		"uw.processing_at",
		"uw.completed_at",
		"uw.notification_sent",
		"uw.notification_sent_at",
		"u.account",
		"t.symbol",
	)

	// Apply pagination and sorting
	model = model.Page(page, pageSize).OrderDesc("uw.created_at")

	// Execute the query
	var result []struct {
		UserWithdrawsId       uint        `json:"user_withdraws_id"`
		UserId                uint64      `json:"user_id"`
		TokenId               uint        `json:"token_id"`
		WalletId              string      `json:"wallet_id"`
		OrderNo               string      `json:"order_no"`
		Address               string      `json:"address"`
		RecipientName         string      `json:"recipient_name"`
		RecipientAccount      string      `json:"recipient_account"`
		RecipientQrcode       string      `json:"recipient_qrcode"`
		FiatType              string      `json:"fiat_type"`
		Amount                float64     `json:"amount"`
		HandlingFee           float64     `json:"handling_fee"`
		ActualAmount          float64     `json:"actual_amount"`
		ConversionTokenSymbol string      `json:"conversion_token_symbol"`
		ConversionRate        string      `json:"conversion_rate"`
		ConvertedAmount       string      `json:"converted_amount"`
		Chan                  string      `json:"chan"`
		State                 uint        `json:"state"`
		TxHash                string      `json:"tx_hash"`
		UserRemark            string      `json:"user_remark"`
		AdminRemark           string      `json:"admin_remark"`
		CreatedAt             *gtime.Time `json:"created_at"`
		CheckedAt             *gtime.Time `json:"checked_at"`
		ProcessingAt          *gtime.Time `json:"processing_at"`
		CompletedAt           *gtime.Time `json:"completed_at"`
		NotificationSent      uint        `json:"notification_sent"`
		NotificationSentAt    *gtime.Time `json:"notification_sent_at"`
		Account               string      `json:"account"`
		Nickname              string      `json:"nickname"`
		Symbol                string      `json:"symbol"`
	}

	err = model.Scan(&result)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询提现记录列表失败")
	}

	// Convert result to DTOs
	for _, item := range result {
		listItem := &user_withdraw.UserWithdrawListItemDTO{
			UserWithdraws: &entity.UserWithdraws{
				UserWithdrawsId:       item.UserWithdrawsId,
				UserId:                item.UserId,
				TokenId:               item.TokenId,
				WalletId:              item.WalletId,
				OrderNo:               item.OrderNo,
				Address:               item.Address,
				RecipientName:         item.RecipientName,
				RecipientAccount:      item.RecipientAccount,
				RecipientQrcode:       item.RecipientQrcode,
				FiatType:              item.FiatType,
				Amount:                decimal.NewFromFloat(item.Amount),
				HandlingFee:           decimal.NewFromFloat(item.HandlingFee),
				ActualAmount:          decimal.NewFromFloat(item.ActualAmount),
				ConversionTokenSymbol: item.ConversionTokenSymbol,
				ConversionRate: func() decimal.Decimal {
					if item.ConversionRate == "" {
						return decimal.Zero
					}
					rate, _ := decimal.NewFromString(item.ConversionRate)
					return rate
				}(),
				ConvertedAmount: func() decimal.Decimal {
					if item.ConvertedAmount == "" {
						return decimal.Zero
					}
					amount, _ := decimal.NewFromString(item.ConvertedAmount)
					return amount
				}(),
				Chan:               item.Chan,
				State:              item.State,
				TxHash:             item.TxHash,
				UserRemark:         item.UserRemark,
				AdminRemark:        item.AdminRemark,
				CreatedAt:          item.CreatedAt,
				CheckedAt:          item.CheckedAt,
				ProcessingAt:       item.ProcessingAt,
				CompletedAt:        item.CompletedAt,
				NotificationSent:   item.NotificationSent,
				NotificationSentAt: item.NotificationSentAt,
			},
			Account:  item.Account,
			Nickname: item.Account,
			Symbol:   item.Symbol,
		}
		list = append(list, listItem)
	}

	return list, total, nil
}

// GetByID retrieves a single user withdraw entry by its ID
func (r *userWithdrawRepository) GetByID(ctx context.Context, id uint) (*entity.UserWithdraws, error) {
	var withdraw *entity.UserWithdraws
	err := dao.UserWithdraws.Ctx(ctx).Where(dao.UserWithdraws.Columns().UserWithdrawsId, id).Scan(&withdraw)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "userWithdrawRepository.GetByID: 查询提现记录失败, ID: %d", id)
	}
	return withdraw, nil
}

// GetDetailByID retrieves a user withdraw detail by its ID
func (r *userWithdrawRepository) GetDetailByID(ctx context.Context, id uint) (*user_withdraw.UserWithdrawDetailDTO, error) {
	// Build the query
	model := dao.UserWithdraws.Ctx(ctx).As("uw")

	// Join with users table to get account and username
	model = model.LeftJoin("users u", "uw.user_id = u.id")

	// Join with tokens table to get symbol
	model = model.LeftJoin("tokens t", "uw.token_id = t.token_id")

	// Apply condition
	model = model.Where("uw.user_withdraws_id", id)

	// Select fields
	model = model.Fields(
		"uw.*",
		"u.account",
		"t.symbol",
	)

	// Define a struct to hold the query result
	type DetailResult struct {
		entity.UserWithdraws
		Account  string // 用户账号
		Nickname string // 用户昵称
		Symbol   string // 币种符号
	}

	// Execute the query
	var result DetailResult
	err := model.Scan(&result)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "userWithdrawRepository.GetDetailByID: 查询提现记录详情失败, ID: %d", id)
	}

	// Convert to DTO
	detail := &user_withdraw.UserWithdrawDetailDTO{
		UserWithdraws: &entity.UserWithdraws{
			UserWithdrawsId:    result.UserWithdrawsId,
			UserId:             result.UserId,
			TokenId:            result.TokenId,
			WalletId:           result.WalletId,
			Name:               result.Name,
			Chan:               result.Chan,
			OrderNo:            result.OrderNo,
			Address:            result.Address,
			RecipientName:      result.RecipientName,
			RecipientAccount:   result.RecipientAccount,
			RecipientQrcode:    result.RecipientQrcode,
			FiatType:           result.FiatType,
			Amount:             result.Amount,
			HandlingFee:        result.HandlingFee,
			ActualAmount:       result.ActualAmount,
			State:              result.State,
			RefuseReasonZh:     result.RefuseReasonZh,
			RefuseReasonEn:     result.RefuseReasonEn,
			TxHash:             result.TxHash,
			ErrorMessage:       result.ErrorMessage,
			UserRemark:         result.UserRemark,
			AdminRemark:        result.AdminRemark,
			CreatedAt:          result.CreatedAt,
			CheckedAt:          result.CheckedAt,
			ProcessingAt:       result.ProcessingAt,
			CompletedAt:        result.CompletedAt,
			UpdatedAt:          result.UpdatedAt,
			Retries:            result.Retries,
			NergyState:         result.NergyState,
			NotificationSent:   result.NotificationSent,
			NotificationSentAt: result.NotificationSentAt,
		},
		Account:  result.Account,
		Nickname: result.Account,
		Symbol:   result.Symbol,
	}

	return detail, nil
}

// UpdateState updates the state of a user withdraw
func (r *userWithdrawRepository) UpdateState(ctx context.Context, tx gdb.TX, id uint, state uint) error {
	// Prepare data
	data := g.Map{
		dao.UserWithdraws.Columns().State:     state,
		dao.UserWithdraws.Columns().UpdatedAt: gtime.Now(),
	}

	// Add timestamps based on state
	switch state {
	case 2: // Processing
		data[dao.UserWithdraws.Columns().ProcessingAt] = gtime.Now()
	case 3: // Rejected
		data[dao.UserWithdraws.Columns().CheckedAt] = gtime.Now()
	case 4, 5: // Completed or Failed
		data[dao.UserWithdraws.Columns().CompletedAt] = gtime.Now()
	}

	// Execute update
	_, err := dao.UserWithdraws.Ctx(ctx).TX(tx).Where(dao.UserWithdraws.Columns().UserWithdrawsId, id).Update(data)
	if err != nil {
		return gerror.Wrapf(err, "userWithdrawRepository.UpdateState: 更新提现记录状态失败, ID: %d, State: %d", id, state)
	}

	return nil
}

// UpdateStateWithReason updates the state of a user withdraw with a reason
func (r *userWithdrawRepository) UpdateStateWithReason(ctx context.Context, tx gdb.TX, id uint, state uint, refuseReasonZh, refuseReasonEn, adminRemark string) error {
	// Prepare data
	data := g.Map{
		dao.UserWithdraws.Columns().State:          state,
		dao.UserWithdraws.Columns().RefuseReasonZh: refuseReasonZh,
		dao.UserWithdraws.Columns().RefuseReasonEn: refuseReasonEn,
		dao.UserWithdraws.Columns().AdminRemark:    adminRemark,
		dao.UserWithdraws.Columns().UpdatedAt:      gtime.Now(),
	}

	// Add timestamps based on state
	switch state {
	case 2: // Processing
		data[dao.UserWithdraws.Columns().ProcessingAt] = gtime.Now()
	case 3: // Rejected
		data[dao.UserWithdraws.Columns().CheckedAt] = gtime.Now()
	case 4, 5: // Completed or Failed
		data[dao.UserWithdraws.Columns().CompletedAt] = gtime.Now()
	}

	// Execute update
	_, err := dao.UserWithdraws.Ctx(ctx).TX(tx).Where(dao.UserWithdraws.Columns().UserWithdrawsId, id).Update(data)
	if err != nil {
		return gerror.Wrapf(err, "userWithdrawRepository.UpdateStateWithReason: 更新提现记录状态失败, ID: %d, State: %d", id, state)
	}

	return nil
}

// UpdateStateWithTxInfo updates the state of a user withdraw with transaction information
func (r *userWithdrawRepository) UpdateStateWithTxInfo(ctx context.Context, tx gdb.TX, id uint, state uint, txHash, errorMessage, adminRemark string) error {
	// Prepare data
	data := g.Map{
		dao.UserWithdraws.Columns().State:        state,
		dao.UserWithdraws.Columns().TxHash:       txHash,
		dao.UserWithdraws.Columns().ErrorMessage: errorMessage,
		dao.UserWithdraws.Columns().AdminRemark:  adminRemark,
		dao.UserWithdraws.Columns().UpdatedAt:    gtime.Now(),
	}

	// Add timestamps based on state
	switch state {
	case 2: // Processing
		data[dao.UserWithdraws.Columns().ProcessingAt] = gtime.Now()
	case 3: // Rejected
		data[dao.UserWithdraws.Columns().CheckedAt] = gtime.Now()
	case 4, 5: // Completed or Failed
		data[dao.UserWithdraws.Columns().CompletedAt] = gtime.Now()
	}

	// Execute update
	_, err := dao.UserWithdraws.Ctx(ctx).TX(tx).Where(dao.UserWithdraws.Columns().UserWithdrawsId, id).Update(data)
	if err != nil {
		return gerror.Wrapf(err, "userWithdrawRepository.UpdateStateWithTxInfo: 更新提现记录状态失败, ID: %d, State: %d", id, state)
	}

	return nil
}

// ListWithAgentInfo retrieves a paginated list of user withdraws with agent and telegram info.
func (r *userWithdrawRepository) ListWithAgentInfo(ctx context.Context, req *v1.ListUserWithdrawsReq) (list []*v1.UserWithdrawsListItem, total int, err error) {
	// Initialize empty list
	list = make([]*v1.UserWithdrawsListItem, 0)

	// Build query model
	model := dao.UserWithdraws.Ctx(ctx).As("uw").
		LeftJoin(dao.Users.Table()+" u", "uw.user_id = u.id").
		LeftJoin("tokens t", "uw.token_id = t.token_id")

	// 添加代理和telegram表的关联查询
	model = utility.AddAgentAndTelegramJoins(model, dao.Users.Table(), dao.UserBackupAccounts.Table())

	// Build conditions
	if req.UserId != nil && *req.UserId > 0 {
		model = model.Where("uw.user_id", *req.UserId)
	}
	if req.Account != nil && *req.Account != "" {
		model = model.Where("u.account", *req.Account)
	}
	if req.Username != nil && *req.Username != "" {
		model = model.WhereLike("u.nickname", "%"+*req.Username+"%")
	}
	if req.TokenId != nil && *req.TokenId > 0 {
		model = model.Where("uw.token_id", *req.TokenId)
	}
	if req.Symbol != nil && *req.Symbol != "" {
		model = model.WhereLike("t.symbol", "%"+*req.Symbol+"%")
	}
	if req.Chain != nil && *req.Chain != "" {
		model = model.Where("uw.chan", *req.Chain)
	}
	if req.Address != nil && *req.Address != "" {
		model = model.Where("uw.address", *req.Address)
	}
	if req.OrderNo != nil && *req.OrderNo != "" {
		model = model.Where("uw.order_no", *req.OrderNo)
	}
	if req.State != nil && *req.State > 0 {
		model = model.Where("uw.state", *req.State)
	}
	if req.AmountMin != nil && *req.AmountMin > 0 {
		model = model.WhereGTE("uw.amount", *req.AmountMin)
	}
	if req.AmountMax != nil && *req.AmountMax > 0 {
		model = model.WhereLTE("uw.amount", *req.AmountMax)
	}

	// Handle date range
	condition := g.Map{}
	utility.AddDateRangeCondition(condition, req.DateRange, "uw.created_at")
	for key, value := range condition {
		model = model.Where(key, value)
	}

	// 新增：三级代理模糊查询
	model = utility.AddAgentSearchConditions(model, req.FirstAgentName, req.SecondAgentName, req.ThirdAgentName)

	// 新增：telegram查询条件
	model = utility.AddTelegramSearchConditions(model, req.TelegramId, req.TelegramUsername, req.FirstName)

	// 新增：法币提现查询条件
	if req.FiatType != "" {
		model = model.Where("uw.fiat_type", req.FiatType)
	}
	if req.RecipientName != "" {
		model = model.WhereLike("uw.recipient_name", "%"+req.RecipientName+"%")
	}
	if req.RecipientAccount != "" {
		model = model.WhereLike("uw.recipient_account", "%"+req.RecipientAccount+"%")
	}

	// Count total
	total, err = model.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户提现记录总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// Select fields
	baseFields := []string{
		"uw.user_withdraws_id",
		"uw.user_id",
		"uw.token_id",
		"uw.wallet_id",
		"uw.order_no",
		"uw.address",
		"uw.recipient_name",
		"uw.recipient_account",
		"uw.recipient_qrcode",
		"uw.fiat_type",
		"uw.amount",
		"uw.handling_fee",
		"uw.actual_amount",
		"uw.conversion_token_symbol",
		"uw.conversion_rate",
		"uw.converted_amount",
		"uw.chan",
		"uw.state",
		"uw.tx_hash",
		"uw.user_remark",
		"uw.admin_remark",
		"uw.created_at",
		"uw.checked_at",
		"uw.processing_at",
		"uw.completed_at",
		"uw.notification_sent",
		"uw.notification_sent_at",
		"u.account",
		"t.symbol",
	}

	// 添加代理和telegram字段
	agentAndTelegramFields := utility.GetAgentAndTelegramFields()
	allFields := append(baseFields, agentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	model = model.Fields(fieldInterfaces...)

	// Apply pagination and sorting
	model = model.Page(req.Page, req.PageSize).OrderDesc("uw.created_at")

	// Execute the query
	var result []struct {
		UserWithdrawsId       uint        `json:"user_withdraws_id"`
		UserId                uint64      `json:"user_id"`
		TokenId               uint        `json:"token_id"`
		WalletId              string      `json:"wallet_id"`
		OrderNo               string      `json:"order_no"`
		Address               string      `json:"address"`
		RecipientName         string      `json:"recipient_name"`
		RecipientAccount      string      `json:"recipient_account"`
		RecipientQrcode       string      `json:"recipient_qrcode"`
		FiatType              string      `json:"fiat_type"`
		Amount                float64     `json:"amount"`
		HandlingFee           float64     `json:"handling_fee"`
		ActualAmount          float64     `json:"actual_amount"`
		ConversionTokenSymbol string      `json:"conversion_token_symbol"`
		ConversionRate        string      `json:"conversion_rate"`
		ConvertedAmount       string      `json:"converted_amount"`
		Chan                  string      `json:"chan"`
		State                 uint        `json:"state"`
		TxHash                string      `json:"tx_hash"`
		UserRemark            string      `json:"user_remark"`
		AdminRemark           string      `json:"admin_remark"`
		CreatedAt             *gtime.Time `json:"created_at"`
		CheckedAt             *gtime.Time `json:"checked_at"`
		ProcessingAt          *gtime.Time `json:"processing_at"`
		CompletedAt           *gtime.Time `json:"completed_at"`
		NotificationSent      uint        `json:"notification_sent"`
		NotificationSentAt    *gtime.Time `json:"notification_sent_at"`
		Account               string      `json:"account"`
		Nickname              string      `json:"nickname"`
		Symbol                string      `json:"symbol"`

		// 新增：代理信息
		FirstAgentName  string `json:"first_agent_name"`
		SecondAgentName string `json:"second_agent_name"`
		ThirdAgentName  string `json:"third_agent_name"`

		// 新增：telegram信息
		TelegramId       string `json:"telegram_id"`
		TelegramUsername string `json:"telegram_username"`
		FirstName        string `json:"first_name"`
	}

	err = model.Scan(&result)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户提现记录列表失败")
	}

	// Convert result to response format
	for _, item := range result {
		listItem := &v1.UserWithdrawsListItem{
			UserWithdrawsId:       item.UserWithdrawsId,
			UserId:                item.UserId,
			Account:               item.Account,
			Nickname:              item.Nickname,
			TokenId:               item.TokenId,
			Symbol:                item.Symbol,
			Chain:                 item.Chan,
			WalletId:              item.WalletId,
			OrderNo:               item.OrderNo,
			Address:               item.Address,
			RecipientName:         item.RecipientName,
			RecipientAccount:      item.RecipientAccount,
			RecipientQrcode:       item.RecipientQrcode,
			FiatType:              item.FiatType,
			Amount:                item.Amount,
			HandlingFee:           item.HandlingFee,
			ActualAmount:          item.ActualAmount,
			ConversionTokenSymbol: item.ConversionTokenSymbol,
			ConversionRate:        item.ConversionRate,
			ConvertedAmount:       item.ConvertedAmount,
			State:                 item.State,
			TxHash:                item.TxHash,
			UserRemark:            item.UserRemark,
			AdminRemark:           item.AdminRemark,
			CreatedAt:             item.CreatedAt,
			CheckedAt:             item.CheckedAt,
			ProcessingAt:          item.ProcessingAt,
			CompletedAt:           item.CompletedAt,
			NotificationSent:      item.NotificationSent,
			NotificationSentAt:    item.NotificationSentAt,

			// 新增：代理信息
			FirstAgentName:  item.FirstAgentName,
			SecondAgentName: item.SecondAgentName,
			ThirdAgentName:  item.ThirdAgentName,

			// 新增：telegram信息
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
		}

		list = append(list, listItem)
	}

	return list, total, nil
}

// GetDetailWithAgentInfo retrieves a user withdraw detail by its ID with agent and telegram info.
func (r *userWithdrawRepository) GetDetailWithAgentInfo(ctx context.Context, id uint) (*v1.UserWithdrawDetailItem, error) {
	// Build the query
	model := dao.UserWithdraws.Ctx(ctx).As("uw").
		LeftJoin(dao.Users.Table()+" u", "uw.user_id = u.id").
		LeftJoin("tokens t", "uw.token_id = t.token_id")

	// 添加代理和telegram表的关联查询
	model = utility.AddAgentAndTelegramJoins(model, dao.Users.Table(), dao.UserBackupAccounts.Table())

	// Apply condition
	model = model.Where("uw.user_withdraws_id", id)

	// Select fields
	baseFields := []string{
		"uw.user_withdraws_id",
		"uw.user_id",
		"uw.token_id",
		"uw.wallet_id",
		"uw.order_no",
		"uw.address",
		"uw.recipient_name",
		"uw.recipient_account",
		"uw.recipient_qrcode",
		"uw.fiat_type",
		"uw.amount",
		"uw.handling_fee",
		"uw.actual_amount",
		"uw.chan",
		"uw.state",
		"uw.refuse_reason_zh",
		"uw.refuse_reason_en",
		"uw.tx_hash",
		"uw.error_message",
		"uw.user_remark",
		"uw.admin_remark",
		"uw.created_at",
		"uw.checked_at",
		"uw.processing_at",
		"uw.completed_at",
		"uw.notification_sent",
		"uw.notification_sent_at",
		"u.account",
		"t.symbol",
	}

	// 添加代理和telegram字段
	agentAndTelegramFields := utility.GetAgentAndTelegramFields()
	allFields := append(baseFields, agentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	model = model.Fields(fieldInterfaces...)

	// Define a struct to hold the query result
	var result struct {
		UserWithdrawsId    uint        `json:"user_withdraws_id"`
		UserId             uint64      `json:"user_id"`
		TokenId            uint        `json:"token_id"`
		WalletId           string      `json:"wallet_id"`
		OrderNo            string      `json:"order_no"`
		Address            string      `json:"address"`
		RecipientName      string      `json:"recipient_name"`
		RecipientAccount   string      `json:"recipient_account"`
		RecipientQrcode    string      `json:"recipient_qrcode"`
		FiatType           string      `json:"fiat_type"`
		Amount             float64     `json:"amount"`
		HandlingFee        float64     `json:"handling_fee"`
		ActualAmount       float64     `json:"actual_amount"`
		Chan               string      `json:"chan"`
		State              uint        `json:"state"`
		RefuseReasonZh     string      `json:"refuse_reason_zh"`
		RefuseReasonEn     string      `json:"refuse_reason_en"`
		TxHash             string      `json:"tx_hash"`
		ErrorMessage       string      `json:"error_message"`
		UserRemark         string      `json:"user_remark"`
		AdminRemark        string      `json:"admin_remark"`
		CreatedAt          *gtime.Time `json:"created_at"`
		CheckedAt          *gtime.Time `json:"checked_at"`
		ProcessingAt       *gtime.Time `json:"processing_at"`
		CompletedAt        *gtime.Time `json:"completed_at"`
		NotificationSent   uint        `json:"notification_sent"`
		NotificationSentAt *gtime.Time `json:"notification_sent_at"`
		Account            string      `json:"account"`
		Nickname           string      `json:"nickname"`
		Symbol             string      `json:"symbol"`

		// 新增：代理信息
		FirstAgentName  string `json:"first_agent_name"`
		SecondAgentName string `json:"second_agent_name"`
		ThirdAgentName  string `json:"third_agent_name"`

		// 新增：telegram信息
		TelegramId       string `json:"telegram_id"`
		TelegramUsername string `json:"telegram_username"`
		FirstName        string `json:"first_name"`
	}

	// Execute the query
	err := model.Scan(&result)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "userWithdrawRepository.GetDetailWithAgentInfo: 查询提现记录详情失败, ID: %d", id)
	}

	// Convert to response format
	detail := &v1.UserWithdrawDetailItem{
		UserWithdrawsId:    result.UserWithdrawsId,
		UserId:             result.UserId,
		Account:            result.Account,
		Nickname:           result.Nickname,
		TokenId:            result.TokenId,
		Symbol:             result.Symbol,
		Chain:              result.Chan,
		WalletId:           result.WalletId,
		OrderNo:            result.OrderNo,
		Address:            result.Address,
		RecipientName:      result.RecipientName,
		RecipientAccount:   result.RecipientAccount,
		RecipientQrcode:    result.RecipientQrcode,
		FiatType:           result.FiatType,
		Amount:             result.Amount,
		HandlingFee:        result.HandlingFee,
		ActualAmount:       result.ActualAmount,
		State:              result.State,
		RefuseReasonZh:     result.RefuseReasonZh,
		RefuseReasonEn:     result.RefuseReasonEn,
		TxHash:             result.TxHash,
		ErrorMessage:       result.ErrorMessage,
		UserRemark:         result.UserRemark,
		AdminRemark:        result.AdminRemark,
		CreatedAt:          result.CreatedAt,
		CheckedAt:          result.CheckedAt,
		ProcessingAt:       result.ProcessingAt,
		CompletedAt:        result.CompletedAt,
		NotificationSent:   result.NotificationSent,
		NotificationSentAt: result.NotificationSentAt,

		// 新增：代理信息
		FirstAgentName:  result.FirstAgentName,
		SecondAgentName: result.SecondAgentName,
		ThirdAgentName:  result.ThirdAgentName,

		// 新增：telegram信息
		TelegramId:       result.TelegramId,
		TelegramUsername: result.TelegramUsername,
		FirstName:        result.FirstName,
	}

	return detail, nil
}
