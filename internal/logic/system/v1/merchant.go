package v1

import (
	"context"
	"strings"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service"
	"admin-api/internal/utility"
	"admin-api/utility/encrypt"
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetMerchantList 获取商户列表
func (s *sSystemLogic) GetMerchantList(ctx context.Context, req *v1.GetMerchantListReq) (res *v1.GetMerchantListRes, err error) {
	// 构建查询条件
	condition := g.Map{}
	if req.MerchantName != "" {
		condition["merchant_name LIKE"] = "%" + req.MerchantName + "%"
	}
	if req.BusinessName != "" {
		condition["business_name LIKE"] = "%" + req.BusinessName + "%"
	}
	if req.Email != "" {
		condition["email LIKE"] = "%" + req.Email + "%"
	}
	if req.Phone != "" {
		condition["phone LIKE"] = "%" + req.Phone + "%"
	}
	if req.Status != nil {
		condition["status"] = *req.Status
	}

	// 使用 DateRange 统一处理时间范围查询
	utility.AddDateRangeCondition(condition, req.DateRange)

	// 如果是导出，则直接返回数据
	if req.Export {
		list, _, err := s.merchantRepo.List(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询商户列表失败")
		}

		// 准备导出数据
		exportData := make([]interface{}, 0, len(list))
		for _, merchant := range list {
			statusText := "待审核"
			switch merchant.Status {
			case -1:
				statusText = "待审核"
			case 0:
				statusText = "禁用"
			case 1:
				statusText = "启用"
			}

			exportItem := map[string]interface{}{
				"商户ID":  merchant.MerchantId,
				"商户名称":  merchant.MerchantName,
				"业务名称":  merchant.BusinessName,
				"商户邮箱":  merchant.Email,
				"联系电话":  merchant.Phone,
				"网站URL": merchant.WebsiteUrl,
				"状态":    statusText,
				"备注":    merchant.Notes,
				"创建时间":  merchant.CreatedAt.Format("2006-01-02 15:04:05"),
				"更新时间":  merchant.UpdatedAt.Format("2006-01-02 15:04:05"),
			}
			exportData = append(exportData, exportItem)
		}

		// 调用Excel导出工具
		return &v1.GetMerchantListRes{}, excel.ExportByStructs(ctx, []string{}, exportData, "商户列表", "商户列表")
	}

	// 查询商户列表
	list, total, err := s.merchantRepo.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	merchantInfoList := make([]*v1.MerchantInfoType, 0, len(list))
	for _, merchant := range list {
		merchantInfo := &v1.MerchantInfoType{
			Merchants:              *merchant,
			PaymentPasswordEnabled: merchant.PaymentPassword != "",
		}
		merchantInfoList = append(merchantInfoList, merchantInfo)
	}

	res = &v1.GetMerchantListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   total,
			TotalPage:   common.CalculateTotalPage(total, req.PageSize),
		},
		Data: merchantInfoList,
	}

	return res, nil
}

// GetMerchant 获取商户详情
func (s *sSystemLogic) GetMerchant(ctx context.Context, req *v1.GetMerchantReq) (res *v1.GetMerchantRes, err error) {
	// 查询商户信息
	merchant, err := s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 查询API密钥列表
	apiKeys, err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().MerchantId, req.MerchantId).
		WhereNull(dao.MerchantApiKeys.Columns().DeletedAt).
		All()
	if err != nil {
		return nil, gerror.Wrap(err, "查询API密钥失败")
	}

	var apiKeyEntities []*entity.MerchantApiKeys
	if err := apiKeys.Structs(&apiKeyEntities); err != nil {
		return nil, gerror.Wrap(err, "转换API密钥数据失败")
	}

	res = &v1.GetMerchantRes{
		Data: &v1.MerchantDetailType{
			Merchants:              *merchant,
			ApiKeys:                apiKeyEntities,
			PaymentPasswordEnabled: merchant.PaymentPassword != "",
		},
	}

	return res, nil
}

// AddMerchant 添加商户
func (s *sSystemLogic) AddMerchant(ctx context.Context, req *v1.AddMerchantReq) (res *v1.AddMerchantRes, err error) {
	// 额外验证商户名称格式（检查连续的下划线或连字符）
	if err := s.validateMerchantName(req.MerchantName); err != nil {
		return nil, err
	}

	// 检查商户名称是否已存在
	exists, err := s.merchantRepo.ExistsByName(ctx, req.MerchantName)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, gerror.NewCodef(codes.CodeMerchantNameExists, "商户名称 [%s] 已存在", req.MerchantName)
	}

	// 检查邮箱是否已存在
	exists, err = s.merchantRepo.ExistsByEmail(ctx, req.Email)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, gerror.NewCodef(codes.CodeMerchantEmailExists, "邮箱 [%s] 已存在", req.Email)
	}

	// 确认密码已通过API验证规则校验

	// 创建商户数据
	merchantData := &do.Merchants{
		MerchantName:     req.MerchantName,
		BusinessName:     req.BusinessName,
		Email:            req.Email,
		Phone:            req.Phone,
		WebsiteUrl:       req.WebsiteUrl,
		ContactEmail:     req.ContactEmail,
		Google2FaSecret:  "", // 默认为空，不生成 TOTP Secret
		RecoveryCodes:    "", // 默认为空，不生成恢复码
		Google2FaEnabled: 0,  // 默认禁用 Google 2FA
		Status:           -1, // 待审核
		Notes:            req.Notes,
		CreatedAt:        gtime.Now(),
		UpdatedAt:        gtime.Now(),
	}

	// 使用事务确保数据一致性
	var merchantId uint
	err = dao.Merchants.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 创建商户记录
		var createErr error
		merchantId, createErr = s.merchantRepo.Create(ctx, merchantData)
		if createErr != nil {
			return createErr
		}

		// 2. 同步到 Casdoor
		if service.MerchantCasdoor() != nil {
			merchant := &entity.Merchants{
				MerchantId:      uint64(merchantId),
				MerchantName:    req.MerchantName,
				Email:           req.Email,
				BusinessName:    req.BusinessName,
				WebsiteUrl:      req.WebsiteUrl,
				Google2FaSecret: "", // 默认为空
				RecoveryCodes:   "", // 默认为空
				Status:          -1, // 待审核
				Notes:           req.Notes,
			}

			// 使用客户提供的密码同步到 Casdoor（不再传递 TOTP 相关参数）
			syncErr := service.MerchantCasdoor().SyncAddMerchantWithMFA(
				ctx, merchant, req.MerchantName, req.Email, req.Password,
				"", []string{}) // 传递空的 TOTP Secret 和空的恢复码数组
			if syncErr != nil {
				g.Log().Errorf(ctx, "同步商户到Casdoor失败: %v", syncErr)
				return gerror.Wrap(syncErr, "同步商户到Casdoor失败")
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	res = &v1.AddMerchantRes{
		MerchantId: merchantId,
	}

	return res, nil
}

// EditMerchant 编辑商户
func (s *sSystemLogic) EditMerchant(ctx context.Context, req *v1.EditMerchantReq) (res *v1.EditMerchantRes, err error) {
	// 额外验证商户名称格式（检查连续的下划线或连字符）
	if err := s.validateMerchantName(req.MerchantName); err != nil {
		return nil, err
	}

	// 检查商户是否存在
	_, err = s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 检查商户名称是否已存在（排除当前商户）
	exists, err := s.merchantRepo.ExistsByName(ctx, req.MerchantName, req.MerchantId)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, gerror.NewCodef(codes.CodeMerchantNameExists, "商户名称 [%s] 已存在", req.MerchantName)
	}

	// 检查邮箱是否已存在（排除当前商户）
	exists, err = s.merchantRepo.ExistsByEmail(ctx, req.Email, req.MerchantId)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, gerror.NewCodef(codes.CodeMerchantEmailExists, "邮箱 [%s] 已存在", req.Email)
	}

	// 先同步到 Casdoor
	if service.MerchantCasdoor() != nil {
		updatedMerchant := &entity.Merchants{
			MerchantId:   uint64(req.MerchantId),
			MerchantName: req.MerchantName,
			Email:        req.Email,
			BusinessName: req.BusinessName,
			WebsiteUrl:   req.WebsiteUrl,
			Notes:        req.Notes,
		}

		if err := service.MerchantCasdoor().SyncUpdateMerchant(ctx, updatedMerchant, req.MerchantName, req.Email); err != nil {
			g.Log().Errorf(ctx, "同步更新商户到Casdoor失败: %v", err)
			return nil, gerror.Wrap(err, "同步更新商户到Casdoor失败")
		}
	}

	// Casdoor 同步成功后，更新数据库
	updateData := g.Map{
		"merchant_name": req.MerchantName,
		"business_name": req.BusinessName,
		"email":         req.Email,
		"phone":         req.Phone,
		"website_url":   req.WebsiteUrl,
		"contact_email": req.ContactEmail,
		"callback_url":  req.CallbackUrl,
		"notes":         req.Notes,
		"updated_at":    gtime.Now(),
	}

	err = s.merchantRepo.Update(ctx, req.MerchantId, updateData)
	if err != nil {
		return nil, err
	}

	res = &v1.EditMerchantRes{Success: true}
	return res, nil
}

// DeleteMerchant 删除商户
func (s *sSystemLogic) DeleteMerchant(ctx context.Context, req *v1.DeleteMerchantReq) (res *v1.DeleteMerchantRes, err error) {
	if len(req.MerchantIds) == 0 {
		return nil, gerror.New("商户ID列表不能为空")
	}

	// 使用事务确保数据一致性
	err = dao.Merchants.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 先获取所有要删除的商户信息（用于同步Casdoor）
		var merchants []*entity.Merchants
		for _, id := range req.MerchantIds {
			merchant, err := s.merchantRepo.GetByID(ctx, id)
			if err != nil {
				// 如果商户不存在，跳过
				if gerror.Code(err) == codes.CodeMerchantNotFound {
					continue
				}
				return err
			}
			merchants = append(merchants, merchant)
		}

		// 从 Casdoor 删除商户
		if service.MerchantCasdoor() != nil {
			for _, merchant := range merchants {
				if err := service.MerchantCasdoor().SyncDeleteMerchant(ctx, merchant.MerchantName); err != nil {
					g.Log().Errorf(ctx, "同步删除Casdoor商户失败: %v", err)
					// 继续删除，不中断流程
				}
			}
		}

		// 硬删除商户（包括关联的API密钥）
		return s.merchantRepo.Delete(ctx, req.MerchantIds)
	})

	if err != nil {
		return nil, err
	}

	res = &v1.DeleteMerchantRes{Success: true}
	return res, nil
}

// UpdateMerchantStatus 更新商户状态
func (s *sSystemLogic) UpdateMerchantStatus(ctx context.Context, req *v1.UpdateMerchantStatusReq) (res *v1.UpdateMerchantStatusRes, err error) {
	// 检查商户是否存在
	_, err = s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 更新状态
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		updateData := g.Map{
			"status":     req.Status,
			"notes":      req.Notes,
			"updated_at": gtime.Now(),
		}

		// 如果是启用状态，记录审核通过时间
		if req.Status == 1 {
			updateData["approved_at"] = gtime.Now()
		}

		return s.merchantRepo.Update(ctx, req.MerchantId, updateData)
	})

	if err != nil {
		return nil, err
	}

	res = &v1.UpdateMerchantStatusRes{Success: true}
	return res, nil
}

// ResetMerchantGoogle2FA 重置商户Google 2FA
func (s *sSystemLogic) ResetMerchantGoogle2FA(ctx context.Context, req *v1.ResetMerchantGoogle2FAReq) (res *v1.ResetMerchantGoogle2FARes, err error) {
	// 检查商户是否存在
	merchant, err := s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}
	if merchant == nil {
		return nil, gerror.NewCode(codes.CodeMerchantNotFound)
	}
	if merchant.Google2FaEnabled == 0 {
		return nil, gerror.NewCode(codes.CodeMerchantGoogle2FAIsNotEnabled)
	}

	dao.Merchants.Ctx(ctx).Data(g.Map{
		dao.Merchants.Columns().Google2FaSecret:  "",
		dao.Merchants.Columns().Google2FaEnabled: 0,
		dao.Merchants.Columns().UpdatedAt:        gtime.Now(),
	}).Where(dao.Merchants.Columns().MerchantId, req.MerchantId).Update()

	res = &v1.ResetMerchantGoogle2FARes{Success: true}
	return res, nil
}

// ResetMerchantPassword 重置商户密码
func (s *sSystemLogic) ResetMerchantPassword(ctx context.Context, req *v1.ResetMerchantPasswordReq) (res *v1.ResetMerchantPasswordRes, err error) {
	// 检查商户是否存在
	merchant, err := s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 先同步到 Casdoor
	if service.MerchantCasdoor() != nil {
		if syncErr := service.MerchantCasdoor().SyncUpdatePassword(ctx, merchant.MerchantName, req.NewPassword); syncErr != nil {
			g.Log().Errorf(ctx, "同步密码到Casdoor失败: %v", syncErr)
			return nil, gerror.Wrap(syncErr, "同步密码到Casdoor失败")
		}
	}

	// 对新密码进行哈希处理
	hashedPassword, err := encrypt.BcryptHash(req.NewPassword)
	if err != nil {
		return nil, gerror.Wrap(err, "密码哈希处理失败")
	}

	// Casdoor 同步成功后，更新数据库密码
	err = s.merchantRepo.Update(ctx, req.MerchantId, g.Map{
		"password": hashedPassword,
	})
	if err != nil {
		return nil, err
	}

	res = &v1.ResetMerchantPasswordRes{Success: true}
	return res, nil
}

// GenerateMerchantApiKey 生成商户API密钥
func (s *sSystemLogic) GenerateMerchantApiKey(ctx context.Context, req *v1.GenerateMerchantApiKeyReq) (res *v1.GenerateMerchantApiKeyRes, err error) {
	// 检查商户是否存在
	_, err = s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 生成API密钥
	apiKey, secretKey, genErr := s.apiKeyGenSvc.Generate(ctx)
	if genErr != nil {
		return nil, gerror.Wrap(genErr, "生成 API 密钥对失败")
	}

	// 创建API密钥记录
	apiKeyData := &do.MerchantApiKeys{
		MerchantId:  req.MerchantId,
		ApiKey:      apiKey,
		Secret:      secretKey,
		Label:       req.Label,
		Scopes:      req.Scopes,
		IpWhitelist: req.IpWhitelist,
		ExpiresAt:   req.ExpiresAt,
		Status:      "active",
		CreatedAt:   gtime.Now(),
		UpdatedAt:   gtime.Now(),
	}

	result, err := dao.MerchantApiKeys.Ctx(ctx).Data(apiKeyData).Insert()
	if err != nil {
		return nil, gerror.Wrap(err, "创建API密钥失败")
	}

	apiKeyId, err := result.LastInsertId()
	if err != nil {
		return nil, gerror.Wrap(err, "获取API密钥ID失败")
	}

	res = &v1.GenerateMerchantApiKeyRes{
		ApiKeyId:  uint(apiKeyId),
		ApiKey:    apiKey,
		SecretKey: secretKey,
	}

	return res, nil
}

// GetMerchantApiKeyList 获取商户API密钥列表
func (s *sSystemLogic) GetMerchantApiKeyList(ctx context.Context, req *v1.GetMerchantApiKeyListReq) (res *v1.GetMerchantApiKeyListRes, err error) {
	// 检查商户是否存在
	_, err = s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 构建查询条件
	condition := g.Map{
		dao.MerchantApiKeys.Columns().MerchantId: req.MerchantId,
	}

	if req.ApiKey != "" {
		condition[dao.MerchantApiKeys.Columns().ApiKey+" LIKE"] = "%" + req.ApiKey + "%"
	}
	if req.Label != "" {
		condition[dao.MerchantApiKeys.Columns().Label+" LIKE"] = "%" + req.Label + "%"
	}
	if req.Status != "" {
		condition[dao.MerchantApiKeys.Columns().Status] = req.Status
	}

	// 使用 DateRange 统一处理时间范围查询
	utility.AddDateRangeCondition(condition, req.DateRange)

	// 查询API密钥列表
	m := dao.MerchantApiKeys.Ctx(ctx).Where(condition).WhereNull(dao.MerchantApiKeys.Columns().DeletedAt)

	total, err := m.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "查询API密钥总数失败")
	}

	var apiKeys []*entity.MerchantApiKeys
	err = m.Page(req.Page, req.PageSize).OrderDesc(dao.MerchantApiKeys.Columns().CreatedAt).Scan(&apiKeys)
	if err != nil {
		return nil, gerror.Wrap(err, "查询API密钥列表失败")
	}

	// 组装返回数据
	apiKeyInfoList := make([]*v1.ApiKeyInfoType, 0, len(apiKeys))
	for _, apiKey := range apiKeys {
		apiKeyInfo := &v1.ApiKeyInfoType{
			ApiKeyId:    apiKey.ApiKeyId,
			MerchantId:  apiKey.MerchantId,
			ApiKey:      apiKey.ApiKey,
			Label:       apiKey.Label,
			Status:      apiKey.Status,
			Scopes:      apiKey.Scopes,
			IpWhitelist: apiKey.IpWhitelist,
			ExpiresAt:   apiKey.ExpiresAt,
			LastUsedAt:  apiKey.LastUsedAt,
			CreatedAt:   apiKey.CreatedAt,
		}
		apiKeyInfoList = append(apiKeyInfoList, apiKeyInfo)
	}

	res = &v1.GetMerchantApiKeyListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   total,
			TotalPage:   common.CalculateTotalPage(total, req.PageSize),
		},
		Data: apiKeyInfoList,
	}

	return res, nil
}

// UpdateMerchantApiKey 更新商户API密钥
func (s *sSystemLogic) UpdateMerchantApiKey(ctx context.Context, req *v1.UpdateMerchantApiKeyReq) (res *v1.UpdateMerchantApiKeyRes, err error) {
	// 检查商户是否存在
	_, err = s.merchantRepo.GetByID(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 检查API密钥是否存在且属于该商户
	apiKey, err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, req.ApiKeyId).
		Where(dao.MerchantApiKeys.Columns().MerchantId, req.MerchantId).
		WhereNull(dao.MerchantApiKeys.Columns().DeletedAt).
		One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询API密钥失败")
	}
	if apiKey.IsEmpty() {
		return nil, gerror.NewCode(codes.CodeApiKeyNotFound)
	}

	// 更新API密钥信息
	updateData := g.Map{
		dao.MerchantApiKeys.Columns().Label:       req.Label,
		dao.MerchantApiKeys.Columns().Scopes:      req.Scopes,
		dao.MerchantApiKeys.Columns().IpWhitelist: req.IpWhitelist,
		dao.MerchantApiKeys.Columns().ExpiresAt:   req.ExpiresAt,
		dao.MerchantApiKeys.Columns().UpdatedAt:   gtime.Now(),
	}

	_, err = dao.MerchantApiKeys.Ctx(ctx).
		Data(updateData).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, req.ApiKeyId).
		Update()
	if err != nil {
		return nil, gerror.Wrap(err, "更新API密钥失败")
	}

	res = &v1.UpdateMerchantApiKeyRes{Success: true}
	return res, nil
}

// RevokeMerchantApiKey 撤销商户API密钥
func (s *sSystemLogic) RevokeMerchantApiKey(ctx context.Context, req *v1.RevokeMerchantApiKeyReq) (res *v1.RevokeMerchantApiKeyRes, err error) {
	// 检查API密钥是否存在
	apiKey, err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, req.ApiKeyId).
		WhereNull(dao.MerchantApiKeys.Columns().DeletedAt).
		One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询API密钥失败")
	}
	if apiKey.IsEmpty() {
		return nil, gerror.NewCode(codes.CodeApiKeyNotFound)
	}

	// 撤销API密钥（软删除）
	_, err = dao.MerchantApiKeys.Ctx(ctx).
		Data(g.Map{
			dao.MerchantApiKeys.Columns().Status:    "revoked",
			dao.MerchantApiKeys.Columns().DeletedAt: gtime.Now(),
		}).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, req.ApiKeyId).
		Update()
	if err != nil {
		return nil, gerror.Wrap(err, "撤销API密钥失败")
	}

	res = &v1.RevokeMerchantApiKeyRes{Success: true}
	return res, nil
}

// validateMerchantName 验证商户名称格式
func (s *sSystemLogic) validateMerchantName(name string) error {
	// 检查是否包含连续的下划线或连字符
	if strings.Contains(name, "__") || strings.Contains(name, "--") {
		return gerror.NewCode(codes.CodeInvalidParameter, "商户名称不能包含连续的下划线或连字符")
	}

	// 检查是否包含下划线和连字符的组合
	if strings.Contains(name, "_-") || strings.Contains(name, "-_") {
		return gerror.NewCode(codes.CodeInvalidParameter, "商户名称不能包含下划线和连字符的组合")
	}

	return nil
}
