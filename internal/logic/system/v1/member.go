package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/consts"
	"admin-api/internal/utility"

	// utilityCasbin "admin-api/utility/casbin" // 导入 casbin 工具包
	"context"
	"fmt"

	"admin-api/internal/model/do" // Keep for AddMember struct conversion for now
	"admin-api/internal/model/entity"

	memberLogic "admin-api/internal/logic/system/v1/member" // Import member logic for BuildMemberTree

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetMemberList 获取用户列表
func (s *sSystemLogic) GetMemberList(ctx context.Context, req *v1.GetMemberListReq) (res *v1.GetMemberListRes, err error) {
	res = &v1.GetMemberListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.MemberListItem, 0),
	}

	condition := g.Map{}
	if req.Username != "" {
		condition["username"] = req.Username
	}
	if req.RealName != "" {
		condition["realName"] = req.RealName
	}
	if req.Mobile != "" {
		condition["mobile"] = req.Mobile
	}
	if req.Status != nil {
		condition["status"] = req.Status
	}
	// Removed DeptId condition
	if req.RoleId != nil {
		condition["roleId"] = req.RoleId
	}
	if req.PostId != nil {
		condition["postId"] = req.PostId
	}
	
	// 使用统一的DateRange处理工具
	utility.AddDateRangeCondition(condition, req.DateRange)

	// 从 DAO 获取基础用户列表
	list, total, err := s.memberRepo.List(ctx, req.Page, req.PageSize, condition) // Use memberRepo
	if err != nil {
		return nil, err
	}

	// 如果列表为空，直接返回
	if len(list) == 0 {
		return res, nil
	}

	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)

	// 提取所有需要的关联 ID
	// roleIdMap := make(map[int64]struct{}) // 用于存储主角色 ID
	memberIds := make([]int64, len(list))
	for i, member := range list {
		memberIds[i] = member.Id
		// Removed DeptId collection
		// if member.RoleId > 0 { // RoleId 存储的是主角色 ID
		// 	roleIdMap[member.RoleId] = struct{}{}
		// }
	}

	// 批量查询关联信息
	// roleMap := make(map[int64]*entity.AdminRole)
	// if len(roleIdMap) > 0 {
	// 	roleIds := make([]int64, 0, len(roleIdMap))
	// 	for id := range roleIdMap {
	// 		roleIds = append(roleIds, id)
	// 	}
	// 	// TODO: 需要在 dao/admin_role.go 中实现 GetRoleMapByIds 方法
	// 	// 示例: func (d *adminRoleDao) GetRoleMapByIds(ctx context.Context, ids []int64) (map[int64]*entity.AdminRole, error)
	// 	roleMap, _ = s.roleRepo.GetRoleMapByIds(ctx, roleIds) // Use roleRepo
	// }

	// 获取用户ID到岗位列表的映射
	// postMap := make(map[int64][]*entity.AdminPost)
	// if len(memberIds) > 0 {
	// 	// TODO: 需要在 dao/admin_member_post.go 中实现 GetMemberPostsMap 方法
	// 	// 示例: func (d *adminMemberPostDao) GetMemberPostsMap(ctx context.Context, memberIds []int64) (map[int64][]*entity.AdminPost, error)
	// 	postMap, _ = s.memberPostRepo.GetMemberPostsMap(ctx, memberIds) // Use memberPostRepo
	// }

	// 组装最终结果
	// res.Data = make([]*v1.MemberListItem, len(list))
	// for i, member := range list {
	// 	listItem := &v1.MemberListItem{
	// 		AdminMember:     member,
	// 		// DeptName removed from MemberListItem struct
	// 		PrimaryRoleName: "",
	// 		PostNames:       make([]string, 0),
	// 	}
	// 	// Removed DeptName assignment
	// 	// if role, ok := roleMap[member.RoleId]; ok && role != nil {
	// 	// 	listItem.PrimaryRoleName = role.Name
	// 	// }
	// 	if posts, ok := postMap[member.Id]; ok {
	// 		for _, post := range posts {
	// 			if post != nil { // 添加 nil 检查
	// 				listItem.PostNames = append(listItem.PostNames, post.Name)
	// 			}
	// 		}
	// 	}
	// 	res.Data[i] = listItem
	// }

	return res, nil
}

// GetMember 获取用户详情
func (s *sSystemLogic) GetMember(ctx context.Context, req *v1.GetMemberReq) (res *v1.GetMemberRes, err error) {
	member, err := s.memberRepo.GetByID(ctx, req.Id) // Use memberRepo
	if err != nil {
		return nil, err
	}

	detail := &v1.MemberDetail{
		AdminMember: member,
		// Dept removed from MemberDetail struct
		Roles: make([]*entity.AdminRole, 0),
		Posts: make([]*entity.AdminPost, 0),
	}

	// Removed department query logic

	// // 查询关联角色 (Use Repositories)
	// roleIds, roleErr := s.memberRoleRepo.GetRoleIDsByMemberID(ctx, req.Id) // Use memberRoleRepo
	// if roleErr != nil {
	// 	g.Log().Warningf(ctx, "查询用户 %d 的角色关联失败: %v", req.Id, roleErr)
	// } else if len(roleIds) > 0 {
	// 	roles, rolesErr := s.roleRepo.GetRolesByIDs(ctx, roleIds) // Use roleRepo
	// 	if rolesErr != nil {
	// 		g.Log().Warningf(ctx, "查询用户 %d 的角色实体失败: %v", req.Id, rolesErr)
	// 	} else {
	// 		detail.Roles = roles
	// 	}
	// }

	// // 查询关联岗位 (Use Repositories)
	// postIds, postErr := s.memberPostRepo.GetPostIdsByMemberId(ctx, req.Id) // Use memberPostRepo (Corrected method name case)
	// if postErr != nil {
	// 	g.Log().Warningf(ctx, "查询用户 %d 的岗位关联失败: %v", req.Id, postErr)
	// } else if len(postIds) > 0 {
	// 	posts, postsErr := s.postRepo.GetPostsByIDs(ctx, postIds) // Use postRepo
	// 	if postsErr != nil {
	// 		g.Log().Warningf(ctx, "查询用户 %d 的岗位实体失败: %v", req.Id, postsErr)
	// 	} else {
	// 		detail.Posts = posts
	// 	}
	// }

	res = &v1.GetMemberRes{
		Data: detail,
	}
	return res, nil
}

// AddMember 新增用户
func (s *sSystemLogic) AddMember(ctx context.Context, req *v1.AddMemberReq) (res *v1.AddMemberRes, err error) {
	// 1. 校验唯一性 (Use Repositories)
	existingMember, _ := s.memberRepo.GetByUsername(ctx, req.Username) // Ignore error for existence check
	if existingMember != nil {
		return nil, gerror.NewCode(codes.CodeUsernameExists)
	}
	if exists, _ := s.memberRepo.ExistsByEmail(ctx, req.Email); exists { // Ignore error for existence check
		return nil, gerror.NewCode(codes.CodeEmailExists)
	}
	if exists, _ := s.memberRepo.ExistsByMobile(ctx, req.Mobile); exists { // Ignore error for existence check
		return nil, gerror.NewCode(codes.CodeMobileExists)
	}
	if req.InviteCode != "" {
		if exists, _ := s.memberRepo.ExistsByInviteCode(ctx, req.InviteCode); exists { // Ignore error for existence check
			return nil, gerror.NewCode(codes.CodeInviteCodeExists)
		}
	}

	// 2. 校验角色、岗位 ID 的有效性 (Use Repositories)
	// Removed department validation
	if len(req.RoleIds) > 0 {
		count, roleErr := s.roleRepo.CountByIDs(ctx, req.RoleIds) // Use roleRepo
		if roleErr != nil {
			return nil, gerror.Wrap(roleErr, "校验角色ID有效性时出错")
		}
		if count != len(req.RoleIds) {
			return nil, gerror.NewCode(codes.CodeInvalidParameter, "包含无效的角色ID")
		}
	} else {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请至少选择一个角色")
	}
	// 校验主角色 ID 是否在角色列表中
	primaryRoleFound := false
	for _, rid := range req.RoleIds {
		if rid == req.PrimaryRoleId {
			primaryRoleFound = true
			break
		}
	}
	if !primaryRoleFound { // 使用 primaryRoleFound 进行判断
		return nil, gerror.NewCode(codes.CodePrimaryRoleNotInRoles)
	}
	// if len(req.PostIds) > 0 {
	// 	count, postErr := s.postRepo.CountByIDs(ctx, req.PostIds) // Use postRepo
	// 	if postErr != nil {
	// 		return nil, gerror.Wrap(postErr, "校验岗位ID有效性时出错")
	// 	}
	// 	if count != len(req.PostIds) {
	// 		return nil, gerror.NewCode(codes.CodeInvalidParameter, "包含无效的岗位ID")
	// 	}
	// }

	// 3. 校验上级 Pid
	var level int = 1
	var tree string = ",0," // 根节点的 tree
	if req.Pid != 0 {
		parentMember, parentErr := s.memberRepo.GetByID(ctx, req.Pid) // Use memberRepo
		if parentErr != nil {
			// Check if the error is specifically CodeMemberNotFound from the repo
			if gerror.Code(parentErr) == codes.CodeMemberNotFound {
				return nil, gerror.NewCode(codes.CodeInvalidParentMember)
			}
			return nil, gerror.Wrapf(parentErr, "查询上级用户失败 (ID: %d)", req.Pid) // Wrap other errors
		}
		// 检查父成员是否存在
		if parentMember == nil {
			return nil, gerror.NewCode(codes.CodeInvalidParentMember)
		}
		level = parentMember.Level + 1
		tree = parentMember.Tree // 获取父级的 tree
	}

	// 4. 密码处理 (Use AuthService)
	passwordHash, hashErr := s.authService.HashPassword(ctx, req.Password)
	if hashErr != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, hashErr, "密码哈希处理失败")
	}

	// 5. 准备数据
	memberDo := &do.AdminMember{}
	if convErr := gconv.Struct(req, memberDo); convErr != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, convErr, "结构体转换失败")
	}
	memberDo.PasswordHash = passwordHash
	memberDo.Salt = "none" // Provide a valid value for salt
	memberDo.Level = level
	// memberDo.RoleId = req.PrimaryRoleId // 将主角色ID存入 admin_member 表
	// Tree 字段将在事务中插入后更新

	// 6. 事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var txErr error
		// 6.1 添加用户基础信息 (Use Repository)
		// Tree is handled within the repository's Create or UpdateFields logic now
		newMemberId, txErr := s.memberRepo.Create(ctx, memberDo) // Pass memberDo directly
		if txErr != nil {
			// Create method should already wrap the error appropriately
			return txErr
		}

		// 6.2 更新 Tree (Use Repository UpdateFields)
		// BuildMemberTree logic is now internal to memberRepo or its helper
		newTree := memberLogic.BuildMemberTree(tree, newMemberId) // Use utility from memberLogic
		txErr = s.memberRepo.UpdateFields(ctx, tx, newMemberId, g.Map{"tree": newTree})
		if txErr != nil {
			return gerror.WrapCode(codes.CodeInternalError, txErr, "更新用户 Tree 失败") // Keep wrapping here for context
		}

		// // 6.3 更新角色关联 (Use Repository)
		// txErr = s.memberRoleRepo.UpdateMemberRoles(ctx, tx, newMemberId, req.RoleIds)
		// if txErr != nil {
		// 	return txErr // Repository method should wrap error
		// }

		// // // 6.4 更新岗位关联 (Use Repository)
		// txErr = s.memberPostRepo.UpdateMemberPosts(ctx, tx, newMemberId, req.PostIds)
		// if txErr != nil {
		// 	return txErr // Repository method should wrap error
		// }

		res = &v1.AddMemberRes{Id: newMemberId}
		return nil
	})

	return res, err
}

// EditMember 编辑用户
func (s *sSystemLogic) EditMember(ctx context.Context, req *v1.EditMemberReq) (res *v1.EditMemberRes, err error) {
	// 1. 校验用户是否存在 (Use Repository)
	memberEntity, err := s.memberRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, err // Repository method should handle NotFound appropriately
	}

	// 2. 校验唯一性 (排除自身) (Use Repository)
	if exists, _ := s.memberRepo.ExistsByEmail(ctx, req.Email, req.Id); exists {
		return nil, gerror.NewCode(codes.CodeEmailExists)
	}
	if exists, _ := s.memberRepo.ExistsByMobile(ctx, req.Mobile, req.Id); exists {
		return nil, gerror.NewCode(codes.CodeMobileExists)
	}
	if req.InviteCode != "" {
		if exists, _ := s.memberRepo.ExistsByInviteCode(ctx, req.InviteCode, req.Id); exists {
			return nil, gerror.NewCode(codes.CodeInviteCodeExists)
		}
	}

	// 3. 校验角色、岗位 ID 的有效性 (Use Repositories)
	// Removed department validation
	if len(req.RoleIds) > 0 {
		count, roleErr := s.roleRepo.CountByIDs(ctx, req.RoleIds) // Use roleRepo
		if roleErr != nil {
			return nil, gerror.Wrap(roleErr, "校验角色ID有效性时出错")
		}
		if count != len(req.RoleIds) {
			return nil, gerror.NewCode(codes.CodeInvalidParameter, "包含无效的角色ID")
		}
	} else {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请至少选择一个角色") // 编辑时也需要至少一个角色
	}
	// 校验主角色 ID 是否在角色列表中
	primaryRoleFound := false
	for _, rid := range req.RoleIds {
		if rid == req.PrimaryRoleId {
			primaryRoleFound = true
			break
		}
	}
	if !primaryRoleFound { // 使用 primaryRoleFound 进行判断
		return nil, gerror.NewCode(codes.CodePrimaryRoleNotInRoles)
	}
	// if len(req.PostIds) > 0 {
	// 	count, postErr := s.postRepo.CountByIDs(ctx, req.PostIds) // Use postRepo
	// 	if postErr != nil {
	// 		return nil, gerror.Wrap(postErr, "校验岗位ID有效性时出错")
	// 	}
	// 	if count != len(req.PostIds) {
	// 		return nil, gerror.NewCode(codes.CodeInvalidParameter, "包含无效的岗位ID")
	// 	}
	// }

	// 4. 准备基础信息更新数据
	updateData := g.Map{
		// Use field names directly as keys for g.Map
		"real_name": req.RealName,
		// "dept_id":     req.DeptId, // Removed dept_id
		"role_id":     req.PrimaryRoleId, // Update primary role ID
		"email":       req.Email,
		"mobile":      req.Mobile,
		"avatar":      req.Avatar,
		"invite_code": req.InviteCode,
		"remark":      req.Remark,
		"status":      req.Status,
		// Pid, Level, Tree are handled separately if changed
	}

	// 5. 处理上级 Pid 变更
	pidChanged := memberEntity.Pid != req.Pid
	var newLevel int
	var newMemberTree string
	var descendantsToUpdate []*entity.AdminMember // 存储需要更新的子孙节点
	var batchUpdateData map[int64]g.Map           // 存储批量更新的数据

	if pidChanged {
		// 5.1 校验不能将父级设置为自身或其子孙
		if req.Pid == req.Id {
			return nil, gerror.NewCode(codes.CodeCannotSetParentToChild)
		}
		// 查找当前用户的所有子孙 (Use Repository)
		descendants, findErr := s.memberRepo.FindDescendantsByTree(ctx, memberEntity.Tree)
		if findErr != nil {
			return nil, gerror.Wrapf(findErr, "查找用户子孙失败 (ID: %d)", req.Id)
		}
		for _, descendant := range descendants {
			if descendant.Id == req.Pid {
				return nil, gerror.NewCode(codes.CodeCannotSetParentToChild)
			}
		}
		descendantsToUpdate = descendants // 保存子孙节点供后续事务内更新

		// 5.2 获取新的父级信息
		var newParentTree string
		if req.Pid == 0 {
			newLevel = 1
			newParentTree = ",0,"
		} else {
			newParentMember, parentErr := s.memberRepo.GetByID(ctx, req.Pid) // Use memberRepo
			if parentErr != nil {
				if gerror.Code(parentErr) == codes.CodeMemberNotFound {
					return nil, gerror.NewCode(codes.CodeInvalidParentMember)
				}
				return nil, gerror.Wrapf(parentErr, "查询新上级用户失败 (ID: %d)", req.Pid)
			}
			newLevel = newParentMember.Level + 1
			newParentTree = newParentMember.Tree
		}
		// 5.3 计算当前用户新的 Level 和 Tree，并准备更新数据 (Use utility)
		newMemberTree = memberLogic.BuildMemberTree(newParentTree, req.Id) // Use utility
		updateData["pid"] = req.Pid
		updateData["level"] = newLevel
		updateData["tree"] = newMemberTree

		// 5.4 准备批量更新子孙节点的数据
		levelDiff := newLevel - memberEntity.Level
		oldMemberTreePrefix := memberEntity.Tree
		batchUpdateData = make(map[int64]g.Map)
		for _, descendant := range descendantsToUpdate {
			// 确保 Tree 字段以旧前缀开头
			if len(descendant.Tree) >= len(oldMemberTreePrefix) && descendant.Tree[:len(oldMemberTreePrefix)] == oldMemberTreePrefix {
				updatedTree := newMemberTree + descendant.Tree[len(oldMemberTreePrefix):]
				updatedLevel := descendant.Level + levelDiff
				batchUpdateData[descendant.Id] = g.Map{
					"tree":  updatedTree,
					"level": updatedLevel,
				}
			} else {
				// 记录异常情况，但不中断流程
				g.Log().Warningf(ctx, "子孙用户 Tree 格式异常，跳过更新 (ID: %d, Tree: %s, ExpectedPrefix: %s)", descendant.Id, descendant.Tree, oldMemberTreePrefix)
			}
		}
	}

	// 6. 事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var txErr error
		// 6.1 更新用户基础信息 (Use Repository UpdateFields)
		txErr = s.memberRepo.UpdateFields(ctx, tx, req.Id, updateData)
		if txErr != nil {
			// Repository method should wrap error
			return txErr
		}

		// 6.2 如果 Pid 变更，递归更新子孙的 Tree 和 Level (Use Repository)
		if pidChanged && len(batchUpdateData) > 0 {
			txErr = s.memberRepo.BatchUpdateLevelTree(ctx, tx, batchUpdateData)
			if txErr != nil {
				return txErr // Repository method should wrap error
			}
		}

		// // 6.3 更新角色关联 (Use Repository)
		// txErr = s.memberRoleRepo.UpdateMemberRoles(ctx, tx, req.Id, req.RoleIds)
		// if txErr != nil {
		// 	return txErr // Repository method should wrap error
		// }

		// // 6.4 更新岗位关联 (Use Repository)
		// txErr = s.memberPostRepo.UpdateMemberPosts(ctx, tx, req.Id, req.PostIds)
		// if txErr != nil {
		// 	return txErr // Repository method should wrap error
		// }

		return nil
	})

	if err != nil {
		return nil, err
	}

	res = &v1.EditMemberRes{} // 成功时返回空响应体
	return res, nil
}

// DeleteMember 删除用户
func (s *sSystemLogic) DeleteMember(ctx context.Context, req *v1.DeleteMemberReq) (res *v1.DeleteMemberRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要删除的用户")
	}

	// 事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var txErr error
		validIdsToDelete := make([]int64, 0, len(req.Ids)) // 存储可以被删除的用户ID

		for _, id := range req.Ids {
			// 1. 检查是否有下级 (Use Repository)
			count, countErr := s.memberRepo.GetDirectSubordinatesCount(ctx, id)
			if countErr != nil {
				// Repository method should wrap error, log and continue
				g.Log().Errorf(ctx, "检查用户 %d 是否有下级失败: %v", id, countErr)
				continue
			}
			if count > 0 {
				// 获取用户名以提示 (Use Repository)
				member, _ := s.memberRepo.GetByID(ctx, id) // Ignore error, best effort
				username := fmt.Sprintf("ID %d", id)
				if member != nil {
					username = member.Username
				}
				// 如果有下级，记录日志并跳过此用户，不中断整个事务
				g.Log().Warningf(ctx, "用户 [%s] (ID: %d) 存在下级，无法删除", username, id)
				// 可以考虑收集无法删除的用户信息，最后统一返回
				continue // 跳过此用户
			}

			// 如果没有下级，则添加到待删除列表
			validIdsToDelete = append(validIdsToDelete, id)

			// // 2. 删除角色关联 (Use Repository - Update with empty list)
			// // Note: UpdateMemberRoles handles deletion internally
			// txErr = s.memberRoleRepo.UpdateMemberRoles(ctx, tx, id, []int64{})
			// if txErr != nil {
			// 	// Repository method should wrap error, log and continue
			// 	g.Log().Errorf(ctx, "删除用户 %d 的角色关联失败: %v", id, txErr)
			// 	// Consider removing id from validIdsToDelete if this failure is critical
			// }

			// // 3. 删除岗位关联 (Use Repository - Update with empty list)
			// // Note: UpdateMemberPosts handles deletion internally
			// txErr = s.memberPostRepo.UpdateMemberPosts(ctx, tx, id, []int64{})
			// if txErr != nil {
			// 	// Repository method should wrap error, log and continue
			// 	g.Log().Errorf(ctx, "删除用户 %d 的岗位关联失败: %v", id, txErr)
			// 	// Consider removing id from validIdsToDelete if this failure is critical
			// }
		}

		// 4. 批量删除用户 (只删除没有下级的用户)
		if len(validIdsToDelete) > 0 {
			// 在事务内执行删除 (Use Repository)
			txErr = s.memberRepo.Delete(ctx, tx, validIdsToDelete)
			if txErr != nil {
				// Repository method should wrap error
				return txErr
			}
		} else if len(req.Ids) > 0 {
			// 如果原始请求中有 ID，但没有一个可以删除（例如都有下级），
			// 可以考虑返回一个特定的错误或信息给调用者。
			// 目前的逻辑是，如果循环中没有遇到致命错误，事务会成功提交，但可能没有用户被实际删除。
			// 可以根据业务需求调整这里的行为。例如，返回一个包含未删除用户信息的错误。
			g.Log().Infof(ctx, "没有用户可供删除 (请求 IDs: %v)", req.Ids)
		}

		// // Casbin: 清理与该用户相关的 g 规则
		// e := utilityCasbin.GetEnforcer()
		// for _, id := range validIdsToDelete { // 在事务内部使用 validIdsToDelete
		// 	userIdStr := gconv.String(id)
		// 	removed, casbinErr := e.RemoveFilteredGroupingPolicy(0, userIdStr, utilityCasbin.DefaultDomain)
		// 	if casbinErr != nil {
		// 		g.Log().Errorf(ctx, "删除用户 %s (ID: %d) 后清理 Casbin 规则失败: %v", userIdStr, id, casbinErr)
		// 		// 这里选择记录错误但不中断事务，因为用户记录本身已经删除
		// 	}
		// 	if removed {
		// 		g.Log().Debugf(ctx, "已清理用户 %s (ID: %d) 在域 %s 下的 Casbin 规则", userIdStr, id, utilityCasbin.DefaultDomain)
		// 	}
		// }

		return nil // 如果没有致命错误发生，返回 nil 提交事务
	})

	if err != nil {
		return nil, err
	}

	res = &v1.DeleteMemberRes{}
	return res, nil
}

// UpdateMemberStatus 更新用户状态
func (s *sSystemLogic) UpdateMemberStatus(ctx context.Context, req *v1.UpdateMemberStatusReq) (res *v1.UpdateMemberStatusRes, err error) {
	// 1. 校验用户是否存在 (Use Repository)
	_, err = s.memberRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, err // Repository method handles NotFound
	}

	// 2. 校验状态值
	if !consts.IsValidStatus(req.Status) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态值")
	}

	// 3. 执行更新 (Use Repository)
	err = s.memberRepo.UpdateStatus(ctx, req.Id, req.Status)
	if err != nil {
		return nil, err // Repository method wraps error
	}

	res = &v1.UpdateMemberStatusRes{}
	return res, nil
}

// ResetMemberPassword 重置用户密码
func (s *sSystemLogic) ResetMemberPassword(ctx context.Context, req *v1.ResetMemberPasswordReq) (res *v1.ResetMemberPasswordRes, err error) {
	// 1. 校验用户是否存在 (Use Repository)
	_, err = s.memberRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, err // Repository method handles NotFound
	}

	// 2. 生成新密码哈希 (Use AuthService)
	passwordHash, hashErr := s.authService.HashPassword(ctx, req.Password)
	if hashErr != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, hashErr, "密码哈希处理失败")
	}

	// 3. 执行更新 (Use Repository)
	err = s.memberRepo.UpdatePassword(ctx, req.Id, passwordHash)
	if err != nil {
		return nil, err // Repository method wraps error
	}

	res = &v1.ResetMemberPasswordRes{}
	return res, nil
}

// AssignRolesToUser 分配用户角色
func (s *sSystemLogic) AssignRolesToUser(ctx context.Context, req *v1.AssignRolesToUserReq) (res *v1.AssignRolesToUserRes, err error) {
	// // 1. 获取 Casbin Enforcer
	// e := utilityCasbin.GetEnforcer()

	// // 2. 将用户 ID 转换为字符串
	// userIdStr := gconv.String(req.Id)

	// // 3. 原子性操作: 先移除该用户在 DefaultDomain 下的所有现有角色映射 (g 规则)
	// // 参数 0 表示 user 是 g 规则的第一个参数
	// removed, err := e.RemoveFilteredGroupingPolicy(0, userIdStr, utilityCasbin.DefaultDomain)
	// if err != nil {
	// 	g.Log().Errorf(ctx, "移除用户 %s 在域 %s 下的现有角色映射失败: %v", userIdStr, utilityCasbin.DefaultDomain, err)
	// 	return nil, gerror.WrapCode(codes.CodeInternalError, err, "移除现有角色映射失败")
	// }
	// if removed {
	// 	g.Log().Debugf(ctx, "已移除用户 %s 在域 %s 下的现有角色映射", userIdStr, utilityCasbin.DefaultDomain)
	// }

	// // 4. 批量添加新的角色映射
	// if len(req.RoleKeys) > 0 {
	// 	rules := make([][]string, 0, len(req.RoleKeys))
	// 	for _, roleKey := range req.RoleKeys {
	// 		rules = append(rules, []string{userIdStr, roleKey, utilityCasbin.DefaultDomain})
	// 	}

	// 	added, err := e.AddGroupingPolicies(rules)
	// 	if err != nil {
	// 		g.Log().Errorf(ctx, "批量添加用户 %s 的角色映射失败: %v", userIdStr, err)
	// 		return nil, gerror.WrapCode(codes.CodeInternalError, err, "批量添加角色映射失败")
	// 	}
	// 	if added {
	// 		g.Log().Debugf(ctx, "已为用户 %s 批量添加角色映射: %v", userIdStr, req.RoleKeys)
	// 	}
	// } else {
	// 	g.Log().Debugf(ctx, "用户 %s 没有需要添加的角色映射", userIdStr)
	// }

	res = &v1.AssignRolesToUserRes{}
	return res, nil
}
