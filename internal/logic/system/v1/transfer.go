package v1

import (
	"context"
	// "math/big" // Removed unused import
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/model/entity"
	"strings"

	// "admin-api/internal/dao" // Removed direct DAO dependency
	// "admin-api/utility/amount" // Removed unused import
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// ListAdminTransfers 获取转账记录列表(后台，带代理和telegram信息)
func (s *sSystemLogic) ListAdminTransfers(ctx context.Context, req *v1.ListAdminTransfersReq) (res *v1.ListAdminTransfersRes, err error) {
	// 初始化返回结果，避免空指针
	// Initialize response struct directly
	res = &v1.ListAdminTransfersRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.TransferAdminInfoItem, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 添加筛选条件
	if req.SenderUserId > 0 {
		condition["t.sender_user_id"] = req.SenderUserId
	}
	// Use SenderUsername to search in sender.name
	if req.SenderUsername != "" {
		condition["sender.name LIKE"] = "%" + req.SenderUsername + "%"
	}
	if req.ReceiverUserId > 0 {
		condition["t.receiver_user_id"] = req.ReceiverUserId
	}
	// Use ReceiverUsername to search in receiver.name
	if req.ReceiverUsername != "" {
		condition["receiver.name LIKE"] = "%" + req.ReceiverUsername + "%"
	}
	if req.TokenId > 0 {
		condition["t.token_id"] = req.TokenId
	}
	if req.TokenSymbol != "" {
		condition["token.symbol LIKE"] = "%" + req.TokenSymbol + "%"
	}
	// Add TransferId filter
	if req.TransferId > 0 {
		condition["t.transfer_id"] = req.TransferId
	}
	// Add Status filter
	if req.Status > 0 {
		condition["t.status"] = req.Status
	}
	// Add SenderAccount filter
	if req.SenderAccount != "" {
		condition["sender.account LIKE"] = "%" + req.SenderAccount + "%"
	}
	// Add ReceiverAccount filter
	if req.ReceiverAccount != "" {
		condition["receiver.account LIKE"] = "%" + req.ReceiverAccount + "%"
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["t.created_at >="] = dateRange[0] + " 00:00:00"
			condition["t.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 如果是导出，查询所有数据
	if req.Export == 1 {
		// 导出时不进行分页限制，但可能会限制最大导出数量
		exportReq := *req
		exportReq.Page = 1
		exportReq.PageSize = 9999999

		list, _, err := s.transferRepo.ListAdminWithAgentInfo(ctx, &exportReq)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询转账记录失败")
		}

		// 转换为导出格式
		// The 'list' already contains *v1.TransferAdminInfoItem with all new fields from the repo layer.
		// We can directly use 'list' for exportData preparation.

		// 定义Excel表头
		excelTags := []string{
			"转账ID", "发送方用户ID", "发送方用户名", "接收方用户ID", "接收方用户名",
			"代币ID", "代币符号", "转账金额", "备注", "创建时间",
			"消息ID", "聊天ID", "状态", "冻结ID", "过期时间", "更新时间",
			"是否需密码", "Key", "转账币种", "消息内容", "内联消息ID",
			"发送方交易ID", "接收方交易ID",
			"发送方一级代理", "发送方二级代理", "发送方三级代理", "发送方Telegram ID", "发送方Telegram用户名", "发送方真实姓名",
			"接收方一级代理", "接收方二级代理", "接收方三级代理", "接收方Telegram ID", "接收方Telegram用户名", "接收方真实姓名",
		}

		// 准备导出数据
		exportData := make([]interface{}, len(list)) // Use 'list' directly
		for i, v := range list {                     // v is already *v1.TransferAdminInfoItem
			createdAt := ""
			if v.CreatedAt != nil {
				createdAt = v.CreatedAt.String()
			}
			expiresAt := ""
			if v.ExpiresAt != nil {
				expiresAt = v.ExpiresAt.String()
			}
			updatedAt := ""
			if v.UpdatedAt != nil {
				updatedAt = v.UpdatedAt.String()
			}

			exportData[i] = struct {
				TransferId               int64         `json:"transferId"`
				SenderUserId             int64         `json:"senderUserId"`
				SenderUsername           string        `json:"senderUsername"`
				ReceiverUserId           int64         `json:"receiverUserId"`
				ReceiverUsername         string        `json:"receiverUsername"`
				TokenId                  int           `json:"tokenId"`
				TokenSymbol              string        `json:"tokenSymbol"`
				AmountStr                string        `json:"amountStr"`
				Memo                     string        `json:"memo"`
				CreatedAt                string        `json:"createdAt"`
				MessageId                int64         `json:"messageId"`
				ChatId                   int64         `json:"chatId"`
				Status                   string        `json:"status"`
				HoldId                   string        `json:"holdId"`
				ExpiresAt                string        `json:"expiresAt"`
				UpdatedAt                string        `json:"updatedAt"`
				NeedPass                 bool          `json:"needPass"`
				Key                      string        `json:"key"`
				TransferSymbol           string        `json:"transferSymbol"`
				Message                  string        `json:"message"`
				InlineMessageId          string        `json:"inlineMessageId"`
				SenderTxId               int64         `json:"senderTxId"`
				ReceiverTxId             int64         `json:"receiverTxId"`
				FirstAgentName           string        `json:"firstAgentName"`
				SecondAgentName          string        `json:"secondAgentName"`
				ThirdAgentName           string        `json:"thirdAgentName"`
				TelegramId               string        `json:"telegramId"`
				TelegramUsername         string        `json:"telegramUsername"`
				FirstName                string        `json:"firstName"`
				ReceiverFirstAgentName   string        `json:"receiverFirstAgentName"`
				ReceiverSecondAgentName  string        `json:"receiverSecondAgentName"`
				ReceiverThirdAgentName   string        `json:"receiverThirdAgentName"`
				ReceiverTelegramId       string        `json:"receiverTelegramId"`
				ReceiverTelegramUsername string        `json:"receiverTelegramUsername"`
				ReceiverFirstName        string        `json:"receiverFirstName"`
				SenderUser               *entity.Users `json:"senderUser"`
				ReceiverUser             *entity.Users `json:"receiverUser"`
			}{
				TransferId:               v.TransferId,
				SenderUserId:             v.SenderUserId,
				SenderUsername:           v.SenderUsername,
				ReceiverUserId:           v.ReceiverUserId,
				ReceiverUsername:         v.ReceiverUsername,
				TokenId:                  v.TokenId,
				TokenSymbol:              v.TokenSymbol,
				AmountStr:                v.AmountStr,
				Memo:                     v.Memo,
				CreatedAt:                createdAt,
				MessageId:                v.MessageId,
				ChatId:                   v.ChatId,
				Status:                   v.Status,
				HoldId:                   v.HoldId,
				ExpiresAt:                expiresAt,
				UpdatedAt:                updatedAt,
				NeedPass:                 v.NeedPass,
				Key:                      v.Key,
				TransferSymbol:           v.TransferSymbol,
				Message:                  v.Message,
				InlineMessageId:          v.InlineMessageId,
				SenderTxId:               v.SenderTxId,
				ReceiverTxId:             v.ReceiverTxId,
				FirstAgentName:           v.FirstAgentName,
				SecondAgentName:          v.SecondAgentName,
				ThirdAgentName:           v.ThirdAgentName,
				TelegramId:               v.TelegramId,
				TelegramUsername:         v.TelegramUsername,
				FirstName:                v.FirstName,
				ReceiverFirstAgentName:   v.ReceiverFirstAgentName,
				ReceiverSecondAgentName:  v.ReceiverSecondAgentName,
				ReceiverThirdAgentName:   v.ReceiverThirdAgentName,
				ReceiverTelegramId:       v.ReceiverTelegramId,
				ReceiverTelegramUsername: v.ReceiverTelegramUsername,
				ReceiverFirstName:        v.ReceiverFirstName,
			}
		}

		// 调用Excel导出工具
		return nil, excel.ExportByStructs(ctx, excelTags, exportData, "转账记录", "转账记录列表")
	}

	// 查询分页数据
	list, total, err := s.transferRepo.ListAdminWithAgentInfo(ctx, req)
	if err != nil {
		return nil, gerror.Wrap(err, "查询转账记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	if req.PageSize > 0 {
		res.Page.TotalPage = (total + req.PageSize - 1) / req.PageSize
	}

	// The 'list' from s.transferRepo.ListAdmin already contains []*v1.TransferAdminInfoItem
	// with all necessary fields correctly mapped.
	// So, we can directly assign it to res.Data.
	res.Data = list

	return res, nil
}

// GetAdminTransferDetail 获取转账记录详情(后台)
func (s *sSystemLogic) GetAdminTransferDetail(ctx context.Context, req *v1.GetAdminTransferDetailReq) (res *v1.GetAdminTransferDetailRes, err error) {
	// 初始化返回结果
	// Initialize response struct directly
	res = &v1.GetAdminTransferDetailRes{}

	// 查询转账记录详情
	// 查询转账记录详情 (使用 Repository)
	transfer, err := s.transferRepo.GetAdminDetail(ctx, req.TransferId)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询转账详情失败, ID: %d", req.TransferId)
	}
	if transfer == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "转账记录不存在")
	}

	// 转换为API响应格式 (直接使用 repository 返回的完整数据)
	res.TransferAdminInfoItem = *transfer

	return res, nil
}
