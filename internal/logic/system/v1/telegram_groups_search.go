package v1

import (
	"context"
	"fmt"
	"strconv"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"

	"github.com/gogf/gf/v2/errors/gerror"
)

// SearchTelegramGroups 搜索Telegram群组
func (s *sSystemLogic) SearchTelegramGroups(ctx context.Context, req *v1.SearchTelegramGroupsReq) (res *v1.SearchTelegramGroupsRes, err error) {
	var (
		m = dao.TelegramGroups.Ctx(ctx)
	)

	// 构建搜索条件 - 使用 AND + LIKE 查询 title 和 chat_id 字段
	// 支持搜索群组标题和聊天ID
	searchCondition := m.Where("1=1") // 基础条件

	if req.Q != "" {
		// 检查搜索词是否为数字（可能是chat_id）
		if chatId, parseErr := strconv.ParseInt(req.Q, 10, 64); parseErr == nil {
			// 如果是数字，同时搜索 title 和 chat_id
			searchCondition = searchCondition.Where(
				"title LIKE ? OR chat_id = ?",
				"%"+req.Q+"%",
				chatId,
			)
		} else {
			// 如果不是数字，只搜索 title
			searchCondition = searchCondition.WhereLike("title", "%"+req.Q+"%")
		}
	}

	// 只返回有效的群组记录
	searchCondition = searchCondition.Where("title IS NOT NULL AND title != ''")

	// 获取总数
	total, err := searchCondition.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "count telegram groups failed")
	}

	// 获取分页数据
	var groups []struct {
		Id         uint64 `json:"id"`
		ChatId     int64  `json:"chat_id"`
		Title      string `json:"title"`
		BotIsAdmin int    `json:"bot_is_admin"`
	}

	err = searchCondition.Fields("id, chat_id, title, bot_is_admin").
		Page(req.Page, req.Limit).
		OrderAsc("title").
		Scan(&groups)
	if err != nil {
		return nil, gerror.Wrap(err, "get telegram groups list failed")
	}

	// 转换为响应结构
	data := make([]*v1.TelegramGroupSearchItem, len(groups))
	for i, group := range groups {
		// 格式化显示文本
		displayText := fmt.Sprintf("%s (ID: %d)", group.Title, group.ChatId)
		if group.BotIsAdmin == 1 {
			displayText += " [管理员]"
		}

		data[i] = &v1.TelegramGroupSearchItem{
			Id:          group.Id,
			ChatId:      group.ChatId,
			Title:       group.Title,
			BotIsAdmin:  group.BotIsAdmin == 1,
			DisplayText: displayText,
		}
	}

	res = &v1.SearchTelegramGroupsRes{
		Data: data,
		Pagination: v1.PaginationInfo{
			Page:    req.Page,
			Limit:   req.Limit,
			Total:   total,
			HasMore: req.Page*req.Limit < total,
		},
	}

	return res, nil
}
