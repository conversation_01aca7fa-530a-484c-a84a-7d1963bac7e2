package v1

import (
	"context"
	"fmt"
	"strings"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
)

// GetGameRngBetDetailsList 获取RNG/FISH游戏投注详情列表
func (s *sSystemLogic) GetGameRngBetDetailsList(ctx context.Context, req *v1.GetGameRngBetDetailsListReq) (res *v1.GetGameRngBetDetailsListRes, err error) {
	// 使用原生SQL查询以支持多表JOIN
	db := g.DB()
	
	// 构建基础查询
	query := `
		SELECT 
			grbd.*,
			uba.telegram_id,
			uba.telegram_username,
			uba.first_name,
			t.username as tenant_username
		FROM game_rng_bet_details grbd
		LEFT JOIN user_backup_accounts uba ON grbd.user_id = uba.user_id AND uba.is_master = 1 AND uba.deleted_at IS NULL
		LEFT JOIN tenants t ON grbd.tenant_id = t.tenant_id AND t.deleted_at IS NULL
		WHERE 1=1
	`
	
	// 构建WHERE条件和参数
	var conditions []string
	var params []interface{}
	
	// 原有条件
	if req.Username != "" {
		conditions = append(conditions, "grbd.username LIKE ?")
		params = append(params, "%"+req.Username+"%")
	}
	if req.GameCode != "" {
		conditions = append(conditions, "grbd.game_code = ?")
		params = append(params, req.GameCode)
	}
	if req.GameName != "" {
		conditions = append(conditions, "grbd.game_name LIKE ?")
		params = append(params, "%"+req.GameName+"%")
	}
	if req.GameCategory != "" {
		conditions = append(conditions, "grbd.game_category = ?")
		params = append(params, req.GameCategory)
	}
	if req.ProductType != nil {
		conditions = append(conditions, "grbd.product_type = ?")
		params = append(params, *req.ProductType)
	}
	if req.BetOrderNo != "" {
		conditions = append(conditions, "grbd.bet_order_no = ?")
		params = append(params, req.BetOrderNo)
	}
	if req.Currency != "" {
		conditions = append(conditions, "grbd.currency = ?")
		params = append(params, req.Currency)
	}
	
	// 时间范围过滤
	if req.BetTimeStart != nil {
		conditions = append(conditions, "grbd.bet_time >= ?")
		params = append(params, req.BetTimeStart)
	}
	if req.BetTimeEnd != nil {
		conditions = append(conditions, "grbd.bet_time <= ?")
		params = append(params, req.BetTimeEnd)
	}
	if req.TransactionTimeStart != nil {
		conditions = append(conditions, "grbd.transaction_time >= ?")
		params = append(params, req.TransactionTimeStart)
	}
	if req.TransactionTimeEnd != nil {
		conditions = append(conditions, "grbd.transaction_time <= ?")
		params = append(params, req.TransactionTimeEnd)
	}
	
	// 金额范围过滤
	if req.MinBetAmount != nil {
		conditions = append(conditions, "grbd.bet_amount >= ?")
		params = append(params, *req.MinBetAmount)
	}
	if req.MaxBetAmount != nil {
		conditions = append(conditions, "grbd.bet_amount <= ?")
		params = append(params, *req.MaxBetAmount)
	}
	if req.MinWinAmount != nil {
		conditions = append(conditions, "grbd.win_amount >= ?")
		params = append(params, *req.MinWinAmount)
	}
	if req.MaxWinAmount != nil {
		conditions = append(conditions, "grbd.win_amount <= ?")
		params = append(params, *req.MaxWinAmount)
	}
	
	// 输赢类型过滤
	if req.WinLossType != "" && req.WinLossType != "all" {
		switch req.WinLossType {
		case "win":
			conditions = append(conditions, "grbd.net_pnl > 0")
		case "loss":
			conditions = append(conditions, "grbd.net_pnl < 0")
		}
	}
	
	// 关键词搜索
	if req.Keyword != "" {
		conditions = append(conditions, "(grbd.username LIKE ? OR grbd.game_name LIKE ? OR grbd.bet_order_no LIKE ?)")
		params = append(params, "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	
	// 新增Telegram字段过滤
	if req.TelegramId != nil {
		conditions = append(conditions, "uba.telegram_id = ?")
		params = append(params, *req.TelegramId)
	}
	if req.TelegramUsername != "" {
		conditions = append(conditions, "uba.telegram_username LIKE ?")
		params = append(params, "%"+req.TelegramUsername+"%")
	}
	if req.FirstName != "" {
		conditions = append(conditions, "uba.first_name LIKE ?")
		params = append(params, "%"+req.FirstName+"%")
	}
	
	// 租户用户名过滤
	if req.TenantUsername != "" {
		conditions = append(conditions, "t.username LIKE ?")
		params = append(params, "%"+req.TenantUsername+"%")
	}
	
	// 添加WHERE条件
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}
	
	// 获取总数
	countQuery := strings.Replace(query, "SELECT grbd.*, uba.telegram_id, uba.telegram_username, uba.first_name, t.username as tenant_username", "SELECT COUNT(*) as total", 1)
	var total int
	result, err := db.Ctx(ctx).Raw(countQuery, params...).Value()
	if err != nil {
		return nil, gerror.Wrap(err, "count game RNG bet details failed")
	}
	if result != nil {
		total = gconv.Int(result)
	}
	
	// 添加排序和分页
	query += " ORDER BY grbd.bet_time DESC, grbd.id DESC"
	query += " LIMIT ? OFFSET ?"
	params = append(params, req.PageSize, (req.Page-1)*req.PageSize)
	
	// 执行查询
	type GameRngBetDetailsWithJoin struct {
		entity.GameRngBetDetails
		TelegramId       *int64  `json:"telegram_id"`
		TelegramUsername *string `json:"telegram_username"`
		FirstName        *string `json:"first_name"`
		TenantUsername   *string `json:"tenant_username"`
	}
	
	var list []GameRngBetDetailsWithJoin
	err = db.Ctx(ctx).Raw(query, params...).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "get game RNG bet details list failed")
	}
	
	// 转换为响应结构
	listItems := make([]*v1.GameRngBetDetailsListItem, len(list))
	for i, item := range list {
		listItems[i] = &v1.GameRngBetDetailsListItem{
			Id:               item.Id,
			Username:         item.Username,
			BetAmount:        item.BetAmount,
			ValidBetAmount:   item.ValidBetAmount,
			WinAmount:        item.WinAmount,
			NetPnl:           item.NetPnl,
			Currency:         item.Currency,
			GameCode:         item.GameCode,
			GameName:         item.GameName,
			ProductType:      item.ProductType,
			GameCategory:     item.GameCategory,
			BetOrderNo:       item.BetOrderNo,
			SessionId:        item.SessionId,
			BetTime:          item.BetTime,
			TransactionTime:  item.TransactionTime,
			ApiStatus:        item.ApiStatus,
			ApiErrorDesc:     item.ApiErrorDesc,
			CreatedAt:        item.CreatedAt,
			UpdatedAt:        item.UpdatedAt,
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
			TenantUsername:   item.TenantUsername,
		}
	}

	// 计算统计信息（使用简化版本，因为我们已经有了数据）
	var stats *v1.GameRngBetDetailsStats
	if len(list) > 0 {
		stats = &v1.GameRngBetDetailsStats{
			TotalBetCount: total,
		}
		// 可以在这里添加更多统计计算
	}

	res = &v1.GetGameRngBetDetailsListRes{
		Total: total,
		List:  listItems,
		Stats: stats,
	}
	return res, nil
}

// GetGameRngBetDetailsDetail 获取RNG/FISH游戏投注详情详情
func (s *sSystemLogic) GetGameRngBetDetailsDetail(ctx context.Context, req *v1.GetGameRngBetDetailsDetailReq) (res *v1.GetGameRngBetDetailsDetailRes, err error) {
	// 使用原生SQL查询以支持多表JOIN
	db := g.DB()
	
	query := `
		SELECT 
			grbd.*,
			uba.telegram_id,
			uba.telegram_username,
			uba.first_name,
			t.username as tenant_username
		FROM game_rng_bet_details grbd
		LEFT JOIN user_backup_accounts uba ON grbd.user_id = uba.user_id AND uba.is_master = 1 AND uba.deleted_at IS NULL
		LEFT JOIN tenants t ON grbd.tenant_id = t.tenant_id AND t.deleted_at IS NULL
		WHERE grbd.id = ?
	`
	
	type GameRngBetDetailsWithJoin struct {
		entity.GameRngBetDetails
		TelegramId       *int64  `json:"telegram_id"`
		TelegramUsername *string `json:"telegram_username"`
		FirstName        *string `json:"first_name"`
		TenantUsername   *string `json:"tenant_username"`
	}
	
	var betDetails GameRngBetDetailsWithJoin
	err = db.Ctx(ctx).Raw(query, req.Id).Scan(&betDetails)
	if err != nil {
		return nil, gerror.Wrap(err, "get game RNG bet details failed")
	}
	if betDetails.Id == 0 {
		return nil, gerror.New("game RNG bet details not found")
	}

	// 解析附加详情JSON
	var additionalDetails interface{}
	if betDetails.AdditionalDetails != nil {
		additionalDetails = betDetails.AdditionalDetails.Interface()
	}

	res = &v1.GetGameRngBetDetailsDetailRes{
		GameRngBetDetailsDetailItem: &v1.GameRngBetDetailsDetailItem{
			Id:                betDetails.Id,
			Username:          betDetails.Username,
			BetAmount:         betDetails.BetAmount,
			ValidBetAmount:    betDetails.ValidBetAmount,
			WinAmount:         betDetails.WinAmount,
			NetPnl:            betDetails.NetPnl,
			Currency:          betDetails.Currency,
			GameCode:          betDetails.GameCode,
			GameName:          betDetails.GameName,
			ProductType:       betDetails.ProductType,
			GameCategory:      betDetails.GameCategory,
			BetOrderNo:        betDetails.BetOrderNo,
			SessionId:         betDetails.SessionId,
			BetTime:           betDetails.BetTime,
			TransactionTime:   betDetails.TransactionTime,
			AdditionalDetails: additionalDetails,
			ApiStatus:         betDetails.ApiStatus,
			ApiErrorDesc:      betDetails.ApiErrorDesc,
			CreatedAt:         betDetails.CreatedAt,
			UpdatedAt:         betDetails.UpdatedAt,
			TelegramId:        betDetails.TelegramId,
			TelegramUsername:  betDetails.TelegramUsername,
			FirstName:         betDetails.FirstName,
			TenantUsername:    betDetails.TenantUsername,
		},
	}
	return res, nil
}

// GetGameRngBetDetailsStats 获取RNG/FISH游戏投注详情统计
func (s *sSystemLogic) GetGameRngBetDetailsStats(ctx context.Context, req *v1.GetGameRngBetDetailsStatsReq) (res *v1.GetGameRngBetDetailsStatsRes, err error) {
	var m = dao.GameRngBetDetails.Ctx(ctx)

	// 构建查询条件（与列表查询相同的过滤逻辑）
	if req.Username != "" {
		m = m.Where("username", req.Username)
	}
	if req.GameCode != "" {
		m = m.Where("game_code", req.GameCode)
	}
	if req.GameCategory != "" {
		m = m.Where("game_category", req.GameCategory)
	}
	if req.ProductType != nil {
		m = m.Where("product_type", *req.ProductType)
	}
	if req.Currency != "" {
		m = m.Where("currency", req.Currency)
	}

	// 时间范围过滤
	if req.BetTimeStart != nil {
		m = m.WhereGTE("bet_time", req.BetTimeStart)
	}
	if req.BetTimeEnd != nil {
		m = m.WhereLTE("bet_time", req.BetTimeEnd)
	}
	if req.TransactionTimeStart != nil {
		m = m.WhereGTE("transaction_time", req.TransactionTimeStart)
	}
	if req.TransactionTimeEnd != nil {
		m = m.WhereLTE("transaction_time", req.TransactionTimeEnd)
	}

	// 计算整体统计
	overall, err := s.calculateGameRngBetDetailsStats(ctx, m)
	if err != nil {
		return nil, gerror.Wrap(err, "calculate overall stats failed")
	}

	// 计算分组统计
	groupData, err := s.calculateGameRngBetDetailsGroupStats(ctx, m, req.GroupBy)
	if err != nil {
		return nil, gerror.Wrap(err, "calculate group stats failed")
	}

	res = &v1.GetGameRngBetDetailsStatsRes{
		Overall:   overall,
		GroupData: groupData,
	}
	return res, nil
}

// ExportGameRngBetDetails 导出RNG/FISH游戏投注详情
func (s *sSystemLogic) ExportGameRngBetDetails(ctx context.Context, req *v1.ExportGameRngBetDetailsReq) (res *v1.ExportGameRngBetDetailsRes, err error) {
	var (
		m    = dao.GameRngBetDetails.Ctx(ctx)
		list []*entity.GameRngBetDetails
	)

	// 构建查询条件（与列表查询相同的过滤逻辑）
	if req.Username != "" {
		m = m.Where("username", req.Username)
	}
	if req.GameCode != "" {
		m = m.Where("game_code", req.GameCode)
	}
	if req.GameName != "" {
		m = m.WhereLike("game_name", "%"+req.GameName+"%")
	}
	if req.GameCategory != "" {
		m = m.Where("game_category", req.GameCategory)
	}
	if req.ProductType != nil {
		m = m.Where("product_type", *req.ProductType)
	}
	if req.BetOrderNo != "" {
		m = m.Where("bet_order_no", req.BetOrderNo)
	}
	if req.Currency != "" {
		m = m.Where("currency", req.Currency)
	}

	// 时间范围过滤
	if req.BetTimeStart != nil {
		m = m.WhereGTE("bet_time", req.BetTimeStart)
	}
	if req.BetTimeEnd != nil {
		m = m.WhereLTE("bet_time", req.BetTimeEnd)
	}
	if req.TransactionTimeStart != nil {
		m = m.WhereGTE("transaction_time", req.TransactionTimeStart)
	}
	if req.TransactionTimeEnd != nil {
		m = m.WhereLTE("transaction_time", req.TransactionTimeEnd)
	}

	// 金额范围过滤
	if req.MinBetAmount != nil {
		m = m.WhereGTE("bet_amount", *req.MinBetAmount)
	}
	if req.MaxBetAmount != nil {
		m = m.WhereLTE("bet_amount", *req.MaxBetAmount)
	}
	if req.MinWinAmount != nil {
		m = m.WhereGTE("win_amount", *req.MinWinAmount)
	}
	if req.MaxWinAmount != nil {
		m = m.WhereLTE("win_amount", *req.MaxWinAmount)
	}

	// 输赢类型过滤
	if req.WinLossType != "" && req.WinLossType != "all" {
		switch req.WinLossType {
		case "win":
			m = m.WhereGT("net_pnl", 0)
		case "loss":
			m = m.WhereLT("net_pnl", 0)
		}
	}

	// 关键词搜索
	if req.Keyword != "" {
		m = m.Where(g.Map{
			"username LIKE ? OR game_name LIKE ? OR bet_order_no LIKE ?": []interface{}{
				"%" + req.Keyword + "%",
				"%" + req.Keyword + "%",
				"%" + req.Keyword + "%",
			},
		})
	}

	// 获取导出数据（限制最大导出数量）
	err = m.Limit(10000). // 限制最大导出10000条
		OrderDesc("bet_time").
		OrderDesc("id").
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "get export data failed")
	}

	if len(list) == 0 {
		return nil, gerror.New("no data to export")
	}

	// 生成导出文件
	fileName := fmt.Sprintf("game_rng_bet_details_%s.%s", 
		gtime.Now().Format("20060102_150405"), req.ExportFormat)
	
	// TODO: 实现实际的文件生成逻辑
	// 这里需要根据实际需求实现Excel/CSV文件生成
	fileUrl := fmt.Sprintf("/exports/%s", fileName)

	res = &v1.ExportGameRngBetDetailsRes{
		FileUrl:     fileUrl,
		FileName:    fileName,
		RecordCount: len(list),
	}

	// 记录导出操作日志
	g.Log().Infof(ctx, "exported game RNG bet details: records=%d, format=%s, file=%s", 
		len(list), req.ExportFormat, fileName)

	return res, nil
}

// calculateGameRngBetDetailsStats 计算投注详情统计信息
func (s *sSystemLogic) calculateGameRngBetDetailsStats(ctx context.Context, m *gdb.Model) (*v1.GameRngBetDetailsStats, error) {
	type StatsResult struct {
		TotalBetAmount     decimal.Decimal `json:"total_bet_amount"`
		TotalValidBetAmount decimal.Decimal `json:"total_valid_bet_amount"`
		TotalWinAmount     decimal.Decimal `json:"total_win_amount"`
		TotalNetPnl        decimal.Decimal `json:"total_net_pnl"`
		TotalBetCount      int             `json:"total_bet_count"`
		WinBetCount        int             `json:"win_bet_count"`
		LossBetCount       int             `json:"loss_bet_count"`
	}

	var result StatsResult
	err := m.Fields(
		"SUM(bet_amount) as total_bet_amount",
		"SUM(valid_bet_amount) as total_valid_bet_amount",
		"SUM(win_amount) as total_win_amount",
		"SUM(net_pnl) as total_net_pnl",
		"COUNT(*) as total_bet_count",
		"SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as win_bet_count",
		"SUM(CASE WHEN net_pnl < 0 THEN 1 ELSE 0 END) as loss_bet_count",
	).Scan(&result)
	if err != nil {
		return nil, err
	}

	// 计算胜率
	var winRate decimal.Decimal
	if result.TotalBetCount > 0 {
		winRate = decimal.NewFromInt(int64(result.WinBetCount)).
			Div(decimal.NewFromInt(int64(result.TotalBetCount))).
			Mul(decimal.NewFromInt(100))
	}

	return &v1.GameRngBetDetailsStats{
		TotalBetAmount:      result.TotalBetAmount,
		TotalValidBetAmount: result.TotalValidBetAmount,
		TotalWinAmount:      result.TotalWinAmount,
		TotalNetPnl:         result.TotalNetPnl,
		TotalBetCount:       result.TotalBetCount,
		WinBetCount:         result.WinBetCount,
		LossBetCount:        result.LossBetCount,
		WinRate:             winRate,
	}, nil
}

// calculateGameRngBetDetailsGroupStats 计算分组统计信息
func (s *sSystemLogic) calculateGameRngBetDetailsGroupStats(ctx context.Context, m *gdb.Model, groupBy string) ([]*v1.GameRngBetDetailsGroupStats, error) {
	var groupField, groupName string
	
	switch groupBy {
	case "game":
		groupField = "game_code"
		groupName = "game_name"
	case "user":
		groupField = "username"
		groupName = "username"
	case "date":
		groupField = "DATE(bet_time)"
		groupName = "DATE(bet_time)"
	case "currency":
		groupField = "currency"
		groupName = "currency"
	default:
		groupField = "game_code"
		groupName = "game_name"
	}

	type GroupStatsResult struct {
		GroupKey            string          `json:"group_key"`
		GroupName           string          `json:"group_name"`
		TotalBetAmount      decimal.Decimal `json:"total_bet_amount"`
		TotalValidBetAmount decimal.Decimal `json:"total_valid_bet_amount"`
		TotalWinAmount      decimal.Decimal `json:"total_win_amount"`
		TotalNetPnl         decimal.Decimal `json:"total_net_pnl"`
		TotalBetCount       int             `json:"total_bet_count"`
		WinBetCount         int             `json:"win_bet_count"`
		LossBetCount        int             `json:"loss_bet_count"`
	}

	var results []GroupStatsResult
	err := m.Fields(
		fmt.Sprintf("%s as group_key", groupField),
		fmt.Sprintf("MAX(%s) as group_name", groupName),
		"SUM(bet_amount) as total_bet_amount",
		"SUM(valid_bet_amount) as total_valid_bet_amount",
		"SUM(win_amount) as total_win_amount",
		"SUM(net_pnl) as total_net_pnl",
		"COUNT(*) as total_bet_count",
		"SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as win_bet_count",
		"SUM(CASE WHEN net_pnl < 0 THEN 1 ELSE 0 END) as loss_bet_count",
	).Group(groupField).
		OrderDesc("total_bet_amount").
		Limit(20). // 限制返回前20个分组
		Scan(&results)
	if err != nil {
		return nil, err
	}

	groupStats := make([]*v1.GameRngBetDetailsGroupStats, len(results))
	for i, result := range results {
		// 计算胜率
		var winRate decimal.Decimal
		if result.TotalBetCount > 0 {
			winRate = decimal.NewFromInt(int64(result.WinBetCount)).
				Div(decimal.NewFromInt(int64(result.TotalBetCount))).
				Mul(decimal.NewFromInt(100))
		}

		groupStats[i] = &v1.GameRngBetDetailsGroupStats{
			GroupKey:            result.GroupKey,
			GroupName:           result.GroupName,
			TotalBetAmount:      result.TotalBetAmount,
			TotalValidBetAmount: result.TotalValidBetAmount,
			TotalWinAmount:      result.TotalWinAmount,
			TotalNetPnl:         result.TotalNetPnl,
			TotalBetCount:       result.TotalBetCount,
			WinBetCount:         result.WinBetCount,
			LossBetCount:        result.LossBetCount,
			WinRate:             winRate,
		}
	}

	return groupStats, nil
}