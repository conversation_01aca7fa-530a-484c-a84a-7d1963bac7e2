package v1

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	addressService "admin-api/internal/service/system/address"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// GetAddressStatistics 获取地址统计信息
func (s *sSystemLogic) GetAddressStatistics(ctx context.Context, req *v1.GetAddressStatisticsReq) (res *v1.GetAddressStatisticsRes, err error) {
	// 初始化返回结果，避免空指针
	res = &v1.GetAddressStatisticsRes{
		Data: make([]*v1.AddressStatisticsItem, 0),
	}

	// 获取地址统计信息
	statistics, err := s.addressRepo.GetStatistics(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取地址统计信息失败")
	}

	// 设置返回数据
	res.Data = statistics

	return res, nil
}

// ImportAddresses 导入地址数据
func (s *sSystemLogic) ImportAddresses(ctx context.Context, req *v1.ImportAddressesReq) (res *v1.ImportAddressesRes, err error) {
	// 初始化返回结果
	res = &v1.ImportAddressesRes{}

	// 检查文件是否存在
	if req.File == nil {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请上传CSV文件")
	}

	// 检查文件类型
	filename := req.File.Filename
	if !strings.HasSuffix(strings.ToLower(filename), ".csv") {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "只支持CSV文件格式")
	}

	// 创建导入任务
	taskId, err := s.addressRepo.CreateImportTask(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "创建导入任务失败")
	}

	// 设置任务ID
	res.TaskId = taskId

	// 在后台处理文件导入
	go s.processAddressImport(taskId, req.File)

	return res, nil
}

// processAddressImport 处理地址导入
func (s *sSystemLogic) processAddressImport(taskId string, file *ghttp.UploadFile) {
	// 创建一个新的上下文，避免请求结束后上下文被取消
	newCtx := context.Background()

	// 打开文件
	f, err := file.Open()
	if err != nil {
		s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, 0, "打开文件失败: "+err.Error())
		return
	}
	defer f.Close()

	// 更新任务状态为验证中
	err = s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusValidating, 0, 0, 0, "")
	if err != nil {
		g.Log().Error(newCtx, "更新导入任务状态失败", err)
		return
	}

	// 验证地址是否已存在
	valid, duplicates, err := s.addressRepo.ValidateAddresses(newCtx, f)
	if err != nil {
		s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, 0, "验证地址失败: "+err.Error())
		return
	}

	// 如果有重复地址，则停止导入
	if !valid {
		errorMsg := fmt.Sprintf("发现%d个重复地址，导入已停止", len(duplicates))
		if len(duplicates) > 10 {
			errorMsg += fmt.Sprintf("，前10个重复地址: %s", strings.Join(duplicates[:10], ", "))
		} else {
			errorMsg += fmt.Sprintf("，重复地址: %s", strings.Join(duplicates, ", "))
		}
		s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, 0, errorMsg)
		return
	}

	// 重新打开文件
	f.Close()
	f, err = file.Open()
	if err != nil {
		s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, 0, "打开文件失败: "+err.Error())
		return
	}
	defer f.Close()

	// 读取CSV文件
	reader := csv.NewReader(f)
	reader.TrimLeadingSpace = true

	// 跳过标题行
	_, err = reader.Read()
	if err != nil {
		s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, 0, "读取CSV文件头失败: "+err.Error())
		return
	}

	// 读取所有记录
	records := make([]addressService.AddressRecord, 0)
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, 0, "读取CSV文件行失败: "+err.Error())
			return
		}

		// 确保至少有两列（链和地址）
		if len(record) < 2 {
			continue
		}

		chain := strings.TrimSpace(record[0])
		addr := strings.TrimSpace(record[1])

		// 跳过空行
		if chain == "" || addr == "" {
			continue
		}

		// 添加到记录列表
		records = append(records, addressService.AddressRecord{
			Chain:   chain,
			Address: addr,
		})

		// 限制最大导入数量
		if len(records) >= 100000 {
			break
		}
	}

	// 如果没有有效记录，则停止导入
	if len(records) == 0 {
		s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, 0, "没有有效的地址记录")
		return
	}

	// 导入地址
	err = s.addressRepo.ImportAddresses(newCtx, taskId, records)
	if err != nil {
		s.addressRepo.UpdateImportTaskStatus(newCtx, taskId, addressService.TaskStatusFailed, 0, 0, len(records), "导入地址失败: "+err.Error())
		return
	}
}

// GetImportProgress 获取导入进度
func (s *sSystemLogic) GetImportProgress(ctx context.Context, req *v1.GetImportProgressReq) (res *v1.GetImportProgressRes, err error) {
	// 初始化返回结果
	res = &v1.GetImportProgressRes{}

	// 获取导入任务状态
	task, err := s.addressRepo.GetImportTaskStatus(ctx, req.TaskId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取导入任务状态失败")
	}

	// 设置返回数据
	res.Data = &v1.ImportProgressItem{
		TaskId:        task.TaskId,
		Status:        task.Status,
		Progress:      task.Progress,
		ProcessedRows: task.ProcessedRows,
		TotalRows:     task.TotalRows,
		ErrorMessage:  task.ErrorMessage,
	}

	return res, nil
}
