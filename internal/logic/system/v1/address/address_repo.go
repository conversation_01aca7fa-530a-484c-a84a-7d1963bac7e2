package address

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/service/system/address"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
)

// Constants for import task status
const (
	TaskStatusPending    = "pending"
	TaskStatusValidating = "validating"
	TaskStatusImporting  = "importing"
	TaskStatusCompleted  = "completed"
	TaskStatusFailed     = "failed"
)

// addressRepository implements the IAddressRepository interface.
type addressRepository struct{}

// NewAddressRepository creates and returns a new instance of addressRepository.
func NewAddressRepository() address.IAddressRepository {
	return &addressRepository{}
}

// GetStatistics retrieves statistics about bound and unbound addresses grouped by chain.
func (r *addressRepository) GetStatistics(ctx context.Context) ([]*v1.AddressStatisticsItem, error) {
	// Initialize empty list to avoid nil response
	result := make([]*v1.AddressStatisticsItem, 0)

	// Query to get statistics grouped by chain
	// We need to count addresses with bind_status=1 and bind_status=0 for each chain
	sql := `
		SELECT
			chan as chain,
			SUM(CASE WHEN bind_status = 1 THEN 1 ELSE 0 END) as bind_num,
			SUM(CASE WHEN bind_status = 0 THEN 1 ELSE 0 END) as unbind_num
		FROM
			address
		GROUP BY
			chan
		ORDER BY
			chan ASC
	`

	// Execute the query
	err := g.DB().Ctx(ctx).Raw(sql).Scan(&result)
	if err != nil {
		return nil, gerror.Wrap(err, "获取地址统计信息失败")
	}

	return result, nil
}

// ValidateAddresses checks if any addresses in the CSV file already exist in the database.
func (r *addressRepository) ValidateAddresses(ctx context.Context, reader io.Reader) (bool, []string, error) {
	// Create a CSV reader
	csvReader := csv.NewReader(reader)
	csvReader.TrimLeadingSpace = true

	// Skip header row
	_, err := csvReader.Read()
	if err != nil {
		return false, nil, gerror.Wrap(err, "读取CSV文件头失败")
	}

	// Read all addresses from the CSV file
	addresses := make([]string, 0)
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return false, nil, gerror.Wrap(err, "读取CSV文件行失败")
		}

		// Ensure we have at least 2 columns (chain and address)
		if len(record) < 2 {
			continue
		}

		// Add the address to the list
		address := strings.TrimSpace(record[1])
		if address != "" {
			addresses = append(addresses, address)
		}
	}

	// Check if any addresses already exist in the database
	// We'll do this in batches to avoid creating a huge query
	batchSize := 1000
	duplicateAddresses := make([]string, 0)

	for i := 0; i < len(addresses); i += batchSize {
		end := i + batchSize
		if end > len(addresses) {
			end = len(addresses)
		}

		batch := addresses[i:end]
		var existingAddresses []string
		err := g.DB().Ctx(ctx).Model("address").
			Fields("address").
			WhereIn("address", batch).
			Scan(&existingAddresses)

		if err != nil {
			return false, nil, gerror.Wrap(err, "检查地址是否存在失败")
		}

		duplicateAddresses = append(duplicateAddresses, existingAddresses...)
	}

	// Return true if all addresses are valid (no duplicates)
	return len(duplicateAddresses) == 0, duplicateAddresses, nil
}

// ImportAddresses imports addresses from a CSV file into the database.
func (r *addressRepository) ImportAddresses(ctx context.Context, taskId string, records []address.AddressRecord) error {
	// Check if the task exists
	_, err := r.GetImportTaskStatus(ctx, taskId)
	if err != nil {
		return gerror.Wrap(err, "获取导入任务状态失败")
	}

	// Update task status to importing
	err = r.UpdateImportTaskStatus(ctx, taskId, TaskStatusImporting, 0, 0, len(records), "")
	if err != nil {
		return gerror.Wrap(err, "更新导入任务状态失败")
	}

	// Import addresses in batches
	batchSize := 1000
	totalRecords := len(records)
	processedRecords := 0

	for i := 0; i < totalRecords; i += batchSize {
		end := i + batchSize
		if end > totalRecords {
			end = totalRecords
		}

		batch := records[i:end]

		// Begin a transaction
		err := g.DB().Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// Prepare batch insert data
			data := make([]map[string]interface{}, len(batch))
			for j, record := range batch {
				data[j] = map[string]interface{}{
					"chan":        record.Chain,
					"address":     record.Address,
					"bind_status": 0, // Default to unbound
					"created_at":  gtime.Now(),
					"updated_at":  gtime.Now(),
				}
			}

			// Insert the batch
			_, err := tx.Model("address").Ctx(ctx).Data(data).Insert()
			if err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			// Update task status to failed
			updateErr := r.UpdateImportTaskStatus(ctx, taskId, TaskStatusFailed,
				float64(processedRecords)/float64(totalRecords)*100,
				processedRecords, totalRecords, err.Error())
			if updateErr != nil {
				g.Log().Error(ctx, "更新导入任务状态失败", updateErr)
			}
			return gerror.Wrap(err, "导入地址失败")
		}

		// Update processed records count
		processedRecords += len(batch)

		// Update task progress
		progress := float64(processedRecords) / float64(totalRecords) * 100
		err = r.UpdateImportTaskStatus(ctx, taskId, TaskStatusImporting,
			progress, processedRecords, totalRecords, "")
		if err != nil {
			g.Log().Error(ctx, "更新导入任务进度失败", err)
		}
	}

	// Update task status to completed
	err = r.UpdateImportTaskStatus(ctx, taskId, TaskStatusCompleted, 100, totalRecords, totalRecords, "")
	if err != nil {
		return gerror.Wrap(err, "更新导入任务状态失败")
	}

	return nil
}

// CreateImportTask creates a new import task and returns its ID.
func (r *addressRepository) CreateImportTask(ctx context.Context) (string, error) {
	// Generate a unique task ID
	taskId := fmt.Sprintf("import_%s_%s",
		gtime.Now().Format("YmdHis"),
		grand.S(8))

	// Create the task in the database
	_, err := g.DB().Ctx(ctx).Model("import_tasks").Insert(g.Map{
		"task_id":        taskId,
		"status":         TaskStatusPending,
		"progress":       0,
		"processed_rows": 0,
		"total_rows":     0,
		"error_message":  "",
		"created_at":     gtime.Now(),
		"updated_at":     gtime.Now(),
	})

	if err != nil {
		return "", gerror.Wrap(err, "创建导入任务失败")
	}

	return taskId, nil
}

// UpdateImportTaskStatus updates the status of an import task.
func (r *addressRepository) UpdateImportTaskStatus(ctx context.Context, taskId string, status string, progress float64, processedRows int, totalRows int, errorMessage string) error {
	_, err := g.DB().Ctx(ctx).Model("import_tasks").
		Data(g.Map{
			"status":         status,
			"progress":       progress,
			"processed_rows": processedRows,
			"total_rows":     totalRows,
			"error_message":  errorMessage,
			"updated_at":     gtime.Now(),
		}).
		Where("task_id", taskId).
		Update()

	if err != nil {
		return gerror.Wrap(err, "更新导入任务状态失败")
	}

	return nil
}

// GetImportTaskStatus retrieves the status of an import task.
func (r *addressRepository) GetImportTaskStatus(ctx context.Context, taskId string) (*address.ImportTask, error) {
	var task address.ImportTask
	err := g.DB().Ctx(ctx).Model("import_tasks").
		Where("task_id", taskId).
		Scan(&task)

	if err != nil {
		return nil, gerror.Wrap(err, "获取导入任务状态失败")
	}

	if task.TaskId == "" {
		return nil, gerror.NewCode(codes.CodeNotFound, "导入任务不存在")
	}

	return &task, nil
}
