package redpacket

import (
	"admin-api/internal/dao"
	"admin-api/internal/service/system/redpacket" // Import the interface package
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// redPacketClaimRepository 实现了 IRedPacketClaimRepository 接口
type redPacketClaimRepository struct{}

// NewRedPacketClaimRepository 创建一个新的 redPacketClaimRepository 实例
func NewRedPacketClaimRepository() redpacket.IRedPacketClaimRepository {
	return &redPacketClaimRepository{}
}

// ListAdmin 获取红包领取记录列表 (Admin)
func (r *redPacketClaimRepository) ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*redpacket.RedPacketClaimAdminInfoDTO, total int, err error) {
	// 添加软删除过滤条件
	condition["red_packet_claims.deleted_at"] = nil

	// 使用 DAO 中已有的关联查询方法
	daoList, total, err := dao.RedPacketClaims.ListAdminRedPacketClaimsWithFullInfo(ctx, page, pageSize, condition)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "redPacketClaimRepository.ListAdmin: 调用 DAO 查询失败")
	}

	// 将 DAO 返回的 model.RedPacketClaimAdminInfo 列表转换为 DTO 列表
	list = make([]*redpacket.RedPacketClaimAdminInfoDTO, 0, len(daoList))
	if err = gconv.Structs(daoList, &list); err != nil {
		return nil, 0, gerror.Wrap(err, "redPacketClaimRepository.ListAdmin: 转换 DAO 结果到 DTO 失败")
	}

	// DTO 结构与 DAO 返回的 model 结构字段一致，无需额外处理

	return list, total, nil
}

// GetClaimsByRedPacketID 获取指定红包的所有领取记录 (Admin DTO)
func (r *redPacketClaimRepository) GetClaimsByRedPacketID(ctx context.Context, redPacketId int64) ([]*redpacket.RedPacketClaimAdminInfoDTO, error) {
	// 使用 DAO 中已有的关联查询方法，查询所有记录
	condition := g.Map{
		"red_packet_claims.red_packet_id": redPacketId,
	}
	daoList, _, err := dao.RedPacketClaims.ListAdminRedPacketClaimsWithFullInfo(ctx, 1, 999999, condition) // 获取所有页
	if err != nil {
		return nil, gerror.Wrapf(err, "redPacketClaimRepository.GetClaimsByRedPacketID: 调用 DAO 查询失败, RedPacketID: %d", redPacketId)
	}

	// 转换
	list := make([]*redpacket.RedPacketClaimAdminInfoDTO, 0, len(daoList))
	if err = gconv.Structs(daoList, &list); err != nil {
		return nil, gerror.Wrap(err, "redPacketClaimRepository.GetClaimsByRedPacketID: 转换 DAO 结果到 DTO 失败")
	}

	// 处理 CreatedAt 类型
	for i := range list {
		if daoList[i].CreatedAt != nil {
			list[i].CreatedAt = daoList[i].CreatedAt
		}
	}

	return list, nil
}
