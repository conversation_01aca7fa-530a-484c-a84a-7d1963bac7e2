package redpacket

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/redpacket" // Import the interface package
	"context"
	"database/sql"
	"errors"

	// "github.com/gogf/gf/v2/database/gdb" // Removed unused import
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	// "github.com/gogf/gf/v2/os/gtime" // Removed unused import
	"github.com/gogf/gf/v2/util/gconv"
)

// redPacketRepository 实现了 IRedPacketRepository 接口
type redPacketRepository struct{}

// NewRedPacketRepository 创建一个新的 redPacketRepository 实例
func NewRedPacketRepository() redpacket.IRedPacketRepository {
	return &redPacketRepository{}
}

// ListAdmin 获取红包列表 (Admin)
func (r *redPacketRepository) ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*redpacket.RedPacketAdminInfoDTO, total int, err error) {
	// 添加软删除过滤条件
	condition["red_packets.deleted_at"] = nil

	// 使用 DAO 中已有的关联查询方法
	daoList, total, err := dao.RedPackets.ListAdminRedPacketsWithFullInfo(ctx, page, pageSize, condition)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "redPacketRepository.ListAdmin: 调用 DAO 查询失败")
	}

	// 将 DAO 返回的 model.RedPacketAdminInfo 列表转换为 DTO 列表
	list = make([]*redpacket.RedPacketAdminInfoDTO, 0, len(daoList))
	if err = gconv.Structs(daoList, &list); err != nil {
		return nil, 0, gerror.Wrap(err, "redPacketRepository.ListAdmin: 转换 DAO 结果到 DTO 失败")
	}

	// DTO 结构与 DAO 返回的 model 结构字段一致，无需额外处理

	return list, total, nil
}

// GetAdminDetailByID 获取单个红包详情 (Admin DTO)
func (r *redPacketRepository) GetAdminDetailByID(ctx context.Context, redPacketId int64) (*redpacket.RedPacketAdminInfoDTO, error) {
	// 使用 DAO 中已有的关联查询方法
	detailModel, err := dao.RedPackets.GetAdminRedPacketDetail(ctx, redPacketId)
	if err != nil {
		// DAO 层已包装错误
		return nil, err
	}
	if detailModel == nil {
		// DAO 层约定未找到返回 nil, nil
		return nil, nil
	}

	// 将 model 转换为 DTO
	var detailDTO redpacket.RedPacketAdminInfoDTO
	if err = gconv.Struct(detailModel, &detailDTO); err != nil {
		return nil, gerror.Wrap(err, "redPacketRepository.GetAdminDetailByID: 转换 DAO 结果到 DTO 失败")
	}

	return &detailDTO, nil
}

// GetByID 获取原始红包实体
func (r *redPacketRepository) GetByID(ctx context.Context, redPacketId int64) (*entity.RedPackets, error) {
	var entity *entity.RedPackets
	// 使用 DAO 的 GetRedPacketForUpdate 或类似方法，如果需要锁定
	// 这里暂时使用普通查询
	err := dao.RedPackets.Ctx(ctx).Where(dao.RedPackets.Columns().RedPacketId, redPacketId).Scan(&entity)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "redPacketRepository.GetByID: 查询红包失败, ID: %d", redPacketId)
	}
	return entity, nil
}

// UpdateStatus 更新红包状态
func (r *redPacketRepository) UpdateStatus(ctx context.Context, redPacketId int64, status string) error {
	// 注意：此方法仅更新状态，不包含业务逻辑检查。
	// 实际业务应在 Service 层处理状态流转和相关操作。
	_, err := dao.RedPackets.Ctx(ctx).
		Data(g.Map{dao.RedPackets.Columns().Status: status}). // 只更新 status
		Where(dao.RedPackets.Columns().RedPacketId, redPacketId).
		Update()
	if err != nil {
		return gerror.Wrapf(err, "redPacketRepository.UpdateStatus: 更新红包状态失败, ID: %d, Status: %s", redPacketId, status)
	}
	return nil
}

// UpdateRefundInfo 更新退款相关信息 (取消红包时使用)
func (r *redPacketRepository) UpdateRefundInfo(ctx context.Context, redPacketId int64, status string, refundAmount int64, refundTransactionId int64) error {
	// 这个方法通常在事务中被调用
	// 注意：此方法仅更新状态，不处理退款金额和交易ID。
	// 退款逻辑应在 Service 层处理，调用钱包服务等。
	_, err := dao.RedPackets.Ctx(ctx). // 假设在事务外调用
						Data(g.Map{
			dao.RedPackets.Columns().Status: status, // 只更新 status
			// 移除不存在的列更新
		}).
		Where(dao.RedPackets.Columns().RedPacketId, redPacketId).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "redPacketRepository.UpdateRefundInfo: 更新红包退款信息失败, ID: %d", redPacketId)
	}
	return nil
}
