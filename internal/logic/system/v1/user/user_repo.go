package user

import (
	"context"
	"database/sql"
	"errors"

	// "fmt" // Removed unused import
	v1 "admin-api/api/system/v1" // Import API definitions for DTOs
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/user" // Import the interface package
	"admin-api/internal/utility"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// userRepository 实现了 IUserRepository 接口
type userRepository struct{}

// NewUserRepository 创建一个新的 userRepository 实例
func NewUserRepository() user.IUserRepository {
	return &userRepository{}
}

// List retrieves a paginated list of users based on criteria, returning DTOs.
func (r *userRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.UserInfoType, total int, err error) {
	// 1. Query user list
	userList, total, err := dao.Users.GetUserList(ctx, page, pageSize, condition)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "userRepository.List: 获取用户列表失败")
	}
	if total == 0 || len(userList) == 0 {
		return make([]*v1.UserInfoType, 0), 0, nil
	}

	// 2. Collect recommender IDs
	recommendIds := make([]uint64, 0, len(userList))
	userIds := make([]uint64, 0, len(userList)) // Also collect user IDs for potential future use
	for _, user := range userList {
		userIds = append(userIds, user.Id)
		if user.RecommendId > 0 {
			recommendIds = append(recommendIds, user.RecommendId)
		}
	}

	// 3. Fetch recommender info
	recommendMap, err := r.GetUserMapByIDs(ctx, recommendIds)
	if err != nil {
		// Log error but proceed, recommender info is optional
		g.Log().Warningf(ctx, "userRepository.List: 获取推荐人信息失败: %v", err)
		recommendMap = make(map[uint64]*entity.Users) // Ensure map is not nil
	}

	// 4. Assemble DTO list
	list = make([]*v1.UserInfoType, 0, len(userList))
	for _, user := range userList {
		dto := &v1.UserInfoType{}
		if errConv := gconv.Struct(user, dto); errConv != nil {
			g.Log().Warningf(ctx, "userRepository.List: 转换用户实体到 DTO 失败: %v, UserID: %d", errConv, user.Id)
			continue // Skip problematic record
		}
		// Manually set boolean fields, account type, and recommender account
		dto.AccountType = user.AccountType
		dto.IsStop = user.IsStop == 1
		dto.Google2faEnabled = user.Google2FaEnabled == 1
		dto.RedPacketPermission = user.RedPacketPermission == 1
		dto.TransferPermission = user.TransferPermission == 1
		dto.WithdrawPermission = user.WithdrawPermission == 1
		dto.FlashTradePermission = user.FlashTradePermission == 1
		dto.RechargePermission = user.RechargePermission == 1
		dto.ReceivePermission = user.ReceivePermission == 1
		dto.PaymentPasswordSet = user.PaymentPassword != ""

		// Create a temporary UserDetailType to pass to AddAgentInfo
		tempDetailDTO := &v1.UserDetailType{}
		tempDetailDTO.UserInfoType = *dto // Copy all fields from UserInfoType

		// Call AddAgentInfo with the correct type
		tempDetailDTO, err = r.AddAgentInfo(ctx, user, tempDetailDTO)
		if err != nil {
			return nil, 0, gerror.Wrap(err, "userRepository.List: 获取用户代理信息失败")
		}

		// Copy the agent information back to our UserInfoType
		dto.FirstAgentName = tempDetailDTO.FirstAgentName
		dto.SecondAgentName = tempDetailDTO.SecondAgentName
		dto.ThirdAgentName = tempDetailDTO.ThirdAgentName

		if recommender, ok := recommendMap[user.RecommendId]; ok {
			dto.RecommendAccount = recommender.Account
		}
		list = append(list, dto)
	}

	return list, total, nil
}

// GetDetailByID retrieves detailed user information by ID, returning a DTO.
func (r *userRepository) GetDetailByID(ctx context.Context, id uint64) (*v1.UserDetailType, error) {
	// 使用联合查询一次性获取用户详情、代理信息和Telegram信息
	var result struct {
		// 用户基础信息
		Id                             uint64      `json:"id"`
		Email                          string      `json:"email"`
		Account                        string      `json:"account"`
		AccountType                    int         `json:"account_type"`
		InviteCode                     string      `json:"invite_code"`
		AreaCode                       string      `json:"area_code"`
		Phone                          string      `json:"phone"`
		Avatar                         string      `json:"avatar"`
		RecommendId                    uint64      `json:"recommend_id"`
		IsStop                         int         `json:"is_stop"`
		Google2faEnabled               int         `json:"google2fa_enabled"`
		RedPacketPermission            int         `json:"red_packet_permission"`
		TransferPermission             int         `json:"transfer_permission"`
		WithdrawPermission             int         `json:"withdraw_permission"`
		FlashTradePermission           int         `json:"flash_trade_permission"`
		RechargePermission             int         `json:"recharge_permission"`
		ReceivePermission              int         `json:"receive_permission"`
		LastLoginTime                  *gtime.Time `json:"last_login_time"`
		CreatedAt                      *gtime.Time `json:"created_at"`
		UpdatedAt                      *gtime.Time `json:"updated_at"`
		Language                       string      `json:"language"`
		Reason                         string      `json:"reason"`
		ResetPaymentPasswordPermission int         `json:"reset_payment_password_permission"`
		PaymentPassword                string      `json:"payment_password"`

		// 推荐人信息
		RecommendAccount string `json:"recommend_account"`

		// 代理信息
		FirstAgentName  string `json:"first_agent_name"`
		SecondAgentName string `json:"second_agent_name"`
		ThirdAgentName  string `json:"third_agent_name"`

		// Telegram信息
		TelegramId       string `json:"telegram_id"`
		TelegramUsername string `json:"telegram_username"`
		FirstName        string `json:"first_name"`
	}

	// 构建联合查询 - 使用agents表获取代理信息
	err := dao.Users.Ctx(ctx).As("u").
		LeftJoin("users recommend_user", "u.recommend_id = recommend_user.id").
		LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id").
		LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id").
		LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1").
		Fields(
			"u.id",
			"u.email",
			"u.account",
			"u.account_type",
			"u.invite_code",
			"u.area_code",
			"u.phone",
			"u.avatar",
			"u.recommend_id",
			"u.is_stop",
			"u.google2fa_enabled",
			"u.red_packet_permission",
			"u.transfer_permission",
			"u.withdraw_permission",
			"u.flash_trade_permission",
			"u.recharge_permission",
			"u.receive_permission",
			"u.last_login_time",
			"u.created_at",
			"u.updated_at",
			"u.language",
			"u.reason",
			"u.reset_payment_password_permission",
			"u.payment_password",
			"recommend_user.account as recommend_account",
			"first_agent.username as first_agent_name",
			"second_agent.username as second_agent_name",
			"third_agent.username as third_agent_name",
			"uba.telegram_id",
			"uba.telegram_username",
			"uba.first_name",
		).
		Where("u.id", id).
		WhereNull("u.deleted_at").
		Scan(&result)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "userRepository.GetDetailByID: 查询用户详情失败, ID: %d", id)
	}

	// 检查是否找到用户
	if result.Id == 0 {
		return nil, nil // Not found
	}

	// 3. 组装DTO
	detailDTO := &v1.UserDetailType{}

	// 填充基础用户信息
	detailDTO.Id = result.Id
	detailDTO.Email = result.Email
	detailDTO.Account = result.Account
	detailDTO.AccountType = result.AccountType
	detailDTO.InviteCode = result.InviteCode
	detailDTO.AreaCode = result.AreaCode
	detailDTO.Phone = result.Phone
	detailDTO.Avatar = result.Avatar
	detailDTO.RecommendId = result.RecommendId
	detailDTO.IsStop = result.IsStop == 1
	detailDTO.Google2faEnabled = result.Google2faEnabled == 1
	detailDTO.RedPacketPermission = result.RedPacketPermission == 1
	detailDTO.TransferPermission = result.TransferPermission == 1
	detailDTO.WithdrawPermission = result.WithdrawPermission == 1
	detailDTO.FlashTradePermission = result.FlashTradePermission == 1
	detailDTO.RechargePermission = result.RechargePermission == 1
	detailDTO.ReceivePermission = result.ReceivePermission == 1
	detailDTO.PaymentPasswordSet = result.PaymentPassword != ""
	detailDTO.LastLoginTime = result.LastLoginTime
	detailDTO.CreatedAt = result.CreatedAt
	detailDTO.UpdatedAt = result.UpdatedAt

	// 填充推荐人信息
	detailDTO.RecommendAccount = result.RecommendAccount

	// 填充代理信息
	detailDTO.FirstAgentName = result.FirstAgentName
	detailDTO.SecondAgentName = result.SecondAgentName
	detailDTO.ThirdAgentName = result.ThirdAgentName

	// 填充Telegram信息
	detailDTO.TelegramId = result.TelegramId
	detailDTO.TelegramUsername = result.TelegramUsername
	detailDTO.FirstName = result.FirstName

	// 填充详情特有字段
	detailDTO.Language = result.Language
	detailDTO.Reason = result.Reason
	detailDTO.ResetPaymentPasswordPermission = result.ResetPaymentPasswordPermission == 1

	return detailDTO, nil
}

// 添加代理信息
func (r *userRepository) AddAgentInfo(ctx context.Context, userEntity *entity.Users, detailDTO *v1.UserDetailType) (detailDTOres *v1.UserDetailType, err error) {
	var agentIds []int64
	if userEntity.FirstId > 0 {
		agentIds = append(agentIds, gconv.Int64(userEntity.FirstId))
	}
	if userEntity.SecondId > 0 {
		agentIds = append(agentIds, gconv.Int64(userEntity.SecondId))
	}
	if userEntity.ThirdId > 0 {
		agentIds = append(agentIds, gconv.Int64(userEntity.ThirdId))
	}
	agents, err := dao.Agents.GetAgentsByIds(ctx, agentIds)
	if err != nil {
		g.Log().Warningf(ctx, "userRepository.GetDetailByID: 获取代理信息失败: %v, AgentIDs: %v", err, agentIds)
		return nil, gerror.Wrap(err, "userRepository.GetDetailByID: 获取代理信息失败")

	} else {
		agentMap := make(map[int64]*entity.Agents)
		for _, agent := range agents {
			agentMap[int64(agent.AgentId)] = agent
		}
		if userEntity.FirstId > 0 {
			if agent, ok := agentMap[int64(userEntity.FirstId)]; ok {
				detailDTO.FirstAgentName = agent.Username
			}
		}
		if userEntity.SecondId > 0 {
			if agent, ok := agentMap[int64(userEntity.SecondId)]; ok {
				detailDTO.SecondAgentName = agent.Username
			}
		}
		if userEntity.ThirdId > 0 {
			if agent, ok := agentMap[int64(userEntity.ThirdId)]; ok {
				detailDTO.ThirdAgentName = agent.Username
			}
		}
	}
	return detailDTO, nil

}

// GetByID retrieves the raw user entity by ID.
func (r *userRepository) GetByID(ctx context.Context, id uint64) (*entity.Users, error) {
	var user *entity.Users
	err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, id).WhereNull(dao.Users.Columns().DeletedAt).Scan(&user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "userRepository.GetByID: 查询用户失败, ID: %d", id)
	}
	return user, nil
}

// GetByAccount retrieves the raw user entity by account name.
func (r *userRepository) GetByAccount(ctx context.Context, account string) (*entity.Users, error) {
	var user *entity.Users
	err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Account, account).WhereNull(dao.Users.Columns().DeletedAt).Scan(&user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "userRepository.GetByAccount: 查询用户失败, Account: %s", account)
	}
	return user, nil
}

// GetByEmail retrieves the raw user entity by email.
func (r *userRepository) GetByEmail(ctx context.Context, email string) (*entity.Users, error) {
	var user *entity.Users
	err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Email, email).WhereNull(dao.Users.Columns().DeletedAt).Scan(&user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "userRepository.GetByEmail: 查询用户失败, Email: %s", email)
	}
	return user, nil
}

// GetByPhone retrieves the raw user entity by phone number.
func (r *userRepository) GetByPhone(ctx context.Context, phone string) (*entity.Users, error) {
	var user *entity.Users
	err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Phone, phone).WhereNull(dao.Users.Columns().DeletedAt).Scan(&user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "userRepository.GetByPhone: 查询用户失败, Phone: %s", phone)
	}
	return user, nil
}

// GetByInviteCode retrieves the raw user entity by invite code.
func (r *userRepository) GetByInviteCode(ctx context.Context, inviteCode string) (*entity.Users, error) {
	var user *entity.Users
	err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().InviteCode, inviteCode).WhereNull(dao.Users.Columns().DeletedAt).Scan(&user)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "userRepository.GetByInviteCode: 查询用户失败, InviteCode: %s", inviteCode)
	}
	return user, nil
}

// GetUserMapByIDs retrieves a map of user ID to user entity.
func (r *userRepository) GetUserMapByIDs(ctx context.Context, ids []uint64) (map[uint64]*entity.Users, error) {
	userMap := make(map[uint64]*entity.Users)
	if len(ids) == 0 {
		return userMap, nil
	}
	var users []*entity.Users
	err := dao.Users.Ctx(ctx).WhereIn(dao.Users.Columns().Id, ids).WhereNull(dao.Users.Columns().DeletedAt).Scan(&users)
	if err != nil {
		return nil, gerror.Wrapf(err, "userRepository.GetUserMapByIDs: 查询用户映射失败, IDs: %v", ids)
	}
	for _, user := range users {
		userMap[user.Id] = user
	}
	return userMap, nil
}

// Create adds a new user record.
func (r *userRepository) Create(ctx context.Context, data *do.Users) (id uint64, err error) {
	data.CreatedAt = gtime.Now()
	// UpdatedAt is usually handled by DB trigger or ORM hook, but set it here if not
	data.UpdatedAt = gtime.Now()
	res, err := dao.Users.Ctx(ctx).Data(data).Insert()
	if err != nil {
		// TODO: Handle specific unique constraint errors if DAO doesn't
		return 0, gerror.Wrap(err, "userRepository.Create: 创建用户失败")
	}
	lastId, err := res.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "userRepository.Create: 获取新用户ID失败")
	}
	return uint64(lastId), nil
}

// Update modifies specific fields of an existing user record.
func (r *userRepository) Update(ctx context.Context, id uint64, data g.Map) error {
	if len(data) == 0 {
		return nil // No fields to update
	}
	data[dao.Users.Columns().UpdatedAt] = gtime.Now() // Ensure update time is set
	_, err := dao.Users.Ctx(ctx).Data(data).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		// TODO: Handle specific unique constraint errors if DAO doesn't
		return gerror.Wrapf(err, "userRepository.Update: 更新用户失败, ID: %d", id)
	}
	return nil
}

// UpdateStatus updates the status (is_stop) and reason for a user.
func (r *userRepository) UpdateStatus(ctx context.Context, id uint64, isStop int, reason string) error {
	data := g.Map{
		dao.Users.Columns().IsStop:    isStop,
		dao.Users.Columns().Reason:    reason,
		dao.Users.Columns().UpdatedAt: gtime.Now(),
	}
	_, err := dao.Users.Ctx(ctx).Data(data).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "userRepository.UpdateStatus: 更新用户状态失败, ID: %d", id)
	}
	return nil
}

// ResetPassword updates the user's password hash.
func (r *userRepository) ResetPassword(ctx context.Context, id uint64, passwordHash string) error {
	data := g.Map{
		dao.Users.Columns().Password:  passwordHash,
		dao.Users.Columns().UpdatedAt: gtime.Now(),
	}
	_, err := dao.Users.Ctx(ctx).Data(data).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "userRepository.ResetPassword: 重置用户密码失败, ID: %d", id)
	}
	return nil
}

// ResetGoogle2FA disables Google 2FA for the user.
func (r *userRepository) ResetGoogle2FA(ctx context.Context, id uint64) error {
	data := g.Map{
		dao.Users.Columns().Google2FaSecret:  "", // Clear the secret
		dao.Users.Columns().Google2FaEnabled: 0,  // Disable 2FA
		dao.Users.Columns().UpdatedAt:        gtime.Now(),
	}
	_, err := dao.Users.Ctx(ctx).Data(data).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "userRepository.ResetGoogle2FA: 重置用户 Google 2FA 失败, ID: %d", id)
	}
	return nil
}

// DeleteSoft soft deletes users by their IDs.
func (r *userRepository) DeleteSoft(ctx context.Context, ids []uint64) error {
	if len(ids) == 0 {
		return nil
	}
	_, err := dao.Users.Ctx(ctx).Data(do.Users{DeletedAt: gtime.Now()}).WhereIn(dao.Users.Columns().Id, ids).Update()
	if err != nil {
		return gerror.Wrapf(err, "userRepository.DeleteSoft: 软删除用户失败, IDs: %v", ids)
	}
	return nil
}

// CheckAccountExists checks if an account name exists (excluding optional ID).
func (r *userRepository) CheckAccountExists(ctx context.Context, account string, excludeId ...uint64) (bool, error) {
	return r.checkFieldExistence(ctx, dao.Users.Columns().Account, account, excludeId...)
}

// CheckEmailExists checks if an email exists (excluding optional ID).
func (r *userRepository) CheckEmailExists(ctx context.Context, email string, excludeId ...uint64) (bool, error) {
	return r.checkFieldExistence(ctx, dao.Users.Columns().Email, email, excludeId...)
}

// CheckPhoneExists checks if a phone number exists (excluding optional ID).
func (r *userRepository) CheckPhoneExists(ctx context.Context, phone string, excludeId ...uint64) (bool, error) {
	return r.checkFieldExistence(ctx, dao.Users.Columns().Phone, phone, excludeId...)
}

// CheckInviteCodeExists checks if an invite code exists (excluding optional ID).
func (r *userRepository) CheckInviteCodeExists(ctx context.Context, inviteCode string, excludeId ...uint64) (bool, error) {
	return r.checkFieldExistence(ctx, dao.Users.Columns().InviteCode, inviteCode, excludeId...)
}

// checkFieldExistence is a helper function for uniqueness checks.

// GetByIDs retrieves multiple user entities by their IDs.
func (r *userRepository) GetByIDs(ctx context.Context, ids []uint64) ([]*entity.Users, error) {
	var users []*entity.Users
	if len(ids) == 0 {
		return users, nil
	}
	err := dao.Users.Ctx(ctx).WhereIn(dao.Users.Columns().Id, ids).WhereNull(dao.Users.Columns().DeletedAt).Scan(&users)
	if err != nil {
		return nil, gerror.Wrapf(err, "userRepository.GetByIDs: 查询用户列表失败, IDs: %v", ids)
	}
	return users, nil
}

// ExistsByID checks if a user exists by ID.
func (r *userRepository) ExistsByID(ctx context.Context, id uint64) (bool, error) {
	count, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, id).WhereNull(dao.Users.Columns().DeletedAt).Count()
	if err != nil {
		return false, gerror.Wrapf(err, "userRepository.ExistsByID: 检查用户是否存在失败, ID: %d", id)
	}
	return count > 0, nil
}

// ResetGoogleSecret clears the Google 2FA secret for a user.
func (r *userRepository) ResetGoogleSecret(ctx context.Context, id uint64) error {
	data := g.Map{
		dao.Users.Columns().Google2FaSecret:  "", // Clear the secret
		dao.Users.Columns().Google2FaEnabled: 0,  // Disable 2FA
		dao.Users.Columns().UpdatedAt:        gtime.Now(),
	}
	_, err := dao.Users.Ctx(ctx).Data(data).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "userRepository.ResetGoogleSecret: 重置用户 Google Secret 失败, ID: %d", id)
	}
	return nil
}

// UpdatePassword updates only the password hash for a user.
func (r *userRepository) UpdatePassword(ctx context.Context, id uint64, passwordHash string) error {
	data := g.Map{
		dao.Users.Columns().Password:  passwordHash,
		dao.Users.Columns().UpdatedAt: gtime.Now(),
	}
	_, err := dao.Users.Ctx(ctx).Data(data).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "userRepository.UpdatePassword: 更新用户密码失败, ID: %d", id)
	}
	return nil
}

func (r *userRepository) checkFieldExistence(ctx context.Context, fieldName string, value interface{}, excludeId ...uint64) (bool, error) {
	m := dao.Users.Ctx(ctx).Where(fieldName, value).WhereNull(dao.Users.Columns().DeletedAt)
	if len(excludeId) > 0 && excludeId[0] > 0 {
		m = m.WhereNot(dao.Users.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrapf(err, "userRepository.checkFieldExistence: 检查字段 '%s' 唯一性失败", fieldName)
	}
	return count > 0, nil
}

// CreateMerchantUser 创建商户类型的用户 (AccountType=2)
// This method directly calls the DAO layer's CreateMerchantUser.
func (r *userRepository) CreateMerchantUser(ctx context.Context, account string, hashedPassword string, email string) (userId uint64, err error) {
	// The DAO method already handles uniqueness checks and error wrapping.
	newUserId, err := dao.Users.CreateMerchantUser(ctx, account, hashedPassword, email)
	if err != nil {
		// The error from DAO should be specific enough (e.g., CodeUsernameExists, CodeEmailExists)
		// or a generic error if something else went wrong.
		// No need to re-wrap extensively here unless adding repository-specific context.
		return 0, err
	}
	return newUserId, nil
}

// ResetPaymentPassword 重置用户支付密码
func (r *userRepository) ResetPaymentPassword(ctx context.Context, id uint64) error {
	return dao.Users.ResetPaymentPassword(ctx, id)
}

// ListWithAgentInfo 获取用户列表（带代理和telegram信息）
func (r *userRepository) ListWithAgentInfo(ctx context.Context, req *v1.GetUserListReq) (list []*v1.UserInfoType, total int, err error) {
	// 构建查询条件
	condition := g.Map{}

	// 基础字段查询条件 - 添加输入验证
	if req.Account != "" && len(req.Account) >= 1 && req.Account != "1" {
		condition["u.account LIKE"] = "%" + req.Account + "%"
	}
	if req.Email != "" && len(req.Email) >= 1 && req.Email != "1" {
		condition["u.email LIKE"] = "%" + req.Email + "%"
	}
	if req.Phone != "" && len(req.Phone) >= 1 && req.Phone != "1" {
		condition["u.phone LIKE"] = "%" + req.Phone + "%"
	}
	if req.AccountType != nil {
		condition["u.account_type"] = *req.AccountType
	}
	if req.IsStop != nil {
		if *req.IsStop {
			condition["u.is_stop"] = "1"
		} else {
			condition["u.is_stop"] = "0"
		}
	}
	if req.RecommendId != nil {
		condition["u.recommend_id"] = *req.RecommendId
	}

	// 时间范围条件
	if req.DateRange != "" {
		dateCondition := utility.ParseDateRange(req.DateRange, "u.created_at")
		if dateCondition != nil {
			for k, v := range dateCondition {
				condition[k] = v
			}
		}
	}

	// 代理搜索条件 - 添加输入验证，使用agents表的username字段
	if req.FirstAgentName != nil && *req.FirstAgentName != "" && len(*req.FirstAgentName) >= 1 && *req.FirstAgentName != "1" {
		condition["first_agent.username LIKE"] = "%" + *req.FirstAgentName + "%"
	}
	if req.SecondAgentName != nil && *req.SecondAgentName != "" && len(*req.SecondAgentName) >= 1 && *req.SecondAgentName != "1" {
		condition["second_agent.username LIKE"] = "%" + *req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != nil && *req.ThirdAgentName != "" && len(*req.ThirdAgentName) >= 1 && *req.ThirdAgentName != "1" {
		condition["third_agent.username LIKE"] = "%" + *req.ThirdAgentName + "%"
	}

	// Telegram搜索条件 - 添加输入验证
	if req.TelegramId != "" && len(req.TelegramId) >= 1 && req.TelegramId != "1" {
		condition["uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" && len(req.TelegramUsername) >= 1 && req.TelegramUsername != "1" {
		condition["uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" && len(req.FirstName) >= 1 && req.FirstName != "1" {
		condition["uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 调用DAO层方法
	return dao.Users.GetUserListWithAgentInfo(ctx, req.Page, req.PageSize, condition)
}

// GetUserMapByIDsWithAgentInfo 根据用户ID列表获取用户信息（包含代理和telegram信息）
func (r *userRepository) GetUserMapByIDsWithAgentInfo(ctx context.Context, ids []uint64) (map[uint64]*v1.UserInfoType, error) {
	userMap := make(map[uint64]*v1.UserInfoType)
	if len(ids) == 0 {
		return userMap, nil
	}

	// 调用DAO层方法获取用户列表（包含代理和telegram信息）
	list, err := dao.Users.GetUsersByIDsWithAgentInfo(ctx, ids)
	if err != nil {
		return nil, gerror.Wrapf(err, "userRepository.GetUserMapByIDsWithAgentInfo: 查询用户映射失败, IDs: %v", ids)
	}

	// 转换为map
	for _, user := range list {
		userMap[user.Id] = user
	}

	return userMap, nil
}

// UpdateWithdrawBettingVolume 更新用户提现流水要求
func (r *userRepository) UpdateWithdrawBettingVolume(ctx context.Context, id uint64, withdrawBettingVolume string) error {
	data := g.Map{
		dao.Users.Columns().WithdrawBettingVolume: withdrawBettingVolume,
		dao.Users.Columns().UpdatedAt:             gtime.Now(),
	}
	_, err := dao.Users.Ctx(ctx).Data(data).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "userRepository.UpdateWithdrawBettingVolume: 更新用户提现流水要求失败, ID: %d", id)
	}
	return nil
}
