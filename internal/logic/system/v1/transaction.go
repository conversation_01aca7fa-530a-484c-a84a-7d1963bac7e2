package v1

import (
	"context"
	"math"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model"
	"admin-api/utility/excel"
)

// ListAdminTransactions 获取后台交易记录列表（带代理和telegram信息）
func (s *sSystemLogic) ListAdminTransactions(ctx context.Context, req *v1.ListAdminTransactionsReq) (res *v1.ListAdminTransactionsRes, err error) {
	// 初始化返回结果
	res = &v1.ListAdminTransactionsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*model.TransactionAdminInfo, 0),
	}

	// 构建查询条件
	condition := g.Map{}
	
	// 添加筛选条件
	if req.UserId > 0 {
		condition["t.user_id"] = req.UserId
	}
	if req.Username != "" {
		condition["u.username LIKE"] = "%" + req.Username + "%"
	}
	if req.Account != "" {
		condition["u.username LIKE"] = "%" + req.Account + "%"
	}
	if req.TokenSymbol != "" {
		condition["tk.symbol"] = req.TokenSymbol
	}
	if req.Type != "" {
		condition["t.type"] = req.Type
	}
	if req.Status != "" {
		condition["t.status"] = req.Status
	}
	if req.Direction != "" {
		condition["t.direction"] = req.Direction
	}
	if req.TransactionId > 0 {
		condition["t.transaction_id"] = req.TransactionId
	}
	if req.RelatedTransactionId > 0 {
		condition["t.related_transaction_id"] = req.RelatedTransactionId
	}
	
	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["t.created_at >="] = dateRange[0] + " 00:00:00"
			condition["t.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}
	
	// 代理查询条件
	if req.FirstAgentName != "" {
		condition["u_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["u_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["u_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}
	
	// Telegram查询条件
	if req.TelegramId != "" {
		condition["u_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["u_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["u_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 处理导出
	if req.Export == 1 {
		// 导出时不进行分页限制
		list, _, err := dao.Transactions.ListAdminTransactionsWithFullInfo(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询失败")
		}
		
		// 转换为导出格式
		exportData := make([]interface{}, len(list))
		for i, item := range list {
			createdAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.String()
			}
			
			exportData[i] = struct {
				TransactionId        uint64 `json:"transactionId" excel:"交易ID"`
				UserId              uint   `json:"userId" excel:"用户ID"`
				Username            string `json:"username" excel:"用户账号"`
				TokenSymbol         string `json:"tokenSymbol" excel:"代币符号"`
				Type                string `json:"type" excel:"交易类型"`
				Status              uint   `json:"status" excel:"状态"`
				Direction           string `json:"direction" excel:"资金方向"`
				Amount              string `json:"amount" excel:"金额"`
				BalanceBefore       string `json:"balanceBefore" excel:"交易前余额"`
				BalanceAfter        string `json:"balanceAfter" excel:"交易后余额"`
				RelatedTransactionId uint64 `json:"relatedTransactionId" excel:"关联交易ID"`
				RelatedEntityId     uint64 `json:"relatedEntityId" excel:"关联实体ID"`
				RelatedEntityType   string `json:"relatedEntityType" excel:"关联实体类型"`
				Memo                string `json:"memo" excel:"备注"`
				CreatedAt           string `json:"createdAt" excel:"创建时间"`
				FirstAgentName      string `json:"firstAgentName" excel:"一级代理"`
				SecondAgentName     string `json:"secondAgentName" excel:"二级代理"`
				ThirdAgentName      string `json:"thirdAgentName" excel:"三级代理"`
				TelegramId          string `json:"telegramId" excel:"Telegram ID"`
				TelegramUsername    string `json:"telegramUsername" excel:"Telegram用户名"`
				FirstName           string `json:"firstName" excel:"真实姓名"`
			}{
				TransactionId:        item.TransactionId,
				UserId:              item.UserId,
				Username:            item.Username,
				TokenSymbol:         item.TokenSymbol,
				Type:                item.Type,
				Status:              item.Status,
				Direction:           item.Direction,
				Amount:              gconv.String(item.Amount),
				BalanceBefore:       gconv.String(item.BalanceBefore),
				BalanceAfter:        gconv.String(item.BalanceAfter),
				RelatedTransactionId: item.RelatedTransactionId,
				RelatedEntityId:     item.RelatedEntityId,
				RelatedEntityType:   item.RelatedEntityType,
				Memo:                item.Memo,
				CreatedAt:           createdAt,
				FirstAgentName:      item.FirstAgentName,
				SecondAgentName:     item.SecondAgentName,
				ThirdAgentName:      item.ThirdAgentName,
				TelegramId:          item.TelegramId,
				TelegramUsername:    item.TelegramUsername,
				FirstName:           item.FirstName,
			}
		}
		
		// 定义Excel表头
		excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取
		
		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "交易记录", "交易记录列表")
	}

	// 查询分页数据
	list, total, err := dao.Transactions.ListAdminTransactionsWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询交易记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 设置数据
	res.Data = list

	return res, nil
}
