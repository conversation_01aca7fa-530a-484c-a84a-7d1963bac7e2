package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/config"
	"admin-api/internal/consts"
	"admin-api/internal/dao" // Re-import for transaction block (temporary)
	"admin-api/internal/utility"
	"context"
	"fmt"
	"math"
	"strconv"
	"time"

	// "admin-api/internal/model/do" // Use entity directly for repo input/output mostly
	"admin-api/internal/model/entity"
	"admin-api/internal/service" // Keep for AgentCasdoor service

	// "admin-api/utility/encrypt" // Replaced by agentAuthService
	"admin-api/utility/excel"

	"math/rand" // Keep math/rand for invitation code generation

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode" // Import gcode
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp" // Import ghttp
	"github.com/gogf/gf/v2/os/gtime"
	// Import system logic for common functions
)

// generateUniqueInvitationCode 生成唯一的邀请码
func (s *sSystemLogic) generateUniqueInvitationCode(ctx context.Context) (string, error) {
	const maxAttempts = 10
	const codeLength = 8
	const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	for i := 0; i < maxAttempts; i++ {
		b := make([]byte, codeLength)
		// Seed the random number generator
		// Use crypto/rand for better randomness if security is critical
		// For simplicity here, using math/rand seeded with time
		// Note: In concurrent scenarios, consider a shared rand source or crypto/rand
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		for i := range b {
			b[i] = letters[r.Intn(len(letters))]
		}
		code := string(b)

		// Check uniqueness using repository
		existingAgent, err := s.agentRepo.GetByInvitationCode(ctx, code)
		if err != nil {
			return "", gerror.Wrap(err, "检查邀请码唯一性失败")
		}
		count := 0
		if existingAgent != nil {
			count = 1
		}
		if count == 0 {
			return code, nil
		}
	}
	return "", gerror.New("无法生成唯一的邀请码")
}

// buildAgentQueryCondition is no longer needed as filtering is handled by the repository List method.
// AddAgent 添加代理
func (s *sSystemLogic) AddAgent(ctx context.Context, req *v1.AddAgentReq) (res *v1.AddAgentRes, err error) {
	// 1. 校验唯一性 using repository
	existingAgent, err := s.agentRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查用户名唯一性失败")
	}
	if existingAgent != nil {
		return nil, gerror.NewCode(codes.CodeAgentUsernameExists)
	}

	// 只有当邮箱不为空时才检查唯一性
	if req.Email != "" {
		existingAgent, err = s.agentRepo.GetByEmail(ctx, req.Email)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查邮箱唯一性失败")
		}
		if existingAgent != nil {
			return nil, gerror.NewCode(codes.CodeAgentEmailExists)
		}
	}

	// // 只有当手机号不为空时才检查唯一性
	// if req.PhoneNumber != "" {
	// 	existingAgent, err = s.agentRepo.GetByPhoneNumber(ctx, req.PhoneNumber)
	// 	if err != nil {
	// 		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查手机号唯一性失败")
	// 	}
	// 	if existingAgent != nil {
	// 		return nil, gerror.NewCode(codes.CodePhoneExists) // 使用通用的手机号已存在错误码
	// 	}
	// }

	// 2. 处理父代理和层级
	var parentAgent *entity.Agents
	var newLevel uint = 1   // 默认为一级代理
	var parentAgentId *uint // 使用指针以允许 NULL 值

	if req.ParentAgentId > 0 {
		parentAgentIdUint := uint(req.ParentAgentId)
		parentAgentId = &parentAgentIdUint

		parentAgent, err = s.agentRepo.GetByID(ctx, req.ParentAgentId)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询上级代理失败")
		}
		// GetByID now returns nil, nil if not found
		if parentAgent == nil {
			return nil, gerror.NewCode(codes.CodeParentAgentNotFound)
		}

		// GetByID already ensures parentAgent is not deleted if found.

		// 检查是否超过最大层级
		maxLevel := g.Cfg().MustGet(ctx, "agent.maxLevel", 3).Uint() // 从配置获取，默认3
		if parentAgent.Level >= maxLevel {
			return nil, gerror.NewCodef(codes.CodeCannotAddUnderMaxLevel, "无法在 %d 级代理下添加新的代理", parentAgent.Level)
		}
		newLevel = parentAgent.Level + 1
	}

	// 3. 生成唯一邀请码
	invitationCode, err := s.generateUniqueInvitationCode(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "生成邀请码失败")
	}

	// 4. 哈希密码 using auth service
	hashedPassword, err := s.agentAuthService.HashPassword(ctx, req.Password)
	if err != nil {
		// Assuming HashPassword wraps errors appropriately
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "密码加密失败")
	}

	// 5. 准备插入数据 (using entity)
	newAgentEntity := &entity.Agents{
		Username:     req.Username,
		PasswordHash: hashedPassword,
		AgentName:    req.AgentName,
		Email:        req.Email,
		// PhoneNumber:    req.PhoneNumber,
		Level:          newLevel,
		Status:         *req.Status, // Status 是指针，需要解引用
		InvitationCode: invitationCode,
		// 新增字段
		BusinessName:     req.BusinessName,
		TelegramAccount:  req.TelegramAccount,
		TelegramBotName:  req.TelegramBotName,
		TelegramBotToken: req.TelegramBotToken,
		// Relationship 在插入后更新
	}
	if parentAgentId != nil {
		newAgentEntity.ParentAgentId = *parentAgentId
	} else {
		newAgentEntity.ParentAgentId = 0
	}

	// 6. 数据库事务：插入代理并更新 relationship
	var newAgentId int64
	
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入代理记录，获取新 ID using repository
		// Note: Repository Create method now handles the insertion and returns the ID.
		// The transaction needs to be passed down if the repo method requires it,
		// or the repo method handles its own transaction (less ideal for multi-step logic).
		// Let's assume for now the repo methods don't manage transactions themselves.
		// We need a way to pass the transaction to the repo.
		// Option 1: Modify repo interface/impl to accept TX.
		// Option 2: Keep DAO calls within the transaction block (defeats purpose slightly).
		// Option 3: Use a Unit of Work pattern.

		// Let's try Option 2 for simplicity now, but Option 1/3 is better.
		result, err := dao.Agents.Ctx(ctx).TX(tx).Data(newAgentEntity).OmitEmpty().Insert()
		if err != nil {
			return gerror.Wrap(err, "插入代理数据失败")
		}
		var err2 error
		newAgentId, err2 = result.LastInsertId()
		if err2 != nil {
			return gerror.Wrap(err2, "获取新代理ID失败")
		}
		newAgentEntity.AgentId = uint(newAgentId)
		// TODO: Refactor later to pass TX to repo methods if needed.

		// 计算 relationship
		var relationship string
		if parentAgent != nil {
			relationship = parentAgent.Relationship + strconv.FormatInt(newAgentId, 10) + "/"
		} else {
			relationship = consts.DefaultAgentRelationshipPrefix + strconv.FormatInt(newAgentId, 10) + "/"
		}

		// 更新 relationship using repository (or DAO within TX for now)
		_, err = dao.Agents.Ctx(ctx).TX(tx).Data(g.Map{
			dao.Agents.Columns().Relationship: relationship,
		}).Where(dao.Agents.Columns().AgentId, newAgentId).Update()
		// TODO: Replace with s.agentRepo.UpdateRelationship(ctx, newAgentId, relationship) if repo accepts TX.
		if err != nil {
			return gerror.Wrap(err, "更新代理关系路径失败")
		}
		
		// 创建代理独立数据库 - 已注释掉，不再创建额外数据库
		// dbName, err = s.createAgentDatabase(ctx, req.Username)
		// if err != nil {
		// 	g.Log().Errorf(ctx, "创建代理数据库失败: %v", err)
		// 	return err
		// }
		// dbCreated = true
		
		// 更新代理记录，添加数据库信息 - 已注释掉，不再创建额外数据库
		// _, err = dao.Agents.Ctx(ctx).TX(tx).Data(g.Map{
		// 	"database_name": dbName,
		// 	"database_created": 1,
		// 	"database_created_at": gtime.Now(),
		// }).Where(dao.Agents.Columns().AgentId, newAgentId).Update()
		// if err != nil {
		// 	return gerror.Wrap(err, "更新代理数据库信息失败")
		// }

		return nil
	})

	if err != nil {
		// 根据错误类型返回不同的错误码
		code := gerror.Code(err)
		if code != gcode.CodeNil { // Correct comparison with the CodeNil variable
			return nil, err // 如果是预定义的业务错误，直接返回
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "添加代理事务失败")
	}

	// 7. 异步同步到 Casdoor（避免影响主事务）
	if service.AgentCasdoor() != nil {
		// 使用 goroutine 异步处理，避免阻塞主流程
		go func() {
			// 使用新的 context 避免请求结束后 context 被取消
			syncCtx := context.Background()
			if err := service.AgentCasdoor().SyncAddAgent(
				syncCtx,
				newAgentEntity,
				req.Password,
			); err != nil {
				// 记录错误，考虑重试机制
				g.Log().Errorf(syncCtx, "同步代理到 Casdoor 失败 - AgentID: %d, Username: %s, Error: %v",
					newAgentEntity.AgentId, newAgentEntity.Username, err)
				// TODO: 可以将失败的同步任务写入队列，后续重试
			} else {
				g.Log().Infof(syncCtx, "成功同步代理到 Casdoor - AgentID: %d, Username: %s",
					newAgentEntity.AgentId, newAgentEntity.Username)
			}
		}()
	}

	res = &v1.AddAgentRes{} // 成功时返回空响应体
	return res, nil
}

// fetchAgentChildren 递归获取子代理
func (s *sSystemLogic) fetchAgentChildren(ctx context.Context, parentItem *v1.AgentListItem, currentLevel int, maxLevel int, botName string) error {
	if currentLevel >= maxLevel {
		return nil
	}

	// 直接使用 DAO 查询子代理
	var childrenEntities []*entity.Agents
	query := dao.Agents.Ctx(ctx).Where(dao.Agents.Columns().ParentAgentId, parentItem.AgentId)
	// 通常还需要排除已软删除的代理
	query = query.WhereNull(dao.Agents.Columns().DeletedAt)
	// 可以根据需要添加排序，例如按创建时间或ID
	// query = query.OrderAsc(dao.Agents.Columns().AgentId)
	err := query.Scan(&childrenEntities)
	if err != nil {
		g.Log().Errorf(ctx, "fetchAgentChildren: dao query failed for parentId %d: %v", parentItem.AgentId, err)
		// 不中断整个过程，只记录错误，或者根据需求决定是否返回错误
		// return gerror.Wrapf(err, "获取子代理失败 (父ID: %d)", parentItem.AgentId)
		return nil
	}

	if len(childrenEntities) == 0 {
		return nil
	}

	parentItem.Children = make([]*v1.AgentListItem, 0, len(childrenEntities))
	for _, childEntity := range childrenEntities {
		childListItem := &v1.AgentListItem{
			Agents: childEntity,
		}

		// 填充时间戳字段
		if childEntity.CreatedAt != nil {
			childListItem.CreatedAt = childEntity.CreatedAt.String()
		}
		if childEntity.UpdatedAt != nil {
			childListItem.UpdatedAt = childEntity.UpdatedAt.String()
		}

		// 构建邀请URL
		if botName != "" && childEntity.InvitationCode != "" {
			childListItem.InvitationUrl = fmt.Sprintf("https://t.me/%s?start=%s", botName, childEntity.InvitationCode)
			g.Log().Debugf(ctx, "fetchAgentChildren - 生成子代理邀请URL: AgentID=%d, InvitationCode='%s', URL='%s'", childEntity.AgentId, childEntity.InvitationCode, childListItem.InvitationUrl)
		} else {
			g.Log().Debugf(ctx, "fetchAgentChildren - 无法生成子代理邀请URL: AgentID=%d, botName='%s', InvitationCode='%s'", childEntity.AgentId, botName, childEntity.InvitationCode)
		}

		err = s.fetchAgentChildren(ctx, childListItem, currentLevel+1, maxLevel, botName)
		if err != nil {
			// 根据需求处理错误，例如记录日志或向上传递
			g.Log().Warningf(ctx, "Error fetching children for agent %d: %v", childEntity.AgentId, err)
		}
		parentItem.Children = append(parentItem.Children, childListItem)
	}
	return nil
}

// GetAgentList 获取代理列表 (包含导出逻辑判断)
func (s *sSystemLogic) GetAgentList(ctx context.Context, req *v1.GetAgentListReq) (res *v1.GetAgentListRes, err error) {
	// Initialize response struct directly
	res = &v1.GetAgentListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.AgentListItem, 0), // 使用新的 AgentListItem
	}

	// 处理导出 (保持原有逻辑，导出扁平数据)
	if req.Export == 1 {
		// Use repository ListAll method for export data
		list, err := s.agentRepo.ListAll(ctx, req) // ListAll 应该返回 []*entity.Agents
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取导出数据失败")
		}

		excelTags := []string{
			"代理ID", "用户名", "代理名称", "邮箱", "手机号", "层级", "上级代理ID", "状态", "邀请码", "关系路径", "创建时间",
		}
		exportData := make([]interface{}, len(list))
		for i, v := range list {
			statusText := "禁用"
			if v.Status == consts.AgentStatusEnabled {
				statusText = "启用"
			}
			exportData[i] = struct {
				AgentId        uint   `json:"agentId"`
				Username       string `json:"username"`
				AgentName      string `json:"agentName"`
				Email          string `json:"email"`
				PhoneNumber    string `json:"phoneNumber"`
				Level          uint   `json:"level"`
				ParentAgentId  uint   `json:"parentAgentId"`
				Status         string `json:"status"`
				InvitationCode string `json:"invitationCode"`
				Relationship   string `json:"relationship"`
				CreatedAt      string `json:"createdAt"`
			}{
				AgentId:   v.AgentId,
				Username:  v.Username,
				AgentName: v.AgentName,
				Email:     v.Email,
				// PhoneNumber:    v.PhoneNumber,
				Level:          v.Level,
				ParentAgentId:  v.ParentAgentId,
				Status:         statusText,
				InvitationCode: v.InvitationCode,
				Relationship:   v.Relationship,
				CreatedAt:      v.CreatedAt.Format("Y-m-d H:i:s"),
			}
		}
		fileName := fmt.Sprintf("代理列表_%s.xlsx", gtime.Now().Format("YmdHis"))
		err = excel.ExportByStructs(ctx, excelTags, exportData, fileName, "代理列表")
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "导出Excel失败")
		}
		ghttp.RequestFromCtx(ctx).Exit()
		return nil, nil
	}

	// 处理列表查询 (获取顶级代理)
	// 假设 req 中没有 ParentAgentId 字段，我们需要让 s.agentRepo.List 支持查询顶级代理
	// 顶级代理的 parent_agent_id 为 0
	// 我们可以创建一个临时的请求对象或修改 repo.List 的方式来传递这个条件
	// 这里我们先假设 s.agentRepo.List(ctx, req) 在 req.Level 为 nil 或 1 时，
	// 或者通过其他内部机制（如 req 中增加一个 unexported parentIdForQuery 字段）
	// 会查询 parent_agent_id = 0 的代理。
	// 过滤条件 (Username, AgentName等) 和分页 (Page, PageSize) 应作用于此查询。

	// 为了明确，我们假设需要一种方式告诉 repo 查询顶级代理。
	// 如果 repo.List 不直接支持，这里可能需要调整。
	// 暂时我们先按计划进行，后续如果 repo 不支持，再调整 repo 或这里的调用。
	// 使用 DAO 直接查询顶级代理 (parent_agent_id = 0) 并应用过滤和分页
	var topLevelAgentsEntities []*entity.Agents
	var total int // Corrected type for m.Count() and res.Page.TotalSize

	m := dao.Agents.Ctx(ctx).Where(dao.Agents.Columns().ParentAgentId, 0)
	m = m.WhereNull(dao.Agents.Columns().DeletedAt) // 排除软删除

	// 应用过滤条件
	if req.Username != "" {
		m = m.WhereLike(dao.Agents.Columns().Username, "%"+req.Username+"%")
	}
	if req.AgentName != "" {
		m = m.WhereLike(dao.Agents.Columns().AgentName, "%"+req.AgentName+"%")
	}
	// if req.PhoneNumber != "" {
	// 	m = m.WhereLike(dao.Agents.Columns().PhoneNumber, "%"+req.PhoneNumber+"%")
	// }
	if req.Email != "" {
		m = m.WhereLike(dao.Agents.Columns().Email, "%"+req.Email+"%")
	}
	if req.Status != nil {
		m = m.Where(dao.Agents.Columns().Status, *req.Status)
	}
	// req.Level 在这里不用于顶级查询的过滤，因为我们已经通过 ParentAgentId = 0 限定了

	// 使用统一的DateRange处理工具
	condition := g.Map{}
	utility.AddDateRangeCondition(condition, req.DateRange)
	for key, value := range condition {
		m = m.Where(key, value)
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取顶级代理总数失败")
	}

	if total == 0 {
		res.Page.TotalSize = 0
		res.Page.TotalPage = 0
		return res, nil
	}

	// 应用分页
	if req.Page > 0 && req.PageSize > 0 { // Ensure Page and PageSize are valid for pagination
		m = m.Page(req.Page, req.PageSize)
	} else {
		// Default or error handling if Page/PageSize are not set for a paginated list
		// For now, assume they are always set if pagination is expected.
		// If not, and we still want all top-level items, remove m.Page() or set a very large PageSize.
		// However, the response struct implies pagination.
	}
	// 可以添加排序
	m = m.OrderDesc(dao.Agents.Columns().AgentId) // 例如按ID降序

	err = m.Scan(&topLevelAgentsEntities)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "扫描顶级代理列表失败")
	}

	maxLevel := g.Cfg().MustGet(ctx, "agent.maxLevel", 3).Int() // 从配置获取，默认3

	// 获取Telegram机器人名称
	botName := config.GetStringWithDefault(ctx, "telegram_bot_setting.name", "")
	// 添加调试日志
	g.Log().Debugf(ctx, "获取Telegram机器人名称: botName='%s'", botName)

	for _, agentEntity := range topLevelAgentsEntities {
		listItem := &v1.AgentListItem{
			Agents: agentEntity,
		}

		// 填充时间戳字段
		if agentEntity.CreatedAt != nil {
			listItem.CreatedAt = agentEntity.CreatedAt.Format("Y-m-d H:i:s")
		}
		if agentEntity.UpdatedAt != nil {
			listItem.UpdatedAt = agentEntity.UpdatedAt.Format("Y-m-d H:i:s")
		}

		// 构建邀请URL
		if botName != "" && agentEntity.InvitationCode != "" {
			listItem.InvitationUrl = fmt.Sprintf("https://t.me/%s?start=%s", botName, agentEntity.InvitationCode)
			g.Log().Debugf(ctx, "生成邀请URL: AgentID=%d, InvitationCode='%s', URL='%s'", agentEntity.AgentId, agentEntity.InvitationCode, listItem.InvitationUrl)
		} else {
			g.Log().Debugf(ctx, "无法生成邀请URL: AgentID=%d, botName='%s', InvitationCode='%s'", agentEntity.AgentId, botName, agentEntity.InvitationCode)
		}

		// 顶级是第1级，子级从第2级开始获取
		err = s.fetchAgentChildren(ctx, listItem, 1, maxLevel, botName) // currentLevel is 1 for top-level agents
		if err != nil {
			// Log error and continue, or decide if it's critical
			g.Log().Errorf(ctx, "Failed to fetch children for top-level agent %d: %v", agentEntity.AgentId, err)
		}
		res.Data = append(res.Data, listItem)
	}

	res.Page.TotalSize = total // total 是顶级代理的数量
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	return res, nil
}

// GetAgentExportData is no longer needed, ListAll from repo is used.
// GetAgent 获取单个代理详情
func (s *sSystemLogic) GetAgent(ctx context.Context, req *v1.GetAgentReq) (res *v1.GetAgentRes, err error) {
	agent, err := s.agentRepo.GetByID(ctx, req.AgentId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询代理详情失败")
	}
	// GetByID returns nil, nil if not found
	if agent == nil {
		return nil, gerror.NewCode(codes.CodeAgentNotFound)
	}

	// Correct assignment for embedded struct
	res = &v1.GetAgentRes{}
	res.Agents = agent // Assign to the embedded field directly

	// 对 TelegramBotToken 进行脱敏处理
	if res.Agents.TelegramBotToken != "" {
		res.Agents.TelegramBotToken = "••••••••••••••••" // 使用占位符替代真实 token
	}

	// 获取Telegram机器人名称
	botName := config.GetStringWithDefault(ctx, "telegram_bot_setting.name", "")
	// 添加调试日志
	g.Log().Debugf(ctx, "GetAgent - 获取Telegram机器人名称: botName='%s'", botName)
	// 构建邀请URL
	if botName != "" && agent.InvitationCode != "" {
		res.InvitationUrl = fmt.Sprintf("https://t.me/%s?start=%s", botName, agent.InvitationCode)
		g.Log().Debugf(ctx, "GetAgent - 生成邀请URL: AgentID=%d, InvitationCode='%s', URL='%s'", agent.AgentId, agent.InvitationCode, res.InvitationUrl)
	} else {
		g.Log().Debugf(ctx, "GetAgent - 无法生成邀请URL: AgentID=%d, botName='%s', InvitationCode='%s'", agent.AgentId, botName, agent.InvitationCode)
	}

	return res, nil
}

// EditAgent 编辑代理基础信息
func (s *sSystemLogic) EditAgent(ctx context.Context, req *v1.EditAgentReq) (res *v1.EditAgentRes, err error) {
	// 1. 检查代理是否存在 using repository
	agent, err := s.agentRepo.GetByID(ctx, req.AgentId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询代理信息失败")
	}
	// GetByID returns nil, nil if not found
	if agent == nil {
		return nil, gerror.NewCode(codes.CodeAgentNotFound)
	}

	// 2. 检查唯一性 (排除自身) using repository
	if req.Email != "" && req.Email != agent.Email {
		existingAgent, err := s.agentRepo.GetByEmail(ctx, req.Email, req.AgentId)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查邮箱唯一性失败")
		}
		if existingAgent != nil {
			return nil, gerror.NewCode(codes.CodeAgentEmailExists)
		}
	}
	// if req.PhoneNumber != "" && req.PhoneNumber != agent.PhoneNumber {
	// 	existingAgent, err := s.agentRepo.GetByPhoneNumber(ctx, req.PhoneNumber, req.AgentId)
	// 	if err != nil {
	// 		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查手机号唯一性失败")
	// 	}
	// 	if existingAgent != nil {
	// 		return nil, gerror.NewCode(codes.CodePhoneExists)
	// 	}
	// }

	// 3. 准备更新数据 (using entity)
	agent.AgentName = req.AgentName
	agent.Email = req.Email
	// agent.PhoneNumber = req.PhoneNumber
	agent.Status = *req.Status
	// 新增字段
	agent.BusinessName = req.BusinessName
	agent.TelegramAccount = req.TelegramAccount
	agent.TelegramBotName = req.TelegramBotName
	// 只有在提供了新的 token 时才更新（不是占位符）
	if req.TelegramBotToken != "" && req.TelegramBotToken != "••••••••••••••••" {
		agent.TelegramBotToken = req.TelegramBotToken
	}
	agent.UpdatedAt = gtime.Now() // Update timestamp

	// 4. 执行更新 using repository
	err = s.agentRepo.Update(ctx, agent)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新代理信息失败")
	}

	// 5. 异步同步到 Casdoor - 暂时关闭
	// if service.AgentCasdoor() != nil {
	// 	go func() {
	// 		syncCtx := context.Background()
	// 		if err := service.AgentCasdoor().SyncUpdateAgent(syncCtx, agent); err != nil {
	// 			g.Log().Errorf(syncCtx, "同步更新代理到 Casdoor 失败 - AgentID: %d, Username: %s, Error: %v",
	// 				agent.AgentId, agent.Username, err)
	// 		} else {
	// 			g.Log().Infof(syncCtx, "成功同步更新代理到 Casdoor - AgentID: %d, Username: %s",
	// 				agent.AgentId, agent.Username)
	// 		}
	// 	}()
	// }

	res = &v1.EditAgentRes{}
	return res, nil
}

// DeleteAgent 批量软删除代理 (包含删除下级逻辑)
func (s *sSystemLogic) DeleteAgent(ctx context.Context, req *v1.DeleteAgentReq) (res *v1.DeleteAgentRes, err error) {
	if len(req.AgentIds) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要删除的代理")
	}

	// 用于存储需要在 Casdoor 中禁用的用户名
	var agentsToDisable []string

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		idsToActuallyDelete := make([]int64, 0, len(req.AgentIds))

		for _, agentId := range req.AgentIds {
			// 1. 检查代理是否存在且未被软删除
			var currentAgent *entity.Agents
			// 使用事务上下文 tx 查询代理信息
			queryErr := dao.Agents.Ctx(ctx).TX(tx).
				Where(dao.Agents.Columns().AgentId, agentId).
				WhereNull(dao.Agents.Columns().DeletedAt). // 确保是未软删除的代理
				Scan(&currentAgent)
			if queryErr != nil {
				return gerror.Wrapf(queryErr, "查询待删除代理 %d 信息失败", agentId)
			}
			if currentAgent == nil {
				g.Log().Warningf(ctx, "尝试删除的代理 ID %d 不存在或已被软删除，已跳过", agentId)
				continue // 跳过这个ID，不中断整个事务，除非业务要求严格一点
			}

			// 2. 检查该代理是否有活动的直接下级
			var activeChildrenCount int
			var countErr error // 声明 countErr
			activeChildrenCount, countErr = dao.Agents.Ctx(ctx).TX(tx).
				Where(dao.Agents.Columns().ParentAgentId, agentId).
				WhereNull(dao.Agents.Columns().DeletedAt). // 只查找未软删除的下级
				Count()                                    // Count() 返回 (int, error)
			if countErr != nil {
				return gerror.Wrapf(countErr, "检查代理 %d 的活动下级数据失败", agentId)
			}

			if activeChildrenCount > 0 {
				// 使用 currentAgent.Username (已从上面查询得到)
				agentIdentifier := fmt.Sprintf("'%s' (ID: %d)", currentAgent.Username, agentId)
				return gerror.NewCodef(codes.CodeAgentHasActiveChildren,
					"代理 %s 尚有活动的下级代理，无法删除", agentIdentifier)
			}

			// 收集需要在 Casdoor 中禁用的用户名
			agentsToDisable = append(agentsToDisable, currentAgent.Username)
			idsToActuallyDelete = append(idsToActuallyDelete, agentId)
		}

		if len(idsToActuallyDelete) == 0 {
			// 如果 req.AgentIds 非空，但 idsToActuallyDelete 为空，说明所有请求的代理ID都无效或已被跳过
			if len(req.AgentIds) > 0 {
				g.Log().Infof(ctx, "没有符合删除条件的代理可供操作。请求数量: %d, 有效待删除数量: %d", len(req.AgentIds), len(idsToActuallyDelete))
				// 可以考虑返回一个特定的用户友好的提示，如果所有ID都被跳过
				// return gerror.NewCode(codes.CodeNoAgentEligibleForDeletion, "所有选择的代理均不符合删除条件或已不存在")
			}
			return nil // 没有实际需要删除的ID
		}

		// 3. 执行批量软删除 (只删除通过检查的ID)
		// s.agentRepo.DeleteSoft 应该能处理事务上下文 (通过 ctx)
		// 如果 DeleteSoft 需要显式事务，则需要修改其接口或使用 dao.Agents.Ctx(ctx).TX(tx).Delete()
		deleteErr := s.agentRepo.DeleteSoft(ctx, idsToActuallyDelete)
		if deleteErr != nil {
			return gerror.Wrap(deleteErr, "软删除代理失败")
		}

		// 可选：处理关联数据，例如禁用关联的 IP 白名单等 (这部分逻辑从原代码保留，但需确认是否仍适用)
		// _, err = dao.IpAccessList.Ctx(ctx).TX(tx).Data(g.Map{dao.IpAccessList.Columns().IsEnabled: 0}).WhereIn(dao.IpAccessList.Columns().AgentId, idsToActuallyDelete).Update()
		// if err != nil {
		//     return gerror.Wrap(err, "禁用关联IP白名单失败")
		// }

		return nil
	})

	if err != nil {
		code := gerror.Code(err)
		// 检查是否是业务预定义错误
		if code != gcode.CodeNil { // 如果 code 不是 CodeNil, 说明是 gerror.NewCodef 等创建的业务错误
			return nil, err
		}
		// 其他错误视为内部错误
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "删除代理事务失败")
	}

	// 异步同步到 Casdoor - 禁用已删除的代理
	if service.AgentCasdoor() != nil && len(agentsToDisable) > 0 {
		go func() {
			syncCtx := context.Background()
			for _, username := range agentsToDisable {
				if err := service.AgentCasdoor().SyncDeleteAgent(syncCtx, username); err != nil {
					g.Log().Errorf(syncCtx, "同步禁用代理到 Casdoor 失败 - Username: %s, Error: %v",
						username, err)
				} else {
					g.Log().Infof(syncCtx, "成功同步禁用代理到 Casdoor - Username: %s", username)
				}
			}
		}()
	}

	res = &v1.DeleteAgentRes{}
	return res, nil
}

// UpdateAgentStatus 批量更新代理状态
func (s *sSystemLogic) UpdateAgentStatus(ctx context.Context, req *v1.UpdateAgentStatusReq) (res *v1.UpdateAgentStatusRes, err error) {
	if len(req.AgentIds) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要更新状态的代理")
	}

	// 检查是否包含自己 (如果需要)
	// currentAgentId := service.Auth().GetIdentity(ctx)
	// if req.Status == consts.AgentStatusDisabled {
	//     for _, id := range req.AgentIds {
	//         if id == currentAgentId {
	//             return nil, gerror.NewCode(codes.CodeCannotDisableSelf)
	//         }
	//     }
	// }

	// 检查代理是否存在 (可选，Update 操作通常允许更新不存在的记录而不报错)
	// count, err := dao.Agents.Ctx(ctx).WhereIn(dao.Agents.Columns().AgentId, req.AgentIds).WhereNull(dao.Agents.Columns().DeletedAt).Count()
	// if err != nil {
	//     return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询代理数量失败")
	// }
	// if count != len(req.AgentIds) {
	//     g.Log().Warningf(ctx, "部分待更新状态的代理不存在或已被删除")
	// }
	// if count == 0 {
	//     return nil, gerror.NewCode(codes.CodeAgentNotFound, "未找到要更新状态的代理")
	// }

	// Use repository method
	err = s.agentRepo.UpdateStatus(ctx, req.AgentIds, req.Status)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新代理状态失败")
	}

	// 可选：如果禁用代理，可能需要处理其下级代理的状态或关联数据

	res = &v1.UpdateAgentStatusRes{}
	return res, nil
}

// UpdateAgentPassword 修改指定代理密码
func (s *sSystemLogic) UpdateAgentPassword(ctx context.Context, req *v1.UpdateAgentPasswordReq) (res *v1.UpdateAgentPasswordRes, err error) {
	// 1. 检查代理是否存在 using repository
	agent, err := s.agentRepo.GetByID(ctx, req.AgentId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询代理信息失败")
	}
	// GetByID returns nil, nil if not found
	if agent == nil {
		return nil, gerror.NewCode(codes.CodeAgentNotFound)
	}

	// 2. 哈希新密码 using auth service
	hashedPassword, err := s.agentAuthService.HashPassword(ctx, req.NewPassword)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "新密码加密失败")
	}

	// 3. 执行更新 using repository
	err = s.agentRepo.UpdatePassword(ctx, req.AgentId, hashedPassword)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新代理密码失败")
	}

	// 4. 异步同步到 Casdoor
	if service.AgentCasdoor() != nil {
		go func() {
			syncCtx := context.Background()
			if err := service.AgentCasdoor().SyncUpdatePassword(syncCtx, agent.Username, req.NewPassword); err != nil {
				g.Log().Errorf(syncCtx, "同步更新密码到 Casdoor 失败 - AgentID: %d, Username: %s, Error: %v",
					agent.AgentId, agent.Username, err)
			} else {
				g.Log().Infof(syncCtx, "成功同步更新密码到 Casdoor - AgentID: %d, Username: %s",
					agent.AgentId, agent.Username)
			}
		}()
	}

	res = &v1.UpdateAgentPasswordRes{}
	return res, nil
}

// ResetAgent2FA 重置指定代理的 Google Authenticator
func (s *sSystemLogic) ResetAgent2FA(ctx context.Context, req *v1.ResetAgent2FAReq) (res *v1.ResetAgent2FARes, err error) {
	// 1. 检查代理是否存在 using repository
	agent, err := s.agentRepo.GetByID(ctx, req.AgentId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询代理信息失败")
	}
	// GetByID returns nil, nil if not found
	if agent == nil {
		return nil, gerror.NewCode(codes.CodeAgentNotFound)
	}

	// 2. 执行重置 using auth service (which uses repo)
	err = s.agentAuthService.Reset2FA(ctx, req.AgentId)
	if err != nil {
		// Wrap error from auth service
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "重置代理2FA失败")
	}

	// 3. 同步删除 Casdoor 中的 MFA 设置
	if service.AgentCasdoor() != nil {
		// 异步执行，避免阻塞主流程
		go func() {
			syncCtx := context.Background()
			if err := service.AgentCasdoor().DeleteMFA(syncCtx, agent.Username); err != nil {
				g.Log().Errorf(syncCtx, "同步删除代理 Casdoor MFA 失败 - AgentID: %d, Username: %s, Error: %v",
					agent.AgentId, agent.Username, err)
			} else {
				g.Log().Infof(syncCtx, "成功同步删除代理 Casdoor MFA - AgentID: %d, Username: %s",
					agent.AgentId, agent.Username)
			}
		}()
	}

	res = &v1.ResetAgent2FARes{}
	return res, nil
}

// GetAgentWhitelist 获取指定代理的 IP 白名单
func (s *sSystemLogic) GetAgentWhitelist(ctx context.Context, req *v1.GetAgentWhitelistReq) (res *v1.GetAgentWhitelistRes, err error) {
	// Initialize response struct directly
	res = &v1.GetAgentWhitelistRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*entity.IpAccessList, 0),
	}

	// 检查代理是否存在 using repository
	agent, err := s.agentRepo.GetByID(ctx, req.AgentId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询代理信息失败")
	}
	if agent == nil {
		return nil, gerror.NewCode(codes.CodeAgentNotFound)
	}

	// Use repository method
	list, total, err := s.agentIPWhitelistRepo.ListByAgentID(ctx, req.AgentId, req.PageRequest)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取IP白名单列表失败")
	}
	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize) // Use common helper
	res.Data = list

	return res, nil
}

// AddAgentWhitelist 为指定代理添加 IP 白名单
func (s *sSystemLogic) AddAgentWhitelist(ctx context.Context, req *v1.AddAgentWhitelistReq) (res *v1.AddAgentWhitelistRes, err error) {
	// 1. 检查代理是否存在 using repository
	agent, err := s.agentRepo.GetByID(ctx, req.AgentId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询代理信息失败")
	}
	if agent == nil {
		return nil, gerror.NewCode(codes.CodeAgentNotFound)
	}

	// 2. 检查 IP 是否已存在于该代理的白名单中 using repository
	existingEntry, err := s.agentIPWhitelistRepo.GetByAgentAndIP(ctx, req.AgentId, req.IpAddress)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查IP白名单是否存在失败")
	}
	if existingEntry != nil {
		return nil, gerror.NewCode(codes.CodeIPWhitelistExists)
	}

	// 3. 准备插入数据 (using entity)
	ipEntity := &entity.IpAccessList{
		AgentId:     req.AgentId, // AgentId is int64 in entity
		ListType:    "whitelist",
		UseType:     "agent_login_whitelist", // Assuming this is the correct type string
		IpAddress:   req.IpAddress,
		Description: req.Description,
		IsEnabled:   1, // 默认启用
		// AddedBy: service.Auth().GetIdentity(ctx).Username // 获取当前操作者用户名, needs Auth service injection
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}

	// 4. 执行插入 using repository
	err = s.agentIPWhitelistRepo.Add(ctx, ipEntity)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "添加IP白名单失败")
	}

	res = &v1.AddAgentWhitelistRes{}
	return res, nil
}

// DeleteAgentWhitelist 删除指定代理的 IP 白名单
func (s *sSystemLogic) DeleteAgentWhitelist(ctx context.Context, req *v1.DeleteAgentWhitelistReq) (res *v1.DeleteAgentWhitelistRes, err error) {
	// 1. 检查记录是否存在并验证类型 using repository
	ipRecord, err := s.agentIPWhitelistRepo.GetByID(ctx, req.IpWhitelistId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询IP白名单记录失败")
	}
	if ipRecord == nil {
		// GetByID returns specific error if not found or wrong type
		return nil, gerror.NewCode(codes.CodeIPWhitelistNotFound)
	}
	// Optional: Check if ipRecord.AgentId matches an expected agent if needed,
	// but deleting by unique ID should be sufficient if ID is primary key.

	// 2. 执行删除 using repository
	err = s.agentIPWhitelistRepo.DeleteByID(ctx, req.IpWhitelistId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "删除IP白名单失败")
	}

	res = &v1.DeleteAgentWhitelistRes{}
	return res, nil
}

// Helper function hashPassword is no longer needed here, handled by agentAuthService.
