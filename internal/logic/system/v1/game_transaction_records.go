package v1

import (
	"context"
	"fmt"
	"strings"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetGameTransactionRecordsList 获取游戏交易记录列表
func (s *sSystemLogic) GetGameTransactionRecordsList(ctx context.Context, req *v1.GetGameTransactionRecordsListReq) (res *v1.GetGameTransactionRecordsListRes, err error) {
	// 使用原生SQL查询以支持多表JOIN
	db := g.DB()

	// 构建基础查询
	query := `
		SELECT
			gtr.id,
			gtr.tenant_id,
			gtr.transaction_id,
			gtr.user_id,
			gtr.snapshot_game_name,
			gtr.snapshot_game_type,
			gtr.provider_code,
			gtr.game_code,
			gtr.session_id,
			gtr.type,
			gtr.status,
			gtr.currency,
			gtr.amount,
			gtr.win_amount,
			gtr.net_amount,
			gtr.provider_transaction_id,
			gtr.reference_id,
			gtr.round_id,
			gtr.bet_id,
			gtr.commission_status,
			gtr.betting_bonus_status,
			gtr.created_at,
			gtr.updated_at,
			uba.telegram_id,
			uba.telegram_username,
			uba.first_name,
			t.username as tenant_username
		FROM game_transaction_records gtr
		LEFT JOIN user_backup_accounts uba ON gtr.user_id = uba.user_id AND uba.is_master = 1 AND uba.deleted_at IS NULL
		LEFT JOIN tenants t ON gtr.tenant_id = t.tenant_id AND t.deleted_at IS NULL
		WHERE 1=1
	`

	// 构建WHERE条件和参数
	var conditions []string
	var params []interface{}

	// 原有条件
	if req.TransactionId != "" {
		conditions = append(conditions, "gtr.transaction_id = ?")
		params = append(params, req.TransactionId)
	}
	if req.UserId != nil {
		conditions = append(conditions, "gtr.user_id = ?")
		params = append(params, *req.UserId)
	}
	if req.GameCatalogId != nil {
		conditions = append(conditions, "gtr.game_catalog_id = ?")
		params = append(params, *req.GameCatalogId)
	}
	if req.ProviderCode != "" {
		conditions = append(conditions, "gtr.provider_code = ?")
		params = append(params, req.ProviderCode)
	}
	if req.GameCode != "" {
		conditions = append(conditions, "gtr.game_code = ?")
		params = append(params, req.GameCode)
	}
	if req.Type != "" {
		conditions = append(conditions, "gtr.type = ?")
		params = append(params, req.Type)
	}
	if req.Status != "" {
		conditions = append(conditions, "gtr.status = ?")
		params = append(params, req.Status)
	}
	if req.Currency != "" {
		conditions = append(conditions, "gtr.currency = ?")
		params = append(params, req.Currency)
	}
	if req.CommissionStatus != "" {
		conditions = append(conditions, "gtr.commission_status = ?")
		params = append(params, req.CommissionStatus)
	}
	if req.BettingBonusStatus != "" {
		conditions = append(conditions, "gtr.betting_bonus_status = ?")
		params = append(params, req.BettingBonusStatus)
	}
	if req.AmountMin != "" {
		conditions = append(conditions, "CAST(gtr.amount as DECIMAL(20,8)) >= ?")
		params = append(params, req.AmountMin)
	}
	if req.AmountMax != "" {
		conditions = append(conditions, "CAST(gtr.amount as DECIMAL(20,8)) <= ?")
		params = append(params, req.AmountMax)
	}
	if req.DateStart != "" {
		conditions = append(conditions, "gtr.created_at >= ?")
		params = append(params, req.DateStart+" 00:00:00")
	}
	if req.DateEnd != "" {
		conditions = append(conditions, "gtr.created_at <= ?")
		params = append(params, req.DateEnd+" 23:59:59")
	}

	// 关键词搜索
	if req.Keyword != "" {
		conditions = append(conditions, "(gtr.snapshot_game_name LIKE ? OR gtr.transaction_id LIKE ? OR gtr.provider_transaction_id LIKE ?)")
		params = append(params, "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 新增Telegram字段过滤
	if req.TelegramId != nil {
		conditions = append(conditions, "uba.telegram_id = ?")
		params = append(params, *req.TelegramId)
	}
	if req.TelegramUsername != "" {
		conditions = append(conditions, "uba.telegram_username LIKE ?")
		params = append(params, "%"+req.TelegramUsername+"%")
	}
	if req.FirstName != "" {
		conditions = append(conditions, "uba.first_name LIKE ?")
		params = append(params, "%"+req.FirstName+"%")
	}

	// 租户用户名过滤
	if req.TenantUsername != "" {
		conditions = append(conditions, "t.username LIKE ?")
		params = append(params, "%"+req.TenantUsername+"%")
	}

	// 添加WHERE条件
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}

	// 获取总数
	countQuery := strings.Replace(query, "SELECT gtr.*, uba.telegram_id, uba.telegram_username, uba.first_name, t.username as tenant_username", "SELECT COUNT(*) as total", 1)
	var total int
	result, err := db.Ctx(ctx).Raw(countQuery, params...).Value()
	if err != nil {
		return nil, gerror.Wrap(err, "count game transaction records failed")
	}
	if result != nil {
		total = gconv.Int(result)
	}

	// 添加排序和分页
	query += " ORDER BY gtr.created_at DESC, gtr.id DESC"
	query += " LIMIT ? OFFSET ?"
	params = append(params, req.PageSize, (req.Page-1)*req.PageSize)

	// 执行查询
	type GameTransactionRecordsWithJoin struct {
		entity.GameTransactionRecords
		TelegramId       *int64  `json:"telegram_id"`
		TelegramUsername *string `json:"telegram_username"`
		FirstName        *string `json:"first_name"`
		TenantUsername   *string `json:"tenant_username"`
	}

	var list []GameTransactionRecordsWithJoin
	err = db.Ctx(ctx).Raw(query, params...).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "get game transaction records list failed")
	}

	// 转换为响应结构
	listItems := make([]*v1.GameTransactionRecordsListItem, len(list))
	for i, item := range list {
		listItems[i] = &v1.GameTransactionRecordsListItem{
			Id:                    item.Id,
			TransactionId:         item.TransactionId,
			UserId:                item.UserId,
			GameCatalogId:         nil, // TODO: handle game_catalog_id properly
			SnapshotGameName:      item.SnapshotGameName,
			SnapshotGameType:      item.SnapshotGameType,
			ProviderCode:          item.ProviderCode,
			GameCode:              item.GameCode,
			SessionId:             item.SessionId,
			Type:                  item.Type,
			Status:                item.Status,
			Currency:              item.Currency,
			Amount:                item.Amount.String(),
			WinAmount:             item.WinAmount.String(),
			NetAmount:             item.NetAmount.String(),
			BalanceBefore:         "", // TODO: add balance fields if needed
			BalanceAfter:          "", // TODO: add balance fields if needed
			ProviderTransactionId: item.ProviderTransactionId,
			ReferenceId:           item.ReferenceId,
			RoundId:               item.RoundId,
			BetId:                 item.BetId,
			CommissionStatus:      item.CommissionStatus,
			BettingBonusStatus:    item.BettingBonusStatus,
			CreatedAt:             item.CreatedAt,
			UpdatedAt:             item.UpdatedAt,
			TelegramId:            item.TelegramId,
			TelegramUsername:      item.TelegramUsername,
			FirstName:             item.FirstName,
			TenantUsername:        item.TenantUsername,
		}
	}

	// 计算统计信息（使用简化版本）
	var stats *v1.GameTransactionRecordsStats
	if len(list) > 0 {
		stats = &v1.GameTransactionRecordsStats{
			TotalTransactions: total,
		}
		// 可以在这里添加更多统计计算
	}

	res = &v1.GetGameTransactionRecordsListRes{
		Total: total,
		List:  listItems,
		Stats: stats,
	}
	return res, nil
}

// GetGameTransactionRecordsDetail 获取游戏交易记录详情
func (s *sSystemLogic) GetGameTransactionRecordsDetail(ctx context.Context, req *v1.GetGameTransactionRecordsDetailReq) (res *v1.GetGameTransactionRecordsDetailRes, err error) {
	// 使用原生SQL查询以支持多表JOIN
	db := g.DB()

	query := `
		SELECT
			gtr.id,
			gtr.tenant_id,
			gtr.transaction_id,
			gtr.user_id,
			gtr.snapshot_game_name,
			gtr.snapshot_game_type,
			gtr.provider_code,
			gtr.game_code,
			gtr.session_id,
			gtr.type,
			gtr.status,
			gtr.currency,
			gtr.amount,
			gtr.win_amount,
			gtr.net_amount,
			gtr.provider_transaction_id,
			gtr.reference_id,
			gtr.round_id,
			gtr.bet_id,
			gtr.commission_status,
			gtr.betting_bonus_status,
			gtr.created_at,
			gtr.updated_at,
			uba.telegram_id,
			uba.telegram_username,
			uba.first_name,
			t.username as tenant_username
		FROM game_transaction_records gtr
		LEFT JOIN user_backup_accounts uba ON gtr.user_id = uba.user_id AND uba.is_master = 1 AND uba.deleted_at IS NULL
		LEFT JOIN tenants t ON gtr.tenant_id = t.tenant_id AND t.deleted_at IS NULL
		WHERE gtr.id = ?
	`

	type GameTransactionRecordsWithJoin struct {
		entity.GameTransactionRecords
		TelegramId       *int64  `json:"telegram_id"`
		TelegramUsername *string `json:"telegram_username"`
		FirstName        *string `json:"first_name"`
		TenantUsername   *string `json:"tenant_username"`
	}

	var record GameTransactionRecordsWithJoin
	err = db.Ctx(ctx).Raw(query, req.Id).Scan(&record)
	if err != nil {
		return nil, gerror.Wrap(err, "get game transaction record detail failed")
	}
	if record.Id == 0 {
		return nil, gerror.New("game transaction record not found")
	}

	res = &v1.GetGameTransactionRecordsDetailRes{
		GameTransactionRecordsListItem: &v1.GameTransactionRecordsListItem{
			Id:                    record.Id,
			TransactionId:         record.TransactionId,
			UserId:                record.UserId,
			GameCatalogId:         nil, // TODO: handle game_catalog_id properly
			SnapshotGameName:      record.SnapshotGameName,
			SnapshotGameType:      record.SnapshotGameType,
			ProviderCode:          record.ProviderCode,
			GameCode:              record.GameCode,
			SessionId:             record.SessionId,
			Type:                  record.Type,
			Status:                record.Status,
			Currency:              record.Currency,
			Amount:                record.Amount.String(),
			WinAmount:             record.WinAmount.String(),
			NetAmount:             record.NetAmount.String(),
			BalanceBefore:         "", // TODO: add balance fields if needed
			BalanceAfter:          "", // TODO: add balance fields if needed
			ProviderTransactionId: record.ProviderTransactionId,
			ReferenceId:           record.ReferenceId,
			RoundId:               record.RoundId,
			BetId:                 record.BetId,
			CommissionStatus:      record.CommissionStatus,
			BettingBonusStatus:    record.BettingBonusStatus,
			CreatedAt:             record.CreatedAt,
			UpdatedAt:             record.UpdatedAt,
			TelegramId:            record.TelegramId,
			TelegramUsername:      record.TelegramUsername,
			FirstName:             record.FirstName,
			TenantUsername:        record.TenantUsername,
		},
	}

	// 获取相关交易记录（同一轮次或会话）
	if record.RoundId != "" || record.SessionId != "" {
		relatedQuery := `
			SELECT 
				gtr.*,
				uba.telegram_id,
				uba.telegram_username,
				uba.first_name,
				t.username as tenant_username
			FROM game_transaction_records gtr
			LEFT JOIN user_backup_accounts uba ON gtr.user_id = uba.user_id AND uba.is_master = 1 AND uba.deleted_at IS NULL
			LEFT JOIN tenants t ON gtr.tenant_id = t.tenant_id AND t.deleted_at IS NULL
			WHERE gtr.id != ?
		`

		var relatedParams []interface{}
		relatedParams = append(relatedParams, req.Id)

		if record.RoundId != "" {
			relatedQuery += " AND gtr.round_id = ?"
			relatedParams = append(relatedParams, record.RoundId)
		} else if record.SessionId != "" {
			relatedQuery += " AND gtr.session_id = ?"
			relatedParams = append(relatedParams, record.SessionId)
		}

		relatedQuery += " ORDER BY gtr.created_at DESC LIMIT 10"

		var relatedRecords []GameTransactionRecordsWithJoin
		err = db.Ctx(ctx).Raw(relatedQuery, relatedParams...).Scan(&relatedRecords)
		if err == nil && len(relatedRecords) > 0 {
			relatedItems := make([]*v1.GameTransactionRecordsListItem, len(relatedRecords))
			for i, related := range relatedRecords {
				relatedItems[i] = &v1.GameTransactionRecordsListItem{
					Id:                    related.Id,
					TransactionId:         related.TransactionId,
					UserId:                related.UserId,
					GameCatalogId:         nil,
					SnapshotGameName:      related.SnapshotGameName,
					SnapshotGameType:      related.SnapshotGameType,
					ProviderCode:          related.ProviderCode,
					GameCode:              related.GameCode,
					SessionId:             related.SessionId,
					Type:                  related.Type,
					Status:                related.Status,
					Currency:              related.Currency,
					Amount:                related.Amount.String(),
					WinAmount:             related.WinAmount.String(),
					NetAmount:             related.NetAmount.String(),
					BalanceBefore:         "",
					BalanceAfter:          "",
					ProviderTransactionId: related.ProviderTransactionId,
					ReferenceId:           related.ReferenceId,
					RoundId:               related.RoundId,
					BetId:                 related.BetId,
					CommissionStatus:      related.CommissionStatus,
					CreatedAt:             related.CreatedAt,
					UpdatedAt:             related.UpdatedAt,
					TelegramId:            related.TelegramId,
					TelegramUsername:      related.TelegramUsername,
					FirstName:             related.FirstName,
					TenantUsername:        related.TenantUsername,
				}
			}
			res.RelatedTransactions = relatedItems
		}
	}

	return res, nil
}

// GetGameTransactionRecordsStats 获取游戏交易记录统计
func (s *sSystemLogic) GetGameTransactionRecordsStats(ctx context.Context, req *v1.GetGameTransactionRecordsStatsReq) (res *v1.GetGameTransactionRecordsStatsRes, err error) {
	stats := &v1.GameTransactionRecordsStats{}

	// 构建基本查询
	m := dao.GameTransactionRecords.Ctx(ctx)

	// 应用过滤条件
	if req.ProviderCode != "" {
		m = m.Where("provider_code", req.ProviderCode)
	}
	if req.GameType != "" {
		m = m.Where("snapshot_game_type", req.GameType)
	}
	if req.TransactionType != "" {
		m = m.Where("type", req.TransactionType)
	}
	if req.Currency != "" {
		m = m.Where("currency", req.Currency)
	}
	if req.DateStart != "" {
		m = m.WhereGTE("created_at", req.DateStart+" 00:00:00")
	}
	if req.DateEnd != "" {
		m = m.WhereLTE("created_at", req.DateEnd+" 23:59:59")
	}

	// 计算基础统计
	totalTransactions, err := m.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "count transactions failed")
	}
	stats.TotalTransactions = totalTransactions

	// 计算金额统计
	amountStats, err := m.Fields(
		"SUM(CAST(amount as DECIMAL(20,8))) as total_bet_amount",
		"SUM(CAST(win_amount as DECIMAL(20,8))) as total_win_amount",
		"SUM(CAST(net_amount as DECIMAL(20,8))) as total_net_amount",
	).One()
	if err != nil {
		return nil, gerror.Wrap(err, "get amount stats failed")
	}

	stats.TotalBetAmount = gconv.String(amountStats["total_bet_amount"])
	stats.TotalWinAmount = gconv.String(amountStats["total_win_amount"])
	stats.TotalNetAmount = gconv.String(amountStats["total_net_amount"])

	// 计算状态统计
	statusStats, err := m.Fields("status", "COUNT(*) as count").Group("status").All()
	if err != nil {
		return nil, gerror.Wrap(err, "get status stats failed")
	}

	for _, stat := range statusStats {
		status := gconv.String(stat["status"])
		count := gconv.Int(stat["count"])
		switch status {
		case "success":
			stats.SuccessfulCount = count
		case "failed":
			stats.FailedCount = count
		case "pending":
			stats.PendingCount = count
		}
	}

	// 根据groupBy参数计算分组统计
	switch req.GroupBy {
	case "provider":
		stats.ProviderBreakdown, _ = s.getProviderStats(ctx, m)
	case "gameType":
		stats.GameTypeBreakdown, _ = s.getGameTypeStats(ctx, m)
	case "transactionType":
		stats.TransactionTypeBreakdown, _ = s.getTransactionTypeStats(ctx, m)
	default:
		// 默认包含所有分组
		stats.ProviderBreakdown, _ = s.getProviderStats(ctx, m)
		stats.GameTypeBreakdown, _ = s.getGameTypeStats(ctx, m)
		stats.TransactionTypeBreakdown, _ = s.getTransactionTypeStats(ctx, m)
	}

	res = &v1.GetGameTransactionRecordsStatsRes{
		GameTransactionRecordsStats: stats,
	}
	return res, nil
}

// ExportGameTransactionRecords 导出游戏交易记录
func (s *sSystemLogic) ExportGameTransactionRecords(ctx context.Context, req *v1.ExportGameTransactionRecordsReq) (res *v1.ExportGameTransactionRecordsRes, err error) {
	// 构建查询
	m := dao.GameTransactionRecords.Ctx(ctx)
	listReq := &v1.GetGameTransactionRecordsListReq{
		TransactionId:      req.TransactionId,
		UserId:             req.UserId,
		GameCatalogId:      req.GameCatalogId,
		ProviderCode:       req.ProviderCode,
		GameCode:           req.GameCode,
		Type:               req.Type,
		Status:             req.Status,
		Currency:           req.Currency,
		CommissionStatus:   req.CommissionStatus,
		BettingBonusStatus: req.BettingBonusStatus,
		AmountMin:          req.AmountMin,
		AmountMax:          req.AmountMax,
		DateStart:          req.DateStart,
		DateEnd:            req.DateEnd,
		Keyword:            req.Keyword,
		TelegramId:         nil, // TODO: Add these fields to export request if needed
		TelegramUsername:   "",
		FirstName:          "",
		TenantUsername:     "",
	}
	m = s.buildGameTransactionRecordsQuery(m, listReq)

	// 获取所有数据（限制最大10000条）
	var records []*entity.GameTransactionRecords
	err = m.Limit(10000).OrderDesc("created_at").Scan(&records)
	if err != nil {
		return nil, gerror.Wrap(err, "get export data failed")
	}

	// 生成文件名
	timestamp := gtime.Now().Format("20060102150405")
	fileName := fmt.Sprintf("game_transaction_records_%s.%s", timestamp, req.Format)

	// TODO: 实现实际的文件导出逻辑（Excel/CSV）
	// 这里应该调用导出服务生成文件并返回下载链接

	res = &v1.ExportGameTransactionRecordsRes{
		FileUrl:  fmt.Sprintf("/downloads/%s", fileName),
		FileName: fileName,
		FileSize: int64(len(records) * 200), // 估算文件大小
	}

	// 记录导出操作日志
	g.Log().Infof(ctx, "exported game transaction records: count=%d, format=%s, file=%s",
		len(records), req.Format, fileName)

	return res, nil
}

// UpdateGameTransactionRecordsCommissionStatus 更新交易记录佣金状态
func (s *sSystemLogic) UpdateGameTransactionRecordsCommissionStatus(ctx context.Context, req *v1.UpdateGameTransactionRecordsCommissionStatusReq) (res *v1.UpdateGameTransactionRecordsCommissionStatusRes, err error) {
	// 使用事务
	err = dao.GameTransactionRecords.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 检查记录是否存在
		count, err := tx.Model("game_transaction_records").WhereIn("id", req.Ids).Count()
		if err != nil {
			return gerror.Wrap(err, "check records exist failed")
		}
		if count != len(req.Ids) {
			return gerror.New("some transaction records not found")
		}

		// 更新佣金状态
		updateData := g.Map{
			"commission_status": req.CommissionStatus,
			"updated_at":        gtime.Now(),
		}

		result, err := tx.Model("game_transaction_records").
			WhereIn("id", req.Ids).
			Update(updateData)
		if err != nil {
			return gerror.Wrap(err, "update commission status failed")
		}

		affected, _ := result.RowsAffected()

		// 记录操作日志
		g.Log().Infof(ctx, "updated commission status: ids=%v, status=%s, affected=%d, remark=%s",
			req.Ids, req.CommissionStatus, affected, req.Remark)

		return nil
	})

	if err != nil {
		return nil, err
	}

	res = &v1.UpdateGameTransactionRecordsCommissionStatusRes{
		UpdatedCount: len(req.Ids),
	}
	return res, nil
}

// GetGameTransactionRecordsAggregate 获取聚合数据
func (s *sSystemLogic) GetGameTransactionRecordsAggregate(ctx context.Context, req *v1.GetGameTransactionRecordsAggregateReq) (res *v1.GetGameTransactionRecordsAggregateRes, err error) {
	m := dao.GameTransactionRecords.Ctx(ctx)

	// 应用过滤条件
	if req.ProviderCode != "" {
		m = m.Where("provider_code", req.ProviderCode)
	}
	if req.GameType != "" {
		m = m.Where("snapshot_game_type", req.GameType)
	}
	if req.Currency != "" {
		m = m.Where("currency", req.Currency)
	}
	if req.DateStart != "" {
		m = m.WhereGTE("created_at", req.DateStart+" 00:00:00")
	}
	if req.DateEnd != "" {
		m = m.WhereLTE("created_at", req.DateEnd+" 23:59:59")
	}

	// 根据时间间隔构建聚合查询
	var dateFormat string
	switch req.Interval {
	case "hour":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m-%d %H:00')"
	case "day":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m-%d')"
	case "week":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%u')"
	case "month":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m')"
	default:
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m-%d')"
	}

	aggregateData, err := m.Fields(
		fmt.Sprintf("%s as period", dateFormat),
		"COUNT(*) as transaction_count",
		"SUM(CAST(amount as DECIMAL(20,8))) as total_bet_amount",
		"SUM(CAST(win_amount as DECIMAL(20,8))) as total_win_amount",
		"SUM(CAST(net_amount as DECIMAL(20,8))) as total_net_amount",
		"COUNT(DISTINCT user_id) as unique_users",
	).Group("period").OrderAsc("period").All()

	if err != nil {
		return nil, gerror.Wrap(err, "get aggregate data failed")
	}

	// 转换结果
	data := make([]*v1.GameTransactionRecordsAggregateItem, len(aggregateData))
	for i, item := range aggregateData {
		data[i] = &v1.GameTransactionRecordsAggregateItem{
			Period:           gconv.String(item["period"]),
			TransactionCount: gconv.Int(item["transaction_count"]),
			TotalBetAmount:   gconv.String(item["total_bet_amount"]),
			TotalWinAmount:   gconv.String(item["total_win_amount"]),
			TotalNetAmount:   gconv.String(item["total_net_amount"]),
			UniqueUsers:      gconv.Int(item["unique_users"]),
		}
	}

	res = &v1.GetGameTransactionRecordsAggregateRes{
		Data: data,
	}
	return res, nil
}

// buildGameTransactionRecordsQuery 构建查询条件
func (s *sSystemLogic) buildGameTransactionRecordsQuery(m *gdb.Model, req *v1.GetGameTransactionRecordsListReq) *gdb.Model {
	if req.TransactionId != "" {
		m = m.Where("transaction_id", req.TransactionId)
	}
	if req.UserId != nil {
		m = m.Where("user_id", *req.UserId)
	}
	if req.GameCatalogId != nil {
		m = m.Where("game_catalog_id", *req.GameCatalogId)
	}
	if req.ProviderCode != "" {
		m = m.Where("provider_code", req.ProviderCode)
	}
	if req.GameCode != "" {
		m = m.Where("game_code", req.GameCode)
	}
	if req.Type != "" {
		m = m.Where("type", req.Type)
	}
	if req.Status != "" {
		m = m.Where("status", req.Status)
	}
	if req.Currency != "" {
		m = m.Where("currency", req.Currency)
	}
	if req.CommissionStatus != "" {
		m = m.Where("commission_status", req.CommissionStatus)
	}
	if req.BettingBonusStatus != "" {
		m = m.Where("betting_bonus_status", req.BettingBonusStatus)
	}
	if req.AmountMin != "" {
		m = m.WhereGTE("CAST(amount as DECIMAL(20,8))", req.AmountMin)
	}
	if req.AmountMax != "" {
		m = m.WhereLTE("CAST(amount as DECIMAL(20,8))", req.AmountMax)
	}
	if req.DateStart != "" {
		m = m.WhereGTE("created_at", req.DateStart+" 00:00:00")
	}
	if req.DateEnd != "" {
		m = m.WhereLTE("created_at", req.DateEnd+" 23:59:59")
	}
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		m = m.Where(m.Builder().Where("snapshot_game_name LIKE ?", keyword).
			WhereOr("transaction_id LIKE ?", keyword).
			WhereOr("user_id LIKE ?", keyword).
			WhereOr("provider_transaction_id LIKE ?", keyword))
	}

	return m
}

// entityToListItem 转换实体到列表项
func (s *sSystemLogic) entityToListItem(record *entity.GameTransactionRecords) *v1.GameTransactionRecordsListItem {
	return &v1.GameTransactionRecordsListItem{
		Id:                    record.Id,
		TransactionId:         record.TransactionId,
		UserId:                record.UserId,
		GameCatalogId:         nil, // TODO: handle game_catalog_id properly
		SnapshotGameName:      record.SnapshotGameName,
		SnapshotGameType:      record.SnapshotGameType,
		ProviderCode:          record.ProviderCode,
		GameCode:              record.GameCode,
		SessionId:             record.SessionId,
		Type:                  record.Type,
		Status:                record.Status,
		Currency:              record.Currency,
		Amount:                record.Amount.String(),
		WinAmount:             record.WinAmount.String(),
		NetAmount:             record.NetAmount.String(),
		BalanceBefore:         "", // TODO: add balance fields if needed
		BalanceAfter:          "", // TODO: add balance fields if needed
		ProviderTransactionId: record.ProviderTransactionId,
		ReferenceId:           record.ReferenceId,
		RoundId:               record.RoundId,
		BetId:                 record.BetId,
		CommissionStatus:      record.CommissionStatus,
		CreatedAt:             record.CreatedAt,
		UpdatedAt:             record.UpdatedAt,
		TelegramId:            nil,
		TelegramUsername:      nil,
		FirstName:             nil,
		TenantUsername:        nil,
	}
}

// calculateTransactionStats 计算交易统计
func (s *sSystemLogic) calculateTransactionStats(ctx context.Context, req *v1.GetGameTransactionRecordsListReq) (*v1.GameTransactionRecordsStats, error) {
	statsReq := &v1.GetGameTransactionRecordsStatsReq{
		ProviderCode:    req.ProviderCode,
		GameType:        "",
		TransactionType: req.Type,
		Currency:        req.Currency,
		DateStart:       req.DateStart,
		DateEnd:         req.DateEnd,
		GroupBy:         "provider",
	}

	statsRes, err := s.GetGameTransactionRecordsStats(ctx, statsReq)
	if err != nil {
		return nil, err
	}

	return statsRes.GameTransactionRecordsStats, nil
}

// getProviderStats 获取提供商统计
func (s *sSystemLogic) getProviderStats(ctx context.Context, m *gdb.Model) ([]v1.ProviderStat, error) {
	data, err := m.Fields(
		"provider_code",
		"COUNT(*) as transaction_count",
		"SUM(CAST(amount as DECIMAL(20,8))) as total_amount",
	).Group("provider_code").All()

	if err != nil {
		return nil, err
	}

	stats := make([]v1.ProviderStat, len(data))
	for i, item := range data {
		stats[i] = v1.ProviderStat{
			ProviderCode:     gconv.String(item["provider_code"]),
			TransactionCount: gconv.Int(item["transaction_count"]),
			TotalAmount:      gconv.String(item["total_amount"]),
		}
	}

	return stats, nil
}

// getGameTypeStats 获取游戏类型统计
func (s *sSystemLogic) getGameTypeStats(ctx context.Context, m *gdb.Model) ([]v1.GameTypeStat, error) {
	data, err := m.Fields(
		"snapshot_game_type as game_type",
		"COUNT(*) as transaction_count",
		"SUM(CAST(amount as DECIMAL(20,8))) as total_amount",
	).Group("snapshot_game_type").All()

	if err != nil {
		return nil, err
	}

	stats := make([]v1.GameTypeStat, len(data))
	for i, item := range data {
		stats[i] = v1.GameTypeStat{
			GameType:         gconv.String(item["game_type"]),
			TransactionCount: gconv.Int(item["transaction_count"]),
			TotalAmount:      gconv.String(item["total_amount"]),
		}
	}

	return stats, nil
}

// getTransactionTypeStats 获取交易类型统计
func (s *sSystemLogic) getTransactionTypeStats(ctx context.Context, m *gdb.Model) ([]v1.TransactionTypeStat, error) {
	data, err := m.Fields(
		"type as transaction_type",
		"COUNT(*) as transaction_count",
		"SUM(CAST(amount as DECIMAL(20,8))) as total_amount",
	).Group("type").All()

	if err != nil {
		return nil, err
	}

	stats := make([]v1.TransactionTypeStat, len(data))
	for i, item := range data {
		stats[i] = v1.TransactionTypeStat{
			TransactionType:  gconv.String(item["transaction_type"]),
			TransactionCount: gconv.Int(item["transaction_count"]),
			TotalAmount:      gconv.String(item["total_amount"]),
		}
	}

	return stats, nil
}
