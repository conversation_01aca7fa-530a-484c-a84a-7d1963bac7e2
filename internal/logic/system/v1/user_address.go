package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"context"
	"math"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// ListUserAddresses 获取用户充值地址列表
func (s *sSystemLogic) ListUserAddresses(ctx context.Context, req *v1.ListUserAddressesReq) (res *v1.ListUserAddressesRes, err error) {
	// 初始化返回结果
	res = &v1.ListUserAddressesRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.UserAddressListItem, 0),
	}

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	condition := g.Map{}

	// 用户ID
	if req.UserId > 0 {
		condition["ua.user_id"] = req.UserId
	}

	// 用户账号（模糊查询）
	if req.Account != "" {
		condition["u.username LIKE"] = "%" + req.Account + "%"
	}

	// 币种ID
	if req.TokenId > 0 {
		condition["ua.token_id"] = req.TokenId
	}

	// 币种名称（模糊查询）
	if req.Symbol != "" {
		condition["ua.name LIKE"] = "%" + req.Symbol + "%"
	}

	// 链名称
	if req.Chan != "" {
		condition["ua.chan"] = req.Chan
	}

	// 地址（模糊查询）
	if req.Address != "" {
		condition["ua.address LIKE"] = "%" + req.Address + "%"
	}

	// 地址类型
	if req.Type != "" {
		condition["ua.type"] = req.Type
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["ua.created_at >="] = dateRange[0] + " 00:00:00"
			condition["ua.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 代理查询条件
	if req.FirstAgentName != "" {
		condition["first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// Telegram查询条件
	if req.TelegramId != "" {
		condition["uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 查询分页数据
	list, total, err := dao.UserAddress.ListAdminUserAddressesWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询充值地址列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 转换DAO结果到API响应格式
	res.Data = make([]*v1.UserAddressListItem, len(list))
	for i, item := range list {
		// 将entity转换为API响应对象
		res.Data[i] = &v1.UserAddressListItem{
			UserAddress: &entity.UserAddress{
				UserAddressId: item.UserAddressId,
				TokenId:       item.TokenId,
				UserId:        item.UserId,
				Lable:         item.Lable,
				Name:          item.Name,
				Chan:          item.Chan,
				Address:       item.Address,
				QrCodeUrl:     item.QrCodeUrl,
				Type:          item.Type,
				CreatedAt:     item.CreatedAt,
				UpdatedAt:     item.UpdatedAt,
			},
			Account:          item.UserAccount,
			FirstAgentName:   item.FirstAgentName,
			SecondAgentName:  item.SecondAgentName,
			ThirdAgentName:   item.ThirdAgentName,
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
		}
	}

	return res, nil
}
