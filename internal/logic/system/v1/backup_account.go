package v1

import (
	"context"
	"math"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetBackupAccounts 获取所有备用账户列表
func (l *sSystemLogic) GetBackupAccounts(ctx context.Context, req *v1.GetBackupAccountsReq) (res *v1.GetBackupAccountsRes, err error) {
	// Initialize response
	res = &v1.GetBackupAccountsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		List: make([]*v1.BackupAccountItem, 0),
	}

	// Use user_ass repository to get list and total count
	list, total, err := l.userAssRepo.List(ctx, req)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取备用账户列表失败")
	}

	// Handle export if requested
	if req.Export == 1 {
		if len(list) == 0 {
			g.Log().Info(ctx, "No backup accounts found to export")
			ghttp.RequestFromCtx(ctx).Exit()
			return nil, nil
		}

		// Define Excel headers
		excelTags := []string{
			"关联ID", "主用户ID", "备用用户ID", "Telegram用户名", "Telegram ID", "创建时间", "更新时间",
		}

		// Prepare export data
		exportData := make([]interface{}, len(list))
		for i, v := range list {
			exportData[i] = struct {
				Id               uint64 `json:"id"`
				AUserId          int64  `json:"aUserId"`
				BUserId          int64  `json:"bUserId"`
				TelegramUsername string `json:"telegramUsername"`
				TelegramId       int64  `json:"telegramId"`
				CreatedAt        string `json:"createdAt"`
				UpdatedAt        string `json:"updatedAt"`
			}{
				Id:               v.UserAss.Id,
				AUserId:          v.UserAss.AUserId,
				BUserId:          v.UserAss.BUserId,
				TelegramUsername: v.TelegramUsername,
				TelegramId:       v.TelegramId,
				CreatedAt:        v.UserAss.CreatedAt.Format("Y-m-d H:i:s"),
				UpdatedAt:        v.UserAss.UpdatedAt.Format("Y-m-d H:i:s"),
			}
		}

		// Call Excel export utility
		fileName := "备用账户列表_" + gtime.Now().Format("YmdHis") + ".xlsx"
		err = excel.ExportByStructs(ctx, excelTags, exportData, fileName, "备用账户")
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "导出Excel失败")
		}
		ghttp.RequestFromCtx(ctx).Exit()
		return nil, nil
	}

	// Set pagination info for non-export case
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// Convert to response format
	if len(list) > 0 {
		res.List = make([]*v1.BackupAccountItem, 0, len(list))
		for _, item := range list {
			backupItem := &v1.BackupAccountItem{
				Id:                   item.UserAss.Id,
				AUserId:              item.UserAss.AUserId,
				BUserId:              item.UserAss.BUserId,
				TelegramUsername:     item.TelegramUsername,
				TelegramId:           item.TelegramId,
				CreatedAt:            item.UserAss.CreatedAt.Format("Y-m-d H:i:s"),
				UpdatedAt:            item.UserAss.UpdatedAt.Format("Y-m-d H:i:s"),
				MainTelegramId:       item.MainTelegramId,
				MainTelegramUsername: item.MainTelegramUsername,
				FirstName:            item.MainFirstName,
			}
			// Set VerifiedAt if not nil
			if item.UserAss.VerifiedAt != nil {
				backupItem.VerifiedAt = item.UserAss.VerifiedAt.Format("Y-m-d H:i:s")
			}
			res.List = append(res.List, backupItem)
		}
	}

	return res, nil
}

// GetUserBackupAccounts 获取用户备用账户列表
func (l *sSystemLogic) GetUserBackupAccounts(ctx context.Context, req *v1.GetUserBackupAccountsReq) (res *v1.GetUserBackupAccountsRes, err error) {
	// Initialize response
	res = &v1.GetUserBackupAccountsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		List: make([]*v1.BackupAccountItem, 0),
	}

	// Check if user exists
	user, err := l.userRepo.GetByID(ctx, uint64(req.UserId))
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询用户信息失败")
	}
	if user == nil {
		return nil, gerror.NewCode(codes.CodeUserNotFound)
	}

	// Use user_ass repository to get list and total count
	list, total, err := l.userAssRepo.ListByAUserID(ctx, req.UserId, req)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取备用账户列表失败")
	}

	// Handle export if requested
	if req.Export == 1 {
		if len(list) == 0 {
			g.Log().Info(ctx, "No backup accounts found to export for user ID:", req.UserId)
			ghttp.RequestFromCtx(ctx).Exit()
			return nil, nil
		}

		// Define Excel headers
		excelTags := []string{
			"关联ID", "主用户ID", "备用用户ID", "Telegram用户名", "Telegram ID", "创建时间", "更新时间",
		}

		// Prepare export data
		exportData := make([]interface{}, len(list))
		for i, v := range list {
			exportData[i] = struct {
				Id               uint64 `json:"id"`
				AUserId          int64  `json:"aUserId"`
				BUserId          int64  `json:"bUserId"`
				TelegramUsername string `json:"telegramUsername"`
				TelegramId       int64  `json:"telegramId"`
				CreatedAt        string `json:"createdAt"`
				UpdatedAt        string `json:"updatedAt"`
			}{
				Id:               v.UserAss.Id,
				AUserId:          v.UserAss.AUserId,
				BUserId:          v.UserAss.BUserId,
				TelegramUsername: v.TelegramUsername,
				TelegramId:       v.TelegramId,
				CreatedAt:        v.UserAss.CreatedAt.Format("Y-m-d H:i:s"),
				UpdatedAt:        v.UserAss.UpdatedAt.Format("Y-m-d H:i:s"),
			}
		}

		// Call Excel export utility
		fileName := "用户备用账户列表_" + gtime.Now().Format("YmdHis") + ".xlsx"
		err = excel.ExportByStructs(ctx, excelTags, exportData, fileName, "用户备用账户")
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "导出Excel失败")
		}
		ghttp.RequestFromCtx(ctx).Exit()
		return nil, nil
	}

	// Set pagination info for non-export case
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// Convert to response format
	if len(list) > 0 {
		res.List = make([]*v1.BackupAccountItem, 0, len(list))
		for _, item := range list {
			backupItem := &v1.BackupAccountItem{
				Id:                   item.UserAss.Id,
				AUserId:              item.UserAss.AUserId,
				BUserId:              item.UserAss.BUserId,
				TelegramUsername:     item.TelegramUsername,
				TelegramId:           item.TelegramId,
				CreatedAt:            item.UserAss.CreatedAt.Format("Y-m-d H:i:s"),
				UpdatedAt:            item.UserAss.UpdatedAt.Format("Y-m-d H:i:s"),
				MainTelegramId:       item.MainTelegramId,
				MainTelegramUsername: item.MainTelegramUsername,
				FirstName:            item.MainFirstName,
			}
			// Set VerifiedAt if not nil
			if item.UserAss.VerifiedAt != nil {
				backupItem.VerifiedAt = item.UserAss.VerifiedAt.Format("Y-m-d H:i:s")
			}
			res.List = append(res.List, backupItem)
		}
	}

	return res, nil
}

// AddUserBackupAccount 添加用户备用账户
func (l *sSystemLogic) AddUserBackupAccount(ctx context.Context, req *v1.AddUserBackupAccountReq) (res *v1.AddUserBackupAccountRes, err error) {
	// Check if A user exists
	aUser, err := l.userRepo.GetByID(ctx, uint64(req.UserId))
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询主用户信息失败")
	}
	if aUser == nil {
		return nil, gerror.NewCode(codes.CodeUserNotFound, "主用户不存在")
	}

	// Check if B user exists
	bUser, err := l.userRepo.GetByID(ctx, uint64(req.BUserId))
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询备用用户信息失败")
	}
	if bUser == nil {
		return nil, gerror.NewCode(codes.CodeUserNotFound, "备用用户不存在")
	}

	// Cannot add self as backup
	if req.UserId == req.BUserId {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "不能将自己设置为备用账户")
	}

	// Check if association already exists (A -> B)
	exists, err := l.userAssRepo.CheckAssociationExists(ctx, req.UserId, req.BUserId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查关联关系失败")
	}
	if exists {
		return nil, gerror.NewCode(codes.CodeBackupAccountAlreadyExists, "备用账户关联已存在")
	}

	// Check if reverse association exists (B -> A) - this is not allowed
	reverseExists, err := l.userAssRepo.CheckReverseAssociationExists(ctx, req.UserId, req.BUserId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查反向关联关系失败")
	}
	if reverseExists {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "不能创建双向关联关系")
	}

	// Create the association
	newId, err := l.userAssRepo.Create(ctx, req.UserId, req.BUserId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "添加备用账户失败")
	}

	res = &v1.AddUserBackupAccountRes{
		Id: newId,
	}
	return res, nil
}

// DeleteUserBackupAccount 删除用户备用账户
func (l *sSystemLogic) DeleteUserBackupAccount(ctx context.Context, req *v1.DeleteUserBackupAccountReq) (res *v1.DeleteUserBackupAccountRes, err error) {
	res = &v1.DeleteUserBackupAccountRes{Success: false}

	// Check if association exists first
	association, err := l.userAssRepo.GetByID(ctx, req.AssociationId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询关联信息失败")
	}
	if association == nil {
		// If not found, consider it successfully deleted (idempotency)
		res.Success = true
		return res, nil
	}

	// Delete association
	err = l.userAssRepo.DeleteByID(ctx, req.AssociationId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "删除备用账户失败")
	}

	res.Success = true
	return res, nil
}

// SetUserBackupAccountVerification 设置备用账户验证状态
func (l *sSystemLogic) SetUserBackupAccountVerification(ctx context.Context, req *v1.SetUserBackupAccountVerificationReq) (res *v1.SetUserBackupAccountVerificationRes, err error) {
	res = &v1.SetUserBackupAccountVerificationRes{Success: false}

	// Check if association exists
	association, err := l.userAssRepo.GetByID(ctx, req.AssociationId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询关联信息失败")
	}
	if association == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "关联记录不存在")
	}

	// Update verification status
	err = l.userAssRepo.UpdateVerifiedStatus(ctx, req.AssociationId, req.Verified)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新验证状态失败")
	}

	res.Success = true
	return res, nil
}
