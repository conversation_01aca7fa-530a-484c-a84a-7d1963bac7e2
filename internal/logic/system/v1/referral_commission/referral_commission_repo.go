package referral_commission

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/referral_commission" // Import the interface package
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// referralCommissionRepository 实现了 IReferralCommissionRepository 接口
type referralCommissionRepository struct{}

// NewReferralCommissionRepository 创建一个新的 referralCommissionRepository 实例
func NewReferralCommissionRepository() referral_commission.IReferralCommissionRepository {
	return &referralCommissionRepository{}
}

// List 获取佣金记录列表（包含用户信息）
func (r *referralCommissionRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*referral_commission.ReferralCommissionWithUserInfoDTO, total int, err error) {
	// 使用 DAO 中的关联查询方法
	daoList, total, err := dao.ReferralCommissions.GetReferralCommissionListWithUserInfo(ctx, page, pageSize, condition)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "referralCommissionRepository.List: 调用 DAO 查询失败")
	}

	// 将 DAO 返回的 map 列表转换为 DTO 列表
	list = make([]*referral_commission.ReferralCommissionWithUserInfoDTO, 0, len(daoList))
	for _, record := range daoList {
		dto := &referral_commission.ReferralCommissionWithUserInfoDTO{}

		// 转换基础实体
		baseEntity := &entity.ReferralCommissions{}
		if err = gconv.Struct(record, baseEntity); err != nil {
			return nil, 0, gerror.Wrap(err, "referralCommissionRepository.List: 转换基础实体失败")
		}
		dto.ReferralCommissions = baseEntity

		// 映射用户信息字段
		dto.ReferrerUsername = gconv.String(record["referrer_username"])
		dto.ReferrerAccount = gconv.String(record["referrer_account"])
		dto.InviteeUsername = gconv.String(record["invitee_username"])
		dto.InviteeAccount = gconv.String(record["invitee_account"])
		dto.TokenSymbol = gconv.String(record["token_symbol"])

		// 映射推荐人代理信息
		dto.ReferrerFirstAgentName = gconv.String(record["referrer_first_agent_name"])
		dto.ReferrerSecondAgentName = gconv.String(record["referrer_second_agent_name"])
		dto.ReferrerThirdAgentName = gconv.String(record["referrer_third_agent_name"])

		// 映射推荐人Telegram信息
		dto.ReferrerTelegramId = gconv.String(record["referrer_telegram_id"])
		dto.ReferrerTelegramUsername = gconv.String(record["referrer_telegram_username"])
		dto.ReferrerFirstName = gconv.String(record["referrer_first_name"])

		// 映射被推荐人代理信息
		dto.InviteeFirstAgentName = gconv.String(record["invitee_first_agent_name"])
		dto.InviteeSecondAgentName = gconv.String(record["invitee_second_agent_name"])
		dto.InviteeThirdAgentName = gconv.String(record["invitee_third_agent_name"])

		// 映射被推荐人Telegram信息
		dto.InviteeTelegramId = gconv.String(record["invitee_telegram_id"])
		dto.InviteeTelegramUsername = gconv.String(record["invitee_telegram_username"])
		dto.InviteeFirstName = gconv.String(record["invitee_first_name"])

		list = append(list, dto)
	}

	return list, total, nil
}

// GetByID (如果需要)
// func (r *referralCommissionRepository) GetByID(ctx context.Context, commissionId int64) (*entity.ReferralCommissions, error) {
// 	var entity *entity.ReferralCommissions
// 	err := dao.ReferralCommissions.Ctx(ctx).Where(dao.ReferralCommissions.Columns().CommissionId, commissionId).Scan(&entity)
// 	if err != nil {
// 		if errors.Is(err, sql.ErrNoRows) {
// 			return nil, nil // Not found
// 		}
// 		return nil, gerror.Wrapf(err, "referralCommissionRepository.GetByID: 查询佣金记录失败, ID: %d", commissionId)
// 	}
// 	return entity, nil
// }

// UpdateStatus (如果需要)
// func (r *referralCommissionRepository) UpdateStatus(ctx context.Context, commissionId int64, status string) error {
// 	_, err := dao.ReferralCommissions.Ctx(ctx).
// 		Data(g.Map{dao.ReferralCommissions.Columns().Status: status}).
// 		Where(dao.ReferralCommissions.Columns().CommissionId, commissionId).
// 		Update()
// 	if err != nil {
// 		return gerror.Wrapf(err, "referralCommissionRepository.UpdateStatus: 更新佣金状态失败, ID: %d, Status: %s", commissionId, status)
// 	}
// 	return nil
// }
