package v1

import (
	"admin-api/api/common" // Added common import
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/consts"
	"admin-api/internal/dao" // Added dao import
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity" // Added entity import
	// utilityCasbin "admin-api/utility/casbin" // Added casbin import
	"context"
	"fmt"
	"strings" // Added strings import

	"github.com/gogf/gf/v2/net/ghttp"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil" // Added gutil import
)

// AddPermission implements permission.IPermissionLogic.
func (s *sSystemLogic) AddPermission(ctx context.Context, txIn gdb.TX, req *v1.AddPermissionReq) (res *v1.AddPermissionRes, err error) {
	var permId int64

	processFunc := func(ctx context.Context, currentTx gdb.TX) error {
		// 1. 校验 Key 唯一性
		exists, e := s.permissionRepo.ExistsByKey(ctx, currentTx, req.Key)
		if e != nil {
			return gerror.Wrap(e, "检查权限 Key 唯一性失败")
		}
		if exists {
			return gerror.NewCode(codes.CodeInvalidParameter, "权限标识 Key 已存在")
		}

		// 2. 校验同级下名称唯一性
		exists, e = s.permissionRepo.ExistsByNameAndPid(ctx, currentTx, req.Name, req.Pid)
		if e != nil {
			return gerror.Wrap(e, "检查同级权限名称唯一性失败")
		}
		if exists {
			return gerror.NewCode(codes.CodeInvalidParameter, "同级下已存在相同名称的权限")
		}

		// 3. 校验状态值
		if !consts.IsValidStatus(req.Status) {
			return gerror.NewCode(codes.CodeInvalidParameter, "无效的状态值")
		}

		// 4. 准备数据
		permissionDo := &do.AdminPermissions{}
		if e = gconv.Struct(req, permissionDo); e != nil {
			return gerror.WrapCode(codes.CodeInternalError, e, "结构体转换失败")
		}

		// 5. 处理树形结构 (Level 和 Tree)
		if req.Pid > 0 {
			parent, e := s.permissionRepo.GetByID(ctx, currentTx, req.Pid)
			if e != nil {
				return gerror.Wrapf(e, "获取父权限信息失败, Pid: %d", req.Pid)
			}
			if parent == nil {
				return gerror.NewCodef(codes.CodeInvalidParameter, "父权限不存在, Pid: %d", req.Pid)
			}
			permissionDo.Level = parent.Level + 1
			permissionDo.Tree = parent.Tree + gconv.String(parent.Id) + ","
		} else {
			permissionDo.Level = 1
			permissionDo.Tree = ",0,"
		}

		// 6. 创建权限
		newId, e := s.permissionRepo.Create(ctx, currentTx, permissionDo)
		if e != nil {
			return e
		}
		permId = newId
		return nil
	}

	if txIn == nil {
		// 如果没有外部事务，则自己开启事务
		err = g.DB().Transaction(ctx, func(ctx context.Context, currentTx gdb.TX) error {
			return processFunc(ctx, currentTx)
		})
	} else {
		// 如果有外部事务，则在外部事务中执行
		err = processFunc(ctx, txIn)
	}

	if err != nil {
		return nil, err
	}

	return &v1.AddPermissionRes{
		Id: permId,
	}, nil
}

// GetAllPermissionList implements permission.IPermissionLogic.
func (s *sSystemLogic) GetAllPermissionList(ctx context.Context, req *v1.GetAllPermissionListReq) (res *v1.GetAllPermissionListRes, err error) {
	// 初始化返回结果
	res = &v1.GetAllPermissionListRes{
		Data: make([]*v1.PermissionTreeNode, 0),
	}

	// 获取所有启用状态的权限
	condition := g.Map{
		dao.AdminPermissions.Columns().Status: consts.StatusEnabled,
	}

	// 使用 DAO 层获取树形结构
	// 注意：dao.AdminPermissions.GetAllPermissionsTree 是直接调用，不通过 s.repo
	tree, err := dao.AdminPermissions.GetAllPermissionsTree(ctx, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取权限树失败")
	}

	res.Data = tree
	return res, nil
}

// GetPermissionList implements permission.IPermissionLogic.
func (s *sSystemLogic) GetPermissionList(ctx context.Context, req *v1.GetPermissionListReq) (res *v1.GetPermissionListRes, err error) {
	// 初始化返回结果
	res = &v1.GetPermissionListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.PermissionTreeNode, 0),
	}

	// 构建查询条件
	condition := g.Map{}
	if req.Name != "" {
		condition["name LIKE ?"] = "%" + req.Name + "%"
	}
	if req.Key != "" {
		condition["key LIKE ?"] = "%" + req.Key + "%"
	}
	if req.Status != nil {
		condition["status"] = *req.Status
	}

	// 获取顶级权限（分页）
	// 在 system.go 中，这里是 parent_key = 0，但在 AdminPermissions 表中，顶级权限的 ParentKey 是空字符串或 "0"
	// 假设 dao.AdminPermissions.Columns().ParentKey 对应数据库中的 parent_key 字段
	// 如果顶级权限的 parent_key 存储的是 "0" (字符串)
	condition[dao.AdminPermissions.Columns().ParentKey] = "0"
	// 如果顶级权限的 parent_key 存储的是 0 (数字) 或 空字符串，需要根据实际情况调整
	// condition[dao.AdminPermissions.Columns().ParentKey] = 0 // or ""

	topPermissions, total, err := s.permissionRepo.ListPaginated(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取权限列表失败")
	}

	if total == 0 {
		res.Page.TotalSize = 0
		res.Page.TotalPage = 0
		return res, nil
	}

	// 获取所有权限（用于构建树）
	// 注意：dao.AdminPermissions.GetAllPermissionsTree 是直接调用，不通过 s.repo
	allPermissions, err := dao.AdminPermissions.GetAllPermissionsTree(ctx, g.Map{})
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取权限树失败")
	}

	// 构建顶级权限的树形结构
	var topPermissionTree []*v1.PermissionTreeNode
	for _, topPerm := range topPermissions {
		var children []*v1.PermissionTreeNode
		// 在所有权限树中查找当前顶级权限的子节点
		for _, allPermNode := range allPermissions {
			if allPermNode.Id == topPerm.Id {
				children = allPermNode.Children
				break
			}
		}
		topPermissionTree = append(topPermissionTree, &v1.PermissionTreeNode{
			AdminPermissions: topPerm, // topPerm 是 *entity.AdminPermissions
			Children:         children,
		})
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)
	res.Data = topPermissionTree

	return res, nil
}

// EditPermission implements permission.IPermissionLogic.
func (s *sSystemLogic) EditPermission(ctx context.Context, req *v1.EditPermissionReq) (res *v1.EditPermissionRes, err error) {
	// 1. 检查权限是否存在 (在事务外检查)
	existingPermission, err := s.permissionRepo.GetByID(ctx, nil, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "获取权限信息失败, ID: %d", req.Id)
	}
	if existingPermission == nil {
		return nil, gerror.NewCodef(codes.CodeNotFound, "权限不存在, ID: %d", req.Id)
	}

	// 2. 禁止修改权限标识 Key
	if existingPermission.Key != req.Key {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "权限标识 Key 不允许修改")
	}

	// 3. 校验 Key 唯一性 (排除自身) - (在事务外检查)
	// 实际上由于上面禁止修改 Key，这一步理论上不会触发 Key 冲突，但保留以防万一或未来需求变更
	exists, err := s.permissionRepo.ExistsByKey(ctx, nil, req.Key, req.Id)
	if err != nil {
		return nil, gerror.Wrap(err, "检查权限 Key 唯一性失败")
	}
	if exists {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "权限标识 Key 已存在")
	}

	// 3. 校验同级下名称唯一性 (排除自身) - (在事务外检查)
	exists, err = s.permissionRepo.ExistsByNameAndPid(ctx, nil, req.Name, req.Pid, req.Id)
	if err != nil {
		return nil, gerror.Wrap(err, "检查同级权限名称唯一性失败")
	}
	if exists {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "同级下已存在相同名称的权限")
	}

	// 4. 校验状态值
	if !consts.IsValidStatus(req.Status) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态值")
	}

	// 5. 准备更新数据
	updateData := g.Map{
		dao.AdminPermissions.Columns().Name:   req.Name,
		dao.AdminPermissions.Columns().Key:    req.Key, // Key 实际上在此处不会被更新，因为前面有校验禁止修改
		dao.AdminPermissions.Columns().Type:   req.Type,
		dao.AdminPermissions.Columns().Remark: req.Remark,
		dao.AdminPermissions.Columns().Status: req.Status,
		dao.AdminPermissions.Columns().Sort:   req.Sort,
	}

	// 6. 处理父权限变更
	parentKeyChanged := false
	// existingPermission.ParentKey 在数据库中是 varchar，可能为空字符串或 "0" 代表顶级
	// req.Pid 是 int64, 0 代表顶级
	if existingPermission.ParentKey == "" { // 假设空字符串代表顶级
		parentKeyChanged = req.Pid != 0
	} else {
		parentKeyInt := gconv.Int64(existingPermission.ParentKey)
		parentKeyChanged = parentKeyInt != req.Pid
	}

	if parentKeyChanged {
		// 检查是否将自己设为自己的父级
		if req.Id == req.Pid {
			return nil, gerror.NewCode(codes.CodeInvalidParameter, "不能将自己设为自己的父级")
		}

		// 检查是否将自己设为自己的子级
		if req.Pid > 0 {
			parentForCheck, errCheck := s.permissionRepo.GetByID(ctx, nil, req.Pid)
			if errCheck != nil {
				return nil, gerror.Wrapf(errCheck, "获取父权限信息失败 (for check), Pid: %d", req.Pid)
			}
			if parentForCheck == nil {
				return nil, gerror.NewCodef(codes.CodeInvalidParameter, "父权限不存在 (for check), Pid: %d", req.Pid)
			}
			if parentForCheck.Tree != "" {
				if strings.Contains(parentForCheck.Tree, ","+gconv.String(req.Id)+",") {
					return nil, gerror.NewCode(codes.CodeInvalidParameter, "不能将自己设为自己子级的父级")
				}
			}
		}

		// 获取所有子权限，准备更新它们的 Level 和 Tree (在事务外获取)
		// existingPermission.Tree 已经是正确的，不需要拼接 existingPermission.Id
		descendants, err := s.permissionRepo.FindDescendantsByTree(ctx, existingPermission.Tree+gconv.String(existingPermission.Id)+",")
		if err != nil {
			return nil, gerror.Wrapf(err, "获取子权限失败, ID: %d", req.Id)
		}

		// 7. 开启事务更新
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			currentUpdateData := gutil.Copy(updateData).(g.Map)
			currentUpdateData[dao.AdminPermissions.Columns().ParentKey] = gconv.String(req.Pid) // ParentKey 是字符串

			newLevel := 1
			newTree := ",0,"

			if req.Pid > 0 {
				parent, e := s.permissionRepo.GetByID(ctx, tx, req.Pid)
				if e != nil {
					return gerror.Wrapf(e, "获取父权限信息失败 (in tx), Pid: %d", req.Pid)
				}
				if parent == nil {
					return gerror.NewCodef(codes.CodeInvalidParameter, "父权限不存在 (in tx), Pid: %d", req.Pid)
				}
				newLevel = parent.Level + 1
				newTree = parent.Tree + gconv.String(parent.Id) + ","
			}
			currentUpdateData[dao.AdminPermissions.Columns().Level] = newLevel
			currentUpdateData[dao.AdminPermissions.Columns().Tree] = newTree

			// 更新当前权限
			if e := s.permissionRepo.Update(ctx, tx, req.Id, currentUpdateData); e != nil {
				return gerror.Wrapf(e, "更新权限失败, ID: %d", req.Id)
			}

			// 如果有子权限，更新它们的 Level 和 Tree
			if len(descendants) > 0 {
				levelDiff := newLevel - existingPermission.Level
				oldSelfTreePath := existingPermission.Tree + gconv.String(existingPermission.Id) + ","
				newSelfTreePath := newTree + gconv.String(req.Id) + "," // req.Id is the same as existingPermission.Id

				batchUpdates := make(map[int64]g.Map)
				for _, desc := range descendants {
					// 子孙节点的 Tree 是以当前节点（旧的）的 Tree + Id + "," 开头的
					if strings.HasPrefix(desc.Tree, oldSelfTreePath) {
						updatedDescLevel := desc.Level + levelDiff
						// 将旧的自身路径前缀替换为新的自身路径前缀
						updatedDescTree := newSelfTreePath + desc.Tree[len(oldSelfTreePath):]
						batchUpdates[desc.Id] = g.Map{
							dao.AdminPermissions.Columns().Level: updatedDescLevel,
							dao.AdminPermissions.Columns().Tree:  updatedDescTree,
						}
					} else {
						g.Log().Warningf(ctx, "Descendant %d tree %s does not match old prefix %s for permission %d", desc.Id, desc.Tree, oldSelfTreePath, req.Id)
					}
				}

				if len(batchUpdates) > 0 {
					if e := s.permissionRepo.BatchUpdateLevelTree(ctx, tx, batchUpdates); e != nil {
						return gerror.Wrap(e, "批量更新子权限失败")
					}
				}
			}
			return nil
		})
		if err != nil {
			return nil, err
		}

	} else {
		// 如果父权限没有变更，直接更新当前权限 (除了 ParentKey, Level, Tree)
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// Key 不应该在这里更新，因为它在前面被禁止修改
			// updateData 已经包含了 Name, Type, Remark, Status, Sort
			if e := s.permissionRepo.Update(ctx, tx, req.Id, updateData); e != nil {
				return gerror.Wrapf(e, "更新权限失败, ID: %d", req.Id)
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	}

	// 8. 返回结果
	return &v1.EditPermissionRes{}, nil
}

// DeletePermission implements permission.IPermissionLogic.
func (s *sSystemLogic) DeletePermission(ctx context.Context, req *v1.DeletePermissionReq) (res *v1.DeletePermissionRes, err error) {
	// 1. 检查权限是否存在 (在事务外检查)
	permissionEntity, err := s.permissionRepo.GetByID(ctx, nil, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "获取权限信息失败, ID: %d", req.Id)
	}
	if permissionEntity == nil {
		return nil, gerror.NewCodef(codes.CodeNotFound, "权限不存在, ID: %d", req.Id)
	}

	// 2. 检查是否有子权限
	hasChildren, err := s.permissionRepo.HasDirectChildren(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查子权限失败, ID: %d", req.Id)
	}
	if hasChildren {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "该权限下有子权限，无法删除")
	}

	// 3. 开启事务删除
	tx, err := g.DB().Begin(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "开启事务失败")
	}
	defer func() {
		if err != nil {
			// 如果后续操作（如Casbin清理）失败，事务也应该回滚
			// 但通常Casbin清理失败不应导致业务操作回滚，所以这里只处理DB事务
			if r := recover(); r != nil {
				tx.Rollback()
				panic(r)
			}
			if err != nil { // Ensure err from DB operations causes rollback
				tx.Rollback()
			}
		}
	}()

	// 4. 执行删除
	if err = s.permissionRepo.Delete(ctx, tx, req.Id); err != nil {
		// defer中的回滚会处理这个err
		return nil, gerror.Wrapf(err, "删除权限失败, ID: %d", req.Id)
	}

	// 5. 提交事务
	if err = tx.Commit(); err != nil {
		// defer中的回滚会处理这个err (虽然commit失败时通常已自动回滚或无需回滚)
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "提交事务失败")
	}

	// // 6. 清理 Casbin 策略 (在事务提交后执行)
	// // 获取 Casbin Enforcer 实例
	// e := utilityCasbin.GetEnforcer()
	// if permissionEntity != nil && permissionEntity.Key != "" {
	// 	// 根据我们的模型 p = sub, dom, obj, act (v0, v1, v2, v3)
	// 	// 我们要删除所有 dom=DefaultDomain 且 obj=permission.Key 的策略
	// 	// RemoveFilteredPolicy(fieldIndex, fieldValues...)
	// 	// fieldIndex 1 对应 p.dom, fieldIndex 2 对应 p.obj
	// 	// 注意：Casbin 的 RemoveFilteredPolicy 的 fieldIndex 是从0开始的策略元素索引
	// 	// ptype (p) | v0 (sub) | v1 (dom) | v2 (obj) | v3 (act)
	// 	// 所以 dom 是第2个元素 (index 1), obj 是第3个元素 (index 2)
	// 	_, casbinErr := e.RemoveFilteredPolicy(1, utilityCasbin.DefaultDomain, permissionEntity.Key)
	// 	if casbinErr != nil {
	// 		// 记录日志，但不返回错误，接受最终一致性
	// 		g.Log().Errorf(ctx, "清理权限 Key '%s' 在 Casbin 中的策略失败: %v", permissionEntity.Key, casbinErr)
	// 	} else {
	// 		g.Log().Infof(ctx, "成功清理权限 Key '%s' 在 Casbin 中的策略", permissionEntity.Key)
	// 	}
	// }

	// 7. 返回结果
	return &v1.DeletePermissionRes{}, nil
}

// PermissionExistsByKey implements permission.IPermissionLogic.
func (s *sSystemLogic) PermissionExistsByKey(ctx context.Context, key string) (bool, error) {
	// 在 system.go 的原始实现中，调用的是 s.permissionRepo.ExistsByKey(ctx, nil, key)
	// nil 代表不传入事务，这通常用于非修改操作的简单检查。
	return s.permissionRepo.ExistsByKey(ctx, nil, key)
}

// GetPermissionByKey implements permission.IPermissionLogic.
func (s *sSystemLogic) GetPermissionByKey(ctx context.Context, tx gdb.TX, key string) (permission *entity.AdminPermissions, err error) {
	return s.permissionRepo.GetByKey(ctx, tx, key)
}
func (s *sSystemLogic) SyncApiPermissions(ctx context.Context, req *v1.SyncApiPermissionsReq) (res *v1.SyncApiPermissionsRes, err error) {
	g.Log().Info(ctx, "开始同步API权限...")

	// 初始化返回结果
	res = &v1.SyncApiPermissionsRes{
		CreatedCount:  0,
		ExistingCount: 0,
		TotalScanned:  0,
	}

	// 1. 获取所有注册的路由
	server := g.Server()
	if server == nil {
		return nil, gerror.NewCode(codes.CodeInternalError, "无法获取HTTP服务器实例")
	}

	// Ensure GetRoutes is available on g.Server() or its actual type.
	// This is a common method in GoFrame for *ghttp.Server.
	var allRoutes []ghttp.RouterItem

	allRoutes = server.GetRoutes()

	if len(allRoutes) == 0 {
		g.Log().Info(ctx, "没有已注册的HTTP路由，无需同步API权限")
		return res, nil
	}

	// 2. 开启数据库事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 3. 数据初始化
		// 用于记录本次扫描到的有效API权限Key
		processedApiKeys := make(map[string]bool)
		// 用于缓存已创建或已存在的路径分段目录权限
		segmentToDirPermissionMap := make(map[string]*entity.AdminPermissions)
		// 统计变量
		createdCount := 0
		updatedCount := 0 // Tracks actual updates to existing permissions
		// totalScanned is already part of 'res' but we'll use a local one for clarity in logs
		currentRunTotalScanned := 0

		// 手动定义支持的HTTP方法列表
		supportedHttpMethods := []string{
			"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", // "CONNECT", "TRACE" are less common for APIs
		}

		// 4. 遍历所有路由
		for _, routeItem := range allRoutes {
			// 跳过中间件和钩子
			if routeItem.Handler == nil ||
				routeItem.Handler.Type == ghttp.HandlerTypeMiddleware ||
				routeItem.Handler.Type == ghttp.HandlerTypeHook {
				continue
			}
			// 跳过非严格路由 (IsStrictRoute usually means it's a user-defined route, not a default/fallback)
			if !routeItem.Handler.Info.IsStrictRoute {
				continue
			}

			// 处理HTTP方法
			currentMethods := []string{routeItem.Method}
			if strings.EqualFold(routeItem.Method, "ALL") {
				currentMethods = supportedHttpMethods
			}

			// 处理每个HTTP方法
			for _, method := range currentMethods {
				currentRunTotalScanned++
				method = strings.ToUpper(method)
				path := routeItem.Route

				// 生成API权限Key
				apiKey := fmt.Sprintf("api:%s:%s", method, path)
				// 生成API权限Name
				apiName := fmt.Sprintf("%s %s", method, path)

				// 确定父权限ID (Pid)
				var pid int64 = 0 // Default to top-level
				var parentDirName string

				// 解析路径获取第一个有效路径分段
				pathSegments := strings.Split(strings.Trim(path, "/"), "/") // Trim slashes before splitting
				var segment string
				if len(pathSegments) > 1 && pathSegments[1] != "" {
					segment = pathSegments[1]
				}

				// 如果有有效的路径分段，处理父目录权限
				if segment != "" {
					// 检查是否已经有此分段的目录权限
					if dirPerm, exists := segmentToDirPermissionMap[segment]; exists {
						pid = dirPerm.Id
					} else {
						// 确定父目录Name - 首字母大写
						if len(segment) > 0 {
							// A more robust way to capitalize: use strings.Title or cases.Title from golang.org/x/text
							// For simplicity here:
							firstChar := strings.ToUpper(segment[0:1])
							restChars := segment[1:]
							parentDirName = firstChar + restChars
						} else {
							parentDirName = segment // Should not happen if segment != ""
						}

						// 父目录Key
						dirKey := fmt.Sprintf("auto_dir_api_segment:%s", segment)

						g.Log().Debugf(ctx, "查询目录权限, Key: %s", dirKey)

						var deletedDirPerm *entity.AdminPermissions
						// Use the transaction-aware model for queries within the transaction
						err := dao.AdminPermissions.Ctx(ctx).TX(tx).
							Where(dao.AdminPermissions.Columns().Key, dirKey).
							WhereNot(dao.AdminPermissions.Columns().DeletedAt, nil). // This means it IS soft-deleted
							Scan(&deletedDirPerm)

						if err != nil {
							return gerror.Wrapf(err, "查询已删除的目录权限失败, Key: %s", dirKey)
						}

						if deletedDirPerm != nil {
							g.Log().Debugf(ctx, "发现已删除的目录权限, Key: %s, ID: %d, 将恢复", dirKey, deletedDirPerm.Id)
							updateData := g.Map{
								dao.AdminPermissions.Columns().DeletedAt: nil, // Unset deleted_at
								dao.AdminPermissions.Columns().UpdatedAt: gtime.Now(),
								dao.AdminPermissions.Columns().Name:      parentDirName, // Update name if needed
								dao.AdminPermissions.Columns().Remark:    "API路径分段自动生成的目录 (已恢复)",
								dao.AdminPermissions.Columns().Status:    consts.StatusEnabled,
							}
							if err := s.permissionRepo.Update(ctx, tx, deletedDirPerm.Id, updateData); err != nil {
								return gerror.Wrapf(err, "恢复已删除的目录权限失败, Key: %s, ID: %d", dirKey, deletedDirPerm.Id)
							}
							// Re-fetch the restored permission to get its full state
							existingDirPerm, err := s.permissionRepo.GetByID(ctx, tx, deletedDirPerm.Id)
							if err != nil || existingDirPerm == nil {
								return gerror.Wrapf(err, "查询恢复后的目录权限失败, ID: %d", deletedDirPerm.Id)
							}
							segmentToDirPermissionMap[segment] = existingDirPerm
							pid = existingDirPerm.Id
							g.Log().Infof(ctx, "恢复API目录权限: %s (ID: %d)", parentDirName, pid)
						} else {
							var existingDirPerm *entity.AdminPermissions
							err := dao.AdminPermissions.Ctx(ctx).TX(tx).
								Where(dao.AdminPermissions.Columns().Key, dirKey).
								WhereNull(dao.AdminPermissions.Columns().DeletedAt). // Not soft-deleted
								Scan(&existingDirPerm)

							if err != nil {
								return gerror.Wrapf(err, "查询目录权限失败, Key: %s", dirKey)
							}

							if existingDirPerm == nil {
								g.Log().Debugf(ctx, "目录权限不存在, Key: %s, 将创建新权限: %s", dirKey, parentDirName)
								dirPermReq := &v1.AddPermissionReq{
									Pid:    0, // 顶级目录
									Name:   parentDirName,
									Key:    dirKey,
									Type:   1, // 目录类型
									Remark: "API路径分段自动生成的目录",
									Status: consts.StatusEnabled,
									Sort:   0,
								}
								dirPermRes, err := s.AddPermission(ctx, tx, dirPermReq)
								if err != nil {
									return gerror.Wrapf(err, "创建目录权限失败, Key: %s", dirKey)
								}
								newDirPerm, err := s.permissionRepo.GetByID(ctx, tx, dirPermRes.Id)
								if err != nil || newDirPerm == nil {
									return gerror.Wrapf(err, "查询新创建的目录权限失败, ID: %d", dirPermRes.Id)
								}
								segmentToDirPermissionMap[segment] = newDirPerm
								pid = newDirPerm.Id // Use newDirPerm.Id which should be same as dirPermRes.Id
								g.Log().Infof(ctx, "创建API目录权限: %s (ID: %d)", parentDirName, pid)
							} else {
								segmentToDirPermissionMap[segment] = existingDirPerm
								pid = existingDirPerm.Id
							}
						}
					}
				}

				// 处理API权限
				var deletedApiPerm *entity.AdminPermissions
				err := dao.AdminPermissions.Ctx(ctx).TX(tx).
					Where(dao.AdminPermissions.Columns().Key, apiKey).
					WhereNot(dao.AdminPermissions.Columns().DeletedAt, nil).
					Scan(&deletedApiPerm)

				if err != nil {
					return gerror.Wrapf(err, "查询已删除的API权限失败, Key: %s", apiKey)
				}

				if deletedApiPerm != nil {
					g.Log().Debugf(ctx, "发现已删除的API权限, Key: %s, ID: %d, 将恢复", apiKey, deletedApiPerm.Id)
					updateData := g.Map{
						dao.AdminPermissions.Columns().DeletedAt: nil,
						dao.AdminPermissions.Columns().UpdatedAt: gtime.Now(),
						dao.AdminPermissions.Columns().Name:      apiName,
						dao.AdminPermissions.Columns().ParentKey: gconv.String(pid), // Ensure ParentKey is updated
						dao.AdminPermissions.Columns().Remark:    "自动同步的API权限 (已恢复)",
						dao.AdminPermissions.Columns().Status:    consts.StatusEnabled,
					}
					// Potentially update Level and Tree here as well if parent changed during restore
					if pid > 0 {
						parentPerm, pErr := s.permissionRepo.GetByID(ctx, tx, pid)
						if pErr != nil || parentPerm == nil {
							return gerror.Wrapf(pErr, "恢复API权限时获取父权限信息失败, Pid: %d", pid)
						}
						updateData[dao.AdminPermissions.Columns().Level] = parentPerm.Level + 1
						updateData[dao.AdminPermissions.Columns().Tree] = parentPerm.Tree + gconv.String(parentPerm.Id) + ","
					} else {
						updateData[dao.AdminPermissions.Columns().Level] = 1
						updateData[dao.AdminPermissions.Columns().Tree] = ",0,"
					}

					if err := s.permissionRepo.Update(ctx, tx, deletedApiPerm.Id, updateData); err != nil {
						return gerror.Wrapf(err, "恢复已删除的API权限失败, Key: %s, ID: %d", apiKey, deletedApiPerm.Id)
					}
					res.ExistingCount++ // Restored is counted as existing for the purpose of this sync
					g.Log().Debugf(ctx, "恢复API权限: %s", apiKey)
				} else {
					var existingApiPerm *entity.AdminPermissions
					err := dao.AdminPermissions.Ctx(ctx).TX(tx).
						Where(dao.AdminPermissions.Columns().Key, apiKey).
						WhereNull(dao.AdminPermissions.Columns().DeletedAt).
						Scan(&existingApiPerm)

					if err != nil {
						return gerror.Wrapf(err, "查询API权限失败, Key: %s", apiKey)
					}

					if existingApiPerm == nil {
						g.Log().Debugf(ctx, "API权限不存在, Key: %s, 将创建新权限", apiKey)
						apiPermReq := &v1.AddPermissionReq{
							Pid:    pid,
							Name:   apiName,
							Key:    apiKey,
							Type:   4, // API类型
							Remark: "自动同步的API权限",
							Status: consts.StatusEnabled,
							Sort:   0,
						}
						// AddPermission should also set Level and Tree based on Pid
						_, err := s.AddPermission(ctx, tx, apiPermReq)
						if err != nil {
							return gerror.Wrapf(err, "创建API权限失败, Key: %s", apiKey)
						}
						createdCount++
						g.Log().Debugf(ctx, "创建API权限: %s", apiKey)
					} else {
						// 已存在，检查是否需要更新
						needUpdate := false
						updateData := g.Map{}

						if existingApiPerm.Name != apiName {
							updateData[dao.AdminPermissions.Columns().Name] = apiName
							needUpdate = true
						}

						// Assuming ParentKey stores the string representation of pid
						existingPid := gconv.Int64(existingApiPerm.ParentKey)
						if existingPid != pid {
							updateData[dao.AdminPermissions.Columns().ParentKey] = gconv.String(pid)
							needUpdate = true
							// If parent ID changes, Level and Tree must be updated
							if pid > 0 {
								parent, pErr := s.permissionRepo.GetByID(ctx, tx, pid)
								if pErr != nil || parent == nil {
									return gerror.Wrapf(pErr, "更新API权限时获取父权限信息失败, Pid: %d", pid)
								}
								updateData[dao.AdminPermissions.Columns().Level] = parent.Level + 1
								updateData[dao.AdminPermissions.Columns().Tree] = parent.Tree + gconv.String(parent.Id) + ","
							} else {
								updateData[dao.AdminPermissions.Columns().Level] = 1
								updateData[dao.AdminPermissions.Columns().Tree] = ",0,"
							}
						}

						if existingApiPerm.Status != consts.StatusEnabled {
							updateData[dao.AdminPermissions.Columns().Status] = consts.StatusEnabled
							needUpdate = true
						}
						// Also check Type, Remark if they can change
						// if existingApiPerm.Type != 4 { ... }
						// if existingApiPerm.Remark != "自动同步的API权限" { ... }

						if needUpdate {
							updateData[dao.AdminPermissions.Columns().UpdatedAt] = gtime.Now()
							if err := s.permissionRepo.Update(ctx, tx, existingApiPerm.Id, updateData); err != nil {
								return gerror.Wrapf(err, "更新API权限失败, ID: %d", existingApiPerm.Id)
							}
							updatedCount++
							g.Log().Debugf(ctx, "更新API权限: %s", apiKey)
						}
						res.ExistingCount++
					}
				}
				processedApiKeys[apiKey] = true
			}
		}

		// 5. 清理不再存在的API权限 (soft delete)
		allDbApiPerms, err := s.permissionRepo.FindByType(ctx, tx, "4") // Type 4 for API
		if err != nil {
			return gerror.Wrap(err, "查询所有API权限失败")
		}

		deletedCount := 0
		for _, apiPerm := range allDbApiPerms {
			if strings.HasPrefix(apiPerm.Key, "api:") && !processedApiKeys[apiPerm.Key] {
				// Check if it's already soft-deleted; if so, skip
				if apiPerm.DeletedAt != nil {
					continue
				}
				// Before soft-deleting, ensure it has no active children (business rule dependent)
				// For this example, we'll just soft-delete.
				// In a real scenario, you might want to prevent deletion if it has roles/users assigned.
				g.Log().Debugf(ctx, "准备软删除过期API权限: %s (ID: %d)", apiPerm.Key, apiPerm.Id)
				softDeleteData := g.Map{
					dao.AdminPermissions.Columns().DeletedAt: gtime.Now(),
					dao.AdminPermissions.Columns().Status:    consts.StatusDisabled, // Optionally mark as disabled
				}
				if err := s.permissionRepo.Update(ctx, tx, apiPerm.Id, softDeleteData); err != nil {
					// Log error but continue if one deletion fails? Or rollback?
					g.Log().Errorf(ctx, "软删除过期API权限失败, ID: %d, Error: %v", apiPerm.Id, err)
					// return gerror.Wrapf(err, "软删除过期API权限失败, ID: %d", apiPerm.Id) // Uncomment to rollback on error
				} else {
					deletedCount++
					g.Log().Debugf(ctx, "软删除过期API权限: %s (ID: %d)", apiPerm.Key, apiPerm.Id)
				}
			}
		}

		// 6. 清理不再需要的自动生成的目录权限 (soft delete if empty)
		dirPerms, err := s.permissionRepo.FindByKeyPrefix(ctx, tx, "auto_dir_api_segment:")
		if err != nil {
			return gerror.Wrap(err, "查询自动生成的目录权限失败")
		}

		for _, dirPerm := range dirPerms {
			if dirPerm.DeletedAt != nil { // Skip already soft-deleted
				continue
			}
			hasChildren, err := s.permissionRepo.HasDirectChildren(ctx, dirPerm.Id) // This needs to check non-soft-deleted children
			if err != nil {
				return gerror.Wrapf(err, "检查目录权限子节点失败, ID: %d", dirPerm.Id)
			}
			if !hasChildren {
				g.Log().Debugf(ctx, "准备软删除空目录权限: %s (ID: %d)", dirPerm.Key, dirPerm.Id)
				softDeleteData := g.Map{
					dao.AdminPermissions.Columns().DeletedAt: gtime.Now(),
					dao.AdminPermissions.Columns().Status:    consts.StatusDisabled,
				}
				if err := s.permissionRepo.Update(ctx, tx, dirPerm.Id, softDeleteData); err != nil {
					g.Log().Errorf(ctx, "软删除空目录权限失败, ID: %d, Error: %v", dirPerm.Id, err)
					// return gerror.Wrapf(err, "软删除空目录权限失败, ID: %d", dirPerm.Id) // Uncomment to rollback
				} else {
					g.Log().Debugf(ctx, "软删除空目录权限: %s (ID: %d)", dirPerm.Key, dirPerm.Id)
				}
			}
		}

		res.CreatedCount = createdCount
		// res.ExistingCount is already accumulated
		res.TotalScanned = currentRunTotalScanned

		// Note: res.ExistingCount includes permissions that were restored or updated.
		// If ExistingCount should only be those that were found and *not* modified/restored, adjust logic.
		// The log message below uses `res.ExistingCount - updatedCount` for "unchanged existing", which might be clearer.
		// However, the original code has `res.ExistingCount` accumulate for both updated and restored.
		// For this example, I'll stick to the original accumulation for res.ExistingCount.
		g.Log().Infof(ctx, "API权限同步完成: 总扫描 %d, 新创建 %d, 已存在/更新/恢复 %d (其中更新 %d), 软删除 %d",
			currentRunTotalScanned, createdCount, res.ExistingCount, updatedCount, deletedCount)
		return nil
	})

	if err != nil {
		// Log the detailed error from the transaction
		g.Log().Errorf(ctx, "同步API权限事务失败: %+v", err)
		return nil, gerror.Wrap(err, "同步API权限事务处理失败") // Keep original error for user
	}

	return res, nil
}
