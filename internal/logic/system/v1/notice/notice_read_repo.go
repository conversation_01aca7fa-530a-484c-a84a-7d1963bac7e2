package notice

import (
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/notice" // Import the interface package
	"context"
	"database/sql"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type noticeReadRepository struct{}

// NewNoticeReadRepository creates and returns a new instance of INoticeReadRepository.
func NewNoticeReadRepository() notice.INoticeReadRepository {
	return &noticeReadRepository{}
}

// MarkReadOrIncrementClick marks a notice as read for a user or increments the click count if already read.
func (r *noticeReadRepository) MarkReadOrIncrementClick(ctx context.Context, userId int64, noticeId int64) error {
	// Try to find existing record first
	var readRecord *entity.AdminNoticeRead
	err := dao.AdminNoticeRead.Ctx(ctx).
		Where(dao.AdminNoticeRead.Columns().MemberId, userId).
		Where(dao.AdminNoticeRead.Columns().NoticeId, noticeId).
		Scan(&readRecord)

	if err != nil && err != sql.ErrNoRows {
		return gerror.WrapCodef(codes.CodeInternalError, err, "查询阅读记录失败 (UserID: %d, NoticeID: %d)", userId, noticeId)
	}

	if readRecord == nil { // Record doesn't exist, create it
		_, err = dao.AdminNoticeRead.Ctx(ctx).Data(do.AdminNoticeRead{
			MemberId:  userId,
			NoticeId:  noticeId,
			Clicks:    1, // First read, set clicks to 1
			CreatedAt: gtime.Now(),
			UpdatedAt: gtime.Now(),
		}).Insert()
		if err != nil {
			return gerror.WrapCodef(codes.CodeInternalError, err, "创建阅读记录失败 (UserID: %d, NoticeID: %d)", userId, noticeId)
		}
	} else { // Record exists, increment clicks
		_, err = dao.AdminNoticeRead.Ctx(ctx).
			Data(g.Map{
				dao.AdminNoticeRead.Columns().Clicks:    gdb.Raw("clicks + 1"),
				dao.AdminNoticeRead.Columns().UpdatedAt: gtime.Now(),
			}).
			Where(dao.AdminNoticeRead.Columns().Id, readRecord.Id).
			Update()
		if err != nil {
			return gerror.WrapCodef(codes.CodeInternalError, err, "增加阅读次数失败 (RecordID: %d)", readRecord.Id)
		}
	}
	return nil
}

// GetReadStatusMap retrieves a map of notice IDs to their read status record for a specific user.
func (r *noticeReadRepository) GetReadStatusMap(ctx context.Context, userId int64, noticeIds []int64) (readMap map[int64]*entity.AdminNoticeRead, err error) {
	readMap = make(map[int64]*entity.AdminNoticeRead)
	if len(noticeIds) == 0 {
		return readMap, nil
	}

	var readRecords []*entity.AdminNoticeRead
	err = dao.AdminNoticeRead.Ctx(ctx).
		Where(dao.AdminNoticeRead.Columns().MemberId, userId).
		WhereIn(dao.AdminNoticeRead.Columns().NoticeId, noticeIds).
		Scan(&readRecords)

	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "批量查询阅读状态失败 (UserID: %d, NoticeIDs: %v)", userId, noticeIds)
	}

	for _, record := range readRecords {
		if record != nil {
			readMap[record.NoticeId] = record
		}
	}
	return readMap, nil
}

// GetReadMemberIDs retrieves a list of member IDs who have read a specific notice.
func (r *noticeReadRepository) GetReadMemberIDs(ctx context.Context, noticeId int64) ([]int64, error) {
	ids, err := dao.AdminNoticeRead.Ctx(ctx).
		Where(dao.AdminNoticeRead.Columns().NoticeId, noticeId).
		Array(dao.AdminNoticeRead.Columns().MemberId)
	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询已读用户ID列表失败 (NoticeID: %d)", noticeId)
	}
	return gconv.Int64s(ids), nil
}

// DeleteByNoticeIDs deletes all read records associated with given notice IDs.
func (r *noticeReadRepository) DeleteByNoticeIDs(ctx context.Context, noticeIds []int64) error {
	if len(noticeIds) == 0 {
		return nil
	}
	_, err := dao.AdminNoticeRead.Ctx(ctx).
		WhereIn(dao.AdminNoticeRead.Columns().NoticeId, noticeIds).
		Delete()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "删除公告阅读记录失败 (NoticeIDs: %v)", noticeIds)
	}
	return nil
}
