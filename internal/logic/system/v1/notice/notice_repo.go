package notice

import (
	"admin-api/internal/codes"
	deptconsts "admin-api/internal/consts/admin"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/notice" // Import the interface package
	"context"
	"database/sql"
	"fmt" // Add fmt import

	// "github.com/gogf/gf/v2/database/gdb" // Removed unused import
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type noticeRepository struct{}

// NewNoticeRepository creates and returns a new instance of INoticeRepository.
func NewNoticeRepository() notice.INoticeRepository {
	return &noticeRepository{}
}

// Create inserts a new notice record.
func (r *noticeRepository) Create(ctx context.Context, noticeDo *do.AdminNotice) (id int64, err error) {
	// Ensure timestamps
	if noticeDo.CreatedAt == nil {
		noticeDo.CreatedAt = gtime.Now()
	}
	if noticeDo.UpdatedAt == nil {
		noticeDo.UpdatedAt = gtime.Now()
	}

	result, err := dao.AdminNotice.Ctx(ctx).Data(noticeDo).OmitEmpty().Insert()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "创建公告失败")
	}
	id, err = result.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新公告ID失败")
	}
	return id, nil
}

// Update updates notice fields.
func (r *noticeRepository) Update(ctx context.Context, id int64, data g.Map) error {
	if len(data) == 0 {
		return nil
	}
	data[dao.AdminNotice.Columns().UpdatedAt] = gtime.Now() // Ensure UpdatedAt is set

	_, err := dao.AdminNotice.Ctx(ctx).
		Data(data).
		Where(dao.AdminNotice.Columns().Id, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新公告失败 (ID: %d)", id)
	}
	return nil
}

// Delete performs a soft delete on notices.
func (r *noticeRepository) Delete(ctx context.Context, ids []int64, deletedBy int64) error {
	if len(ids) == 0 {
		return nil
	}
	_, err := dao.AdminNotice.Ctx(ctx).
		Data(g.Map{
			dao.AdminNotice.Columns().DeletedAt: gtime.Now(),
			// dao.AdminNotice.Columns().DeletedBy: deletedBy, // Removed DeletedBy as it's not in columns
		}).
		WhereIn(dao.AdminNotice.Columns().Id, ids).
		WhereNull(dao.AdminNotice.Columns().DeletedAt).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "软删除公告失败 (IDs: %v)", ids)
	}
	// Note: Also need to delete related AdminNoticeRead records, handled in logic layer or here?
	// Let's assume logic layer handles calling INoticeReadRepository.DeleteByNoticeIDs
	return nil
}

// GetByID retrieves a single admin notice by ID.
func (r *noticeRepository) GetByID(ctx context.Context, id int64) (*entity.AdminNotice, error) {
	var notice *entity.AdminNotice
	err := dao.AdminNotice.Ctx(ctx).
		Where(dao.AdminNotice.Columns().Id, id).
		WhereNull(dao.AdminNotice.Columns().DeletedAt).
		Scan(&notice)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeNoticeNotFound, "公告不存在 (ID: %d)", id)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询公告失败 (ID: %d)", id)
	}
	return notice, nil
}

// ListAdmin retrieves a paginated list of admin notices based on conditions.
func (r *noticeRepository) ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminNotice, total int, err error) {
	m := dao.AdminNotice.Ctx(ctx).WhereNull(dao.AdminNotice.Columns().DeletedAt)
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	total, err = m.Count()
	if err != nil || total == 0 {
		if err != nil && err != sql.ErrNoRows {
			return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "获取公告总数失败")
		}
		return []*entity.AdminNotice{}, 0, nil
	}

	list = make([]*entity.AdminNotice, 0)
	// Correct OrderDesc usage: chain Order calls
	err = m.Page(page, pageSize).OrderDesc(dao.AdminNotice.Columns().Sort).OrderDesc(dao.AdminNotice.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询公告列表失败")
	}
	return list, total, nil
}

// ListUser retrieves a list of notices visible to a specific user based on conditions.
func (r *noticeRepository) ListUser(ctx context.Context, userId int64, condition g.Map) (list []*entity.AdminNotice, err error) {
	// Base query for published and not deleted notices
	m := dao.AdminNotice.Ctx(ctx).
		Where(dao.AdminNotice.Columns().Status, deptconsts.NoticeStatusPublished).
		WhereNull(dao.AdminNotice.Columns().DeletedAt)

	// Apply type and tag conditions if provided
	if noticeType, ok := condition[dao.AdminNotice.Columns().Type]; ok {
		m = m.Where(dao.AdminNotice.Columns().Type, noticeType)
	}
	if tag, ok := condition[dao.AdminNotice.Columns().Tag]; ok {
		m = m.Where(dao.AdminNotice.Columns().Tag, tag)
	}

	// Filter by receiver for private messages or include all notifications
	m = m.Where(g.Map{
		// Either it's a notification OR it's a private message and the user is in the receiver list
		fmt.Sprintf("%s = ? OR (%s = ? AND JSON_CONTAINS(%s, JSON_QUOTE(?)))",
			dao.AdminNotice.Columns().Type,
			dao.AdminNotice.Columns().Type,
			dao.AdminNotice.Columns().Receiver): []interface{}{
			deptconsts.NoticeTypeNotification,
			deptconsts.NoticeTypePrivateMessage,
			gconv.String(userId), // Need to cast userId to string for JSON_CONTAINS
		},
	})

	list = make([]*entity.AdminNotice, 0)
	// Correct OrderDesc usage: chain Order calls
	err = m.OrderDesc(dao.AdminNotice.Columns().Sort).OrderDesc(dao.AdminNotice.Columns().Id).Scan(&list)
	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询用户公告列表失败 (UserID: %d)", userId)
	}
	return list, nil
}

// GetUserUnreadNoticeCount calculates the number of unread notices for a user.
func (r *noticeRepository) GetUserUnreadNoticeCount(ctx context.Context, userId int64) (count int, err error) {
	// This is complex as it involves checking which notices the user *should* see
	// and comparing against the read records. This might be better handled in the logic layer
	// by combining results from ListUser and INoticeReadRepository.
	// Returning a placeholder implementation for now.
	// A more accurate DB query would be needed here.
	g.Log().Warning(ctx, "GetUserUnreadNoticeCount in noticeRepository is a placeholder and may not be accurate.")

	// Placeholder: Count all published notices potentially visible to the user
	// This doesn't account for read status accurately.
	m := dao.AdminNotice.Ctx(ctx).
		Where(dao.AdminNotice.Columns().Status, deptconsts.NoticeStatusPublished).
		WhereNull(dao.AdminNotice.Columns().DeletedAt).
		Where(g.Map{
			fmt.Sprintf("%s = ? OR (%s = ? AND JSON_CONTAINS(%s, JSON_QUOTE(?)))",
				dao.AdminNotice.Columns().Type,
				dao.AdminNotice.Columns().Type,
				dao.AdminNotice.Columns().Receiver): []interface{}{
				deptconsts.NoticeTypeNotification,
				deptconsts.NoticeTypePrivateMessage,
				gconv.String(userId),
			},
		})

	totalVisible, err := m.Count()
	if err != nil {
		return 0, gerror.WrapCodef(codes.CodeInternalError, err, "计算用户可见公告总数失败 (UserID: %d)", userId)
	}

	// Placeholder: Count read records for this user
	readCount, err := dao.AdminNoticeRead.Ctx(ctx).Where(dao.AdminNoticeRead.Columns().MemberId, userId).Count()
	if err != nil {
		return 0, gerror.WrapCodef(codes.CodeInternalError, err, "计算用户已读公告数失败 (UserID: %d)", userId)
	}

	// This subtraction is NOT accurate because readCount includes notices the user might no longer be eligible to see.
	unreadCount := totalVisible - readCount
	if unreadCount < 0 {
		unreadCount = 0 // Cannot be negative
	}

	return unreadCount, nil
}
