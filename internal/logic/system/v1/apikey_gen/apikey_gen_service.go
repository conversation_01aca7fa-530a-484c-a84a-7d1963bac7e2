package apikey_gen

import (
	"admin-api/internal/codes"
	"admin-api/internal/service/system/apikey_gen" // Import the interface package
	"context"
	"crypto/rand"
	"encoding/base64"

	"github.com/gogf/gf/v2/errors/gerror"
	// "github.com/gogf/gf/v2/util/gconv" // Not strictly needed for random generation
	// "github.com/gogf/gf/v2/util/grand" // Can use crypto/rand for better randomness
)

type apiKeyGenerationService struct{}

// NewApiKeyGenerationService creates and returns a new instance of IApiKeyGenerationService.
func NewApiKeyGenerationService() apikey_gen.IApiKeyGenerationService {
	return &apiKeyGenerationService{}
}

// generateRandomKey generates a secure random string of specified byte length, encoded in base64 URL encoding.
func generateRandomKey(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	// Use URL encoding to avoid issues with '+' and '/' characters if keys are used in URLs
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// Generate creates a new unique API key and a corresponding secret key.
func (s *apiKeyGenerationService) Generate(ctx context.Context) (apiKey string, secretKey string, err error) {
	// Generate API Key (e.g., 32 bytes, base64 encoded)
	apiKey, err = generateRandomKey(32)
	if err != nil {
		return "", "", gerror.WrapCode(codes.CodeInternalError, err, "生成 API Key 失败")
	}

	// Generate Secret Key (e.g., 64 bytes, base64 encoded)
	secretKey, err = generateRandomKey(64)
	if err != nil {
		// Consider logging the partially generated apiKey if needed for debugging
		return "", "", gerror.WrapCode(codes.CodeInternalError, err, "生成 Secret Key 失败")
	}

	// Potentially add prefix or other formatting if desired
	// apiKey = "xp_live_" + apiKey // Example prefix

	return apiKey, secretKey, nil
}
