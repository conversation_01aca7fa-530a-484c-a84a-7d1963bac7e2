package v1

import (
	"context"
	// "encoding/json" // 移除未使用的导入
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/consts"
	"admin-api/internal/utility"
	deptconsts "admin-api/internal/consts/admin" // 导入包含数据范围常量的包

	// "admin-api/internal/dao"                     // 移除未使用的 dao 导入
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	// "strings" // 引入 strings 包

	// "admin-api/utility/casbin" // 修改为 utility/casbin 包

	"github.com/gogf/gf/v2/database/gdb"

	// "github.com/gogf/gf/v2/encoding/gjson" // 移除未使用的导入
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetRoleList 获取角色列表
func (s *sSystemLogic) GetRoleList(ctx context.Context, req *v1.GetRoleListReq) (res *v1.GetRoleListRes, err error) {
	// Initialize response struct directly
	res = &v1.GetRoleListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*entity.AdminRole, 0),
	}

	condition := g.Map{}
	if req.Name != "" {
		condition["name LIKE ?"] = "%" + req.Name + "%" // Use string literal
	}
	if req.Key != "" {
		condition["key LIKE ?"] = "%" + req.Key + "%" // Use string literal
	}
	if req.Status != nil {
		condition["status"] = *req.Status // Use string literal
	}
	
	// 使用统一的DateRange处理工具
	utility.AddDateRangeCondition(condition, req.DateRange)

	// Use repository to get the list
	list, total, err := s.roleRepo.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "获取角色列表失败")
	}

	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)
	res.Data = list

	return res, nil
}

// GetRole 获取角色详情
func (s *sSystemLogic) GetRole(ctx context.Context, req *v1.GetRoleReq) (res *v1.GetRoleRes, err error) {
	// Use repository to get the role by ID
	role, err := s.roleRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询角色详情失败, ID: %d", req.Id)
	}
	if role == nil {
		return nil, gerror.NewCodef(codes.CodeRoleNotFound, "角色不存在, ID: %d", req.Id)
	}
	// Initialize response struct directly
	res = &v1.GetRoleRes{
		Data: role,
	}
	return res, nil
}

// AddRole 新增角色
func (s *sSystemLogic) AddRole(ctx context.Context, req *v1.AddRoleReq) (res *v1.AddRoleRes, err error) {
	// 1. 校验 Key 唯一性 (使用 Repository)
	exists, err := s.roleRepo.IsKeyExist(ctx, req.Key)
	if err != nil {
		return nil, gerror.Wrap(err, "检查角色 Key 唯一性失败")
	}
	if exists {
		return nil, gerror.NewCode(codes.CodeRoleKeyExists)
	}

	// 2. 校验 Status
	if !consts.IsValidStatus(req.Status) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态值")
	}

	// 3. 准备数据 (Pid, Level, Tree 暂时不处理，如果需要层级管理再添加)
	roleDo := &do.AdminRole{}
	if err = gconv.Struct(req, roleDo); err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "结构体转换失败")
	}
	// 设置默认数据范围为本部门
	roleDo.DataScope = deptconsts.DataScopeDept

	// 4. 执行插入 (使用 Repository)
	lastInsertId, err := s.roleRepo.Create(ctx, roleDo)
	if err != nil {
		return nil, gerror.Wrap(err, "新增角色失败")
	}

	// Initialize response struct directly
	res = &v1.AddRoleRes{
		Id: lastInsertId,
	}
	return res, nil
}

// EditRole 编辑角色 (仅基础信息)
func (s *sSystemLogic) EditRole(ctx context.Context, req *v1.EditRoleReq) (res *v1.EditRoleRes, err error) {
	// 1. 校验角色是否存在 (使用 Repository)
	existingRole, err := s.roleRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查角色是否存在失败, ID: %d", req.Id)
	}
	if existingRole == nil {
		return nil, gerror.NewCodef(codes.CodeRoleNotFound, "要编辑的角色不存在, ID: %d", req.Id)
	}

	// 2. 校验 Key 唯一性 (排除自身, 使用 Repository)
	exists, err := s.roleRepo.IsKeyExist(ctx, req.Key, req.Id)
	if err != nil {
		return nil, gerror.Wrap(err, "检查角色 Key 唯一性失败")
	}
	if exists {
		return nil, gerror.NewCode(codes.CodeRoleKeyExists)
	}

	// 3. 校验 Status
	if !consts.IsValidStatus(req.Status) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态值")
	}

	// 4. 准备数据 (使用 g.Map 传递给 DAO，避免更新权限字段)
	updateData := g.Map{
		"name":   req.Name,   // Use string literal
		"key":    req.Key,    // Use string literal
		"remark": req.Remark, // Use string literal
		"sort":   req.Sort,   // Use string literal
		"status": req.Status, // Use string literal
	}

	// 5. 执行更新 (使用 Repository)
	err = s.roleRepo.Update(ctx, req.Id, updateData)
	if err != nil {
		return nil, gerror.Wrap(err, "编辑角色失败")
	}

	res = &v1.EditRoleRes{}
	return res, nil
}

// DeleteRole 删除角色
func (s *sSystemLogic) DeleteRole(ctx context.Context, req *v1.DeleteRoleReq) (res *v1.DeleteRoleRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要删除的角色")
	}

	// // 检查是否有用户关联这些角色 (使用 Repository)
	// assigned, err := s.memberRoleRepo.IsRoleAssigned(ctx, req.Ids)
	// if err != nil {
	// 	return nil, gerror.Wrap(err, "检查角色分配情况失败")
	// }
	// if assigned {
	// 	return nil, gerror.NewCode(codes.CodeRoleHasUsers, "所选角色中存在已分配给成员的角色，无法删除") // Need to define CodeRoleHasUsers
	// }

	// 获取要删除的角色 Key 列表 (使用 Repository)
	rolesToDelete, err := s.roleRepo.GetByIDs(ctx, req.Ids)
	if err != nil {
		return nil, gerror.Wrap(err, "查询待删除角色信息失败")
	}
	roleKeys := make([]string, 0, len(rolesToDelete))
	for _, role := range rolesToDelete {
		roleKeys = append(roleKeys, role.Key)
	}

	// 执行删除（在事务中处理关联数据和 Casbin 策略）
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 软删除 admin_role 表记录 (使用 Repository)
		err = s.roleRepo.DeleteSoft(ctx, req.Ids) // Pass tx if repo method supports it
		if err != nil {
			return gerror.Wrap(err, "软删除角色失败")
		}

		// // 2. 删除 admin_member_role 表关联记录 (使用 Repository)
		// err = s.memberRoleRepo.DeleteMemberRolesByRoleIds(ctx, tx, req.Ids)
		// if err != nil {
		// 	return gerror.Wrap(err, "删除成员角色关联失败")
		// }

		// // 3. 删除 admin_role_menu 表关联记录
		// 调整 DeleteRoleMenusByRoleId 支持批量 roleId 或在循环外查询所有 roleId
		// 假设 DeleteRoleMenusByRoleId 已修改为支持批量
		// err = dao.AdminRole.DeleteRoleMenusByRoleIds(ctx, tx, req.Ids) // 假设有此方法
		// 临时方案：循环删除 (效率较低)
		// 3. 删除 admin_role_menu 表关联记录 (使用 Repository)
		// 临时方案：循环删除 (效率较低) - 保持不变，因为 DAO 方法可能包含特定逻辑
		// TODO: Consider adding a BatchDeleteRoleMenusByRoleIds to the repository if performance is critical
		// for _, roleId := range req.Ids {
		// 	err = s.roleMenuRepo.DeleteRoleMenusByRoleId(ctx, tx, roleId)
		// 	if err != nil {
		// 		return gerror.Wrapf(err, "删除角色 %d 的菜单关联失败", roleId)
		// 	}
		// }

		// // 4. 删除 Casbin 中的相关策略
		// if casbin.Enforcer != nil && len(roleKeys) > 0 {
		// 	e := casbin.GetEnforcer()
		// 	for _, roleKey := range roleKeys {
		// 		// 清理与该角色相关的 p 规则
		// 		_, errP := e.RemoveFilteredPolicy(0, roleKey, casbin.DefaultDomain)
		// 		if errP != nil {
		// 			g.Log().Warningf(ctx, "从 Casbin 删除角色 [%s] 的 p 策略失败: %v", roleKey, errP)
		// 		} else {
		// 			g.Log().Infof(ctx, "已从 Casbin 中移除角色 [%s] 的相关 p 策略", roleKey)
		// 		}

		// 		// 清理与该角色相关的 g 规则
		// 		_, errG := e.RemoveFilteredGroupingPolicy(1, roleKey, casbin.DefaultDomain)
		// 		if errG != nil {
		// 			g.Log().Warningf(ctx, "从 Casbin 删除角色 [%s] 的 g 策略失败: %v", roleKey, errG)
		// 		} else {
		// 			g.Log().Infof(ctx, "已从 Casbin 中移除角色 [%s] 的相关 g 策略", roleKey)
		// 		}
		// 	}
		// } else if len(roleKeys) > 0 {
		// 	g.Log().Warning(ctx, "Casbin Enforcer 未初始化，无法移除角色策略")
		// }

		return nil // 事务提交
	})

	if err != nil {
		return nil, err // 返回事务错误
	}

	res = &v1.DeleteRoleRes{}
	return res, nil
}

// GetRoleMenuIds 获取角色关联的菜单ID列表
func (s *sSystemLogic) GetRoleMenuIds(ctx context.Context, req *v1.GetRoleMenuIdsReq) (res *v1.GetRoleMenuIdsRes, err error) {
	// 校验角色是否存在 (使用 Repository)
	_, err = s.roleRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查角色是否存在失败, ID: %d", req.Id)
	}
	// No need to check for nil here, as GetRoleMenuIds will handle it or error out

	// // 使用 Repository 获取菜单 ID
	// menuIds, err := s.roleMenuRepo.GetMenuIdsByRoleId(ctx, req.Id)
	// if err != nil {
	// 	return nil, gerror.Wrapf(err, "获取角色菜单ID列表失败, RoleID: %d", req.Id)
	// }

	// // Initialize response struct directly
	// res = &v1.GetRoleMenuIdsRes{
	// 	MenuIds: menuIds,
	// }
	return res, nil
}

// AssignRoleMenus 分配角色菜单
func (s *sSystemLogic) AssignRoleMenus(ctx context.Context, req *v1.AssignRoleMenusReq) (res *v1.AssignRoleMenusRes, err error) {
	// 1. 校验角色是否存在 (使用 Repository)
	// role, err := s.roleRepo.GetByID(ctx, req.Id)
	// if err != nil {
	// 	return nil, gerror.Wrapf(err, "检查角色是否存在失败, ID: %d", req.Id)
	// }
	// if role == nil {
	// 	return nil, gerror.NewCodef(codes.CodeRoleNotFound, "角色不存在, ID: %d", req.Id)
	// }
	// roleKey := role.Key

	// 2. 在事务中更新数据库关联并同步 Casbin 策略
	// err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
	// TODO: 实际项目中这里应该有更新 admin_role_menu 关联表的逻辑
	// 例如:
	// 1. 删除旧的 admin_role_menu 记录 for role.Id
	// if err := s.roleMenuRepo.DeleteRoleMenusByRoleId(ctx, tx, req.Id); err != nil {
	//    return gerror.Wrap(err, "清空旧角色菜单关联失败")
	// }
	// 2. 插入新的 admin_role_menu 记录 for role.Id and req.MenuIds
	// if len(req.MenuIds) > 0 {
	//    if err := s.roleMenuRepo.BatchAddRoleMenus(ctx, tx, req.Id, req.MenuIds); err != nil {
	//        return gerror.Wrap(err, "添加新角色菜单关联失败")
	//    }
	// }

	// 	// 3. 同步 Casbin p 规则
	// 	e := casbin.GetEnforcer()
	// 	if e == nil {
	// 		return gerror.NewCode(codes.CodeInternalError, "Casbin Enforcer 未初始化")
	// 	}

	// 	// 3.1 清理该角色旧的菜单查看权限 (obj 以 "menu:" 开头, act 为 "view")
	// 	// 更精确的做法是获取所有旧的 menu 权限然后删除，这里简化为前缀匹配（如果适配器支持）
	// 	// 或者先获取所有 p 规则，再过滤删除
	// 	currentPolicies, err := e.GetFilteredPolicy(0, roleKey, casbin.DefaultDomain)
	// 	if err != nil {
	// 		g.Log().Warningf(ctx, "获取角色 [%s] 的现有策略失败: %v", roleKey, err)
	// 		// 根据业务需求决定是否中断，这里选择继续尝试清理和添加
	// 	}

	// 	var policiesToRemove [][]string
	// 	for _, p := range currentPolicies {
	// 		if len(p) >= 4 && strings.HasPrefix(p[2], "menu:") && p[3] == "view" {
	// 			policiesToRemove = append(policiesToRemove, p)
	// 		}
	// 	}
	// 	if len(policiesToRemove) > 0 {
	// 		removed, err := e.RemovePolicies(policiesToRemove)
	// 		if err != nil {
	// 			g.Log().Warningf(ctx, "从 Casbin 删除角色 [%s] 的旧菜单查看策略失败: %v", roleKey, err)
	// 			// 可以选择返回错误或继续
	// 		}
	// 		if !removed && err == nil { // Casbin V2 RemovePolicies 在没有策略被移除时可能返回 (false, nil)
	// 			g.Log().Infof(ctx, "角色 [%s] 没有需要移除的旧菜单查看策略，或者部分策略移除失败但未报错。", roleKey)
	// 		} else if removed {
	// 			g.Log().Infof(ctx, "已从 Casbin 中移除角色 [%s] 的 %d 条旧菜单查看策略", roleKey, len(policiesToRemove))
	// 		}
	// 	}

	// 	// 3.2 添加新的菜单查看权限
	// 	if len(req.MenuIds) > 0 {
	// 		// 根据 MenuIds 获取菜单实体列表
	// 		menus, err := s.menuRepo.List(ctx, g.Map{"id IN (?)": req.MenuIds, "status": consts.StatusEnabled})
	// 		if err != nil {
	// 			return gerror.Wrap(err, "获取菜单信息失败")
	// 		}

	// 		var rulesToAdd [][]string
	// 		for _, menuEntity := range menus {
	// 			if menuEntity.Path != "" { // 确保菜单路径不为空
	// 				// 确保 admin_permissions 表中存在 "menu:"+menuEntity.Path 的权限定义
	// 				// 这一步通常在 AddMenu 时已完成，此处可选择性校验或依赖 AddMenu 的正确性
	// 				permissionObj := "menu:" + menuEntity.Path
	// 				rulesToAdd = append(rulesToAdd, []string{roleKey, casbin.DefaultDomain, permissionObj, "view"})
	// 			}
	// 		}

	// 		if len(rulesToAdd) > 0 {
	// 			added, err := e.AddPolicies(rulesToAdd)
	// 			if err != nil {
	// 				g.Log().Warningf(ctx, "向 Casbin 批量添加角色 [%s] 的新菜单查看策略失败: %v", roleKey, err)
	// 				return gerror.Wrap(err, "分配角色菜单权限到 Casbin 失败")
	// 			}
	// 			if !added && err == nil { // Casbin V2 AddPolicies 在没有策略被添加时可能返回 (false, nil)
	// 				g.Log().Infof(ctx, "角色 [%s] 没有需要添加的新菜单查看策略，或者部分策略添加失败但未报错。", roleKey)
	// 			} else if added {
	// 				g.Log().Infof(ctx, "已向 Casbin 批量添加角色 [%s] 的 %d 条新菜单查看策略", roleKey, len(rulesToAdd))
	// 			}
	// 		}
	// 	}
	// 	return nil // 事务提交
	// })

	// if err != nil {
	// 	return nil, err // 返回事务错误
	// }

	res = &v1.AssignRoleMenusRes{}
	return res, nil
}

// UpdateRoleDataScope 更新角色数据范围
func (s *sSystemLogic) UpdateRoleDataScope(ctx context.Context, req *v1.UpdateRoleDataScopeReq) (res *v1.UpdateRoleDataScopeRes, err error) {
	// 1. 校验角色是否存在 (使用 Repository)
	_, err = s.roleRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查角色是否存在失败, ID: %d", req.Id)
	}
	// No need to check for nil here, as UpdateRoleDataScope will handle it or error out

	// 2. 校验数据范围值
	if !deptconsts.IsValidDataScope(req.DataScope) {
		return nil, gerror.NewCode(codes.CodeRoleInvalidDataScope)
	}

	// 3. 如果是自定义范围，校验 customDept 是否为有效的 JSON 数组
	var customDeptJson []byte
	if req.DataScope == deptconsts.DataScopeCustom {
		// 检查是否为 nil 或不是 JSON 类型
		if req.CustomDept == nil || req.CustomDept.IsNil() {
			// 允许为空数组，表示不选择任何自定义部门
			customDeptJson = []byte("[]")
		} else {
			// 尝试解析为 []int64 验证格式
			var deptIds []int64
			if err = req.CustomDept.Scan(&deptIds); err != nil {
				return nil, gerror.NewCode(codes.CodeInvalidParameter, "自定义数据范围的部门ID列表格式错误")
			}
			// 获取原始 JSON 字节
			customDeptJson, err = req.CustomDept.MarshalJSON()
			if err != nil {
				return nil, gerror.WrapCode(codes.CodeInternalError, err, "序列化自定义部门ID失败")
			}
		}
	}

	// 4. 执行更新 (使用 Repository)
	err = s.roleRepo.UpdateDataScope(ctx, req.Id, req.DataScope, customDeptJson)
	if err != nil {
		return nil, gerror.Wrap(err, "更新角色数据范围失败")
	}

	res = &v1.UpdateRoleDataScopeRes{}
	return res, nil
}

// AssignPermissionsToRole 分配角色权限
func (s *sSystemLogic) AssignPermissionsToRole(ctx context.Context, req *v1.AssignPermissionsToRoleReq) (res *v1.AssignPermissionsToRoleRes, err error) {
	// // 1. 获取 Casbin Enforcer
	// e := casbin.GetEnforcer()
	// if e == nil {
	// 	return nil, gerror.NewCode(codes.CodeInternalError, "Casbin Enforcer 未初始化")
	// }

	// // 2. 原子性操作: 先移除该角色在 DefaultDomain 下的所有现有 p 规则
	// // RemoveFilteredPolicy(0, req.RoleKey, casbin.DefaultDomain)
	// _, err = e.RemoveFilteredPolicy(0, req.RoleKey, casbin.DefaultDomain)
	// if err != nil {
	// 	g.Log().Warningf(ctx, "从 Casbin 删除角色 [%s] 的现有策略失败: %v", req.RoleKey, err)
	// 	// 根据业务需求，这里可以选择返回错误或继续尝试添加新策略
	// 	// 为简单起见，先记录错误并继续
	// }

	// // 3. 遍历权限列表，添加新的策略
	// // 优先使用 AddPolicies 进行批量添加
	// if len(req.Permissions) > 0 {
	// 	rules := make([][]string, 0, len(req.Permissions))
	// 	for _, pa := range req.Permissions {
	// 		rules = append(rules, []string{req.RoleKey, casbin.DefaultDomain, pa.PermissionKey, pa.Action})
	// 	}

	// 	added, err := e.AddPolicies(rules)
	// 	if err != nil {
	// 		g.Log().Warningf(ctx, "向 Casbin 批量添加角色 [%s] 的策略失败: %v", req.RoleKey, err)
	// 		// 批量添加失败，可以考虑回滚已添加的部分，或者记录错误并返回
	// 		// 为简单起见，先记录错误并返回
	// 		return nil, gerror.Wrap(err, "分配角色权限失败")
	// 	}
	// 	if added {
	// 		g.Log().Infof(ctx, "已向 Casbin 批量添加角色 [%s] 的 %d 条策略", req.RoleKey, len(rules))
	// 	}
	// }

	res = &v1.AssignPermissionsToRoleRes{}
	return res, nil
}
