package v1

import (

	// Logic implementations

	"admin-api/internal/logic/system/v1/address"
	"admin-api/internal/logic/system/v1/agent"
	apiKeyLogic "admin-api/internal/logic/system/v1/api_key"
	apiKeyGenLogic "admin-api/internal/logic/system/v1/apikey_gen"
	authLogic "admin-api/internal/logic/system/v1/auth"
	backupAccountLogic "admin-api/internal/logic/system/v1/backup_account"
	userAssLogic "admin-api/internal/logic/system/v1/user_ass"
	configLogic "admin-api/internal/logic/system/v1/config"
	dashboardLogic "admin-api/internal/logic/system/v1/dashboard"

	// deptLogic "admin-api/internal/logic/system/v1/dept"
	google2faLogic "admin-api/internal/logic/system/v1/google2fa"
	ipAccessListLogic "admin-api/internal/logic/system/v1/ip_access_list"
	loginLogLogic "admin-api/internal/logic/system/v1/login_log"
	memberLogic "admin-api/internal/logic/system/v1/member"
	menuLogic "admin-api/internal/logic/system/v1/menu"
	merchantLogic "admin-api/internal/logic/system/v1/merchant"
	noticeLogic "admin-api/internal/logic/system/v1/notice"
	operationLogLogic "admin-api/internal/logic/system/v1/operation_log"
	paymentRequestLogic "admin-api/internal/logic/system/v1/payment_request"
	permissionLogic "admin-api/internal/logic/system/v1/permission" // Added permission logic import

	// postLogic "admin-api/internal/logic/system/v1/post"
	redpacketLogic "admin-api/internal/logic/system/v1/redpacket"
	referralCommissionLogic "admin-api/internal/logic/system/v1/referral_commission"
	roleLogic "admin-api/internal/logic/system/v1/role"
	tokenLogic "admin-api/internal/logic/system/v1/token"
	// transactionLogic "admin-api/internal/logic/system/v1/transaction" // Deprecated: logic层直接调用DAO
	transferLogic "admin-api/internal/logic/system/v1/transfer"
	userLogic "admin-api/internal/logic/system/v1/user"
	userRechargeLogic "admin-api/internal/logic/system/v1/user_recharge"
	userWithdrawLogic "admin-api/internal/logic/system/v1/user_withdraw"
	walletLogic "admin-api/internal/logic/system/v1/wallet"

	// Service interfaces
	"admin-api/internal/service"
	addressService "admin-api/internal/service/system/address"
	agentService "admin-api/internal/service/system/agent"
	apiKeyService "admin-api/internal/service/system/api_key"
	apiKeyGenService "admin-api/internal/service/system/apikey_gen"
	authService "admin-api/internal/service/system/auth"
	backupAccountService "admin-api/internal/service/system/backup_account"
	userAssService "admin-api/internal/service/system/user_ass"
	configService "admin-api/internal/service/system/config"
	dashboardService "admin-api/internal/service/system/dashboard"
	"admin-api/internal/service/system/user_withdraw"

	// deptService "admin-api/internal/service/system/dept"
	google2faService "admin-api/internal/service/system/google2fa"
	ipAccessListService "admin-api/internal/service/system/ip_access_list"
	loginLogService "admin-api/internal/service/system/login_log"
	memberService "admin-api/internal/service/system/member"
	menuService "admin-api/internal/service/system/menu"
	merchantService "admin-api/internal/service/system/merchant"
	noticeService "admin-api/internal/service/system/notice"
	operationLogService "admin-api/internal/service/system/operation_log"
	paymentRequestService "admin-api/internal/service/system/payment_request"
	permissionService "admin-api/internal/service/system/permission" // Added permission service import

	// postService "admin-api/internal/service/system/post"
	redpacketService "admin-api/internal/service/system/redpacket"
	referralCommissionService "admin-api/internal/service/system/referral_commission"
	roleService "admin-api/internal/service/system/role"
	tokenService "admin-api/internal/service/system/token"
	// transactionService "admin-api/internal/service/system/transaction" // Deprecated: logic层直接调用DAO
	transferService "admin-api/internal/service/system/transfer"
	userService "admin-api/internal/service/system/user"
	userRechargeService "admin-api/internal/service/system/user_recharge"
	walletService "admin-api/internal/service/system/wallet"
)

type sSystemLogic struct {
	agentRepo               agentService.IAgentRepository
	agentIPWhitelistRepo    agentService.IAgentIPWhitelistRepository
	agentAuthService        agentService.IAgentAuthService
	apiKeyRepo              apiKeyService.IApiKeyRepository
	authRepo                authService.IAuthRepository
	authService             authService.IAuthService
	loginLogRepo            loginLogService.ILoginLogRepository
	backupAccountRepo       backupAccountService.IBackupAccountRepository
	userAssRepo            userAssService.IUserAssRepository
	userRepo                userService.IUserRepository // Corrected user interface type
	// deptRepo                deptService.IDeptRepository
	// deptTypeService         deptService.IDeptTypeService
	tokenRepo           tokenService.ITokenRepository
	ipAccessListRepo    ipAccessListService.IIpAccessListRepository
	memberRepo          memberService.IMemberRepository
	roleRepo            roleService.IRoleRepository
	// postRepo               postService.IPostRepository
	// memberPostRepo         postService.IMemberPostRepository
	redPacketRepo          redpacketService.IRedPacketRepository
	redPacketClaimRepo     redpacketService.IRedPacketClaimRepository
	referralCommissionRepo referralCommissionService.IReferralCommissionRepository
	menuRepo               menuService.IMenuRepository
	merchantRepo           merchantService.IMerchantRepository
	google2faSvc           google2faService.IGoogle2FAService
	apiKeyGenSvc           apiKeyGenService.IApiKeyGenerationService
	noticeRepo             noticeService.INoticeRepository
	noticeReadRepo         noticeService.INoticeReadRepository
	operationLogRepo       operationLogService.IOperationLogRepository
	paymentRequestRepo     paymentRequestService.IPaymentRequestRepository
	transferRepo           transferService.ITransferRepository
	// transactionRepo        transactionService.ITransactionRepository // Deprecated: logic层直接调用DAO
	userRechargeRepo       userRechargeService.IUserRechargeRepository // Added user recharge repository field
	addressRepo            addressService.IAddressRepository           // Added address repository field
	userWithdrawRepo       user_withdraw.IUserWithdrawRepository       // Added user withdraw repository field
	walletRepo             walletService.IWalletRepository
	configRepo             configService.IConfigRepository         // Added config repository field
	dashboardRepo          dashboardService.IDashboardRepository   // Added dashboard repository field
	permissionLogic        permissionService.IPermissionLogic      // Changed to permissionLogic
	permissionRepo         permissionService.IPermissionRepository // Added permission repository field
}

func init() {
	service.RegisterSystem(NewSystemLogicV1())
}

func NewSystemLogicV1() *sSystemLogic {
	// Create repository instances
	agentRepo := agent.NewAgentRepository()
	agentIPWhitelistRepo := agent.NewAgentIPWhitelistRepository()
	apiKeyRepo := apiKeyLogic.NewApiKeyRepository()
	authSvc := authLogic.NewAuthService()
	// authRepo := authLogic.NewAuthRepository(authSvc) // Pass repos below
	loginLogRepo := loginLogLogic.NewLoginLogRepository()
	backupAccountRepo := backupAccountLogic.NewBackupAccountRepository()
	userAssRepo := userAssLogic.NewUserAssRepository()
	userRepo := userLogic.NewUserRepository() // Instantiate correct user repo
	// deptRepo := deptLogic.NewDeptRepository()
	ipAccessListRepo := ipAccessListLogic.NewIpAccessListRepository()
	memberRepo := memberLogic.NewMemberRepository()
	roleRepo := roleLogic.NewRoleRepository()
	// memberRoleRepo := roleLogic.NewMemberRoleRepository()
	// roleMenuRepo := roleLogic.NewRoleMenuRepository()
	// postRepo := postLogic.NewPostRepository()
	// memberPostRepo := postLogic.NewMemberPostRepository()
	redPacketRepo := redpacketLogic.NewRedPacketRepository()
	redPacketClaimRepo := redpacketLogic.NewRedPacketClaimRepository()
	referralCommissionRepo := referralCommissionLogic.NewReferralCommissionRepository()
	menuRepo := menuLogic.NewMenuRepository()
	merchantRepo := merchantLogic.NewMerchantRepository()
	noticeRepo := noticeLogic.NewNoticeRepository()
	noticeReadRepo := noticeLogic.NewNoticeReadRepository()
	operationLogRepo := operationLogLogic.NewOperationLogRepository()
	paymentRequestRepo := paymentRequestLogic.NewPaymentRequestRepository()
	tokenRepo := tokenLogic.NewTokenRepository()
	transferRepo := transferLogic.NewTransferRepository()
	// transactionRepo := transactionLogic.NewTransactionRepository() // Deprecated: logic层直接调用DAO
	userRechargeRepo := userRechargeLogic.NewUserRechargeRepository() // Create user recharge repository instance
	addressRepo := address.NewAddressRepository()                     // Create address repository instance
	userWithdrawRepo := userWithdrawLogic.NewUserWithdrawRepository() // Create user withdraw repository instance
	walletRepo := walletLogic.NewWalletRepository()
	configRepo := configLogic.NewConfigRepository()             // Create config repository instance
	dashboardRepo := dashboardLogic.NewDashboardRepository()    // Create dashboard repository instance
	permissionRepo := permissionLogic.NewPermissionRepository() // Create permission repository instance
	// permissionLgc := permissionLogic.NewPermissionLogic(permissionRepo) // This line will be removed

	// Instantiate authRepo after its dependencies (roleMenuRepo, menuRepo) are created
	authRepo := authLogic.NewAuthRepository(authSvc, roleRepo, menuRepo)

	// Create service instances, injecting dependencies
	agentAuthService := agent.NewAgentAuthService(agentRepo)
	google2faSvc := google2faLogic.NewGoogle2FAService()
	apiKeyGenSvc := apiKeyGenLogic.NewApiKeyGenerationService()

	s := &sSystemLogic{
		agentRepo:               agentRepo,
		agentIPWhitelistRepo:    agentIPWhitelistRepo,
		agentAuthService:        agentAuthService,
		apiKeyRepo:              apiKeyRepo,
		authRepo:                authRepo,
		authService:             authSvc,
		loginLogRepo:            loginLogRepo,
		backupAccountRepo:       backupAccountRepo,
		userAssRepo:            userAssRepo,
		userRepo:                userRepo,
		// deptRepo:                deptRepo,
		// deptTypeService:         deptTypeService,
		tokenRepo:              tokenRepo,
		ipAccessListRepo:       ipAccessListRepo,
		memberRepo:             memberRepo,
		roleRepo:               roleRepo,
		redPacketRepo:          redPacketRepo,
		redPacketClaimRepo:     redPacketClaimRepo,
		referralCommissionRepo: referralCommissionRepo,
		menuRepo:               menuRepo,
		merchantRepo:           merchantRepo,
		google2faSvc:           google2faSvc,
		apiKeyGenSvc:           apiKeyGenSvc,
		noticeRepo:             noticeRepo,
		noticeReadRepo:         noticeReadRepo,
		operationLogRepo:       operationLogRepo,
		paymentRequestRepo:     paymentRequestRepo,
		transferRepo:           transferRepo,
		// transactionRepo:        transactionRepo, // Deprecated: logic层直接调用DAO
		userRechargeRepo:       userRechargeRepo, // Inject user recharge repository
		addressRepo:            addressRepo,      // Inject address repository
		userWithdrawRepo:       userWithdrawRepo, // Inject user withdraw repository
		walletRepo:             walletRepo,
		configRepo:             configRepo,     // Inject config repository
		dashboardRepo:          dashboardRepo,  // Inject dashboard repository
		permissionRepo:         permissionRepo, // Inject permission repository
		// permissionLogic will be set to s below
	}
	s.permissionLogic = s // sSystemLogic itself implements IPermissionLogic
	return s
}
