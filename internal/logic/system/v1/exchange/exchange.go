package exchange

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service"
)

type sExchange struct{}

func init() {
	service.RegisterExchange(New())
}

func New() service.IExchange {
	return &sExchange{}
}

// GetProductList 获取兑换产品列表
func (s *sExchange) GetProductList(ctx context.Context, req *v1.ExchangeProductListReq) (*v1.ExchangeProductListRes, error) {
	var (
		m     = dao.ExchangeProducts.Ctx(ctx)
		total int
		list  = make([]*v1.ExchangeProductItem, 0)
	)

	// 构建查询条件
	if req.BaseToken != "" {
		m = m.Where("base_token", req.BaseToken)
	}
	if req.QuoteToken != "" {
		m = m.Where("quote_token", req.QuoteToken)
	}
	if req.Symbol != "" {
		m = m.WhereLike("symbol", "%"+req.Symbol+"%")
	}
	if req.IsActive != nil {
		m = m.Where("is_active", *req.IsActive)
	}
	if req.Status != nil {
		m = m.Where("status", *req.Status)
	}

	// 统计总数
	total, err := m.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	err = m.Page(req.Page, req.PageSize).
		OrderDesc("display_order").
		OrderDesc("created_at").
		Scan(&list)
	if err != nil {
		return nil, err
	}

	return &v1.ExchangeProductListRes{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetProductDetail 获取兑换产品详情
func (s *sExchange) GetProductDetail(ctx context.Context, req *v1.ExchangeProductDetailReq) (*v1.ExchangeProductDetailRes, error) {
	var product v1.ExchangeProductDetail

	err := dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Scan(&product)
	if err != nil {
		return nil, err
	}

	if product.ProductId == 0 {
		return nil, fmt.Errorf("兑换产品不存在")
	}

	return &v1.ExchangeProductDetailRes{
		ExchangeProductDetail: &product,
	}, nil
}

// CreateProduct 创建兑换产品
func (s *sExchange) CreateProduct(ctx context.Context, req *v1.ExchangeProductCreateReq) (*v1.ExchangeProductCreateRes, error) {
	// 检查交易对是否已存在
	count, err := dao.ExchangeProducts.Ctx(ctx).
		Where("symbol", req.Symbol).
		Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, fmt.Errorf("交易对符号已存在")
	}

	// 验证最小金额小于最大金额
	if req.MinBaseAmountPerTx.GreaterThan(req.MaxBaseAmountPerTx) {
		return nil, fmt.Errorf("单笔最小金额不能大于最大金额")
	}

	// 创建产品
	id, err := dao.ExchangeProducts.Ctx(ctx).Data(req).InsertAndGetId()
	if err != nil {
		return nil, err
	}

	// 记录操作日志
	g.Log().Infof(ctx, "创建兑换产品: ID=%d, Symbol=%s", id, req.Symbol)

	return &v1.ExchangeProductCreateRes{
		ProductId: uint(id),
	}, nil
}

// UpdateProduct 更新兑换产品
func (s *sExchange) UpdateProduct(ctx context.Context, req *v1.ExchangeProductUpdateReq) (*v1.ExchangeProductUpdateRes, error) {
	// 检查产品是否存在
	var product entity.ExchangeProducts
	err := dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Scan(&product)
	if err != nil {
		return nil, err
	}
	if product.ProductId == 0 {
		return nil, fmt.Errorf("兑换产品不存在")
	}

	// 如果修改了交易对符号，检查是否重复
	if req.Symbol != "" && req.Symbol != product.Symbol {
		count, err := dao.ExchangeProducts.Ctx(ctx).
			Where("symbol", req.Symbol).
			Where("product_id !=", req.ProductId).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, fmt.Errorf("交易对符号已存在")
		}
	}

	// 验证最小金额小于最大金额
	if !req.MinBaseAmountPerTx.IsZero() && !req.MaxBaseAmountPerTx.IsZero() {
		if req.MinBaseAmountPerTx.GreaterThan(req.MaxBaseAmountPerTx) {
			return nil, fmt.Errorf("单笔最小金额不能大于最大金额")
		}
	}

	// 构建更新数据
	data := g.Map{}
	if req.BaseToken != "" {
		data["base_token"] = req.BaseToken
	}
	if req.QuoteToken != "" {
		data["quote_token"] = req.QuoteToken
	}
	if req.Symbol != "" {
		data["symbol"] = req.Symbol
	}
	if req.ProductType != "" {
		data["product_type"] = req.ProductType
	}
	if req.IsActive != nil {
		data["is_active"] = *req.IsActive
	}
	if req.AllowBuy != nil {
		data["allow_buy"] = *req.AllowBuy
	}
	if req.AllowSell != nil {
		data["allow_sell"] = *req.AllowSell
	}
	if req.MaintenanceMessage != "" {
		data["maintenance_message"] = req.MaintenanceMessage
	}
	if !req.MinBaseAmountPerTx.IsZero() {
		data["min_base_amount_per_tx"] = req.MinBaseAmountPerTx
	}
	if !req.MaxBaseAmountPerTx.IsZero() {
		data["max_base_amount_per_tx"] = req.MaxBaseAmountPerTx
	}
	if !req.DailyBaseVolumeLimit.IsZero() {
		data["daily_base_volume_limit"] = req.DailyBaseVolumeLimit
	}
	if !req.TotalBaseVolumeLimit.IsZero() {
		data["total_base_volume_limit"] = req.TotalBaseVolumeLimit
	}
	if req.PriceSource != "" {
		data["price_source"] = req.PriceSource
	}
	if !req.AllowedSlippagePercent.IsZero() {
		data["allowed_slippage_percent"] = req.AllowedSlippagePercent
	}
	if !req.SpreadRate.IsZero() {
		data["spread_rate"] = req.SpreadRate
	}
	if req.RateRefreshIntervalSec != nil {
		data["rate_refresh_interval_sec"] = *req.RateRefreshIntervalSec
	}
	// 注意: fee_rate, fee_charged_in, min_fee_amount_base_equivalent 字段在数据库中不存在
	// 已移除这些字段的更新逻辑，避免更新错误
	// 如果需要这些字段，请先在数据库中添加相应的列
	if req.FeeStrategy != "" {
		data["fee_strategy"] = req.FeeStrategy
	}
	if !req.OutputFeeRate.IsZero() {
		data["output_fee_rate"] = req.OutputFeeRate
	}
	if !req.MinOutputFeeAmount.IsZero() {
		data["min_output_fee_amount"] = req.MinOutputFeeAmount
	}
	if req.DisplayOrder != nil {
		data["display_order"] = *req.DisplayOrder
	}
	if req.Description != "" {
		data["description"] = req.Description
	}

	// 检查是否有任何字段需要更新
	if len(data) == 0 {
		return nil, gerror.New("没有提供任何需要更新的字段")
	}

	// 执行更新
	_, err = dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Data(data).
		Update()
	if err != nil {
		return nil, err
	}

	// 记录操作日志
	g.Log().Infof(ctx, "更新兑换产品: ID=%d", req.ProductId)

	return &v1.ExchangeProductUpdateRes{}, nil
}

// DeleteProduct 删除兑换产品（软删除）
func (s *sExchange) DeleteProduct(ctx context.Context, req *v1.ExchangeProductDeleteReq) (*v1.ExchangeProductDeleteRes, error) {
	// 检查产品是否存在
	count, err := dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, fmt.Errorf("兑换产品不存在")
	}

	// 检查是否有进行中的订单
	orderCount, err := dao.ExchangeOrders.Ctx(ctx).
		Where("product_id", req.ProductId).
		Where("status IN", []string{"pending", "processing"}).
		Count()
	if err != nil {
		return nil, err
	}
	if orderCount > 0 {
		return nil, fmt.Errorf("该产品有进行中的订单，无法删除")
	}

	// 软删除产品
	_, err = dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Data(g.Map{
			"status":     0,
			"is_active":  0,
			"deleted_at": gdb.Raw("NOW()"),
		}).
		Update()
	if err != nil {
		return nil, err
	}

	// 记录操作日志
	g.Log().Infof(ctx, "删除兑换产品: ID=%d", req.ProductId)

	return &v1.ExchangeProductDeleteRes{}, nil
}

// UpdateProductStatus 更新兑换产品状态
func (s *sExchange) UpdateProductStatus(ctx context.Context, req *v1.ExchangeProductStatusReq) (*v1.ExchangeProductStatusRes, error) {
	// 检查产品是否存在
	count, err := dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, fmt.Errorf("兑换产品不存在")
	}

	// 更新状态
	_, err = dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Data(g.Map{
			"is_active": req.IsActive,
			"status":    req.Status,
		}).
		Update()
	if err != nil {
		return nil, err
	}

	// 记录操作日志
	g.Log().Infof(ctx, "更新兑换产品状态: ID=%d, IsActive=%d, Status=%d", req.ProductId, req.IsActive, req.Status)

	return &v1.ExchangeProductStatusRes{}, nil
}

// GetProductVolume 获取兑换产品交易量
func (s *sExchange) GetProductVolume(ctx context.Context, req *v1.ExchangeProductVolumeReq) (*v1.ExchangeProductVolumeRes, error) {
	// 获取产品信息
	var product entity.ExchangeProducts
	err := dao.ExchangeProducts.Ctx(ctx).
		Where("product_id", req.ProductId).
		Scan(&product)
	if err != nil {
		return nil, err
	}
	if product.ProductId == 0 {
		return nil, fmt.Errorf("兑换产品不存在")
	}

	// 获取今日交易量（从Redis获取，这里简化处理，直接从数据库计算）
	dailyVolume, err := dao.ExchangeOrders.Ctx(ctx).
		Where("product_id", req.ProductId).
		Where("status", "completed").
		Where("created_at >= CURDATE()").
		Sum("amount_base")
	if err != nil {
		return nil, err
	}

	// 获取累计交易量
	totalVolume, err := dao.ExchangeOrders.Ctx(ctx).
		Where("product_id", req.ProductId).
		Where("status", "completed").
		Sum("amount_base")
	if err != nil {
		return nil, err
	}

	// 计算剩余额度
	dailyVolumeDecimal := decimal.NewFromFloat(gconv.Float64(dailyVolume))
	totalVolumeDecimal := decimal.NewFromFloat(gconv.Float64(totalVolume))

	res := &v1.ExchangeProductVolumeRes{
		DailyVolume: dailyVolumeDecimal,
		TotalVolume: totalVolumeDecimal,
		DailyLimit:  product.DailyBaseVolumeLimit,
		TotalLimit:  product.TotalBaseVolumeLimit,
	}

	if !product.DailyBaseVolumeLimit.IsZero() {
		res.DailyRemaining = product.DailyBaseVolumeLimit.Sub(res.DailyVolume)
		if res.DailyRemaining.LessThan(decimal.Zero) {
			res.DailyRemaining = decimal.Zero
		}
	}

	if !product.TotalBaseVolumeLimit.IsZero() {
		res.TotalRemaining = product.TotalBaseVolumeLimit.Sub(res.TotalVolume)
		if res.TotalRemaining.LessThan(decimal.Zero) {
			res.TotalRemaining = decimal.Zero
		}
	}

	return res, nil
}
