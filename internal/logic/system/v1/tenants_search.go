package v1

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"

	"github.com/gogf/gf/v2/errors/gerror"
)

// SearchTenants 搜索租户
func (s *sSystemLogic) SearchTenants(ctx context.Context, req *v1.SearchTenantsReq) (res *v1.SearchTenantsRes, err error) {
	var (
		m = dao.Tenants.Ctx(ctx)
	)

	// 只搜索活跃的租户
	m = m.Where("status", 1).
		Where("deleted_at IS NULL").
		WhereLike("username", "%"+req.Q+"%")

	// 获取总数
	total, err := m.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "count tenants failed")
	}

	// 获取分页数据
	var tenants []struct {
		TenantId uint   `json:"tenant_id"`
		Username string `json:"username"`
	}

	err = m.Fields("tenant_id, username").
		Page(req.<PERSON>, req.Limit).
		OrderAsc("username").
		Scan(&tenants)
	if err != nil {
		return nil, gerror.Wrap(err, "get tenants list failed")
	}

	// 转换为响应结构
	data := make([]*v1.TenantSearchItem, len(tenants))
	for i, tenant := range tenants {
		data[i] = &v1.TenantSearchItem{
			Id:       tenant.TenantId,
			Username: tenant.Username,
		}
	}

	res = &v1.SearchTenantsRes{
		Data: data,
		Pagination: v1.PaginationInfo{
			Page:    req.Page,
			Limit:   req.Limit,
			Total:   total,
			HasMore: req.Page*req.Limit < total,
		},
	}

	return res, nil
}