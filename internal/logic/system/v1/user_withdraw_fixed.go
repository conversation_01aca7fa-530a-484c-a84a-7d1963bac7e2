package v1

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	adminConstants "admin-api/internal/constants"
	"admin-api/internal/dao"
	"admin-api/internal/service"
	"admin-api/internal/service/paybot"
	"admin-api/internal/utils"

	"github.com/yalks/wallet"
	"github.com/yalks/wallet/constants"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/guid"
)

// ListUserWithdraws 获取提现记录列表（带代理和telegram信息）
func (s *sSystemLogic) ListUserWithdraws(ctx context.Context, req *v1.ListUserWithdrawsReq) (res *v1.ListUserWithdrawsRes, err error) {
	// 初始化返回结果
	res = &v1.ListUserWithdrawsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.UserWithdrawsListItem, 0),
	}

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户提现记录列表（带代理和telegram信息）
	list, total, err := s.userWithdrawRepo.ListWithAgentInfo(ctx, req)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取用户提现记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)

	// 设置返回数据
	res.Data = list

	return res, nil
}

// GetUserWithdrawDetail 获取提现记录详情
func (s *sSystemLogic) GetUserWithdrawDetail(ctx context.Context, req *v1.GetUserWithdrawDetailReq) (res *v1.GetUserWithdrawDetailRes, err error) {
	// 初始化返回结果
	res = &v1.GetUserWithdrawDetailRes{
		Data: nil,
	}

	// 查询数据（带代理和telegram信息）
	detail, err := s.userWithdrawRepo.GetDetailWithAgentInfo(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if detail == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 直接使用返回的数据（已包含代理和telegram信息）
	res.Data = detail

	return res, nil
}

// ReviewUserWithdraw 审核提现记录
func (s *sSystemLogic) ReviewUserWithdraw(ctx context.Context, req *v1.ReviewUserWithdrawReq) (res *v1.ReviewUserWithdrawRes, err error) {
	// 初始化返回结果
	res = &v1.ReviewUserWithdrawRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查状态是否为待审核
	if withdraw.State != 1 && withdraw.State != 2 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "只能审核待审核状态的提现记录")
	}

	// 使用事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var updateErr error

		// 根据操作类型更新状态
		if req.Action == "approve" {
			// 检查提现类型是否为auth_pay
			if withdraw.WithdrawType == "auth_pay" {
				// 获取用户信息
				user, err := dao.Users.Ctx(ctx).Where("id = ?", withdraw.UserId).One()
				if err != nil {
					return gerror.Wrap(err, "获取用户信息失败")
				}
				if user.IsEmpty() {
					return gerror.NewCode(codes.CodeUserNotFound, "用户不存在")
				}

				// 获取代币信息
				token, err := s.tokenRepo.GetByID(ctx, uint(withdraw.TokenId))
				if err != nil {
					return gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", withdraw.TokenId)
				}
				if token == nil {
					return gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", withdraw.TokenId)
				}

				// 检查是否已存在相同订单号的授权支付
				existingAuthOrder, err := dao.PaybotAuthOrders.Ctx(ctx).
					Where("merchant_order_no", withdraw.OrderNo).
					One()
				if err != nil {
					return gerror.Wrap(err, "检查授权支付订单失败")
				}

				// 如果已存在授权支付订单，根据状态处理
				var skipAuthPayment bool
				var existingPaybotOrderNo string
				if !existingAuthOrder.IsEmpty() {
					status := existingAuthOrder["status"].String()
					switch status {
					case "pending":
						// 待处理状态，检查是否超时（超过5分钟）
						createdAt := existingAuthOrder["created_at"].GTime()
						if createdAt != nil && gtime.Now().Sub(createdAt) > 5*time.Minute {
							// 超时的pending订单，可以重试
							g.Log().Warningf(ctx, "提现订单 %s 的授权支付已超时（创建于: %s），将使用新订单号重试",
								withdraw.OrderNo, createdAt.Format("Y-m-d H:i:s"))
							// 继续执行，将使用新的订单号
						} else {
							// 正常的pending状态，提示正在处理
							return gerror.New("该提现订单正在授权支付处理中，请稍后再试")
						}
					case "completed":
						// 已完成，跳过创建授权支付，直接更新提现状态
						g.Log().Infof(ctx, "提现订单 %s 的授权支付已完成，跳过创建", withdraw.OrderNo)
						skipAuthPayment = true
						existingPaybotOrderNo = existingAuthOrder["paybot_order_no"].String()
					case "failed":
						// 失败状态，可以重试，但使用新的订单号
						g.Log().Infof(ctx, "提现订单 %s 的授权支付曾失败，将使用新订单号重试", withdraw.OrderNo)
					default:
						// 其他状态，记录日志
						g.Log().Warningf(ctx, "提现订单 %s 存在未知状态的授权支付: %s", withdraw.OrderNo, status)
					}
				}

				// 仅在不跳过授权支付的情况下创建
				var authResult *paybot.AuthPaymentResult
				if !skipAuthPayment {
					// 生成订单号，如果是失败重试或超时的pending，使用新的订单号
					merchantOrderNo := withdraw.OrderNo
					if !existingAuthOrder.IsEmpty() {
						status := existingAuthOrder["status"].String()
						if status == "failed" {
							// 为失败的授权支付生成新的订单号，添加后缀以区分
							merchantOrderNo = fmt.Sprintf("%s_R%s", withdraw.OrderNo, guid.S()[:8])
						} else if status == "pending" {
							// 为超时的pending订单生成新的订单号
							createdAt := existingAuthOrder["created_at"].GTime()
							if createdAt != nil && gtime.Now().Sub(createdAt) > 5*time.Minute {
								merchantOrderNo = fmt.Sprintf("%s_T%s", withdraw.OrderNo, guid.S()[:8])
							}
						}
					}

					// 创建授权支付请求
					authReq := &paybot.CreateAuthPaymentRequest{
						UserID:          uint64(withdraw.UserId),
						UserAccount:     user["account"].String(),
						OrderType:       "add", // 给用户加款，给商户扣款
						TokenSymbol:     token.Symbol,
						Amount:          withdraw.Amount,
						AuthReason:      fmt.Sprintf("提现审核通过 - %s", withdraw.OrderNo),
						MerchantOrderNo: merchantOrderNo,
						ExpireMinutes:   60, // 60分钟过期
						CallbackUrl:     "", // TODO: 配置回调URL
					}

					// 创建一个独立的context用于PayBot API调用，避免事务超时影响
					// 使用较短的超时时间，确保不会超过数据库事务超时
					paybotCtx, cancel := context.WithTimeout(context.Background(), 25*time.Second)
					defer cancel()

					// 保留请求信息用于新context
					if r := g.RequestFromCtx(ctx); r != nil {
						paybotCtx = r.Context()
						// 创建新的超时context，继承请求信息
						paybotCtx, cancel = context.WithTimeout(paybotCtx, 25*time.Second)
						defer cancel()
					}

					// 调用PayBot创建授权支付
					authResult, err = service.PayBot().CreateAuthPayment(paybotCtx, authReq)
					if err != nil {
						// 授权支付失败，不修改订单状态，直接返回错误
						g.Log().Errorf(ctx, "创建授权支付失败: %v", err)

						// 检查是否是context取消或超时
						if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
							return gerror.New("支付服务响应超时，请稍后重试")
						}

						// 返回更友好的错误信息
						if strings.Contains(err.Error(), "no such host") || strings.Contains(err.Error(), "connection refused") {
							return gerror.New("支付服务暂时不可用，请稍后重试")
						}

						// 检查是否是余额不足错误
						if strings.Contains(err.Error(), "商户余额不足") {
							return gerror.New("商户余额不足，无法处理提现，请联系技术支持")
						}

						// 检查是否是商户账户不可用
						if strings.Contains(err.Error(), "Merchant account is not available") {
							return gerror.New("商户账户状态异常，请联系技术支持")
						}

						return gerror.New("授权支付处理失败，请稍后重试")
					}
				}

				// 如果不需要创建授权支付（已完成的情况），或者创建成功，则更新提现记录
				var paybotOrderNo string
				if skipAuthPayment {
					// 使用已存在的授权支付订单号
					paybotOrderNo = existingPaybotOrderNo
				} else if authResult != nil {
					// 使用新创建的授权支付订单号
					paybotOrderNo = authResult.OrderNo
				}

				// 更新提现记录为已完成
				updateData := g.Map{
					"state":              4, // 已完成
					"admin_remark":       req.AdminRemark,
					"paybot_order_no":    paybotOrderNo,
					"paybot_sync_status": "synced", // 使用正确的枚举值
					"paybot_sync_at":     gtime.Now(),
					"checked_at":         gtime.Now(),
					"completed_at":       gtime.Now(),
					"tx_hash":            fmt.Sprintf("auth_pay_%s", paybotOrderNo),
				}
				_, err = dao.UserWithdraws.Ctx(ctx).TX(tx).Where("user_withdraws_id = ?", req.UserWithdrawsId).Data(updateData).Update()
				if err != nil {
					return gerror.Wrap(err, "更新提现记录失败")
				}

				g.Log().Infof(ctx, "提现已完成。PayBot订单号: %s", paybotOrderNo)

				// 记录提现操作日志
				service.TelegramAdminLog().RecordWithdrawalOperationLog(ctx, withdraw, "approve", req.AdminRemark, 1, "")

				return nil // 成功处理auth_pay，直接返回

			} else {
				// 非auth_pay类型，直接更新为已完成
				updateErr = s.userWithdrawRepo.UpdateStateWithReason(ctx, tx, req.UserWithdrawsId, 4, "", "", req.AdminRemark)

				// 记录提现操作日志
				if updateErr == nil {
					service.TelegramAdminLog().RecordWithdrawalOperationLog(ctx, withdraw, "approve", req.AdminRemark, 1, "")
				}
			}
		} else if req.Action == "reject" {
			// 拒绝: 状态更新为已拒绝(3)
			updateErr = s.userWithdrawRepo.UpdateStateWithReason(ctx, tx, req.UserWithdrawsId, 3, req.RefuseReasonZh, req.RefuseReasonEn, req.AdminRemark)

			// 获取代币信息以获取符号 (Symbol) 和 精度 (Decimals)
			token, err := s.tokenRepo.GetByID(ctx, uint(withdraw.TokenId))
			if err != nil {
				return gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", withdraw.TokenId)
			}
			if token == nil {
				return gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", withdraw.TokenId)
			}
			symbol := token.Symbol

			// --- 事务开始 ---
			// 获取管理员名称
			adminName := ""
			if r := g.RequestFromCtx(ctx); r == nil {
				return codes.NewError(codes.CodeUnauthorized)
			} else {
				adminName = r.GetCtxVar("username").String()
			}

			// 使用新的资金操作描述器
			descriptor := utils.NewFundOperationDescriptor("zh")
			businessID := descriptor.GenerateBusinessID(adminConstants.FundOpWithdrawRefund, withdraw.UserId, gtime.Now().Unix())

			// 构建描述信息
			memo := fmt.Sprintf("%s-%s", adminName, req.AdminRemark)
			description := descriptor.FormatDescriptionWithMemo(adminConstants.FundOpWithdrawRefund, withdraw.Amount.String(), symbol, memo)

			// 使用新的请求构建方法
			walletReq := &constants.FundOperationRequest{
				UserID:      uint64(withdraw.UserId),
				TokenSymbol: symbol,
				Amount:      withdraw.Amount,
				BusinessID:  businessID,
				FundType:    constants.FundTypeWithdrawRefund,
				Description: description,
				Metadata: map[string]string{
					"type":          "withdraw_refund",
					"request_id":    fmt.Sprintf("%d", withdraw.UserId),
					"operation":     "withdraw_refund",
					"changeReason":  req.AdminRemark,
					"admin_name":    adminName,
					"withdraw_id":   fmt.Sprintf("%d", withdraw.UserWithdrawsId),
					"refuse_reason": req.RefuseReasonZh,
				},
				RequestSource: "admin",
			}

			_, err = wallet.Manager().ProcessFundOperationInTx(ctx, tx, walletReq)
			if err != nil {
				g.Log().Errorf(ctx, "退还用户资金失败: %v", err)
				return gerror.Wrap(err, "退还用户资金失败")
			}

			// 记录提现操作日志
			if updateErr == nil {
				service.TelegramAdminLog().RecordWithdrawalOperationLog(ctx, withdraw, "reject", req.AdminRemark, 1, "")
			}

		}

		if updateErr != nil {
			return updateErr
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// UpdateUserWithdrawStatus 更新提现记录状态
func (s *sSystemLogic) UpdateUserWithdrawStatus(ctx context.Context, req *v1.UpdateUserWithdrawStatusReq) (res *v1.UpdateUserWithdrawStatusRes, err error) {
	// 初始化返回结果
	res = &v1.UpdateUserWithdrawStatusRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查状态转换是否合法
	if !isValidStatusTransition(withdraw.State, req.State) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态转换")
	}

	// 使用事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新状态
		return s.userWithdrawRepo.UpdateStateWithTxInfo(ctx, tx, req.UserWithdrawsId, req.State, req.TxHash, req.ErrorMessage, req.AdminRemark)
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// isValidStatusTransition 检查状态转换是否合法
func isValidStatusTransition(currentState, targetState uint) bool {
	// 状态定义:
	// 1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败

	// 只允许以下状态转换:
	// 2(处理中) -> 4(已完成)
	// 2(处理中) -> 5(失败)

	if currentState == 2 && (targetState == 4 || targetState == 5) {
		return true
	}

	// 特殊情况: 允许从任何状态直接设置为处理中(2)
	// 这通常用于管理员手动干预
	if targetState == 2 {
		return true
	}

	return false
}
