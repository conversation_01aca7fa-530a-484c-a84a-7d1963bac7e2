package v1

import (
	"admin-api/internal/codes"
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// createAgentDatabase 为代理创建独立数据库
func (s *sSystemLogic) createAgentDatabase(ctx context.Context, username string) (string, error) {
	// 构建数据库名称
	dbName := fmt.Sprintf("%s_database", username)
	
	// 获取数据库连接配置
	dbConfig := g.Cfg().MustGet(ctx, "database.default").MapStrVar()
	if dbConfig == nil {
		return "", gerror.New("无法获取数据库配置")
	}
	
	// 解析连接字符串获取主机、端口、用户名、密码
	link := dbConfig["link"].String()
	// 移除 mysql: 前缀
	if strings.HasPrefix(link, "mysql:") {
		link = strings.TrimPrefix(link, "mysql:")
	}
	
	parts := strings.Split(link, "@tcp(")
	if len(parts) != 2 {
		return "", gerror.New("数据库连接字符串格式错误")
	}
	
	authParts := strings.Split(parts[0], ":")
	if len(authParts) < 2 {
		return "", gerror.New("数据库认证信息格式错误")
	}
	
	dbUser := authParts[0]
	dbPass := authParts[1]
	
	hostParts := strings.Split(parts[1], ")/")
	if len(hostParts) < 1 {
		return "", gerror.New("数据库主机信息格式错误")
	}
	
	dbHost := strings.TrimSuffix(hostParts[0], ")")
	
	// 创建临时数据库连接（不指定具体数据库）
	tempDB, err := gdb.New(gdb.ConfigNode{
		Link: fmt.Sprintf("mysql:%s:%s@tcp(%s)/?loc=Local&parseTime=true&charset=utf8mb4", dbUser, dbPass, dbHost),
		Type: "mysql",
	})
	if err != nil {
		return "", gerror.Wrap(err, "创建数据库连接失败")
	}
	defer tempDB.Close(ctx)
	
	// 检查数据库是否已存在
	var count int
	value, err := tempDB.GetValue(ctx, fmt.Sprintf(
		"SELECT COUNT(*) FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = '%s'", 
		dbName,
	))
	if err != nil {
		return "", gerror.Wrap(err, "检查数据库是否存在失败")
	}
	count = value.Int()
	if count < 0 {
		count = 0
	}
	
	if count > 0 {
		return "", gerror.NewCode(codes.CodeAgentDatabaseExists, fmt.Sprintf("数据库 %s 已存在", dbName))
	}
	
	// 创建数据库
	_, err = tempDB.Exec(ctx, fmt.Sprintf("CREATE DATABASE `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName))
	if err != nil {
		return "", gerror.Wrap(err, "创建数据库失败")
	}
	
	// 导入SQL文件
	sqlFilePath := "manifest/sql/agent.sql"
	if _, err := os.Stat(sqlFilePath); os.IsNotExist(err) {
		// 如果相对路径不存在，尝试绝对路径
		sqlFilePath = "/app/manifest/sql/agent.sql"
		if _, err := os.Stat(sqlFilePath); os.IsNotExist(err) {
			return dbName, gerror.New("找不到agent.sql文件")
		}
	}
	
	// 使用mysql命令导入SQL
	cmd := exec.Command("mysql",
		"-h", strings.Split(dbHost, ":")[0],
		"-P", getPort(dbHost),
		"-u", dbUser,
		fmt.Sprintf("-p%s", dbPass),
		dbName,
	)
	
	// 读取SQL文件
	sqlContent, err := os.ReadFile(sqlFilePath)
	if err != nil {
		return dbName, gerror.Wrap(err, "读取SQL文件失败")
	}
	
	// 将SQL内容通过stdin传递给mysql命令
	cmd.Stdin = strings.NewReader(string(sqlContent))
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		g.Log().Errorf(ctx, "导入SQL失败: %s", string(output))
		return dbName, gerror.Wrap(err, "导入SQL文件失败")
	}
	
	g.Log().Infof(ctx, "成功创建代理数据库: %s", dbName)
	return dbName, nil
}

// getPort 从主机字符串中提取端口，默认返回3306
func getPort(host string) string {
	parts := strings.Split(host, ":")
	if len(parts) > 1 {
		return parts[1]
	}
	return "3306"
}

// rollbackAgentDatabase 回滚：删除已创建的数据库
func (s *sSystemLogic) rollbackAgentDatabase(ctx context.Context, dbName string) error {
	if dbName == "" {
		return nil
	}
	
	// 获取数据库连接配置
	dbConfig := g.Cfg().MustGet(ctx, "database.default").MapStrVar()
	if dbConfig == nil {
		return gerror.New("无法获取数据库配置")
	}
	
	// 解析连接字符串
	link := dbConfig["link"].String()
	// 移除 mysql: 前缀
	if strings.HasPrefix(link, "mysql:") {
		link = strings.TrimPrefix(link, "mysql:")
	}
	
	parts := strings.Split(link, "@tcp(")
	if len(parts) != 2 {
		return gerror.New("数据库连接字符串格式错误")
	}
	
	authParts := strings.Split(parts[0], ":")
	if len(authParts) < 2 {
		return gerror.New("数据库认证信息格式错误")
	}
	
	dbUser := authParts[0]
	dbPass := authParts[1]
	
	hostParts := strings.Split(parts[1], ")/")
	if len(hostParts) < 1 {
		return gerror.New("数据库主机信息格式错误")
	}
	
	dbHost := strings.TrimSuffix(hostParts[0], ")")
	
	// 创建临时数据库连接
	tempDB, err := gdb.New(gdb.ConfigNode{
		Link: fmt.Sprintf("mysql:%s:%s@tcp(%s)/?loc=Local&parseTime=true&charset=utf8mb4", dbUser, dbPass, dbHost),
		Type: "mysql",
	})
	if err != nil {
		return gerror.Wrap(err, "创建数据库连接失败")
	}
	defer tempDB.Close(ctx)
	
	// 删除数据库
	_, err = tempDB.Exec(ctx, fmt.Sprintf("DROP DATABASE IF EXISTS `%s`", dbName))
	if err != nil {
		return gerror.Wrap(err, "删除数据库失败")
	}
	
	g.Log().Infof(ctx, "成功回滚代理数据库: %s", dbName)
	return nil
}