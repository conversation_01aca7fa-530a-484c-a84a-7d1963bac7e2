package transfer

import (
	v1 "admin-api/api/system/v1" // Import v1 for DTO definition
	"admin-api/internal/dao"
	"admin-api/internal/service/system/transfer" // Import the interface package
	"admin-api/internal/utility"
	"admin-api/utility/amount" // Import amount utility
	"context"
	"math/big"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	// "github.com/gogf/gf/v2/util/gconv" // Removed unused import
)

// transferRepository 实现了 ITransferRepository 接口
type transferRepository struct{}

// NewTransferRepository 创建一个新的 transferRepository 实例
func NewTransferRepository() transfer.ITransferRepository {
	return &transferRepository{}
}

// ListAdmin 获取后台转账记录列表
func (r *transferRepository) ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.TransferAdminInfoItem, total int, err error) {
	// 使用 DAO 中已有的关联查询方法
	// 注意：需要确认 dao.Transfers.ListAdminTransfers 的返回类型
	// 假设它返回 []*dao.TransferAdminInfo, total, error
	daoList, total, err := dao.Transfers.ListAdminTransfers(ctx, page, pageSize, condition)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "transferRepository.ListAdmin: 调用 DAO 查询失败")
	}

	// 将 DAO 返回的结构列表转换为 DTO 列表
	list = make([]*v1.TransferAdminInfoItem, 0, len(daoList))
	for _, item := range daoList {
		// 格式化金额
		amountStr := amount.FormatBalance(big.NewInt(item.Amount), item.TokenDecimals)

		dtoItem := &v1.TransferAdminInfoItem{
			TransferId:       item.TransferId,
			SenderUserId:     item.SenderUserId,
			SenderUsername:   item.SenderUsername,
			SenderAccount:    item.SenderAccount,
			ReceiverUserId:   item.ReceiverUserId,
			ReceiverUsername: item.ReceiverUsername,
			ReceiverAccount:  item.ReceiverAccount,
			TokenId:          item.TokenId,
			TokenSymbol:      item.TokenSymbol, // From tokens table
			TokenDecimals:    item.TokenDecimals,
			AmountStr:        amountStr, // 使用格式化后的金额
			Memo:             item.Memo,
			SenderTxId:       item.SenderTxId,
			ReceiverTxId:     item.ReceiverTxId,
			CreatedAt:        item.CreatedAt,
			MessageId:        item.MessageId,
			ChatId:           item.ChatId,
			Status:           item.Status,
			HoldId:           item.HoldId,
			ExpiresAt:        item.ExpiresAt,
			UpdatedAt:        item.UpdatedAt,
			NeedPass:         item.NeedPass == 1, // Convert int to bool
			Key:              item.Key,
			TransferSymbol:   item.Symbol, // From transfers table
			Message:          item.Message,
			InlineMessageId:  item.InlineMessageId,
			// 发送方代理和Telegram信息
			FirstAgentName:   item.FirstAgentName,
			SecondAgentName:  item.SecondAgentName,
			ThirdAgentName:   item.ThirdAgentName,
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
			// 接收方代理和Telegram信息
			ReceiverFirstAgentName:   item.ReceiverFirstAgentName,
			ReceiverSecondAgentName:  item.ReceiverSecondAgentName,
			ReceiverThirdAgentName:   item.ReceiverThirdAgentName,
			ReceiverTelegramId:       item.ReceiverTelegramId,
			ReceiverTelegramUsername: item.ReceiverTelegramUsername,
			ReceiverFirstName:        item.ReceiverFirstName,
		}
		list = append(list, dtoItem)
	}

	return list, total, nil
}

// GetAdminDetail 获取后台转账记录详情
func (r *transferRepository) GetAdminDetail(ctx context.Context, transferId int64) (detail *v1.TransferAdminInfoItem, err error) {
	// 使用 DAO 中已有的关联查询方法
	// 假设 dao.Transfers.GetAdminTransferDetail 返回 *dao.TransferAdminInfo, error
	daoDetail, err := dao.Transfers.GetAdminTransferDetail(ctx, transferId)
	if err != nil {
		// DAO 层可能已处理 Not Found，这里直接包装
		return nil, gerror.Wrapf(err, "transferRepository.GetAdminDetail: 调用 DAO 查询失败, TransferID: %d", transferId)
	}
	if daoDetail == nil {
		// DAO 层约定未找到返回 nil, nil
		return nil, nil
	}

	// 将 DAO 返回的结构转换为 DTO
	amountStr := amount.FormatBalance(big.NewInt(daoDetail.Amount), daoDetail.TokenDecimals)
	detail = &v1.TransferAdminInfoItem{
		TransferId:       daoDetail.TransferId,
		SenderUserId:     daoDetail.SenderUserId,
		SenderUsername:   daoDetail.SenderUsername,
		ReceiverUserId:   daoDetail.ReceiverUserId,
		ReceiverUsername: daoDetail.ReceiverUsername,
		TokenId:          daoDetail.TokenId,
		TokenSymbol:      daoDetail.TokenSymbol, // From tokens table
		TokenDecimals:    daoDetail.TokenDecimals,
		AmountStr:        amountStr,
		Memo:             daoDetail.Memo,
		SenderTxId:       daoDetail.SenderTxId,
		ReceiverTxId:     daoDetail.ReceiverTxId,
		CreatedAt:        daoDetail.CreatedAt,
		MessageId:        daoDetail.MessageId,
		ChatId:           daoDetail.ChatId,
		Status:           daoDetail.Status,
		HoldId:           daoDetail.HoldId,
		ExpiresAt:        daoDetail.ExpiresAt,
		UpdatedAt:        daoDetail.UpdatedAt,
		NeedPass:         daoDetail.NeedPass == 1, // Convert int to bool
		Key:              daoDetail.Key,
		TransferSymbol:   daoDetail.Symbol, // From transfers table
		Message:          daoDetail.Message,
		InlineMessageId:  daoDetail.InlineMessageId,
		// 发送方代理和Telegram信息
		FirstAgentName:   daoDetail.FirstAgentName,
		SecondAgentName:  daoDetail.SecondAgentName,
		ThirdAgentName:   daoDetail.ThirdAgentName,
		TelegramId:       daoDetail.TelegramId,
		TelegramUsername: daoDetail.TelegramUsername,
		FirstName:        daoDetail.FirstName,
		// 接收方代理和Telegram信息
		ReceiverFirstAgentName:   daoDetail.ReceiverFirstAgentName,
		ReceiverSecondAgentName:  daoDetail.ReceiverSecondAgentName,
		ReceiverThirdAgentName:   daoDetail.ReceiverThirdAgentName,
		ReceiverTelegramId:       daoDetail.ReceiverTelegramId,
		ReceiverTelegramUsername: daoDetail.ReceiverTelegramUsername,
		ReceiverFirstName:        daoDetail.ReceiverFirstName,
	}

	return detail, nil
}

// ListAdminWithAgentInfo 获取后台转账记录列表（带代理和telegram信息）
func (r *transferRepository) ListAdminWithAgentInfo(ctx context.Context, req *v1.ListAdminTransfersReq) (list []*v1.TransferAdminInfoItem, total int, err error) {
	// 构建查询条件
	condition := g.Map{}

	// 发送方用户ID
	if req.SenderUserId > 0 {
		condition["t.sender_user_id"] = req.SenderUserId
	}

	// 发送方用户名
	if req.SenderUsername != "" {
		condition["t.sender_username LIKE"] = "%" + req.SenderUsername + "%"
	}

	// 接收方用户ID
	if req.ReceiverUserId > 0 {
		condition["t.receiver_user_id"] = req.ReceiverUserId
	}

	// 接收方用户名
	if req.ReceiverUsername != "" {
		condition["t.receiver_username LIKE"] = "%" + req.ReceiverUsername + "%"
	}

	// 代币ID
	if req.TokenId > 0 {
		condition["t.token_id"] = req.TokenId
	}

	// 代币符号
	if req.TokenSymbol != "" {
		condition["token.symbol LIKE"] = "%" + req.TokenSymbol + "%"
	}

	// 转账状态
	if req.Status > 0 {
		condition["t.status"] = req.Status
	}

	// 转账ID
	if req.TransferId > 0 {
		condition["t.transfer_id"] = req.TransferId
	}

	// 发送方账户
	if req.SenderAccount != "" {
		condition["sender.account LIKE"] = "%" + req.SenderAccount + "%"
	}

	// 接收方账户
	if req.ReceiverAccount != "" {
		condition["receiver.account LIKE"] = "%" + req.ReceiverAccount + "%"
	}

	// 金额范围
	if req.AmountMin != "" {
		condition["t.amount >="] = req.AmountMin
	}
	if req.AmountMax != "" {
		condition["t.amount <="] = req.AmountMax
	}

	// 使用 DateRange 统一处理时间范围查询
	utility.AddDateRangeCondition(condition, req.DateRange, "t.created_at")

	// 新增：三级代理模糊查询条件（发送方）
	if req.FirstAgentName != "" {
		condition["sender_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["sender_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["sender_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// 新增：telegram查询条件（发送方）
	if req.TelegramId != "" {
		condition["sender_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["sender_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["sender_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 新增：根据 Key 精确查询
	if req.Key != "" {
		condition["t.key"] = req.Key
	}

	// 调用 DAO 方法
	daoList, total, err := dao.Transfers.ListAdminTransfersWithAgentInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "transferRepository.ListAdminWithAgentInfo: 调用 DAO 查询失败")
	}

	// 将 DAO 返回的结构列表转换为 DTO 列表
	list = make([]*v1.TransferAdminInfoItem, 0, len(daoList))
	for _, item := range daoList {
		// 格式化金额
		amountStr := amount.FormatBalance(big.NewInt(item.Amount), item.TokenDecimals)

		dtoItem := &v1.TransferAdminInfoItem{
			TransferId:       item.TransferId,
			SenderUserId:     item.SenderUserId,
			SenderUsername:   item.SenderUsername,
			SenderAccount:    item.SenderAccount,
			ReceiverUserId:   item.ReceiverUserId,
			ReceiverUsername: item.ReceiverUsername,
			ReceiverAccount:  item.ReceiverAccount,
			TokenId:          item.TokenId,
			TokenSymbol:      item.TokenSymbol,
			TokenDecimals:    item.TokenDecimals,
			AmountStr:        amountStr,
			Memo:             item.Memo,
			SenderTxId:       item.SenderTxId,
			ReceiverTxId:     item.ReceiverTxId,
			CreatedAt:        item.CreatedAt,
			MessageId:        item.MessageId,
			ChatId:           item.ChatId,
			Status:           item.Status,
			HoldId:           item.HoldId,
			ExpiresAt:        item.ExpiresAt,
			UpdatedAt:        item.UpdatedAt,
			NeedPass:         item.NeedPass == 1,
			Key:              item.Key,
			TransferSymbol:   item.Symbol,
			Message:          item.Message,
			InlineMessageId:  item.InlineMessageId,
			// 发送方代理和Telegram信息
			FirstAgentName:   item.FirstAgentName,
			SecondAgentName:  item.SecondAgentName,
			ThirdAgentName:   item.ThirdAgentName,
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
			// 接收方代理和Telegram信息
			ReceiverFirstAgentName:   item.ReceiverFirstAgentName,
			ReceiverSecondAgentName:  item.ReceiverSecondAgentName,
			ReceiverThirdAgentName:   item.ReceiverThirdAgentName,
			ReceiverTelegramId:       item.ReceiverTelegramId,
			ReceiverTelegramUsername: item.ReceiverTelegramUsername,
			ReceiverFirstName:        item.ReceiverFirstName,
		}
		list = append(list, dtoItem)
	}

	return list, total, nil
}
