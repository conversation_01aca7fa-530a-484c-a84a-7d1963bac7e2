package v1

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetGameCatalogList 获取游戏目录列表
func (s *sSystemLogic) GetGameCatalogList(ctx context.Context, req *v1.GetGameCatalogListReq) (res *v1.GetGameCatalogListRes, err error) {
	var (
		m    = dao.GameCatalog.Ctx(ctx)
		list []*entity.GameCatalog
	)

	// 构建查询条件
	if req.ProductType != "" {
		m = m.Where("product_type", req.ProductType)
	}
	if req.Platform != "" {
		m = m.Where("platform", req.Platform)
	}
	if req.GameType != "" {
		m = m.Where("game_type", req.GameType)
	}
	if req.DisplayStatus != nil {
		m = m.Where("display_status", *req.DisplayStatus)
	}
	if req.Keyword != "" {
		m = m.WhereLike("game_name", "%"+req.Keyword+"%").WhereOrLike("tcg_game_code", "%"+req.Keyword+"%")
	}

	// 获取总数
	total, err := m.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "count game catalog failed")
	}

	// 获取分页数据
	err = m.Page(req.Page, req.PageSize).
		OrderDesc("sort").
		OrderDesc("id").
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "get game catalog list failed")
	}

	// 转换为响应结构
	listItems := make([]*v1.GameCatalogListItem, len(list))
	for i, item := range list {
		listItems[i] = &v1.GameCatalogListItem{
			Id:            item.Id,
			TcgGameCode:   item.TcgGameCode,
			GameName:      item.GameName,
			ProductCode:   item.ProductCode,
			ProductType:   item.ProductType,
			Platform:      item.Platform,
			GameType:      item.GameType,
			DisplayStatus: item.DisplayStatus,
			TrialSupport:  item.TrialSupport,
			Sort:          item.Sort,
			CreatedAt:     item.CreatedAt,
			UpdatedAt:     item.UpdatedAt,
		}
	}

	res = &v1.GetGameCatalogListRes{
		Total: total,
		List:  listItems,
	}
	return res, nil
}

// GetGameCatalogDetail 获取游戏目录详情
func (s *sSystemLogic) GetGameCatalogDetail(ctx context.Context, req *v1.GetGameCatalogDetailReq) (res *v1.GetGameCatalogDetailRes, err error) {
	var gameCatalog entity.GameCatalog
	err = dao.GameCatalog.Ctx(ctx).Where("id", req.Id).Scan(&gameCatalog)
	if err != nil {
		return nil, gerror.Wrap(err, "get game catalog detail failed")
	}
	if gameCatalog.Id == 0 {
		return nil, gerror.New("game catalog not found")
	}

	res = &v1.GetGameCatalogDetailRes{
		GameCatalogListItem: &v1.GameCatalogListItem{
			Id:            gameCatalog.Id,
			TcgGameCode:   gameCatalog.TcgGameCode,
			GameName:      gameCatalog.GameName,
			ProductCode:   gameCatalog.ProductCode,
			ProductType:   gameCatalog.ProductType,
			Platform:      gameCatalog.Platform,
			GameType:      gameCatalog.GameType,
			DisplayStatus: gameCatalog.DisplayStatus,
			TrialSupport:  gameCatalog.TrialSupport,
			Sort:          gameCatalog.Sort,
			CreatedAt:     gameCatalog.CreatedAt,
			UpdatedAt:     gameCatalog.UpdatedAt,
		},
	}
	return res, nil
}

// CreateGameCatalog 创建游戏目录
func (s *sSystemLogic) CreateGameCatalog(ctx context.Context, req *v1.CreateGameCatalogReq) (res *v1.CreateGameCatalogRes, err error) {

	// 检查TCG游戏代码是否已存在
	count, err := dao.GameCatalog.Ctx(ctx).Where("tcg_game_code", req.TcgGameCode).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "check game code exists failed")
	}
	if count > 0 {
		return nil, gerror.New("tcg game code already exists")
	}

	// 创建游戏目录
	id, err := dao.GameCatalog.Ctx(ctx).InsertAndGetId(do.GameCatalog{
		TcgGameCode:   req.TcgGameCode,
		GameName:      req.GameName,
		ProductCode:   req.ProductCode,
		ProductType:   req.ProductType,
		Platform:      req.Platform,
		GameType:      req.GameType,
		DisplayStatus: req.DisplayStatus,
		TrialSupport:  req.TrialSupport,
		Sort:          req.Sort,
	})
	if err != nil {
		return nil, gerror.Wrap(err, "create game catalog failed")
	}

	res = &v1.CreateGameCatalogRes{
		Id: gconv.Uint64(id),
	}

	// 记录操作日志
	g.Log().Infof(ctx, "created game catalog: id=%d, tcg_game_code=%s, game_name=%s", id, req.TcgGameCode, req.GameName)

	return res, nil
}

// UpdateGameCatalog 更新游戏目录
func (s *sSystemLogic) UpdateGameCatalog(ctx context.Context, req *v1.UpdateGameCatalogReq) (res *v1.UpdateGameCatalogRes, err error) {

	// 检查游戏目录是否存在
	var gameCatalog entity.GameCatalog
	err = dao.GameCatalog.Ctx(ctx).Where("id", req.Id).Scan(&gameCatalog)
	if err != nil {
		return nil, gerror.Wrap(err, "get game catalog failed")
	}
	if gameCatalog.Id == 0 {
		return nil, gerror.New("game catalog not found")
	}

	// 如果修改了TCG游戏代码，检查是否重复
	if gameCatalog.TcgGameCode != req.TcgGameCode {
		count, err := dao.GameCatalog.Ctx(ctx).
			Where("tcg_game_code", req.TcgGameCode).
			Where("id != ?", req.Id).
			Count()
		if err != nil {
			return nil, gerror.Wrap(err, "check game code exists failed")
		}
		if count > 0 {
			return nil, gerror.New("tcg game code already exists")
		}
	}

	// 更新游戏目录
	_, err = dao.GameCatalog.Ctx(ctx).Where("id", req.Id).Update(do.GameCatalog{
		TcgGameCode:   req.TcgGameCode,
		GameName:      req.GameName,
		ProductCode:   req.ProductCode,
		ProductType:   req.ProductType,
		Platform:      req.Platform,
		GameType:      req.GameType,
		DisplayStatus: req.DisplayStatus,
		TrialSupport:  req.TrialSupport,
		Sort:          req.Sort,
	})
	if err != nil {
		return nil, gerror.Wrap(err, "update game catalog failed")
	}

	res = &v1.UpdateGameCatalogRes{}

	// 记录操作日志
	g.Log().Infof(ctx, "updated game catalog: id=%d, tcg_game_code=%s, game_name=%s", req.Id, req.TcgGameCode, req.GameName)

	return res, nil
}

// DeleteGameCatalog 删除游戏目录
func (s *sSystemLogic) DeleteGameCatalog(ctx context.Context, req *v1.DeleteGameCatalogReq) (res *v1.DeleteGameCatalogRes, err error) {
	// 检查游戏目录是否存在
	count, err := dao.GameCatalog.Ctx(ctx).Where("id", req.Id).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "check game catalog exists failed")
	}
	if count == 0 {
		return nil, gerror.New("game catalog not found")
	}

	// 删除游戏目录
	_, err = dao.GameCatalog.Ctx(ctx).Where("id", req.Id).Delete()
	if err != nil {
		return nil, gerror.Wrap(err, "delete game catalog failed")
	}

	res = &v1.DeleteGameCatalogRes{}

	// 记录操作日志
	g.Log().Infof(ctx, "deleted game catalog: id=%d", req.Id)

	return res, nil
}

// UpdateGameCatalogStatus 更新游戏目录状态
func (s *sSystemLogic) UpdateGameCatalogStatus(ctx context.Context, req *v1.UpdateGameCatalogStatusReq) (res *v1.UpdateGameCatalogStatusRes, err error) {
	// 使用事务
	err = dao.GameCatalog.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 检查游戏目录是否存在
		var gameCatalog entity.GameCatalog
		err := tx.Model("game_catalog").Where("id", req.Id).Scan(&gameCatalog)
		if err != nil {
			return gerror.Wrap(err, "get game catalog failed")
		}
		if gameCatalog.Id == 0 {
			return gerror.New("game catalog not found")
		}

		// 更新状态
		_, err = tx.Model("game_catalog").Where("id", req.Id).Update(g.Map{
			"display_status": req.DisplayStatus,
		})
		if err != nil {
			return gerror.Wrap(err, "update game catalog status failed")
		}

		// 记录操作日志
		statusText := "inactive"
		if req.DisplayStatus == 1 {
			statusText = "active"
		}
		g.Log().Infof(ctx, "updated game catalog status: id=%d, status=%s", req.Id, statusText)

		return nil
	})

	if err != nil {
		return nil, err
	}

	res = &v1.UpdateGameCatalogStatusRes{}
	return res, nil
}