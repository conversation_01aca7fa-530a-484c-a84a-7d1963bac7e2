package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/utility/excel"
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// generateUniqueInvitationCodeForTenant 生成租户唯一的邀请码
func (s *sSystemLogic) generateUniqueInvitationCodeForTenant(ctx context.Context) (string, error) {
	const maxAttempts = 10
	const codeLength = 8
	const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	for i := 0; i < maxAttempts; i++ {
		b := make([]byte, codeLength)
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		for i := range b {
			b[i] = letters[r.Intn(len(letters))]
		}
		code := string(b)

		// Check uniqueness in tenants table
		existingTenant, err := dao.Tenants.GetTenantByUsername(ctx, code) // Temporary check, should have GetByInvitationCode
		if err != nil {
			return "", gerror.Wrap(err, "检查邀请码唯一性失败")
		}
		if existingTenant == nil {
			return code, nil
		}
	}
	return "", gerror.New("无法生成唯一的邀请码")
}

// AddTenant 添加租户
func (s *sSystemLogic) AddTenant(ctx context.Context, req *v1.AddTenantReq) (res *v1.AddTenantRes, err error) {
	// 1. 校验唯一性
	existingTenant, err := dao.Tenants.GetTenantByUsername(ctx, req.Username)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查用户名唯一性失败")
	}
	if existingTenant != nil {
		return nil, gerror.NewCode(codes.CodeTenantUsernameExists)
	}

	// 只有当邮箱不为空时才检查唯一性
	if req.Email != "" {
		existingTenant, err = dao.Tenants.GetTenantByEmail(ctx, req.Email)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查邮箱唯一性失败")
		}
		if existingTenant != nil {
			return nil, gerror.NewCode(codes.CodeTenantEmailExists)
		}
	}

	// 2. 生成唯一邀请码
	invitationCode, err := s.generateUniqueInvitationCodeForTenant(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "生成邀请码失败")
	}

	// 3. 哈希密码
	hashedPassword, err := s.agentAuthService.HashPassword(ctx, req.Password)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "密码加密失败")
	}

	// 4. 准备插入数据
	newTenantEntity := &entity.Tenants{
		Username:         req.Username,
		PasswordHash:     hashedPassword,
		TenantName:       req.TenantName,
		Status:           *req.Status,
		InvitationCode:   invitationCode,
		BusinessName:     req.BusinessName,
		TelegramAccount:  req.TelegramAccount,
		TelegramBotName:  req.TelegramBotName,
		TelegramBotToken: req.TelegramBotToken,
		Group:            req.Group,
		Customer:         req.Customer,
		TelegramGroupsId: req.TelegramGroupsId,
	}

	// 只有当邮箱不为空时才设置邮箱字段
	if req.Email != "" {
		newTenantEntity.Email = req.Email
	}

	if req.Level != nil {
		newTenantEntity.Level = *req.Level
	}

	// 5. 插入数据库
	result, err := dao.Tenants.Ctx(ctx).Data(newTenantEntity).OmitEmpty().Insert()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "插入租户数据失败")
	}

	newTenantId, err := result.LastInsertId()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取新租户ID失败")
	}

	g.Log().Infof(ctx, "成功添加租户: ID=%d, 用户名=%s", newTenantId, req.Username)
	return &v1.AddTenantRes{}, nil
}

// GetTenantList 获取租户列表
func (s *sSystemLogic) GetTenantList(ctx context.Context, req *v1.GetTenantListReq) (res *v1.GetTenantListRes, err error) {
	// 1. 构建查询条件
	condition := g.Map{}
	if req.Username != "" {
		condition[dao.Tenants.Columns().Username+" LIKE"] = "%" + req.Username + "%"
	}
	if req.TenantName != "" {
		condition[dao.Tenants.Columns().TenantName+" LIKE"] = "%" + req.TenantName + "%"
	}
	if req.Email != "" {
		condition[dao.Tenants.Columns().Email+" LIKE"] = "%" + req.Email + "%"
	}
	if req.Level != nil {
		condition[dao.Tenants.Columns().Level] = *req.Level
	}
	if req.Status != nil {
		condition[dao.Tenants.Columns().Status] = *req.Status
	}

	// 处理日期范围
	if req.DateRange != "" {
		dates := strings.Split(req.DateRange, ",")
		if len(dates) == 2 {
			startDate := strings.TrimSpace(dates[0])
			endDate := strings.TrimSpace(dates[1])
			if startDate != "" && endDate != "" {
				condition[dao.Tenants.Columns().CreatedAt+" >= "] = startDate + " 00:00:00"
				condition[dao.Tenants.Columns().CreatedAt+" <= "] = endDate + " 23:59:59"
			}
		}
	}

	// 2. 如果是导出请求，导出所有数据
	if req.Export == 1 {
		return s.exportTenantList(ctx, condition)
	}

	// 3. 分页查询
	tenantList, err := dao.Tenants.GetTenantList(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询租户列表失败")
	}

	total, err := dao.Tenants.GetTenantCount(ctx, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询租户总数失败")
	}

	// 4. 转换为响应格式
	var tenantItems []*v1.TenantListItem
	for _, tenant := range tenantList {
		item := &v1.TenantListItem{
			Tenants:       tenant,
			InvitationUrl: s.generateTenantInvitationUrl(ctx, tenant.TelegramBotName, tenant.InvitationCode),
			CreatedAt:     tenant.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     tenant.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		tenantItems = append(tenantItems, item)
	}

	return &v1.GetTenantListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   total,
			TotalPage:   common.CalculateTotalPage(total, req.PageSize),
		},
		Data: tenantItems,
	}, nil
}

// exportTenantList 导出租户列表
func (s *sSystemLogic) exportTenantList(ctx context.Context, condition g.Map) (*v1.GetTenantListRes, error) {
	// 获取所有数据（不分页）
	tenantList, err := dao.Tenants.GetTenantList(ctx, 1, 999999, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询租户列表失败")
	}

	// 准备Excel数据
	exportData := make([]interface{}, len(tenantList))
	for i, tenant := range tenantList {
		statusText := "禁用"
		if tenant.Status == 1 {
			statusText = "启用"
		}

		exportData[i] = struct {
			TenantId        uint64 `excel:"租户ID"`
			Username        string `excel:"用户名"`
			TenantName      string `excel:"租户名称"`
			Email           string `excel:"邮箱"`
			Status          string `excel:"状态"`
			Level           uint64 `excel:"级别"`
			BusinessName    string `excel:"业务名称"`
			TelegramAccount int64  `excel:"Telegram账户"`
			CreatedAt       string `excel:"创建时间"`
		}{
			TenantId:        uint64(tenant.TenantId),
			Username:        tenant.Username,
			TenantName:      tenant.TenantName,
			Email:           tenant.Email,
			Status:          statusText,
			Level:           uint64(tenant.Level),
			BusinessName:    tenant.BusinessName,
			TelegramAccount: tenant.TelegramAccount,
			CreatedAt:       tenant.CreatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	// 定义Excel表头
	excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取

	// 调用Excel导出工具
	filename := fmt.Sprintf("tenant_list_%s", time.Now().Format("20060102_150405"))
	err = excel.ExportByStructs(ctx, excelTags, exportData, filename, "租户列表")
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "导出Excel失败")
	}

	return &v1.GetTenantListRes{}, nil
}

// generateTenantInvitationUrl 生成租户邀请链接
func (s *sSystemLogic) generateTenantInvitationUrl(ctx context.Context, botName, invitationCode string) string {
	if botName == "" || invitationCode == "" {
		return ""
	}
	// 生成Telegram机器人邀请链接
	return fmt.Sprintf("https://t.me/%s?start=%s", botName, invitationCode)
}

// GetTenant 获取单个租户详情
func (s *sSystemLogic) GetTenant(ctx context.Context, req *v1.GetTenantReq) (res *v1.GetTenantRes, err error) {
	tenant, err := dao.Tenants.GetTenantById(ctx, req.TenantId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询租户失败")
	}
	if tenant == nil {
		return nil, gerror.NewCode(codes.CodeTenantNotFound)
	}

	return &v1.GetTenantRes{
		Tenants:       tenant,
		InvitationUrl: s.generateTenantInvitationUrl(ctx, tenant.TelegramBotName, tenant.InvitationCode),
	}, nil
}

// EditTenant 编辑租户
func (s *sSystemLogic) EditTenant(ctx context.Context, req *v1.EditTenantReq) (res *v1.EditTenantRes, err error) {
	// 1. 校验租户是否存在
	existingTenant, err := dao.Tenants.GetTenantById(ctx, req.TenantId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询租户失败")
	}
	if existingTenant == nil {
		return nil, gerror.NewCode(codes.CodeTenantNotFound)
	}

	// 2. 校验邮箱唯一性（如果邮箱发生变化）
	if req.Email != "" && req.Email != existingTenant.Email {
		emailTenant, err := dao.Tenants.GetTenantByEmail(ctx, req.Email)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查邮箱唯一性失败")
		}
		if emailTenant != nil {
			return nil, gerror.NewCode(codes.CodeTenantEmailExists)
		}
	}

	// 3. 准备更新数据
	updateData := g.Map{
		dao.Tenants.Columns().TenantName:       req.TenantName,
		dao.Tenants.Columns().Status:           *req.Status,
		dao.Tenants.Columns().BusinessName:     req.BusinessName,
		dao.Tenants.Columns().TelegramAccount:  req.TelegramAccount,
		dao.Tenants.Columns().TelegramBotName:  req.TelegramBotName,
		dao.Tenants.Columns().Group:            req.Group,
		dao.Tenants.Columns().Customer:         req.Customer,
		dao.Tenants.Columns().TelegramGroupsId: req.TelegramGroupsId,
	}

	// 只有当 TelegramBotToken 不为空时才更新
	if req.TelegramBotToken != "" {
		updateData[dao.Tenants.Columns().TelegramBotToken] = req.TelegramBotToken
	}

	// 只有当邮箱不为空时才更新邮箱字段，避免空字符串导致唯一约束冲突
	if req.Email != "" {
		updateData[dao.Tenants.Columns().Email] = req.Email
	} else {
		// 如果邮箱为空，设置为 NULL
		updateData[dao.Tenants.Columns().Email] = nil
	}

	if req.Level != nil {
		updateData[dao.Tenants.Columns().Level] = *req.Level
	}

	// 4. 执行更新
	err = dao.Tenants.UpdateTenant(ctx, req.TenantId, updateData)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新租户信息失败")
	}

	g.Log().Infof(ctx, "成功更新租户: ID=%d", req.TenantId)
	return &v1.EditTenantRes{}, nil
}

// DeleteTenant 批量软删除租户
func (s *sSystemLogic) DeleteTenant(ctx context.Context, req *v1.DeleteTenantReq) (res *v1.DeleteTenantRes, err error) {
	if len(req.TenantIds) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要删除的租户")
	}

	// 执行批量软删除
	err = dao.Tenants.SoftDeleteTenantsByIds(ctx, nil, req.TenantIds)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "删除租户失败")
	}

	g.Log().Infof(ctx, "成功删除租户: IDs=%v", req.TenantIds)
	return &v1.DeleteTenantRes{}, nil
}

// UpdateTenantStatus 批量更新租户状态
func (s *sSystemLogic) UpdateTenantStatus(ctx context.Context, req *v1.UpdateTenantStatusReq) (res *v1.UpdateTenantStatusRes, err error) {
	if len(req.TenantIds) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要更新状态的租户")
	}

	// 构建更新数据
	updateData := g.Map{
		dao.Tenants.Columns().Status: req.Status,
	}

	// 批量更新状态
	for _, tenantId := range req.TenantIds {
		err = dao.Tenants.UpdateTenant(ctx, tenantId, updateData)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, fmt.Sprintf("更新租户%d状态失败", tenantId))
		}
	}

	g.Log().Infof(ctx, "成功更新租户状态: IDs=%v, Status=%d", req.TenantIds, req.Status)
	return &v1.UpdateTenantStatusRes{}, nil
}

// UpdateTenantPassword 更新租户密码
func (s *sSystemLogic) UpdateTenantPassword(ctx context.Context, req *v1.UpdateTenantPasswordReq) (res *v1.UpdateTenantPasswordRes, err error) {
	// 1. 校验租户是否存在
	existingTenant, err := dao.Tenants.GetTenantById(ctx, req.TenantId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询租户失败")
	}
	if existingTenant == nil {
		return nil, gerror.NewCode(codes.CodeTenantNotFound)
	}

	// 2. 哈希新密码
	hashedPassword, err := s.agentAuthService.HashPassword(ctx, req.NewPassword)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "密码加密失败")
	}

	// 3. 更新密码
	updateData := g.Map{
		dao.Tenants.Columns().PasswordHash: hashedPassword,
	}

	err = dao.Tenants.UpdateTenant(ctx, req.TenantId, updateData)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新密码失败")
	}

	g.Log().Infof(ctx, "成功更新租户密码: ID=%d", req.TenantId)
	return &v1.UpdateTenantPasswordRes{}, nil
}

// ResetTenant2FA 重置租户2FA
func (s *sSystemLogic) ResetTenant2FA(ctx context.Context, req *v1.ResetTenant2FAReq) (res *v1.ResetTenant2FARes, err error) {
	// 1. 校验租户是否存在
	existingTenant, err := dao.Tenants.GetTenantById(ctx, req.TenantId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询租户失败")
	}
	if existingTenant == nil {
		return nil, gerror.NewCode(codes.CodeTenantNotFound)
	}

	// 2. 重置2FA（清空Google Authenticator秘钥）
	updateData := g.Map{
		dao.Tenants.Columns().GoogleAuthenticatorSecret: "",
	}

	err = dao.Tenants.UpdateTenant(ctx, req.TenantId, updateData)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "重置2FA失败")
	}

	g.Log().Infof(ctx, "成功重置租户2FA: ID=%d", req.TenantId)
	return &v1.ResetTenant2FARes{}, nil
}
