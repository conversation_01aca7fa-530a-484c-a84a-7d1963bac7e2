package auth

import (
	"context"
	"database/sql"

	// "fmt"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes" // 需要 StatusEnabled
	"admin-api/internal/consts"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/auth"             // Import the interface package
	menuService "admin-api/internal/service/system/menu" // Import menu service for repo interface
	roleService "admin-api/internal/service/system/role" // Import role service for repo interface

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// authRepository implements the IAuthRepository interface.
type authRepository struct {
	authService auth.IAuthService
	roleRepo    roleService.IRoleRepository // Add roleMenuRepo field
	menuRepo    menuService.IMenuRepository // Add menuRepo field
}

// NewAuthRepository creates and returns a new instance of authRepository.
func NewAuthRepository(
	authService auth.IAuthService,
	roleMenuRepo roleService.IRoleRepository, // Add roleMenuRepo parameter
	menuRepo menuService.IMenuRepository, // Add menuRepo parameter
) auth.IAuthRepository {
	return &authRepository{
		authService: authService,
		roleRepo:    roleMenuRepo, // Initialize roleMenuRepo
		menuRepo:    menuRepo,     // Initialize menuRepo
	}
}

// FindAdminMemberByUsername retrieves an admin member by their username.
func (r *authRepository) FindAdminMemberByUsername(ctx context.Context, username string) (*entity.AdminMember, error) {
	g.Log().Debugf(ctx, "[AuthRepo] Finding admin member by username: %s", username) // 添加日志
	var member *entity.AdminMember
	err := dao.AdminMember.Ctx(ctx).Where(dao.AdminMember.Columns().Username, username).Scan(&member)
	if err != nil {
		// If no rows found, return nil, nil as per interface contract
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "Failed to find admin member by username: %s", username)
	}
	// If Scan succeeds but member is still nil (shouldn't happen with Scan), return nil, nil
	if member == nil {
		g.Log().Warningf(ctx, "[AuthRepo] Found no admin member for username: %s", username) // 添加日志
		return nil, nil
	}
	g.Log().Debugf(ctx, "[AuthRepo] Found admin member for username %s. PasswordHash: %s", username, member.PasswordHash) // 添加日志，记录读取到的哈希
	return member, nil
}

// GetAdminInfo 获取管理员详细信息，包含菜单、岗位、部门和角色信息
func (r *authRepository) GetAdminInfo(ctx context.Context, memberId int64) (*v1.GetAdminInfoRes, error) {
	var (
		result *v1.GetAdminInfoRes
		err    error
	)

	// TODO: 注入 memberRepo 并替换下面的 DAO 调用
	// 1. 获取管理员基本信息
	memberEntity, err := dao.AdminMember.Ctx(ctx).Where(dao.AdminMember.Columns().Id, memberId).One()
	if err != nil {
		return nil, gerror.Wrapf(err, "获取管理员信息失败 (ID: %d)", memberId)
	}
	if memberEntity == nil {
		return nil, gerror.NewCodef(codes.CodeMemberNotFound, "管理员不存在 (ID: %d)", memberId)
	}

	// 转换为响应结构
	result = &v1.GetAdminInfoRes{}
	err = gconv.Struct(memberEntity, result)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "管理员信息转换失败")
	}
	// // 构建前端所需的菜单树
	// result.Menus = r.buildMenuDataItemTree(menuEntities) // 调用新的辅助函数
	result.Menus, err = dao.AdminMenu.GetAllMenusTree(ctx, g.Map{
		dao.AdminMenu.Columns().Status: consts.StatusEnabled,
	}) // 调用新的辅助函数
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取菜单失败")
	}

	return result, nil
}

// UpdateAdminInfo updates the admin member's information.
// Note: This method was not part of the original request but exists in the file.
// Keeping it for now, assuming it's needed elsewhere.
func (r *authRepository) UpdateAdminInfo(ctx context.Context, memberId int64, req *v1.UpdateAdminInfoReq) error {
	// TODO: Inject memberRepo and replace DAO call
	// 1. Check if member exists
	member, err := dao.AdminMember.Ctx(ctx).Where(dao.AdminMember.Columns().Id, memberId).One()
	if err != nil {
		return gerror.Wrapf(err, "查询管理员信息失败 (ID: %d)", memberId)
	}
	if member == nil {
		return gerror.NewCodef(codes.CodeMemberNotFound, "管理员不存在 (ID: %d)", memberId)
	}

	// 2. Prepare update data
	updateData := g.Map{}
	// 使用 != "" 检查字符串，移除 * 解引用
	if req.RealName != "" {
		updateData[dao.AdminMember.Columns().RealName] = req.RealName
	}
	if req.Avatar != "" {
		updateData[dao.AdminMember.Columns().Avatar] = req.Avatar
	}
	if req.Email != "" {
		// Check email uniqueness excluding self
		exists, checkErr := dao.AdminMember.Ctx(ctx).Where(dao.AdminMember.Columns().Email, req.Email).WhereNot(dao.AdminMember.Columns().Id, memberId).Count()
		if checkErr != nil {
			return gerror.Wrapf(checkErr, "检查邮箱唯一性失败")
		}
		if exists > 0 {
			return gerror.NewCode(codes.CodeUserEmailExists) // 使用 User 模块的错误码
		}
		updateData[dao.AdminMember.Columns().Email] = req.Email
	}
	if req.Mobile != "" {
		// Check mobile uniqueness excluding self
		exists, checkErr := dao.AdminMember.Ctx(ctx).Where(dao.AdminMember.Columns().Mobile, req.Mobile).WhereNot(dao.AdminMember.Columns().Id, memberId).Count()
		if checkErr != nil {
			return gerror.Wrapf(checkErr, "检查手机号唯一性失败")
		}
		if exists > 0 {
			return gerror.NewCode(codes.CodeUserPhoneExists) // 使用 User 模块的错误码
		}
		updateData[dao.AdminMember.Columns().Mobile] = req.Mobile
	}
	// 移除 Remark 处理，因为它不在 UpdateAdminInfoReq 中

	// 3. Update password if provided
	if req.Password != "" {
		// 移除 OldPassword 校验，因为它不在 UpdateAdminInfoReq 中
		// // Verify old password - VerifyPassword 只返回 bool
		// passwordMatch := r.authService.VerifyPassword(ctx, *req.OldPassword, member[dao.AdminMember.Columns().PasswordHash].String())
		// if !passwordMatch {
		// 	// 使用 CodeLoginFailed 代替 CodePasswordIncorrect
		// 	return gerror.NewCode(codes.CodeLoginFailed)
		// }

		// Hash new password
		newPasswordHash, hashErr := r.authService.HashPassword(ctx, req.Password) // 移除 *
		if hashErr != nil {
			return gerror.WrapCode(codes.CodeInternalError, hashErr, "新密码哈希处理失败")
		}
		updateData[dao.AdminMember.Columns().PasswordHash] = newPasswordHash
	}

	// 4. Perform update if there's data to update
	if len(updateData) > 0 {
		_, updateErr := dao.AdminMember.Ctx(ctx).Data(updateData).Where(dao.AdminMember.Columns().Id, memberId).Update()
		if updateErr != nil {
			return gerror.Wrapf(updateErr, "更新管理员信息失败 (ID: %d)", memberId)
		}
	}

	return nil
}
