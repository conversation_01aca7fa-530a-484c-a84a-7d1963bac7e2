package auth

import (
	v1 "admin-api/api/system/v1" // Import v1 for request structure
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/auth" // Import the interface package
	"context"
	"database/sql"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
)

// loginLogRepository implements the ILoginLogRepository interface.
type loginLogRepository struct{}

// NewLoginLogRepository creates and returns a new instance of loginLogRepository.
func NewLoginLogRepository() auth.ILoginLogRepository {
	return &loginLogRepository{}
}

// List retrieves a paginated list of login logs based on the provided criteria.
func (r *loginLogRepository) List(ctx context.Context, req *v1.GetLoginLogListReq) ([]*entity.LoginLog, int, error) {
	m := dao.LoginLog.Ctx(ctx).WhereNull(dao.LoginLog.Columns().DeletedAt)

	// Apply filters
	if req.Id != 0 {
		m = m.Where(dao.LoginLog.Columns().Id, req.Id)
	}
	if req.Username != "" {
		m = m.WhereLike(dao.LoginLog.Columns().Username, "%"+req.Username+"%")
	}
	if req.Ip != "" {
		m = m.WhereLike(dao.LoginLog.Columns().LoginIp, "%"+req.Ip+"%") // Assuming DB column is login_ip
	}
	if req.Status != -1 { // -1 means all
		m = m.Where(dao.LoginLog.Columns().Status, req.Status)
	}
	// Handle date range using StartTime and EndTime if DateRange is deprecated or less precise
	var startTime, endTime *gtime.Time
	if req.StartTime != "" {
		startTime = gtime.NewFromStr(req.StartTime + " 00:00:00")
	}
	if req.EndTime != "" {
		endTime = gtime.NewFromStr(req.EndTime + " 23:59:59")
	}
	// Handle DateRange if StartTime/EndTime are not provided
	if startTime == nil && endTime == nil && req.DateRange != "" {
		parts := strings.Split(req.DateRange, ",")
		if len(parts) == 2 {
			startTime = gtime.NewFromStr(parts[0] + " 00:00:00")
			endTime = gtime.NewFromStr(parts[1] + " 23:59:59")
		}
	}
	if startTime != nil {
		m = m.WhereGTE(dao.LoginLog.Columns().LoginAt, startTime) // Assuming DB column is login_at
	}
	if endTime != nil {
		m = m.WhereLTE(dao.LoginLog.Columns().LoginAt, endTime)
	}

	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to count login logs")
	}

	if req.Page > 0 && req.PageSize > 0 {
		m = m.Page(req.Page, req.PageSize)
	}

	// Default sorting
	m = m.OrderDesc(dao.LoginLog.Columns().LoginAt) // Assuming DB column is login_at

	var results []*entity.LoginLog
	if err := m.Scan(&results); err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to retrieve login logs")
	}
	return results, total, nil
}

// FindByID retrieves a single login log entry by its ID.
func (r *loginLogRepository) FindByID(ctx context.Context, id int64) (*entity.LoginLog, error) {
	var log *entity.LoginLog
	err := dao.LoginLog.Ctx(ctx).Where(dao.LoginLog.Columns().Id, id).Scan(&log)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "Failed to find login log by ID: %d", id)
	}
	return log, nil
}

// CreateLoginLog records a new login attempt.
func (r *loginLogRepository) CreateLoginLog(ctx context.Context, log *entity.LoginLog) error {
	// Assuming log entity contains all necessary fields (Username, Ip, Status, Msg, etc.)
	// The DAO insert method likely takes the entity directly.
	_, err := dao.LoginLog.Ctx(ctx).Data(log).Insert()
	if err != nil {
		// It's often better not to expose detailed DB errors directly.
		// Log the detailed error internally and return a generic error message.
		// g.Log().Errorf(ctx, "Failed to insert login log: %v", err)
		return gerror.Wrap(err, "Failed to record login log") // Or return a more generic error
	}
	return nil
}
