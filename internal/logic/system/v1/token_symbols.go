package v1

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/consts"
	"admin-api/internal/dao"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type TokenSymbol struct {
	Symbol string `orm:"symbol"`
}

// GetTokenSymbols 获取代币符号列表 (用于搜索条件下拉框)
func (s *sSystemLogic) GetTokenSymbols(ctx context.Context, req *v1.GetTokenSymbolsReq) (res *v1.GetTokenSymbolsRes, err error) {

	var tokenSymbols []TokenSymbol
	// 初始化响应结构
	res = &v1.GetTokenSymbolsRes{
		Symbols: []string{},
	}

	// 构建查询条件：只查询状态为上架(1)的代币
	condition := g.Map{
		"status": consts.StatusEnabled, // 使用常量表示状态为1（上架）
	}

	// 执行查询，只获取 symbol 字段
	err = dao.Tokens.Ctx(ctx).
		Where(condition).
		WhereNull(dao.Tokens.Columns().DeletedAt). // 排除已删除的代币
		Fields(dao.Tokens.Columns().Symbol).       // 只查询 symbol 字段
		OrderAsc(dao.Tokens.Columns().Order).      // 按照 order 字段升序排序
		Scan(&tokenSymbols)

	if err != nil {
		return nil, gerror.Wrap(err, "获取代币符号列表失败")
	}
	for _, tokenSymbol := range tokenSymbols {
		if tokenSymbol.Symbol == "" {
			continue
		}
		res.Symbols = append(res.Symbols, tokenSymbol.Symbol)
	}

	return res, nil
}
