package v1

import (
	"context"

	v1 "admin-api/api/system/v1" // API 定义
)

// AssignRolesToAdminMember assigns roles to an admin member and syncs Casbin g rules.
// It first removes all existing roles of the admin member in the default domain,
// then adds the new roles.
func (s *sSystemLogic) AssignRolesToAdminMember(ctx context.Context, req *v1.AssignRolesToAdminMemberReq) (res *v1.AssignRolesToAdminMemberRes, err error) {
	// adminMemberIdStr := gconv.String(req.AdminMemberId)

	// e := casbin.GetEnforcer()
	// if e == nil {
	// 	return nil, gerror.NewCode(codes.CodeInternalError, "Casbin Enforcer 未初始化")
	// }

	// // 1. Remove all existing roles (grouping policies) for the admin member in the default domain.
	// // RemoveFilteredGroupingPolicy(fieldIndex, fieldValues...)
	// // fieldIndex 0 is sub (adminMemberIdStr)
	// // fieldIndex 2 is domain (casbin.DefaultDomain) - if we want to be specific about domain.
	// // If domain is not specified in RemoveFilteredGroupingPolicy, it might remove across all domains if sub matches.
	// // To be safe and explicit for default domain:
	// // e.RemoveFilteredGroupingPolicy(0, adminMemberIdStr) // This removes all g rules where sub = adminMemberIdStr
	// // A more precise way if other domains might exist for the same user:
	// // Get all roles for user in domain, then remove them one by one or build a list for RemoveGroupingPolicies.
	// // However, RemoveFilteredGroupingPolicy(0, adminMemberIdStr, "", casbin.DefaultDomain) should work if adapter supports it.
	// // Let's use the simpler e.RemoveFilteredGroupingPolicy(0, adminMemberIdStr) and assume it correctly targets the user's roles.
	// // If we need to be domain-specific for removal:
	// existingRoles, err := e.GetRolesForUser(adminMemberIdStr, casbin.DefaultDomain)
	// if err != nil {
	// 	g.Log().Warningf(ctx, "获取管理员 [%s] 在域 [%s] 的现有角色失败: %v", adminMemberIdStr, casbin.DefaultDomain, err)
	// 	// Decide if this is a fatal error. For now, proceed to add new roles.
	// }
	// if len(existingRoles) > 0 {
	// 	var policiesToRemove [][]string
	// 	for _, roleKey := range existingRoles {
	// 		policiesToRemove = append(policiesToRemove, []string{adminMemberIdStr, roleKey, casbin.DefaultDomain})
	// 	}
	// 	removed, errRemove := e.RemoveGroupingPolicies(policiesToRemove)
	// 	if errRemove != nil {
	// 		g.Log().Warningf(ctx, "从 Casbin 移除管理员 [%s] 的旧角色分配失败: %v", adminMemberIdStr, errRemove)
	// 		// Potentially return error here
	// 	}
	// 	if !removed && errRemove == nil {
	// 		g.Log().Infof(ctx, "管理员 [%s] 没有需要移除的旧角色分配，或者部分策略移除失败但未报错。", adminMemberIdStr)
	// 	} else if removed {
	// 		g.Log().Infof(ctx, "已从 Casbin 中移除管理员 [%s] 的 %d 条旧角色分配", adminMemberIdStr, len(policiesToRemove))
	// 	}
	// }

	// // 2. Add new roles (grouping policies) for the admin member.
	// if len(req.RoleKeys) > 0 {
	// 	var rulesToAdd [][]string
	// 	for _, roleKey := range req.RoleKeys {
	// 		// Ensure roleKey is valid by checking against admin_role table if necessary,
	// 		// but for now, assume roleKeys are valid.
	// 		rulesToAdd = append(rulesToAdd, []string{adminMemberIdStr, roleKey, casbin.DefaultDomain})
	// 	}

	// 	added, err := e.AddGroupingPolicies(rulesToAdd)
	// 	if err != nil {
	// 		g.Log().Errorf(ctx, "向 Casbin 批量添加管理员 [%s] 的新角色分配失败: %v", adminMemberIdStr, err)
	// 		return nil, gerror.Wrap(err, "分配管理员角色失败")
	// 	}
	// 	if !added && err == nil { // Casbin V2 AddGroupingPolicies might return (false, nil) if no policies were effectively added
	// 		g.Log().Infof(ctx, "管理员 [%s] 没有需要添加的新角色分配，或者部分策略添加失败但未报错。", adminMemberIdStr)
	// 	} else if added {
	// 		g.Log().Infof(ctx, "已向 Casbin 批量添加管理员 [%s] 的 %d 条新角色分配", adminMemberIdStr, len(rulesToAdd))
	// 	}
	// }

	// // Persist changes if auto-save is not enabled (though SyncedEnforcer usually handles this)
	// // err = e.SavePolicy()
	// // if err != nil {
	// //    g.Log().Errorf(ctx, "保存 Casbin 策略失败: %v", err)
	// // 	  return nil, gerror.Wrap(err, "保存 Casbin 策略失败")
	// // }

	res = &v1.AssignRolesToAdminMemberRes{}
	return res, nil
}

// GetAdminMemberAssignedRoles retrieves the roles assigned to an admin member from Casbin.
func (s *sSystemLogic) GetAdminMemberAssignedRoles(ctx context.Context, req *v1.GetAdminMemberAssignedRolesReq) (res *v1.GetAdminMemberAssignedRolesRes, err error) {
	// adminMemberIdStr := gconv.String(req.AdminMemberId)

	// e := casbin.GetEnforcer()
	// if e == nil {
	// 	return nil, gerror.NewCode(codes.CodeInternalError, "Casbin Enforcer 未初始化")
	// }

	// roleKeys, err := e.GetRolesForUser(adminMemberIdStr, casbin.DefaultDomain)
	// if err != nil {
	// 	g.Log().Errorf(ctx, "从 Casbin 获取管理员 [%s] 在域 [%s] 的角色列表失败: %v", adminMemberIdStr, casbin.DefaultDomain, err)
	// 	return nil, gerror.Wrap(err, "获取管理员角色列表失败")
	// }

	res = &v1.GetAdminMemberAssignedRolesRes{
		// RoleKeys: roleKeys,
		RoleKeys: []string{"admin"},
	}
	return res, nil
}
