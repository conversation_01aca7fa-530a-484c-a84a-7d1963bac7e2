package config

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/config" // Import the interface
	"context"

	// "github.com/gogf/gf/v2/database/gdb" // Removed unused import
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	// "github.com/gogf/gf/v2/util/gconv" // Removed unused import
)

type sConfigRepository struct{}

// NewConfigRepository creates and returns a repository object for config management.
func NewConfigRepository() config.IConfigRepository {
	return &sConfigRepository{}
}

// --- Category Repository Methods ---

func (r *sConfigRepository) CreateCategory(ctx context.Context, category *do.AdminConfigCategories) (id int64, err error) {
	// Assuming ID is auto-increment or handled by DAO/DB
	result, err := dao.AdminConfigCategories.Ctx(ctx).Data(category).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "创建配置分类数据库操作失败")
	}
	id, err = result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取新创建分类ID失败")
	}
	return id, nil
}

func (r *sConfigRepository) UpdateCategoryFields(ctx context.Context, id int64, data g.Map) error {
	_, err := dao.AdminConfigCategories.Ctx(ctx).Data(data).Where(dao.AdminConfigCategories.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "更新配置分类数据库操作失败 (ID: %d)", id)
	}
	return nil
}

func (r *sConfigRepository) DeleteCategories(ctx context.Context, ids []int64) error {
	// Assuming soft delete is not implemented based on schema, perform hard delete
	_, err := dao.AdminConfigCategories.Ctx(ctx).WhereIn(dao.AdminConfigCategories.Columns().Id, ids).Delete()
	if err != nil {
		return gerror.Wrapf(err, "删除配置分类数据库操作失败 (IDs: %v)", ids)
	}
	return nil
}

func (r *sConfigRepository) FindCategoryByID(ctx context.Context, id int64) (*entity.AdminConfigCategories, error) {
	var category *entity.AdminConfigCategories
	err := dao.AdminConfigCategories.Ctx(ctx).Where(dao.AdminConfigCategories.Columns().Id, id).Scan(&category)
	if err != nil {
		return nil, gerror.Wrapf(err, "按 ID 查询配置分类失败 (ID: %d)", id)
	}
	return category, nil
}

func (r *sConfigRepository) FindCategoryByKey(ctx context.Context, key string) (*entity.AdminConfigCategories, error) {
	var category *entity.AdminConfigCategories
	err := dao.AdminConfigCategories.Ctx(ctx).Where(dao.AdminConfigCategories.Columns().CategoryKey, key).Scan(&category)
	if err != nil {
		return nil, gerror.Wrapf(err, "按 Key 查询配置分类失败 (Key: %s)", key)
	}
	return category, nil
}

func (r *sConfigRepository) ListCategoriesPaginated(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminConfigCategories, total int, err error) {
	query := dao.AdminConfigCategories.Ctx(ctx).Where(condition).OrderDesc(dao.AdminConfigCategories.Columns().SortOrder).OrderDesc(dao.AdminConfigCategories.Columns().Id) // Default sort
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取配置分类总数失败")
	}
	if total == 0 {
		return []*entity.AdminConfigCategories{}, 0, nil
	}
	err = query.Page(page, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "分页查询配置分类列表失败")
	}
	return list, total, nil
}

func (r *sConfigRepository) CountItemsInCategory(ctx context.Context, categoryId int64) (int, error) {
	count, err := dao.AdminConfigItems.Ctx(ctx).Where(dao.AdminConfigItems.Columns().CategoryId, categoryId).Count()
	if err != nil {
		return 0, gerror.Wrapf(err, "统计分类下配置项数量失败 (CategoryID: %d)", categoryId)
	}
	return count, nil
}

// --- Item Repository Methods ---

func (r *sConfigRepository) CreateItem(ctx context.Context, item *do.AdminConfigItems) (id int64, err error) {
	result, err := dao.AdminConfigItems.Ctx(ctx).Data(item).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "创建配置项数据库操作失败")
	}
	id, err = result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取新创建配置项ID失败")
	}
	return id, nil
}

func (r *sConfigRepository) UpdateItemFields(ctx context.Context, id int64, data g.Map) error {
	_, err := dao.AdminConfigItems.Ctx(ctx).Data(data).Where(dao.AdminConfigItems.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "更新配置项数据库操作失败 (ID: %d)", id)
	}
	return nil
}

func (r *sConfigRepository) DeleteItems(ctx context.Context, ids []int64) error {
	_, err := dao.AdminConfigItems.Ctx(ctx).WhereIn(dao.AdminConfigItems.Columns().Id, ids).Delete()
	if err != nil {
		return gerror.Wrapf(err, "删除配置项数据库操作失败 (IDs: %v)", ids)
	}
	return nil
}

func (r *sConfigRepository) DeleteItemsByCategoryIDList(ctx context.Context, categoryIds []int64) error {
	_, err := dao.AdminConfigItems.Ctx(ctx).WhereIn(dao.AdminConfigItems.Columns().CategoryId, categoryIds).Delete()
	if err != nil {
		return gerror.Wrapf(err, "按分类ID列表删除配置项失败 (CategoryIDs: %v)", categoryIds)
	}
	return nil
}

func (r *sConfigRepository) FindItemByID(ctx context.Context, id int64) (*entity.AdminConfigItems, error) {
	var item *entity.AdminConfigItems
	err := dao.AdminConfigItems.Ctx(ctx).Where(dao.AdminConfigItems.Columns().Id, id).Scan(&item)
	if err != nil {
		return nil, gerror.Wrapf(err, "按 ID 查询配置项失败 (ID: %d)", id)
	}
	return item, nil
}

func (r *sConfigRepository) FindItemByKey(ctx context.Context, key string) (*entity.AdminConfigItems, error) {
	var item *entity.AdminConfigItems
	err := dao.AdminConfigItems.Ctx(ctx).Where(dao.AdminConfigItems.Columns().Key, key).Scan(&item)
	if err != nil {
		return nil, gerror.Wrapf(err, "按 Key 查询配置项失败 (Key: %s)", key)
	}
	return item, nil
}

func (r *sConfigRepository) ListItemsByCategoryID(ctx context.Context, categoryId int64) ([]*entity.AdminConfigItems, error) {
	var list []*entity.AdminConfigItems
	err := dao.AdminConfigItems.Ctx(ctx).Where(dao.AdminConfigItems.Columns().CategoryId, categoryId).OrderAsc(dao.AdminConfigItems.Columns().Id).Scan(&list) // Default sort by ID
	if err != nil {
		return nil, gerror.Wrapf(err, "按 CategoryID 查询配置项列表失败 (CategoryID: %d)", categoryId)
	}
	return list, nil
}

// ListItemsByCategoryIDPaginated retrieves a paginated list of config items for a specific category.
func (r *sConfigRepository) ListItemsByCategoryIDPaginated(ctx context.Context, categoryId int64, page, pageSize int, condition g.Map) (list []*entity.AdminConfigItems, total int, err error) {
	// Start with the base condition for category ID
	query := dao.AdminConfigItems.Ctx(ctx).Where(dao.AdminConfigItems.Columns().CategoryId, categoryId)

	// Apply additional conditions if provided
	if len(condition) > 0 {
		query = query.Where(condition)
	}

	// Get total count
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrapf(err, "获取分类下配置项总数失败 (CategoryID: %d)", categoryId)
	}

	// If no items found, return empty list
	if total == 0 {
		return []*entity.AdminConfigItems{}, 0, nil
	}

	// Apply pagination and sorting
	err = query.Page(page, pageSize).OrderAsc(dao.AdminConfigItems.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrapf(err, "分页查询配置项列表失败 (CategoryID: %d)", categoryId)
	}

	return list, total, nil
}
