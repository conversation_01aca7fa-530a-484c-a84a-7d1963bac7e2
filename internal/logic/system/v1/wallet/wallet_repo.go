package wallet

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/wallet" // Import the interface package
	"context"
	"database/sql"
	"errors"
	"fmt" // Add fmt import

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode" // Add gcode import
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv" // Add gconv import
)

// walletRepository 实现了 IWalletRepository 接口
type walletRepository struct{}

// NewWalletRepository 创建一个新的 walletRepository 实例
func NewWalletRepository() wallet.IWalletRepository {
	return &walletRepository{}
}

// GetOrCreate 获取或创建钱包记录 (需要处理并发)
func (r *walletRepository) GetOrCreate(ctx context.Context, userId uint32, tokenId uint32) (*entity.Wallets, error) {
	// 尝试查询钱包
	walletInfo, err := r.GetWalletInfo(ctx, userId, tokenId)
	if err != nil && !errors.Is(err, sql.ErrNoRows) { // 如果是数据库错误（非未找到）
		return nil, gerror.Wrap(err, "walletRepository.GetOrCreate: 查询钱包失败")
	}

	// 如果钱包存在，直接返回
	if walletInfo != nil {
		return walletInfo, nil
	}

	// 如果钱包不存在，尝试创建
	// 注意：这里有并发风险，多个请求可能同时尝试创建同一个钱包
	// 实际应用中需要使用数据库唯一约束或分布式锁来保证原子性
	newWallet := &do.Wallets{
		UserId:           int64(userId),
		TokenId:          tokenId, // 使用TokenId字段
		AvailableBalance: 0,
		FrozenBalance:    0,
		CreatedAt:        gtime.Now(),
		UpdatedAt:        gtime.Now(),
		// DecimalPlaces might need to be fetched from token info if required here
	}
	res, err := dao.Wallets.Ctx(ctx).Data(newWallet).Insert()
	if err != nil {
		// 如果插入失败是因为唯一约束冲突（另一个请求已创建），则再次查询
		if errors.Is(err, sql.ErrNoRows) || gerror.Code(err) == gcode.CodeDbOperationError {
			walletInfo, errRetry := r.GetWalletInfo(ctx, userId, tokenId)
			if errRetry != nil {
				return nil, gerror.Wrap(errRetry, "walletRepository.GetOrCreate: 重试查询钱包失败")
			}
			if walletInfo != nil {
				return walletInfo, nil
			}
			// 如果重试查询仍然失败，返回原始插入错误
		}
		return nil, gerror.Wrap(err, "walletRepository.GetOrCreate: 创建钱包失败")
	}

	// 创建成功，获取新钱包信息
	lastId, _ := res.LastInsertId() // 忽略获取 ID 错误，因为我们有 user_id 和 token_id
	walletInfo, err = r.GetWalletInfo(ctx, userId, tokenId)
	if err != nil {
		g.Log().Warningf(ctx, "walletRepository.GetOrCreate: 创建钱包后查询失败, UserID: %d, TokenID: %d, LastID: %d, Err: %v", userId, tokenId, lastId, err)
		// 即使查询失败，也返回刚插入的数据（可能不完整）
		var createdWallet entity.Wallets
		if convErr := gconv.Struct(newWallet, &createdWallet); convErr == nil {
			createdWallet.WalletId = int(lastId) // 将lastId转换为int类型
			// Manually assign potentially missing fields if needed
			createdWallet.AvailableBalance = 0
			createdWallet.FrozenBalance = 0
			return &createdWallet, nil
		}
		return nil, gerror.Wrap(err, "walletRepository.GetOrCreate: 创建钱包后查询失败")

	}
	return walletInfo, nil
}

// GetForUpdate 获取钱包记录并锁定以进行更新 (事务性)
func (r *walletRepository) GetForUpdate(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32) (*entity.Wallets, error) {
	var walletInfo *entity.Wallets
	err := dao.Wallets.Ctx(ctx).TX(tx).
		Where(dao.Wallets.Columns().UserId, userId).
		Where(dao.Wallets.Columns().TokenId, tokenId). // 使用TokenId字段
		LockUpdate().                                  // FOR UPDATE lock
		Scan(&walletInfo)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "walletRepository.GetForUpdate: 查询并锁定钱包失败, UserID: %d, TokenID: %d", userId, tokenId)
	}
	return walletInfo, nil
}

// UpdateBalance 更新钱包余额 (事务性)
func (r *walletRepository) UpdateBalance(ctx context.Context, tx gdb.TX, walletId uint64, availableDelta int64, frozenDelta int64) error {
	// 使用 gdb.Raw 构建 SQL 以原子方式更新余额
	// WHERE 条件需要确保余额足够扣减（如果 delta 为负）
	sqlStr := fmt.Sprintf( // Use imported fmt
		"UPDATE %s SET %s = %s + ?, %s = %s + ?, %s = ? WHERE %s = ? AND %s + ? >= 0 AND %s + ? >= 0",
		dao.Wallets.Table(),
		dao.Wallets.Columns().AvailableBalance, dao.Wallets.Columns().AvailableBalance, // Use Columns struct
		dao.Wallets.Columns().FrozenBalance, dao.Wallets.Columns().FrozenBalance, // Use Columns struct
		dao.Wallets.Columns().UpdatedAt,        // Use Columns struct
		dao.Wallets.Columns().WalletId,         // Use Columns struct
		dao.Wallets.Columns().AvailableBalance, // Condition check param 1
		dao.Wallets.Columns().FrozenBalance,    // Condition check param 2
	)

	_, err := tx.Ctx(ctx).Exec(sqlStr, availableDelta, frozenDelta, gtime.Now(), walletId, availableDelta, frozenDelta) // Correct tx.Exec usage

	if err != nil {
		// 检查是否是余额不足导致的更新失败（例如，通过检查受影响的行数或特定错误码）
		// if err == sql.ErrNoRows || affectedRows == 0 {
		//     return gerror.New("余额不足") // 或者更具体的错误码
		// }
		return gerror.Wrapf(err, "walletRepository.UpdateBalance: 更新钱包余额失败, WalletID: %d", walletId)
	}
	// 可以在这里检查受影响的行数，如果为 0，则可能是余额不足或记录不存在
	// res, _ := tx.Exec(...)
	// affected, _ := res.RowsAffected()
	// if affected == 0 { ... }
	return nil
}

// GetAvailableBalanceInt64 获取可用余额 (int64 格式)
func (r *walletRepository) GetAvailableBalanceInt64(ctx context.Context, userId uint32, tokenId uint32) (balance int64, found bool, err error) {
	walletInfo, err := r.GetWalletInfo(ctx, userId, tokenId)
	if err != nil {
		return 0, false, err // GetWalletInfo already wraps error
	}
	if walletInfo == nil {
		return 0, false, nil // Not found
	}
	return walletInfo.AvailableBalance, true, nil // Correct field name based on re-read
}

// GetWalletInfo 获取钱包实体信息 (包含可用和冻结余额)
func (r *walletRepository) GetWalletInfo(ctx context.Context, userId uint32, tokenId uint32) (*entity.Wallets, error) {
	var walletInfo *entity.Wallets
	err := dao.Wallets.Ctx(ctx).
		Where(dao.Wallets.Columns().UserId, userId).
		Where(dao.Wallets.Columns().TokenId, tokenId). // 使用TokenId字段
		Scan(&walletInfo)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "walletRepository.GetWalletInfo: 查询钱包信息失败, UserID: %d, TokenID: %d", userId, tokenId)
	}
	return walletInfo, nil
}

// List 获取钱包列表 (包含用户信息)
func (r *walletRepository) List(ctx context.Context, page, pageSize int, condition map[string]interface{}) (list []*v1.WalletListItem, total int, err error) {
	// 初始化列表
	list = make([]*v1.WalletListItem, 0)

	// 构建查询模型
	model := dao.Wallets.Ctx(ctx).As("w")

	// 关联用户表以获取用户名和账号
	model = model.LeftJoin(dao.Users.Table()+" u", "w.user_id = u.id")

	// 应用条件过滤
	if userId, ok := condition["userId"]; ok && userId != nil {
		model = model.Where("w."+dao.Wallets.Columns().UserId, userId)
	}
	if tokenId, ok := condition["tokenId"]; ok && tokenId != nil {
		model = model.Where("w."+dao.Wallets.Columns().TokenId, tokenId)
	}
	if walletType, ok := condition["type"]; ok && walletType != nil {
		model = model.Where("w."+dao.Wallets.Columns().Type, walletType)
	}

	// 获取总数
	total, err = model.Count()
	if err != nil || total == 0 {
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, 0, gerror.Wrap(err, "walletRepository.List: 获取钱包总数失败")
		}
		return list, 0, nil // 返回空列表
	}

	// 选择字段
	model = model.Fields(
		"w.*",
		"u."+dao.Users.Columns().Account+" as user_name",
		"u."+dao.Users.Columns().Account+" as user_account",
	)

	// 应用分页和排序
	model = model.Page(page, pageSize).OrderDesc("w." + dao.Wallets.Columns().UpdatedAt)

	// 执行查询
	var result []map[string]interface{}
	err = model.Scan(&result)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "walletRepository.List: 查询钱包列表失败")
	}

	// 转换结果
	for _, item := range result {
		walletItem := &v1.WalletListItem{}
		if err = gconv.Struct(item, walletItem); err != nil {
			g.Log().Warningf(ctx, "walletRepository.List: 转换钱包数据失败: %v", err)
			continue
		}

		// 手动设置账号字段
		walletItem.Account = gconv.String(item["user_account"]) // Corrected field name

		list = append(list, walletItem)
	}

	return list, total, nil
}

// ListWalletsWithDetails 获取钱包列表，包含用户和代币的详细信息
func (r *walletRepository) ListWalletsWithDetails(ctx context.Context, input interface{}) (list []*v1.WalletListItem, total int, err error) {
	// Type assert input to dao.WalletListInput
	listInput, ok := input.(dao.WalletListInput)
	if !ok {
		return nil, 0, gerror.New("walletRepository.ListWalletsWithDetails: invalid input type")
	}

	return dao.Wallets.ListWalletsWithDetails(ctx, listInput)
}

// ListWalletsWithAgentInfo 获取钱包列表（带代理和telegram信息）
// Deprecated: 此方法已废弃，仅保留空实现以保持接口兼容性
func (r *walletRepository) ListWalletsWithAgentInfo(ctx context.Context, req *v1.ListWalletsReq) (list []*v1.WalletListItem, total int, err error) {
	// 临时返回空数组，实际功能已迁移到Logic层直接调用DAO
	return make([]*v1.WalletListItem, 0), 0, nil
}
