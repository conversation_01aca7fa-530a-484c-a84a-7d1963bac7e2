package v1

import (
	"context" // 引入 errors 包
	"fmt"     // 用于构建 tree
	"sort"    // 用于排序

	// "strings" // 用于处理菜单权限 Key

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/cache"
	"admin-api/internal/codes"
	"admin-api/internal/consts"
	"admin-api/internal/dao"
	"admin-api/internal/model/do" // Keep for Add/Edit struct conversion for now
	"admin-api/internal/model/entity"
	"admin-api/internal/utility"

	// "admin-api/utility/casbin" // 导入 casbin 工具包

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetMenuList 获取菜单列表 (树状结构)
func (s *sSystemLogic) GetMenuList(ctx context.Context, req *v1.GetMenuListReq) (res *v1.GetMenuListRes, err error) {
	res = &v1.GetMenuListRes{
		Data: make([]*v1.MenuTreeNode, 0),
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
	}

	// 获取所有已启用的、完整且已排序的菜单树
	allEnabledAndSortedTree, err := dao.AdminMenu.GetAllMenusTree(ctx, g.Map{
		dao.AdminMenu.Columns().Status: consts.StatusEnabled,
	})
	if err != nil {
		g.Log().Errorf(ctx, "获取所有启用菜单树失败: %+v", err)
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "获取所有启用菜单树失败")
	}

	// 将完整树转换为 Map 以便快速查找顶级菜单
	// Key: menu.Id, Value: *v1.MenuTreeNode
	menuNodeMap := make(map[int64]*v1.MenuTreeNode)
	var flattenTree func([]*v1.MenuTreeNode)
	flattenTree = func(nodes []*v1.MenuTreeNode) {
		if nodes == nil {
			return
		}
		for _, node := range nodes {
			if node != nil && node.AdminMenu != nil {
				menuNodeMap[node.AdminMenu.Id] = node
				flattenTree(node.Children)
			}
		}
	}
	flattenTree(allEnabledAndSortedTree)

	// 构建查询条件以获取符合条件的顶级菜单实体
	condition := g.Map{
		dao.AdminMenu.Columns().Pid: 0, // 只查询顶级菜单
	}
	if req.Name != "" {
		// 使用 name 字段进行模糊查询
		condition[dao.AdminMenu.Columns().Name+" LIKE ?"] = "%" + req.Name + "%"
	}

	if req.Status != nil {
		condition[dao.AdminMenu.Columns().Status] = *req.Status
	} else {
		// 默认情况下，如果客户端没有指定状态，我们只从已启用的顶级菜单中筛选。
		condition[dao.AdminMenu.Columns().Status] = consts.StatusEnabled
	}
	
	// 使用统一的DateRange处理工具
	utility.AddDateRangeCondition(condition, req.DateRange)

	// 使用分页查询获取扁平化的顶级菜单实体列表
	topMenuEntities, total, err := s.menuRepo.ListPaginated(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		g.Log().Errorf(ctx, "获取顶层菜单列表失败: %+v", err)
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "获取顶层菜单列表失败")
	}

	// 从 menuNodeMap 中提取这些顶级菜单对应的完整节点（已包含排序好的子孙）
	resultTreeNodes := make([]*v1.MenuTreeNode, 0, len(topMenuEntities))
	for _, entity := range topMenuEntities {
		if entity != nil { // 安全检查
			if node, ok := menuNodeMap[entity.Id]; ok {
				resultTreeNodes = append(resultTreeNodes, node)
			}
		}
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)
	res.Data = resultTreeNodes

	return res, nil
}

// buildMenuTreeFromEntities 辅助函数：将扁平化的菜单实体列表构建成树形结构，并排序
func buildMenuTreeFromEntities(menus []*entity.AdminMenu) []*v1.MenuTreeNode {
	if len(menus) == 0 {
		return []*v1.MenuTreeNode{}
	}

	// 创建所有菜单节点映射
	menuMap := make(map[int64]*v1.MenuTreeNode)
	for _, menu := range menus {
		if menu != nil { // 安全检查
			menuMap[menu.Id] = &v1.MenuTreeNode{
				AdminMenu: menu,
				Children:  make([]*v1.MenuTreeNode, 0),
			}
		}
	}

	// 构建树形结构
	var rootNodes []*v1.MenuTreeNode
	for _, menu := range menus {
		if menu != nil { // 安全检查
			if node, ok := menuMap[menu.Id]; ok { // 确保节点在 map 中存在
				if menu.Pid != 0 {
					// 将当前节点添加到父节点的子节点列表中
					if parent, exists := menuMap[menu.Pid]; exists {
						parent.Children = append(parent.Children, node)
					}
				} else {
					// 如果是根节点（Pid=0），则添加到根节点列表
					rootNodes = append(rootNodes, node)
				}
			}
		}
	}

	// 递归排序所有节点的子节点
	var sortChildrenRecursive func([]*v1.MenuTreeNode)
	sortChildrenRecursive = func(nodes []*v1.MenuTreeNode) {
		if len(nodes) == 0 {
			return
		}
		for _, node := range nodes {
			if node != nil && len(node.Children) > 1 { // 安全检查
				sort.Slice(node.Children, func(i, j int) bool {
					// 主要按 Sort 字段升序排序
					if node.Children[i].AdminMenu.Sort != node.Children[j].AdminMenu.Sort {
						return node.Children[i].AdminMenu.Sort < node.Children[j].AdminMenu.Sort
					}
					// 如果 Sort 相同，则按 Id 升序排序以确保稳定性
					return node.Children[i].AdminMenu.Id < node.Children[j].AdminMenu.Id
				})
			}
			if node != nil { // 安全检查
				sortChildrenRecursive(node.Children) // 递归处理子节点的子节点
			}
		}
	}
	sortChildrenRecursive(rootNodes)

	return rootNodes
}

// GetUserAccessibleMenus 获取当前用户可访问的菜单树（基于缓存权限）
func (s *sSystemLogic) GetUserAccessibleMenus(ctx context.Context, req *v1.GetUserAccessibleMenusReq) (res *v1.GetUserAccessibleMenusRes, err error) {
	res = &v1.GetUserAccessibleMenusRes{
		Data: make([]*v1.MenuTreeNode, 0),
	}

	// 1. 从请求上下文中获取当前用户名
	r := g.RequestFromCtx(ctx)
	if r == nil {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取请求上下文")
	}

	usernameVar := r.GetCtxVar("username")
	if usernameVar.IsNil() {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取当前用户名")
	}

	username := usernameVar.String()
	if username == "" {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无效的用户名")
	}

	// 2. 从缓存获取用户菜单权限
	cacheManager := cache.GetInstance()
	menuPermissions, err := cacheManager.GetUserMenuPermissions(ctx, username)
	if err != nil {
		g.Log().Errorf(ctx, "获取用户菜单权限失败: username=%s, error=%v", username, err)
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取用户权限失败")
	}

	// 如果没有菜单权限，返回空菜单树
	if menuPermissions == nil || len(menuPermissions) == 0 {
		g.Log().Debugf(ctx, "用户没有菜单权限: username=%s", username)
		return res, nil
	}

	// 3. 提取有权限的菜单路径
	menuPaths := make([]string, 0, len(menuPermissions))
	for menuPath, hasPermission := range menuPermissions {
		if hasPermission {
			menuPaths = append(menuPaths, menuPath)
		}
	}

	// 如果没有有效权限，返回空菜单树
	if len(menuPaths) == 0 {
		g.Log().Debugf(ctx, "用户没有有效的菜单权限: username=%s", username)
		return res, nil
	}

	// 4. 根据权限路径获取菜单列表（只获取启用的菜单）
	menus, err := s.menuRepo.GetMenusByPaths(ctx, menuPaths, consts.StatusEnabled)
	if err != nil {
		g.Log().Errorf(ctx, "根据菜单路径获取菜单列表失败: username=%s, paths=%v, error=%v", username, menuPaths, err)
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取菜单列表失败")
	}

	// 5. 将菜单列表构建成树形结构
	menuTree := buildMenuTreeFromEntities(menus)

	// 6. 过滤菜单树，只保留用户有权限的菜单节点
	filteredMenuTree := filterMenuTreeByPermissions(menuTree, menuPermissions)

	// 7. 返回结果
	res.Data = filteredMenuTree
	g.Log().Debugf(ctx, "成功获取用户可访问菜单: username=%s, menuCount=%d", username, len(filteredMenuTree))
	return res, nil
}

// filterMenuTreeByPermissions 根据用户权限过滤菜单树
func filterMenuTreeByPermissions(menuTree []*v1.MenuTreeNode, permissions map[string]bool) []*v1.MenuTreeNode {
	if len(menuTree) == 0 || len(permissions) == 0 {
		return []*v1.MenuTreeNode{}
	}

	var filteredTree []*v1.MenuTreeNode

	for _, node := range menuTree {
		if node == nil || node.AdminMenu == nil {
			continue
		}

		menu := node.AdminMenu
		// 检查当前菜单是否有权限
		hasPermission, exists := permissions[menu.Path]
		if !exists || !hasPermission {
			// 如果当前菜单没有权限，但可能子菜单有权限，递归检查子菜单
			filteredChildren := filterMenuTreeByPermissions(node.Children, permissions)
			if len(filteredChildren) > 0 {
				// 如果子菜单有权限，保留父菜单但更新子菜单
				newNode := &v1.MenuTreeNode{
					AdminMenu: menu,
					Children:  filteredChildren,
				}
				filteredTree = append(filteredTree, newNode)
			}
			// 如果子菜单也没有权限，则完全忽略这个节点
		} else {
			// 当前菜单有权限，递归过滤子菜单
			filteredChildren := filterMenuTreeByPermissions(node.Children, permissions)
			newNode := &v1.MenuTreeNode{
				AdminMenu: menu,
				Children:  filteredChildren,
			}
			filteredTree = append(filteredTree, newNode)
		}
	}

	return filteredTree
}

// // GetMenu 获取菜单详情
// func (s *sSystemLogic) GetMenu(ctx context.Context, req *v1.GetMenuReq) (res *v1.GetMenuRes, err error) {
// 	if req.Id <= 0 {
// 		return nil, gerror.NewCodef(codes.CodeInvalidParameter, "菜单ID(%d)无效", req.Id)
// 	}

// 	menu, err := s.menuRepo.GetByID(ctx, req.Id) // Use menuRepo
// 	if err != nil {
// 		g.Log().Errorf(ctx, "获取菜单详情失败: %+v", err)
// 		// 保留原始错误码，但增加日志
// 		return nil, err
// 	}

// 	// 安全检查，确保返回的menu不为nil
// 	if menu == nil {
// 		g.Log().Errorf(ctx, "获取菜单详情返回空对象 (ID: %d)", req.Id)
// 		return nil, gerror.NewCodef(codes.CodeMenuNotFound, "菜单不存在 (ID: %d)", req.Id)
// 	}

// 	res = &v1.GetMenuRes{
// 		Data: menu,
// 	}
// 	return res, nil
// }

// AddMenu 新增菜单
func (s *sSystemLogic) AddMenu(ctx context.Context, req *v1.AddMenuReq) (res *v1.AddMenuRes, err error) {
	// 1. 校验菜单名称在同级下是否唯一 (Use Repository)
	exists, err := s.menuRepo.ExistsByNameAndPid(ctx, req.Name, req.Pid)
	if err != nil {
		return nil, err // Repository method should wrap error
	}
	if exists {
		return nil, gerror.NewCodef(codes.CodeMenuNameExists, "同级目录下已存在名称为 [%s] 的菜单", req.Name)
	}

	// 2. 校验父节点是否存在
	var parentMenu *entity.AdminMenu
	var level int
	var treePath string

	if req.Pid == 0 { // 根目录
		level = 1
		treePath = ",0,"
	} else {
		parentMenu, err = s.menuRepo.GetByID(ctx, req.Pid) // Use menuRepo
		if err != nil {
			// 优化错误处理，提供更明确的错误信息
			if gerror.Code(err) == codes.CodeMenuNotFound {
				return nil, gerror.NewCodef(codes.CodeMenuParentNotFound, "父菜单(ID: %d)不存在，请检查父菜单ID是否正确", req.Pid)
			}
			g.Log().Errorf(ctx, "查询父菜单失败: %+v", err)
			return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询父菜单失败 (ID: %d)", req.Pid)
		}

		// 安全检查，确保parentMenu不为nil
		if parentMenu == nil {
			g.Log().Errorf(ctx, "获取到空的父菜单对象 (ID: %d)", req.Pid)
			return nil, gerror.NewCodef(codes.CodeInternalError, "系统错误: 获取到空的父菜单对象 (ID: %d)", req.Pid)
		}

		// 移除父子类型兼容性校验
		level = parentMenu.Level + 1
		treePath = parentMenu.Tree
	}

	// 3. 准备插入数据 (移除类型校验)
	menuDo := &do.AdminMenu{}
	if err = gconv.Struct(req, menuDo); err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "结构体转换失败")
	}
	// Tree 和 Level 在事务中处理

	// 4. 在事务中执行插入和 Tree/Level 更新
	var lastInsertId int64
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 4.1 插入基础数据
		var createErr error
		// Create 方法不接收 tx，因为它在事务外被调用以获取 ID，然后在事务内更新 tree/level
		// 因此，我们将 Create 调用移到事务之外
		// lastInsertId, createErr = s.menuRepo.Create(ctx, tx, menuDo) // 移除 tx
		// 正确的逻辑：先在事务外 Create，再在事务内 Update tree/level
		// --> 这部分逻辑需要重构：Create 必须在事务内，但 Update 需要 ID
		// --> 解决方案：Create 返回 ID，然后在同一事务内 Update
		// --> 临时修复：移除 tx 参数传递，后续需要修改 Repo 接口和实现
		lastInsertId, createErr = s.menuRepo.Create(ctx, menuDo) // 移除 tx
		if createErr != nil {
			g.Log().Errorf(ctx, "事务中创建菜单基础数据失败: %+v", createErr)
			// 尝试从 createErr 获取更具体的错误码，如果 repo 层有包装的话
			return gerror.NewCodef(codes.CodeMenuCreateFailed, "创建菜单失败：%s", gerror.Current(createErr).Error())
		}
		if lastInsertId == 0 {
			// 插入成功但未返回 ID，这是异常情况
			g.Log().Errorf(ctx, "事务中创建菜单后未获取到 lastInsertId")
			return gerror.NewCode(codes.CodeInternalError, "创建菜单失败，无法获取新ID")
		}

		// 4.2 计算并更新 Tree 和 Level
		newLevel := level                                       // 使用之前计算的 level
		newTree := fmt.Sprintf("%s%d,", treePath, lastInsertId) // 使用父路径拼接新ID

		// 注意：menuRepo.Update 需要接收 tx
		updateErr := s.menuRepo.Update(ctx, tx, lastInsertId, g.Map{
			"tree":  newTree,
			"level": newLevel,
		}) // 假设 menuRepo.Update 已修改为接收 tx
		if updateErr != nil {
			g.Log().Errorf(ctx, "事务中更新菜单 Tree/Level 失败 (ID: %d): %v", lastInsertId, updateErr)
			// 尝试从 updateErr 获取更具体的错误码
			return gerror.WrapCode(codes.CodeInternalError, updateErr, "更新菜单层级信息失败")
		}

		// // 4.3 创建关联的菜单权限
		// menuPermissionKey := "menu:" + req.Path // 构造权限 Key
		// addPermReq := &v1.AddPermissionReq{
		// 	Pid:    0, // 菜单权限 Pid 统一设置为 0
		// 	Name:   req.Name,
		// 	Key:    menuPermissionKey,
		// 	Type:   2, // 菜单权限类型
		// 	Status: req.Status,
		// 	Sort:   req.Sort,
		// 	Remark: "自动生成的菜单权限：" + req.Name,
		// }
		// // 调用 s.AddPermission 创建权限，传入当前事务 tx
		// _, permCreateErr := s.AddPermission(ctx, tx, addPermReq)
		// if permCreateErr != nil {
		// 	g.Log().Errorf(ctx, "事务中创建关联菜单权限失败: %+v", permCreateErr)
		// 	// Return the error to trigger transaction rollback
		// 	return gerror.Wrap(permCreateErr, "创建关联菜单权限失败")
		// }

		return nil // 事务提交
	})

	if err != nil {
		// 如果事务失败，返回错误
		return nil, err
	}

	// 5. 返回结果

	res = &v1.AddMenuRes{
		Id: lastInsertId,
	}
	return res, nil
}

// EditMenu 编辑菜单
func (s *sSystemLogic) EditMenu(ctx context.Context, req *v1.EditMenuReq) (res *v1.EditMenuRes, err error) {
	// 1. 校验菜单是否存在并获取旧菜单信息
	oldMenu, err := s.menuRepo.GetByID(ctx, req.Id) // Use oldMenu to store original data
	if err != nil {
		return nil, err // Repository method handles NotFound
	}

	// 2. 校验菜单名称在同级下是否唯一 (排除自身) (Use Repository)
	exists, err := s.menuRepo.ExistsByNameAndPid(ctx, req.Name, req.Pid, req.Id)
	if err != nil {
		return nil, err // Repository method wraps error
	}
	if exists {
		return nil, gerror.NewCodef(codes.CodeMenuNameExists, "同级目录下已存在名称为 [%s] 的菜单", req.Name)
	}

	// 3. 准备基础更新数据 (移除 Type, Permissions 等字段)
	updateData := g.Map{
		// Use string keys for g.Map
		"name":                 req.Name,
		"path":                 req.Path,
		"icon":                 req.Icon,
		"sort":                 req.Sort,
		"remark":               req.Remark,
		"status":               req.Status,
		"hide_in_menu":         req.HideInMenu,
		"hide_children_in_menu": req.HideChildrenInMenu,
		"target":               req.Target,
		"access":               req.Access,
		"key":                  req.Key,
	}

	// 4. 处理父部门变更
	pidChanged := oldMenu.Pid != req.Pid
	if pidChanged {
		// 5.1 校验不能将父菜单设置为自身或其子孙菜单
		if req.Pid == req.Id {
			return nil, gerror.NewCode(codes.CodeMenuCannotSetParentToChild)
		}
		descendants, err := s.menuRepo.FindDescendantsByTree(ctx, oldMenu.Tree) // Use menuRepo
		if err != nil {
			return nil, gerror.Wrapf(err, "查找子孙菜单失败 (ID: %d)", req.Id)
		}
		for _, descendant := range descendants {
			if descendant.Id == req.Pid {
				return nil, gerror.NewCode(codes.CodeMenuCannotSetParentToChild)
			}
		}

		// 5.2 获取新的父菜单信息并校验类型兼容性
		var newParentMenu *entity.AdminMenu
		var newLevel int
		var newParentTree string
		if req.Pid == 0 { // 设置为根目录
			newLevel = 1
			newParentTree = ",0,"
		} else {
			newParentMenu, err = s.menuRepo.GetByID(ctx, req.Pid) // Use menuRepo
			if err != nil {
				if gerror.Code(err) == codes.CodeMenuNotFound {
					return nil, gerror.NewCodef(codes.CodeMenuParentNotFound, "父菜单(ID: %d)不存在，请检查父菜单ID是否正确", req.Pid)
				}
				g.Log().Errorf(ctx, "查询新父菜单失败: %+v", err)
				return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询新父菜单失败 (ID: %d)", req.Pid)
			}

			// 安全检查，确保newParentMenu不为nil
			if newParentMenu == nil {
				g.Log().Errorf(ctx, "获取到空的新父菜单对象 (ID: %d)", req.Pid)
				return nil, gerror.NewCodef(codes.CodeInternalError, "系统错误: 获取到空的父菜单对象 (ID: %d)", req.Pid)
			}

			// 移除父子类型兼容性校验
			newLevel = newParentMenu.Level + 1
			newParentTree = newParentMenu.Tree
		}

		// 5.3 计算当前菜单新的 Level 和 Tree
		newMenuTree := fmt.Sprintf("%s%d,", newParentTree, req.Id)
		updateData["pid"] = req.Pid
		updateData["level"] = newLevel
		updateData["tree"] = newMenuTree

	}

	// 5. 执行更新（在事务中处理）
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 调用 Repository 层处理更新，区分 pid 是否改变
		if pidChanged {
			// 手动实现 UpdateWithDescendants 的逻辑
			// 1. 更新当前节点
			// 假设 menuRepo.Update 接收 tx
			err = s.menuRepo.Update(ctx, tx, req.Id, updateData)
			if err != nil {
				return err // Repository method should wrap error
			}

			// 2. 更新子孙节点
			newLevel := updateData["level"].(int)
			newTree := updateData["tree"].(string)
			oldTreePrefix := oldMenu.Tree
			levelDiff := newLevel - oldMenu.Level

			// 临时修复：移除 tx 参数传递，后续需要修改 Repo 接口和实现
			descendantsToUpdate, findErr := s.menuRepo.FindDescendantsByTree(ctx, oldTreePrefix) // 移除 tx
			if findErr != nil {
				return gerror.Wrapf(findErr, "查找子孙菜单失败 (TreePrefix: %s)", oldTreePrefix)
			}

			batchUpdateData := make(map[int64]g.Map)
			for _, descendant := range descendantsToUpdate {
				// 确保是真正的子孙且 Tree 格式正确
				if descendant.Id != req.Id && len(descendant.Tree) > len(oldTreePrefix) && descendant.Tree[:len(oldTreePrefix)] == oldTreePrefix {
					updatedTree := newTree + descendant.Tree[len(oldTreePrefix):]
					updatedLevel := descendant.Level + levelDiff
					batchUpdateData[descendant.Id] = g.Map{
						"tree":  updatedTree,
						"level": updatedLevel,
					}
				} else if descendant.Id != req.Id { // 排除自身，记录其他格式异常
					g.Log().Warningf(ctx, "子孙菜单 Tree 格式异常或非预期，跳过更新 (ID: %d, Tree: %s, ExpectedPrefix: %s)", descendant.Id, descendant.Tree, oldTreePrefix)
				}
			}

			if len(batchUpdateData) > 0 {
				// 假设 BatchUpdateLevelTree 接收 tx
				updateErr := s.menuRepo.BatchUpdateLevelTree(ctx, tx, batchUpdateData)
				if updateErr != nil {
					return updateErr // Repository method wraps error
				}
			}
			if err != nil {
				return err // Repository method should wrap error
			}
		} else {
			// 假设 menuRepo.Update 接收 tx
			err = s.menuRepo.Update(ctx, tx, req.Id, updateData)
			if err != nil {
				return err // Repository method should wrap error
			}
		}
		// --- Add Permission Update Logic Here ---
		// oldMenuPermissionKey := "menu:" + oldMenu.Path // Old permission Key

		// If Path changed, return error (方案A)
		// if oldMenu.Path != req.Path {
		// 	// Path changed, return error as per 方案A
		// 	return gerror.NewCode(codes.CodeInvalidParameter, "菜单路径 Path 不允许修改，请删除旧菜单后新建")
		// }

		// // If Path did NOT change, check if Name, Status, or Sort changed
		// if oldMenu.Name != req.Name || oldMenu.Status != req.Status || oldMenu.Sort != req.Sort {
		// 	// Find the corresponding permission record by Key
		// 	// Need to use the context with the transaction (ctx)
		// 	permission, permErr := s.permissionLogic.GetPermissionByKey(ctx, tx, oldMenuPermissionKey) // Use oldMenuPermissionKey (same as new key if path didn't change)
		// 	if permErr != nil {
		// 		g.Log().Errorf(ctx, "事务中获取关联菜单权限失败, Key: %s, Error: %v", oldMenuPermissionKey, permErr)
		// 		return gerror.Wrapf(permErr, "获取关联菜单权限失败, Key: %s", oldMenuPermissionKey)
		// 	}

		// 	// If found, update its Name, Status, Sort, Remark
		// 	if permission != nil {
		// 		editPermReq := &v1.EditPermissionReq{
		// 			Id:     permission.Id,
		// 			Pid:    gconv.Int64(permission.ParentKey), // Use existing Pid
		// 			Name:   req.Name,
		// 			Key:    permission.Key,             // Key remains unchanged
		// 			Type:   gconv.Int(permission.Type), // Type remains unchanged, convert to int
		// 			Status: req.Status,
		// 			Sort:   req.Sort,
		// 			Remark: "自动生成的菜单权限：" + req.Name, // Update Remark
		// 		}
		// 		// Call s.EditPermission to update the permission record
		// 		_, permEditErr := s.EditPermission(ctx, editPermReq)
		// 		if permEditErr != nil {
		// 			g.Log().Errorf(ctx, "事务中更新关联菜单权限失败: %+v", permEditErr)
		// 			return gerror.Wrap(permEditErr, "更新关联菜单权限失败")
		// 		}
		// 	} else {
		// 		// Permission not found for an existing menu with unchanged path. Log a warning.
		// 		g.Log().Warningf(ctx, "事务中未找到关联菜单权限，Key: %s", oldMenuPermissionKey)
		// 		return gerror.NewCodef(codes.CodeInternalError, "系统错误: 未找到关联菜单权限，Key: %s", oldMenuPermissionKey)
		// 	}
		// }

		return nil // 事务提交
	})

	if err != nil {
		return nil, err // Return transaction error
	}

	res = &v1.EditMenuRes{}
	return res, nil
}

// DeleteMenu 删除菜单
func (s *sSystemLogic) DeleteMenu(ctx context.Context, req *v1.DeleteMenuReq) (res *v1.DeleteMenuRes, err error) {
	// 1. 检查是否有子节点 (Use Repository)
	hasChildren, err := s.menuRepo.HasDirectChildren(ctx, req.Id)
	if err != nil {
		return nil, err // Repository method wraps error
	}
	if hasChildren {
		return nil, gerror.NewCode(codes.CodeMenuHasChildren)
	}

	// 2. 获取被删除菜单的信息，用于后续删除关联权限
	// deletedMenu, err := s.menuRepo.GetByID(ctx, req.Id) // Fetch menu before deletion
	// if err != nil {
	// 	// If menu not found, maybe it was already deleted? Or an error occurred.
	// 	// If GetByID returns NotFound, we can probably proceed or return NotFound.
	// 	// Let's return the error from GetByID if it's not NotFound.
	// 	if gerror.Code(err) != codes.CodeMenuNotFound {
	// 		return nil, err // Return other errors from GetByID
	// 	}
	// 	// If NotFound, the menu is already gone, nothing to delete.
	// 	// But the instruction implies we should delete the permission *if* the menu was successfully deleted.
	// 	// This suggests we should only proceed if GetByID finds the menu.
	// 	// Let's return the NotFound error if GetByID returns it.
	// 	return nil, err // Return NotFound error from GetByID
	// }

	// 3. 执行删除（在事务中处理关联数据）
	// err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
	// 	// 3.1 删除 admin_menu 表记录 (Use Repository)
	// 	err = s.menuRepo.Delete(ctx, tx, req.Id)
	// 	if err != nil {
	// 		return err // Repository method wraps error
	// 	}

	// 	// // 3.2 删除 admin_role_menu 表关联记录 (Use Repository)
	// 	// err = s.roleMenuRepo.DeleteByMenuID(ctx, tx, req.Id)
	// 	// if err != nil {
	// 	// 	return err // Repository method wraps error
	// 	// }

	// 	// 3.3 移除 Casbin 相关逻辑

	// 	// // 3.2 删除 admin_role_menu 表关联记录 (Use Repository)
	// 	// // err = s.roleMenuRepo.DeleteByMenuID(ctx, tx, req.Id)
	// 	// // if err != nil {
	// 	// // 	return err // Repository method wraps error
	// 	// // }

	// 	// // 3.3 删除关联的菜单权限
	// 	// menuPermissionKey := "menu:" + deletedMenu.Path // Construct permission Key from fetched menu
	// 	// // According to the instruction, find the permission by key *after* menu deletion.
	// 	// // Need to use the context with the transaction (ctx)
	// 	// permission, permErr := s.permissionLogic.GetPermissionByKey(ctx, tx, menuPermissionKey)
	// 	// if permErr != nil {
	// 	// 	g.Log().Errorf(ctx, "事务中查询关联菜单权限失败，Key: %s, Error: %v", menuPermissionKey, permErr)
	// 	// 	return gerror.Wrapf(permErr, "查询关联菜单权限失败，Key: %s", menuPermissionKey)
	// 	// } else if permission != nil {
	// 	// 	// If found, call s.DeletePermission to delete it (internal Casbin cleanup)
	// 	// 	delPermReq := &v1.DeletePermissionReq{
	// 	// 		Id: permission.Id,
	// 	// 	}
	// 	// 	// Call s.DeletePermission to delete the permission (internal Casbin cleanup)
	// 	// 	_, permDeleteErr := s.DeletePermission(ctx, delPermReq)
	// 	// 	if permDeleteErr != nil {
	// 	// 		g.Log().Errorf(ctx, "事务中删除关联菜单权限失败: %+v", permDeleteErr)
	// 	// 		return gerror.Wrap(permDeleteErr, "删除关联菜单权限失败")
	// 	// 	}
	// 	// } else {
	// 	// 	// Permission not found for a menu that was just deleted. Log a warning.
	// 	// 	g.Log().Warningf(ctx, "事务中未找到关联菜单权限，Key: %s", menuPermissionKey)
	// 	// 	return gerror.NewCodef(codes.CodeInternalError, "系统错误: 未找到关联菜单权限，Key: %s", menuPermissionKey)
	// 	// }

	// 	return nil // 事务提交
	// })

	if err != nil {
		return nil, err // Return transaction error
	}

	res = &v1.DeleteMenuRes{}
	return res, nil
}

// SyncMenuPermissions 同步菜单到权限表
func (s *sSystemLogic) SyncMenuPermissions(ctx context.Context, req *v1.SyncMenuPermissionsReq) (res *v1.SyncMenuPermissionsRes, err error) {
	res = &v1.SyncMenuPermissionsRes{} // 初始化 res

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var internalErr error

		// 步骤 2.1: 清除旧的菜单权限 (type="2")
		_, internalErr = tx.Model(dao.AdminPermissions.Table()).Where(dao.AdminPermissions.Columns().Type, "2").Delete()
		if internalErr != nil {
			return gerror.Wrap(internalErr, "清除旧菜单权限失败 (type='2')")
		}
		g.Log().Info(ctx, "已清除所有 type='2' 的旧菜单权限")

		// 步骤 2.2: 获取所有状态的菜单树
		allMenuTree, internalErr := dao.AdminMenu.GetAllMenusTree(ctx, g.Map{}) // 获取所有菜单
		if internalErr != nil {
			return gerror.Wrap(internalErr, "获取所有菜单树失败")
		}
		if len(allMenuTree) == 0 {
			g.Log().Info(ctx, "菜单表中没有数据，无需同步权限")
			return nil // 没有菜单，事务正常结束
		}

		// 步骤 2.3: 初始化辅助数据结构并调用递归函数
		processedMenuIDs := make(map[int64]bool)
		// 注意：res 是在事务外部声明的指针，在事务内部填充其字段
		internalErr = syncPermissionsRecursive(ctx, tx, allMenuTree, 0, s, res, processedMenuIDs)
		if internalErr != nil {
			// 递归函数内部的错误会传递到这里，导致事务回滚
			return internalErr
		}

		return nil // 事务成功提交
	})

	if err != nil {
		g.Log().Errorf(ctx, "SyncMenuPermissions 事务执行失败: %v", err)
		return nil, err // 将事务错误返回给调用者
	}

	g.Log().Infof(ctx, "菜单权限同步完成：总扫描菜单 %d, 新创建权限 %d, 已存在权限 %d", res.TotalScanned, res.CreatedCount, res.ExistingCount)
	return res, nil
}

// syncPermissionsRecursive 递归同步菜单权限
func syncPermissionsRecursive(ctx context.Context, tx gdb.TX, menuNodes []*v1.MenuTreeNode, parentPermissionId int64, s *sSystemLogic, res *v1.SyncMenuPermissionsRes, processedMenuIDs map[int64]bool) error {
	for _, menuNode := range menuNodes {
		if menuNode == nil || menuNode.AdminMenu == nil {
			continue
		}
		menu := menuNode.AdminMenu

		if _, exists := processedMenuIDs[menu.Id]; exists {
			continue
		}
		processedMenuIDs[menu.Id] = true

		if menu.Path == "" {
			g.Log().Debugf(ctx, "菜单 [ID: %d, Name: %s] Path 为空，跳过权限同步", menu.Id, menu.Name)
			continue
		}

		res.TotalScanned++

		permissionKey := "menu:" + menu.Path
		permissionName := menu.Name
		currentPermissionId := int64(0)

		// 假设 permissionLogic 的方法可以通过 Ctx(ctx).TX(tx) 来使用外部事务
		existingPermission, getErr := s.permissionLogic.GetPermissionByKey(ctx, tx, permissionKey)
		if getErr != nil && gerror.Code(getErr) != codes.CodeNotFound {
			g.Log().Warningf(ctx, "检查权限 [%s] 是否存在时发生意外错误: %v，跳过此菜单", permissionKey, getErr)
			// return gerror.Wrapf(getErr, "检查权限 %s 失败", permissionKey) // 如果希望单个错误中断整个同步
			continue // 当前设计：跳过此菜单及其子项
		}

		if existingPermission != nil {
			res.ExistingCount++
			currentPermissionId = existingPermission.Id
			g.Log().Debugf(ctx, "菜单 [ID: %d, Path: %s] 的权限 [%s] 已存在 (ID: %d)", menu.Id, menu.Path, permissionKey, currentPermissionId)
			// 当前计划：不更新已存在权限的属性。
		} else {
			addPermReq := &v1.AddPermissionReq{
				Pid:    parentPermissionId,
				Name:   permissionName,
				Key:    permissionKey,
				Type:   2, // 权限类型为整数 2 (菜单)
				Status: menu.Status,
				Sort:   menu.Sort,
				Remark: "自动同步生成的菜单权限：" + menu.Name,
			}
			// 调用已修改的 AddPermission，传入事务 tx
			addRes, addErr := s.AddPermission(ctx, tx, addPermReq)
			if addErr != nil {
				g.Log().Warningf(ctx, "为菜单 [ID: %d, Path: %s] 创建权限 [%s] 失败: %v", menu.Id, menu.Path, permissionKey, addErr)
				// return gerror.Wrapf(addErr, "为菜单 %s 创建权限 %s 失败", menu.Name, permissionKey) // 如果希望单个错误中断
				continue // 当前设计：跳过此菜单及其子项
			}
			res.CreatedCount++
			currentPermissionId = addRes.Id
			g.Log().Infof(ctx, "为菜单 [ID: %d, Path: %s] 成功创建权限 [%s] (ID: %d, ParentPID: %d)", menu.Id, menu.Path, permissionKey, currentPermissionId, parentPermissionId)
		}

		if len(menuNode.Children) > 0 {
			if currentPermissionId != 0 { // 仅当父权限成功处理后才递归子权限
				err := syncPermissionsRecursive(ctx, tx, menuNode.Children, currentPermissionId, s, res, processedMenuIDs)
				if err != nil {
					// 将错误向上传播，以便主事务可以回滚
					return err
				}
			} else {
				g.Log().Warningf(ctx, "菜单 [ID: %d, Name: %s] 自身权限处理失败或未找到，其子菜单的权限将不会被正确设置父级。", menu.Id, menu.Name)
			}
		}
	}
	return nil
}
