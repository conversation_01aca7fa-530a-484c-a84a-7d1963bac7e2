package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/utility"
	"context"
	"math"

	// "admin-api/internal/dao" // Removed direct DAO dependency
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal" // Import decimal library for precise amount comparisons
)

// GetTokenList 获取代币列表
func (s *sSystemLogic) GetTokenList(ctx context.Context, req *v1.GetTokenListReq) (res *v1.GetTokenListRes, err error) {
	// Initialize response struct directly
	res = &v1.GetTokenListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*entity.Tokens, 0), // Initialize empty slice
	}

	condition := g.Map{}

	condition["token_standard != ?"] = "fiat" // Use string literal

	if req.Network != nil && *req.Network != "" {
		condition["network"] = *req.Network // Use string literal
	}
	if req.Symbol != nil && *req.Symbol != "" {
		condition["symbol LIKE ?"] = "%" + *req.Symbol + "%" // Use string literal
	}
	if req.IsActive != nil {
		condition["is_active"] = *req.IsActive // Use string literal
	}
	
	// 使用统一的DateRange处理工具
	utility.AddDateRangeCondition(condition, req.DateRange)

	// Default sorting if not provided or invalid
	orderBy := "order" // Use string literal, assuming 'order' is the column name
	orderDirection := "asc"
	if req.OrderBy != nil {
		orderBy = *req.OrderBy // Assuming validation happens in DAO or API layer for allowed fields
	}
	if req.OrderDirection != nil {
		orderDirection = *req.OrderDirection // Assuming validation happens in API layer (in:asc,desc)
	}

	// Use repository to get the list
	list, total, err := s.tokenRepo.List(ctx, req.Page, req.PageSize, condition, orderBy, orderDirection)
	if err != nil {
		return nil, gerror.Wrap(err, "获取代币列表失败")
	}

	res.Data = list
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	return res, nil
}

// GetTokenDetail 获取代币详情
func (s *sSystemLogic) GetTokenDetail(ctx context.Context, req *v1.GetTokenDetailReq) (res *v1.GetTokenDetailRes, err error) {
	// Extract token_id from request meta (path parameter)
	r := ghttp.RequestFromCtx(ctx)
	tokenId := gconv.Uint(r.Get("token_id").String()) // Use gconv for safe conversion
	if tokenId == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的代币ID")
	}

	// Use repository to get the token by ID
	token, err := s.tokenRepo.GetByID(ctx, tokenId)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询代币详情失败, ID: %d", tokenId)
	}
	if token == nil {
		return nil, gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在, ID: %d", tokenId)
	}

	res = &v1.GetTokenDetailRes{
		Tokens: token, // Assign the found token entity
	}
	return res, nil
}

// CreateToken 创建代币
func (s *sSystemLogic) CreateToken(ctx context.Context, req *v1.CreateTokenReq) (res *v1.CreateTokenRes, err error) {
	// --- Business Validation ---
	// 1. Check network + symbol uniqueness (use Repository)
	exists, err := s.tokenRepo.CheckExistence(ctx, req.Network, req.Symbol, "symbol", 0) // Use string literal for field
	if err != nil {
		return nil, gerror.Wrap(err, "检查代币符号唯一性失败")
	}
	if exists {
		return nil, gerror.NewCode(codes.CodeTokenNetworkSymbolExists)
	}

	// 2. Check network + contract_address uniqueness (if provided)
	if req.ContractAddress != nil && *req.ContractAddress != "" {
		exists, err = s.tokenRepo.CheckExistence(ctx, req.Network, *req.ContractAddress, "contract_address", 0) // Use string literal for field
		if err != nil {
			return nil, gerror.Wrap(err, "检查合约地址唯一性失败")
		}
		if exists {
			return nil, gerror.NewCode(codes.CodeTokenNetworkContractExists)
		}
	}

	// 3. Validate amount limits (min <= max if both are not -1)
	err = s.validateAmountLimits(
		req.MinDepositAmount, req.MaxDepositAmount,
		req.MinWithdrawalAmount, req.MaxWithdrawalAmount,
		// MinWithdrawalFee, MaxWithdrawalFee removed
	)
	if err != nil {
		return nil, err // Returns CodeTokenInvalidAmountLimit if validation fails
	}

	// 4. Validate withdrawal fee amount (must be non-negative)
	if req.WithdrawalFeeAmount != nil {
		feeAmountDec, parseErr := decimal.NewFromString(*req.WithdrawalFeeAmount)
		if parseErr != nil || feeAmountDec.IsNegative() {
			return nil, gerror.NewCodef(codes.CodeTokenInvalidFeeAmount, "提现手续费金额格式无效或为负数: %s", *req.WithdrawalFeeAmount)
		}
		// Removed validation for MinWithdrawalFee/MaxWithdrawalFee as they are removed
	}

	// --- Prepare DO for DAO ---
	var doToken do.Tokens
	if err = gconv.Struct(req, &doToken); err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "请求参数转换失败")
	}

	// --- Call Repository ---
	lastInsertId, err := s.tokenRepo.Create(ctx, &doToken)
	if err != nil {
		// Repository should wrap errors appropriately
		return nil, err
	}

	// Initialize response struct directly
	res = &v1.CreateTokenRes{
		TokenId: uint(lastInsertId),
	}
	return res, nil
}

// UpdateToken 更新代币
func (s *sSystemLogic) UpdateToken(ctx context.Context, req *v1.UpdateTokenReq) (res *v1.UpdateTokenRes, err error) {
	// Extract token_id from request meta
	r := ghttp.RequestFromCtx(ctx)
	tokenId := gconv.Uint(r.Get("token_id").String())
	if tokenId == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的代币ID")
	}

	// 1. Check if token exists (use Repository)
	existingToken, err := s.tokenRepo.GetByID(ctx, tokenId)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查代币是否存在失败, ID: %d", tokenId)
	}
	if existingToken == nil {
		return nil, gerror.NewCodef(codes.CodeTokenNotFound, "要更新的代币不存在, ID: %d", tokenId)
	}

	// --- Business Validation ---
	// We need the original network to check uniqueness if symbol/contract is updated
	// However, the plan doesn't allow updating network/symbol. Let's assume they are fixed.
	// If contract address is updated, check uniqueness excluding current token.
	if req.ContractAddress != nil && *req.ContractAddress != "" {
		// 已经获取了现有代币的network信息
		exists, checkErr := s.tokenRepo.CheckExistence(ctx, existingToken.Network, *req.ContractAddress, "contract_address", tokenId)
		if checkErr != nil {
			return nil, gerror.Wrap(checkErr, "检查更新后合约地址唯一性失败")
		}
		if exists {
			return nil, gerror.NewCode(codes.CodeTokenNetworkContractExists)
		}
	}

	// 3. Validate amount limits (min <= max if both are not -1) for updated values
	err = s.validateAmountLimits(
		req.MinDepositAmount, req.MaxDepositAmount,
		req.MinWithdrawalAmount, req.MaxWithdrawalAmount,
		// MinWithdrawalFee, MaxWithdrawalFee removed
	)
	if err != nil {
		return nil, err
	}

	// 4. Validate withdrawal fee amount (must be non-negative) if updated
	if req.WithdrawalFeeAmount != nil {
		feeAmountDec, parseErr := decimal.NewFromString(*req.WithdrawalFeeAmount)
		if parseErr != nil || feeAmountDec.IsNegative() {
			return nil, gerror.NewCodef(codes.CodeTokenInvalidFeeAmount, "提现手续费金额格式无效或为负数: %s", *req.WithdrawalFeeAmount)
		}
		// Removed validation for MinWithdrawalFee/MaxWithdrawalFee
	}

	// --- Prepare Update Data Map ---
	updateDataMap := g.Map{}
	// Use gconv.Struct to convert non-nil fields to map, but be careful with zero values
	// Manual mapping is safer for updates to avoid unintended overwrites with zero values
	// Use string literals for column names
	if req.Name != nil {
		updateDataMap["name"] = *req.Name
	}
	if req.ContractAddress != nil {
		updateDataMap["contract_address"] = *req.ContractAddress
	}
	if req.TokenStandard != nil {
		updateDataMap["token_standard"] = *req.TokenStandard
	}
	if req.LogoUrl != nil {
		updateDataMap["logo_url"] = *req.LogoUrl
	}
	if req.ProjectWebsite != nil {
		updateDataMap["project_website"] = *req.ProjectWebsite
	}
	if req.Description != nil {
		updateDataMap["description"] = *req.Description
	}
	if req.Order != nil {
		updateDataMap["order"] = *req.Order
	}
	if req.IsActive != nil {
		updateDataMap["is_active"] = *req.IsActive
	}
	if req.MinDepositAmount != nil {
		updateDataMap["min_deposit_amount"] = *req.MinDepositAmount
	}
	if req.MaxDepositAmount != nil {
		updateDataMap["max_deposit_amount"] = *req.MaxDepositAmount
	}
	if req.MinWithdrawalAmount != nil {
		updateDataMap["min_withdrawal_amount"] = *req.MinWithdrawalAmount
	}
	if req.MaxWithdrawalAmount != nil {
		updateDataMap["max_withdrawal_amount"] = *req.MaxWithdrawalAmount
	}
	if req.WithdrawalFeeType != nil {
		updateDataMap["withdrawal_fee_type"] = *req.WithdrawalFeeType
	}
	if req.WithdrawalFeeAmount != nil {
		updateDataMap["withdrawal_fee_amount"] = *req.WithdrawalFeeAmount
	}
	if req.AllowDeposit != nil {
		updateDataMap["allow_deposit"] = *req.AllowDeposit
	}
	if req.AllowWithdraw != nil {
		updateDataMap["allow_withdraw"] = *req.AllowWithdraw
	}
	if req.AllowTransfer != nil {
		updateDataMap["allow_transfer"] = *req.AllowTransfer
	}
	if req.AllowReceive != nil {
		updateDataMap["allow_receive"] = *req.AllowReceive
	}
	if req.AllowRedPacket != nil {
		updateDataMap["allow_red_packet"] = *req.AllowRedPacket
	}
	if req.AllowTrading != nil {
		updateDataMap["allow_trading"] = *req.AllowTrading
	}

	// --- Call Repository ---
	err = s.tokenRepo.Update(ctx, tokenId, updateDataMap)
	if err != nil {
		// Repository should wrap errors appropriately
		return nil, err
	}

	res = &v1.UpdateTokenRes{} // Empty response on success
	return res, nil
}

// DeleteToken 删除代币 (软删除)
func (s *sSystemLogic) DeleteToken(ctx context.Context, req *v1.DeleteTokenReq) (res *v1.DeleteTokenRes, err error) {
	// Extract token_id from request meta
	r := ghttp.RequestFromCtx(ctx)
	tokenId := gconv.Uint(r.Get("token_id").String())
	if tokenId == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的代币ID")
	}

	// 1. Check if token exists (optional, SoftDeleteToken in DAO handles not found)
	// _, err = dao.Tokens.GetTokenByID(ctx, tokenId)
	// if err != nil {
	// 	return nil, err // Propagate CodeTokenNotFound or other DB errors
	// }

	// --- Call Repository ---
	err = s.tokenRepo.DeleteSoft(ctx, tokenId)
	if err != nil {
		// Repository should wrap errors appropriately
		return nil, err
	}

	res = &v1.DeleteTokenRes{} // Empty response on success
	return res, nil
}

// validateAmountLimits validates pairs of min/max amount strings.
// It expects pairs of arguments: min1, max1, min2, max2, ...
// Returns CodeTokenInvalidAmountLimit error if validation fails.
func (s *sSystemLogic) validateAmountLimits(limits ...*string) error {
	if len(limits)%2 != 0 {
		return gerror.New("validateAmountLimits: must provide pairs of min/max limits") // Internal error
	}

	for i := 0; i < len(limits); i += 2 {
		minStrPtr := limits[i]
		maxStrPtr := limits[i+1]

		// Skip validation if both are nil (not provided in request)
		if minStrPtr == nil && maxStrPtr == nil {
			continue
		}

		// Handle cases where only one is provided (assume the other remains unchanged or default)
		// This basic validation only checks if the provided values are valid formats.
		// A more robust validation might need to fetch the existing record to compare against.
		// For now, we validate the format of provided values.

		var minStr, maxStr string
		if minStrPtr != nil {
			minStr = *minStrPtr
		} else {
			minStr = "-1" // Assume default or unchanged is -1 if not provided
		}
		if maxStrPtr != nil {
			maxStr = *maxStrPtr
		} else {
			maxStr = "-1" // Assume default or unchanged is -1 if not provided
		}

		// Validate format using decimal library
		minDec, errMin := decimal.NewFromString(minStr)
		maxDec, errMax := decimal.NewFromString(maxStr)

		isMinValid := (minStr == "-1" || (errMin == nil && !minDec.IsNegative()))
		isMaxValid := (maxStr == "-1" || (errMax == nil && !maxDec.IsNegative())) // Max can be 0

		if !isMinValid || !isMaxValid {
			return gerror.NewCodef(codes.CodeTokenInvalidAmountLimit, "金额限制格式无效 (min: %s, max: %s)，必须是有效数字或-1", minStr, maxStr)
		}

		// If both are valid numbers (not -1), check min <= max
		if minStr != "-1" && maxStr != "-1" {
			if minDec.GreaterThan(maxDec) {
				return gerror.NewCodef(codes.CodeTokenInvalidAmountLimit, "无效的金额限制：最小值 (%s) 不能大于最大值 (%s)", minStr, maxStr)
			}
		}
	}
	return nil
}
