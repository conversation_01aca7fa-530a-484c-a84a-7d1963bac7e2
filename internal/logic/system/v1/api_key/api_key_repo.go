package api_key

import (
	v1 "admin-api/api/system/v1" // Import v1 for DTO definition
	"admin-api/internal/dao"
	"admin-api/internal/model/do" // Add do import
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/api_key" // Import the interface package
	"context"
	"database/sql"
	"errors"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// apiKeyRepository 实现了 IApiKeyRepository 接口
type apiKeyRepository struct{}

// NewApiKeyRepository 创建一个新的 apiKeyRepository 实例
func NewApiKeyRepository() api_key.IApiKeyRepository {
	return &apiKeyRepository{}
}

// List 获取 API 密钥列表 (包含商户名称)
func (r *apiKeyRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.ApiKeyInfoType, total int, err error) {
	// 构建基本查询
	model := dao.MerchantApiKeys.Ctx(ctx).As("ak").
		LeftJoin(dao.Merchants.Table()+" m", "ak.merchant_id = m.merchant_id").
		Where(condition).
		WhereNull("ak." + dao.MerchantApiKeys.Columns().DeletedAt) // 指定表别名用于deleted_at

	// 查询总数 - 使用COUNT(1)而不是COUNT(fields)，避免语法错误
	total, err = model.Count("1")
	if err != nil {
		return nil, 0, gerror.Wrap(err, "apiKeyRepository.List: 获取 API 密钥总数失败")
	}
	if total == 0 {
		return make([]*v1.ApiKeyInfoType, 0), 0, nil
	}

	// 指定字段，用于查询数据而非计数
	model = model.Fields("ak.*, m.merchant_name") // 从两个表中选择字段

	// 使用ScanList扫描到包含连接字段的结构体切片
	var daoList []*struct {
		entity.MerchantApiKeys
		MerchantName string `json:"merchantName"`
	}
	err = model.Page(page, pageSize).OrderDesc("ak." + dao.MerchantApiKeys.Columns().ApiKeyId).Scan(&daoList) // 指定表别名用于排序
	if err != nil {
		return nil, 0, gerror.Wrap(err, "apiKeyRepository.List: 查询 API 密钥列表失败")
	}

	// 将DAO结果转换为DTO列表
	list = make([]*v1.ApiKeyInfoType, len(daoList))
	for i, item := range daoList {
		dto := &v1.ApiKeyInfoType{}
		// 转换基础实体字段
		if errConv := gconv.Struct(item.MerchantApiKeys, dto); errConv != nil {
			g.Log().Warningf(ctx, "apiKeyRepository.List: 转换基础实体失败: %v, ApiKeyId: %d", errConv, item.ApiKeyId)
			continue // 跳过有问题的记录
		}
		// 分配连接字段
		dto.MerchantName = item.MerchantName
		// 遮盖API密钥(例如，只显示前缀/后缀)
		if len(dto.ApiKey) > 10 {
			dto.ApiKey = dto.ApiKey[:5] + "..." + dto.ApiKey[len(dto.ApiKey)-5:]
		} else {
			dto.ApiKey = "..." // 或者适当处理较短的键
		}

		list[i] = dto
	}

	return list, total, nil
}

// GetByID 获取原始 API 密钥实体
func (r *apiKeyRepository) GetByID(ctx context.Context, apiKeyId uint) (*entity.MerchantApiKeys, error) {
	var entity *entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).
		WhereNull(dao.MerchantApiKeys.Columns().DeletedAt).
		Scan(&entity)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "apiKeyRepository.GetByID: 查询 API 密钥失败, ID: %d", apiKeyId)
	}
	return entity, nil
}

// Update 更新 API 密钥信息
func (r *apiKeyRepository) Update(ctx context.Context, apiKeyId uint, data g.Map) error {
	if len(data) == 0 {
		return nil // No fields to update
	}
	data[dao.MerchantApiKeys.Columns().UpdatedAt] = gtime.Now() // Ensure update time is set
	_, err := dao.MerchantApiKeys.Ctx(ctx).Data(data).Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).Update()
	if err != nil {
		return gerror.Wrapf(err, "apiKeyRepository.Update: 更新 API 密钥失败, ID: %d", apiKeyId)
	}
	return nil
}

// UpdateStatus 更新 API 密钥状态
func (r *apiKeyRepository) UpdateStatus(ctx context.Context, apiKeyId uint, status string) error {
	data := g.Map{
		dao.MerchantApiKeys.Columns().Status:    status,
		dao.MerchantApiKeys.Columns().UpdatedAt: gtime.Now(),
	}
	_, err := dao.MerchantApiKeys.Ctx(ctx).Data(data).Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).Update()
	if err != nil {
		return gerror.Wrapf(err, "apiKeyRepository.UpdateStatus: 更新 API 密钥状态失败, ID: %d, Status: %s", apiKeyId, status)
	}
	return nil
}

// DeleteSoft 软删除 API 密钥
func (r *apiKeyRepository) DeleteSoft(ctx context.Context, apiKeyIds []uint) error {
	if len(apiKeyIds) == 0 {
		return nil
	}
	_, err := dao.MerchantApiKeys.Ctx(ctx).
		Data(do.MerchantApiKeys{DeletedAt: gtime.Now()}). // Use do model for soft delete field
		WhereIn(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyIds).
		Update()
	if err != nil {
		return gerror.Wrapf(err, "apiKeyRepository.DeleteSoft: 软删除 API 密钥失败, IDs: %v", apiKeyIds)
	}
	return nil
}

// CreateRaw 插入原始 API 密钥数据 (Service 层应负责生成 Key/Secret)
func (r *apiKeyRepository) CreateRaw(ctx context.Context, data *entity.MerchantApiKeys) error {
	data.CreatedAt = gtime.Now()
	data.UpdatedAt = gtime.Now()
	_, err := dao.MerchantApiKeys.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return gerror.Wrap(err, "apiKeyRepository.CreateRaw: 创建 API 密钥记录失败")
	}
	return nil
}

// FindByKey 根据 API Key 字符串查找记录
func (r *apiKeyRepository) FindByKey(ctx context.Context, apiKey string) (*entity.MerchantApiKeys, error) {
	var entity *entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKey, apiKey).
		WhereNull(dao.MerchantApiKeys.Columns().DeletedAt).
		Scan(&entity)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrap(err, "apiKeyRepository.FindByKey: 查询 API 密钥失败")
	}
	return entity, nil
}

// UpdateLastUsed 更新最后使用时间
func (r *apiKeyRepository) UpdateLastUsed(ctx context.Context, apiKeyId uint) error {
	data := g.Map{
		dao.MerchantApiKeys.Columns().LastUsedAt: gtime.Now(),
		// Do not update UpdatedAt here to avoid confusion with manual updates
	}
	_, err := dao.MerchantApiKeys.Ctx(ctx).Data(data).Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).Update()
	if err != nil {
		// Log the error but don't necessarily block the request
		g.Log().Warningf(ctx, "apiKeyRepository.UpdateLastUsed: 更新最后使用时间失败, ID: %d, Error: %v", apiKeyId, err)
		// return gerror.Wrapf(err, "apiKeyRepository.UpdateLastUsed: 更新最后使用时间失败, ID: %d", apiKeyId)
	}
	return nil // Return nil even if update fails, as it's often non-critical
}

// GetByMerchantID 根据商户ID获取API密钥列表
func (r *apiKeyRepository) GetByMerchantID(ctx context.Context, merchantId uint) ([]*entity.MerchantApiKeys, error) {
	var list []*entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantId).
		WhereNull(dao.MerchantApiKeys.Columns().DeletedAt).
		OrderDesc(dao.MerchantApiKeys.Columns().ApiKeyId).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrapf(err, "apiKeyRepository.GetByMerchantID: 查询商户API密钥列表失败, MerchantID: %d", merchantId)
	}
	return list, nil
}

// Create 创建API密钥并返回ID
func (r *apiKeyRepository) Create(ctx context.Context, data *do.MerchantApiKeys) (uint, error) {
	if data.CreatedAt.IsZero() {
		data.CreatedAt = gtime.Now()
	}
	if data.UpdatedAt.IsZero() {
		data.UpdatedAt = gtime.Now()
	}

	result, err := dao.MerchantApiKeys.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "apiKeyRepository.Create: 创建API密钥记录失败")
	}

	// 获取插入的ID
	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "apiKeyRepository.Create: 获取API密钥记录ID失败")
	}

	return uint(lastInsertId), nil
}

// Revoke 撤销API密钥 (将状态设为 "revoked")
func (r *apiKeyRepository) Revoke(ctx context.Context, apiKeyId uint) error {
	return r.UpdateStatus(ctx, apiKeyId, "revoked")
}
