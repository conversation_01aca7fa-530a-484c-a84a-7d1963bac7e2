package v1

import (
	"context"
	"strings"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"

	// "admin-api/internal/dao" // Removed direct DAO dependency
	"admin-api/internal/model/do"
	// "admin-api/internal/model/entity" // Removed unused import
	// "admin-api/utility/casbin" // 引入 Casbin 工具包
	"admin-api/utility/encrypt"
	"admin-api/utility/excel"

	// "github.com/gogf/gf/v2/database/gdb" // 引入 gdb 包
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	// "github.com/gogf/gf/v2/util/gconv" // 引入 gconv 包
	"github.com/gogf/gf/v2/util/grand"
)

// GetUserList 获取用户列表
func (s *sSystemLogic) GetUserList(ctx context.Context, req *v1.GetUserListReq) (res *v1.GetUserListRes, err error) {
	// Initialize response struct directly
	res = &v1.GetUserListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.UserInfoType, 0),
	}

	// 注意：查询条件现在由 ListWithAgentInfo 方法内部处理

	// 查询用户列表 (使用新的带代理和telegram信息的Repository方法)
	list, total, err := s.userRepo.ListWithAgentInfo(ctx, req)
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = (total + req.PageSize - 1) / req.PageSize

	// Repository List method already returns the correct DTO slice
	// Repository List method already returns the correct DTO slice
	// Repository List method already returns the correct DTO slice
	res.Data = list

	// 如果是导出，则直接返回数据
	if req.Export {
		// 导出逻辑可以在这里实现
		// 例如：生成Excel文件并返回下载链接
		// 导出时不分页，查询所有符合条件的数据
		// Use repository to get all users for export
		// Use repository to get all users for export
		// Use repository to get all users for export
		// 创建导出请求对象
		exportReq := *req
		exportReq.Page = 1
		exportReq.PageSize = 9999999
		
		// 使用新的带代理和telegram信息的Repository方法获取所有数据
		list, _, err := s.userRepo.ListWithAgentInfo(ctx, &exportReq)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询用户列表失败")
		}

		if len(list) == 0 {
			return res, nil // 没有数据可导出
		}

		// Recommender info is already included in the DTO from the List method

		// 准备导出数据
		exportData := make([]interface{}, len(list))
		for i, userDTO := range list { // Iterate over DTOs
			// 状态和权限转换为文本
			statusText := "正常"
			if userDTO.IsStop { // Use boolean field from DTO
				statusText = "已停用"
			}
			google2faText := "未启用"
			if userDTO.Google2faEnabled { // Use boolean field from DTO
				google2faText = "已启用"
			}
			redPacketPermissionText := "已禁用"
			if userDTO.RedPacketPermission { // Use boolean field from DTO
				redPacketPermissionText = "已启用"
			}
			transferPermissionText := "已禁用"
			if userDTO.TransferPermission { // Use boolean field from DTO
				transferPermissionText = "已启用"
			}
			withdrawPermissionText := "已禁用"
			if userDTO.WithdrawPermission { // Use boolean field from DTO
				withdrawPermissionText = "已启用"
			}
			flashTradePermissionText := "已禁用"
			if userDTO.FlashTradePermission { // Use boolean field from DTO
				flashTradePermissionText = "已启用"
			}
			rechargePermissionText := "已禁用"
			if userDTO.RechargePermission { // Use boolean field from DTO
				rechargePermissionText = "已启用"
			}
			receivePermissionText := "已禁用"
			if userDTO.ReceivePermission { // Use boolean field from DTO
				receivePermissionText = "已启用"
			}
			lastLoginTimeText := ""
			if userDTO.LastLoginTime != nil {
				lastLoginTimeText = userDTO.LastLoginTime.Format("Y-m-d H:i:s")
			}
			createdAtText := ""
			if userDTO.CreatedAt != nil {
				createdAtText = userDTO.CreatedAt.Format("Y-m-d H:i:s")
			}

			// 准备导出结构体 (using DTO fields)
			exportData[i] = struct {
				Id                   uint64 `json:"id" excel:"用户ID"`
				Account              string `json:"account" excel:"账号"`
				Email                string `json:"email" excel:"邮箱"`
				AreaCode             string `json:"areaCode" excel:"区号"`
				Phone                string `json:"phone" excel:"电话"`
				InviteCode           string `json:"inviteCode" excel:"邀请码"`
				RecommendId          uint64 `json:"recommendId" excel:"推荐人ID"`
				RecommendAccount     string `json:"recommendAccount" excel:"推荐人账号"`
				Status               string `json:"status" excel:"状态"`
				Google2fa            string `json:"google2fa" excel:"谷歌验证"`
				RedPacketPermission  string `json:"redPacketPermission" excel:"红包权限"`
				TransferPermission   string `json:"transferPermission" excel:"转账权限"`
				WithdrawPermission   string `json:"withdrawPermission" excel:"提现权限"`
				FlashTradePermission string `json:"flashTradePermission" excel:"闪兑权限"`
				RechargePermission   string `json:"rechargePermission" excel:"充值权限"`
				ReceivePermission    string `json:"receivePermission" excel:"收款权限"`
				LastLoginTime        string `json:"lastLoginTime" excel:"最后登录时间"`
				CreatedAt            string `json:"createdAt" excel:"创建时间"`
			}{
				Id:                   userDTO.Id,
				Account:              userDTO.Account,
				Email:                userDTO.Email,
				AreaCode:             userDTO.AreaCode,
				Phone:                userDTO.Phone,
				InviteCode:           userDTO.InviteCode,
				RecommendId:          userDTO.RecommendId,
				RecommendAccount:     userDTO.RecommendAccount, // Already in DTO
				Status:               statusText,
				Google2fa:            google2faText,
				RedPacketPermission:  redPacketPermissionText,
				TransferPermission:   transferPermissionText,
				WithdrawPermission:   withdrawPermissionText,
				FlashTradePermission: flashTradePermissionText,
				RechargePermission:   rechargePermissionText,
				ReceivePermission:    receivePermissionText,
				LastLoginTime:        lastLoginTimeText,
				CreatedAt:            createdAtText,
			}
		}

		// 定义Excel表头 (使用 excel tag)
		excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "用户列表", "用户列表")
	}

	return res, nil
}

// GetUser 获取用户详情
func (s *sSystemLogic) GetUser(ctx context.Context, req *v1.GetUserReq) (res *v1.GetUserRes, err error) {
	// Initialize response struct directly
	res = &v1.GetUserRes{
		Data: &v1.UserDetailType{},
	}

	userDetail, err := s.userRepo.GetDetailByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询用户详情失败, ID: %d", req.Id)
	}
	if userDetail == nil {
		return nil, gerror.NewCodef(codes.CodeUserNotFound, "用户不存在, ID: %d", req.Id)
	}

	res.Data = userDetail

	return res, nil
}

// AddUser 添加用户
func (s *sSystemLogic) AddUser(ctx context.Context, req *v1.AddUserReq) (res *v1.AddUserRes, err error) {
	res = &v1.AddUserRes{}

	// 1. 校验唯一性
	if exists, _ := s.userRepo.CheckAccountExists(ctx, req.Account); exists {
		return nil, gerror.NewCode(codes.CodeUserAccountExists)
	}
	if req.Email != "" {
		if exists, _ := s.userRepo.CheckEmailExists(ctx, req.Email); exists {
			return nil, gerror.NewCode(codes.CodeUserEmailExists)
		}
	}
	if req.Phone != "" {
		if exists, _ := s.userRepo.CheckPhoneExists(ctx, req.Phone); exists {
			return nil, gerror.NewCode(codes.CodeUserPhoneExists)
		}
	}

	// 2. 校验推荐人
	if req.RecommendId > 0 {
		recommender, err := s.userRepo.GetByID(ctx, req.RecommendId)
		if err != nil {
			return nil, gerror.Wrapf(err, "查询推荐人信息失败, ID: %d", req.RecommendId)
		}
		if recommender == nil {
			return nil, gerror.NewCodef(codes.CodeUserNotFound, "推荐人不存在, ID: %d", req.RecommendId)
		}
		if recommender.IsStop == 1 {
			return nil, gerror.NewCode(codes.CodeInvalidParameter, "推荐人已被停用")
		}
	}

	// 3. 生成邀请码 (如果未提供)
	inviteCode := req.InviteCode
	if inviteCode == "" {
		// 生成随机邀请码
		for i := 0; i < 10; i++ {
			inviteCode = strings.ToUpper(grand.Letters(8))
			exists, _ := s.userRepo.CheckInviteCodeExists(ctx, inviteCode)
			if !exists {
				break
			}
		}
	} else {
		// 校验邀请码唯一性
		if exists, _ := s.userRepo.CheckInviteCodeExists(ctx, inviteCode); exists {
			return nil, gerror.NewCode(codes.CodeUserInviteCodeExists)
		}
	}

	// 4. 哈希密码
	passwordHash, err := encrypt.BcryptHash(req.Password)
	if err != nil {
		return nil, gerror.Wrap(err, "密码加密失败")
	}

	// 5. 准备用户数据
	userData := &do.Users{
		Account:                        req.Account,
		Password:                       passwordHash,
		Email:                          req.Email,
		AreaCode:                       req.AreaCode,
		Phone:                          req.Phone,
		Avatar:                         req.Avatar,
		InviteCode:                     inviteCode,
		RecommendId:                    req.RecommendId,
		Language:                       req.Language,
		IsStop:                         boolToInt(req.IsStop),
		Reason:                         req.Reason,
		RedPacketPermission:            boolToInt(req.RedPacketPermission),
		TransferPermission:             boolToInt(req.TransferPermission),
		WithdrawPermission:             boolToInt(req.WithdrawPermission),
		FlashTradePermission:           boolToInt(req.FlashTradePermission),
		RechargePermission:             boolToInt(req.RechargePermission),
		ReceivePermission:              boolToInt(req.ReceivePermission),
		ResetPaymentPasswordPermission: boolToInt(req.ResetPaymentPasswordPermission),
		CreatedAt:                      gtime.Now(),
	}

	// 6. 添加用户 (使用 Repository)
	userId, err := s.userRepo.Create(ctx, userData)
	if err != nil {
		return nil, gerror.Wrap(err, "添加用户失败")
	}

	res.Id = userId
	return res, nil
}

// EditUser 编辑用户
func (s *sSystemLogic) EditUser(ctx context.Context, req *v1.EditUserReq) (res *v1.EditUserRes, err error) {
	// Initialize response struct directly (it might be empty or just contain success)
	res = &v1.EditUserRes{
		Success: true,
	}

	// 1. 校验用户是否存在 (使用 Repository)
	user, err := s.userRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查用户是否存在失败, ID: %d", req.Id)
	}
	if user == nil {
		return nil, gerror.NewCodef(codes.CodeUserNotFound, "要编辑的用户不存在, ID: %d", req.Id)
	}

	// 2. 校验唯一性 (排除自身)
	if req.Email != "" && req.Email != user.Email {
		if exists, _ := s.userRepo.CheckEmailExists(ctx, req.Email, req.Id); exists {
			return nil, gerror.NewCode(codes.CodeUserEmailExists)
		}
	}
	if req.Phone != "" && req.Phone != user.Phone {
		if exists, _ := s.userRepo.CheckPhoneExists(ctx, req.Phone, req.Id); exists {
			return nil, gerror.NewCode(codes.CodeUserPhoneExists)
		}
	}
	if req.InviteCode != "" && req.InviteCode != user.InviteCode {
		if exists, _ := s.userRepo.CheckInviteCodeExists(ctx, req.InviteCode, req.Id); exists {
			return nil, gerror.NewCode(codes.CodeUserInviteCodeExists)
		}
	}

	// 3. 准备更新数据
	updateData := g.Map{}

	if req.Email != "" {
		updateData["email"] = req.Email
	}
	if req.AreaCode != "" {
		updateData["area_code"] = req.AreaCode
	}
	if req.Phone != "" {
		updateData["phone"] = req.Phone
	}
	if req.Nickname != "" {
		updateData["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updateData["avatar"] = req.Avatar
	}
	if req.InviteCode != "" {
		updateData["invite_code"] = req.InviteCode
	}
	if req.Language != "" {
		updateData["language"] = req.Language
	}

	// 更新权限
	updateData["red_packet_permission"] = boolToInt(req.RedPacketPermission)
	updateData["transfer_permission"] = boolToInt(req.TransferPermission)
	updateData["withdraw_permission"] = boolToInt(req.WithdrawPermission)
	updateData["flash_trade_permission"] = boolToInt(req.FlashTradePermission)
	updateData["recharge_permission"] = boolToInt(req.RechargePermission)
	updateData["receive_permission"] = boolToInt(req.ReceivePermission)
	updateData["reset_payment_password_permission"] = boolToInt(req.ResetPaymentPasswordPermission)

	// UpdatedAt is handled by the repository Update method

	// 4. 更新用户 (使用 Repository)
	err = s.userRepo.Update(ctx, req.Id, updateData)
	if err != nil {
		// return nil, gerror.Wrap(err, "编辑用户失败")
		g.Log().Error(ctx, "编辑用户失败", err)
		res.Success = false
	}
	res.Success = true

	// Success is typically implied by nil error, or the struct might not have a Success field.
	// If EditUserRes has a Success field, set it directly: res.Success = true
	// Assuming EditUserRes is empty for now based on API definition.
	return res, nil
}

// DeleteUser 删除用户
func (s *sSystemLogic) DeleteUser(ctx context.Context, req *v1.DeleteUserReq) (res *v1.DeleteUserRes, err error) {
	res = &v1.DeleteUserRes{}
	// var deleteErr error

	// // 在事务中执行软删除和 Casbin 策略清理
	// err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
	// 	// 1. 执行软删除 (使用 Repository)
	// 	// 注意：如果 s.userRepo.DeleteSoft 需要在事务中运行，它应该接收 tx 作为参数。
	// 	// 假设当前的 s.userRepo.DeleteSoft 可以在事务外或内运行，或者它内部处理事务。
	// 	// 为保持简单，我们先假设它可以在这里被调用。如果它需要 tx，则需要调整。
	// 	// 理想情况下，Repository 方法应设计为可接收可选的 tx。
	// 	// 鉴于当前 DeleteSoft 不接收 tx，我们先这样调用。
	// 	// 如果 DeleteSoft 内部自己开启事务，则外部事务可能不是必须的，除非 Casbin 操作也需要同事务。
	// 	// Casbin 的数据库适配器通常是每个操作自动提交，所以将它们放在应用层事务中以确保一致性。
	// 	deleteErr = s.userRepo.DeleteSoft(ctx, req.Ids) // req.Ids is []uint64
	// 	if deleteErr != nil {
	// 		g.Log().Errorf(ctx, "软删除用户失败: %v", deleteErr)
	// 		return gerror.Wrap(deleteErr, "软删除用户失败")
	// 	}

	// 	// 2. 清理 Casbin g 规则
	// 	e := casbin.GetEnforcer()
	// 	if e == nil {
	// 		g.Log().Warning(ctx, "Casbin Enforcer 未初始化，无法清理用户角色分配策略")
	// 		// 根据业务决定是否中断，这里选择记录警告并继续（因为用户数据已软删除）
	// 		return nil // 或者返回错误 gerror.NewCode(codes.CodeInternalError, "Casbin Enforcer 未初始化")
	// 	}

	// 	for _, userId := range req.Ids {
	// 		userIdStr := gconv.String(userId)
	// 		// RemoveFilteredGroupingPolicy(fieldIndex 0 for subject, subject, domain (optional third field))
	// 		// To remove all roles for a user in a specific domain:
	// 		removed, casbinErr := e.RemoveFilteredGroupingPolicy(0, userIdStr, "", casbin.DefaultDomain)
	// 		if casbinErr != nil {
	// 			g.Log().Warningf(ctx, "从 Casbin 删除用户 [%s] 在域 [%s] 的角色分配失败: %v", userIdStr, casbin.DefaultDomain, casbinErr)
	// 			// 不中断整个删除流程，只记录日志
	// 		}
	// 		if removed {
	// 			g.Log().Infof(ctx, "已从 Casbin 中移除用户 [%s] 在域 [%s] 的所有角色分配", userIdStr, casbin.DefaultDomain)
	// 		}
	// 	}
	// 	return nil
	// })

	// if err != nil { // 这是事务本身的错误
	// 	// return nil, err // 直接返回事务错误
	// 	g.Log().Errorf(ctx, "删除用户事务处理失败: %v", err)
	// 	res.Success = false
	// 	return res, err // 将事务错误传递给上层
	// }

	// // 如果 deleteErr (来自 s.userRepo.DeleteSoft) 不为空，也应该认为是失败
	// if deleteErr != nil {
	// 	res.Success = false
	// 	// err 已经被 deleteErr 包装并返回了
	// } else {
	// 	res.Success = true
	// }
	return res, nil
}

// UpdateUserStatus 更新用户状态
func (s *sSystemLogic) UpdateUserStatus(ctx context.Context, req *v1.UpdateUserStatusReq) (res *v1.UpdateUserStatusRes, err error) {
	// Initialize response struct directly
	res = &v1.UpdateUserStatusRes{}

	// 检查用户是否存在 (使用 Repository)
	_, err = s.userRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查用户是否存在失败, ID: %d", req.Id)
	}
	// No need to check for nil here, UpdateStatus will handle it or error out

	// 更新状态 (使用 Repository)
	err = s.userRepo.UpdateStatus(ctx, req.Id, boolToInt(req.IsStop), req.Reason)
	if err != nil {
		return nil, gerror.Wrap(err, "更新用户状态失败")
	}

	// Assuming UpdateUserStatusRes is empty.
	return res, nil
}

// ResetUserPassword 重置用户密码
func (s *sSystemLogic) ResetUserPassword(ctx context.Context, req *v1.ResetUserPasswordReq) (res *v1.ResetUserPasswordRes, err error) {
	// Initialize response struct directly
	res = &v1.ResetUserPasswordRes{}

	// 检查用户是否存在 (使用 Repository)
	_, err = s.userRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查用户是否存在失败, ID: %d", req.Id)
	}
	// No need to check for nil here, ResetPassword will handle it or error out

	// 哈希密码
	// passwordHash, err := encrypt.BcryptHash(req.Password)
	// if err != nil {
	// 	return nil, gerror.Wrap(err, "密码加密失败")
	// }

	// 重置密码 (使用 Repository)
	err = s.userRepo.ResetPaymentPassword(ctx, req.Id)
	if err != nil {
		// return nil, gerror.Wrap(err, "重置用户密码失败")
		g.Log().Error(ctx, err)
		res.Success = false
	}
	res.Success = true

	// Assuming ResetUserPasswordRes is empty.
	return res, nil
}

// ResetUserGoogle2FA 重置用户Google 2FA
func (s *sSystemLogic) ResetUserGoogle2FA(ctx context.Context, req *v1.ResetUserGoogle2FAReq) (res *v1.ResetUserGoogle2FARes, err error) {
	// Initialize response struct directly
	res = &v1.ResetUserGoogle2FARes{}

	// 检查用户是否存在 (使用 Repository)
	_, err = s.userRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查用户是否存在失败, ID: %d", req.Id)
	}
	// No need to check for nil here, ResetGoogle2FA will handle it or error out

	// 重置Google 2FA
	// 重置Google 2FA (使用 Repository)
	// 重置Google 2FA (使用 Repository)
	err = s.userRepo.ResetGoogle2FA(ctx, req.Id)
	if err != nil {
		// return nil, gerror.Wrap(err, "重置用户 Google 2FA 失败")
		g.Log().Error(ctx, err)
		res.Success = false
	}
	res.Success = true

	// Assuming ResetUserGoogle2FARes is empty.
	return res, nil
}

// UpdateUserWithdrawBettingVolume 更新用户提现流水要求
func (s *sSystemLogic) UpdateUserWithdrawBettingVolume(ctx context.Context, req *v1.UpdateUserWithdrawBettingVolumeReq) (res *v1.UpdateUserWithdrawBettingVolumeRes, err error) {
	// Initialize response struct directly
	res = &v1.UpdateUserWithdrawBettingVolumeRes{}

	// 检查用户是否存在 (使用 Repository)
	_, err = s.userRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.Wrapf(err, "检查用户是否存在失败, ID: %d", req.Id)
	}

	// 更新用户提现流水要求 (使用 Repository)
	err = s.userRepo.UpdateWithdrawBettingVolume(ctx, req.Id, req.WithdrawBettingVolume)
	if err != nil {
		g.Log().Error(ctx, err)
		res.Success = false
		return res, gerror.Wrap(err, "更新用户提现流水要求失败")
	}
	
	res.Success = true
	return res, nil
}

// boolToInt 将布尔值转换为整数 (true=1, false=0)
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}
