// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/hex"

	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/utility/encrypt"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// merchantApiKeysDao is the data access object for the table merchant_api_keys.
// You can define custom methods on it to extend its functionality as needed.
type merchantApiKeysDao struct {
	*internal.MerchantApiKeysDao
}

var (
	// MerchantApiKeys is a globally accessible object for table merchant_api_keys operations.
	MerchantApiKeys = merchantApiKeysDao{internal.NewMerchantApiKeysDao()}
)

// Add your custom methods and functionality below.

// GenerateApiKey 生成新的API密钥
func (d *merchantApiKeysDao) GenerateApiKey(ctx context.Context, merchantId uint, label, scopes, ipWhitelist string, expiresAt *gtime.Time) (apiKeyId uint, apiKey, secretKey string, err error) {
	// 生成随机的API Key (32字节)
	apiKeyBytes := make([]byte, 32)
	_, err = rand.Read(apiKeyBytes)
	if err != nil {
		return 0, "", "", gerror.Wrap(err, "生成API Key失败")
	}
	apiKey = hex.EncodeToString(apiKeyBytes)

	// 生成随机的Secret Key (32字节)
	secretKeyBytes := make([]byte, 32)
	_, err = rand.Read(secretKeyBytes)
	if err != nil {
		return 0, "", "", gerror.Wrap(err, "生成Secret Key失败")
	}
	secretKey = hex.EncodeToString(secretKeyBytes)

	// 对Secret Key进行哈希处理
	secretKeyHash, err := encrypt.BcryptHash(secretKey)
	if err != nil {
		return 0, "", "", gerror.Wrap(err, "哈希Secret Key失败")
	}

	// 创建API密钥记录
	data := &do.MerchantApiKeys{
		MerchantId:  merchantId,
		ApiKey:      apiKey,
		SecretHash:  secretKeyHash,
		Secret:      secretKey,
		Label:       label,
		Status:      "active",
		Scopes:      scopes,
		IpWhitelist: ipWhitelist,
		ExpiresAt:   expiresAt,
		CreatedAt:   gtime.Now(),
	}

	// 插入数据库
	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, "", "", gerror.Wrap(err, "保存API密钥失败")
	}

	// 获取新插入的ID
	lastInsertId, err := res.LastInsertId()
	if err != nil {
		return 0, "", "", gerror.Wrap(err, "获取API密钥ID失败")
	}

	return uint(lastInsertId), apiKey, secretKey, nil
}

// GetApiKeysByMerchantId 获取商户的API密钥列表
func (d *merchantApiKeysDao) GetApiKeysByMerchantId(ctx context.Context, merchantId uint) ([]*entity.MerchantApiKeys, error) {
	var apiKeys []*entity.MerchantApiKeys
	err := d.Ctx(ctx).Where(d.Columns().MerchantId, merchantId).WhereNull(d.Columns().DeletedAt).OrderDesc(d.Columns().ApiKeyId).Scan(&apiKeys)
	if err != nil {
		return nil, gerror.Wrap(err, "获取API密钥列表失败")
	}
	return apiKeys, nil
}

// GetApiKeyById 根据ID获取API密钥
func (d *merchantApiKeysDao) GetApiKeyById(ctx context.Context, apiKeyId uint) (*entity.MerchantApiKeys, error) {
	var apiKey *entity.MerchantApiKeys
	err := d.Ctx(ctx).Where(d.Columns().ApiKeyId, apiKeyId).WhereNull(d.Columns().DeletedAt).Scan(&apiKey)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeApiKeyNotFound)
		}
		return nil, gerror.Wrap(err, "获取API密钥失败")
	}
	if apiKey == nil {
		return nil, gerror.NewCode(codes.CodeApiKeyNotFound)
	}
	return apiKey, nil
}

// RevokeApiKey 撤销API密钥
func (d *merchantApiKeysDao) RevokeApiKey(ctx context.Context, apiKeyId uint) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().Status: "revoked",
	}).Where(d.Columns().ApiKeyId, apiKeyId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "撤销API密钥失败")
	}
	return nil
}

// DeleteApiKey 软删除API密钥
func (d *merchantApiKeysDao) DeleteApiKey(ctx context.Context, apiKeyId uint) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().DeletedAt: gtime.Now(),
	}).Where(d.Columns().ApiKeyId, apiKeyId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "删除API密钥失败")
	}
	return nil
}

// DeleteApiKeys 批量软删除API密钥
func (d *merchantApiKeysDao) DeleteApiKeys(ctx context.Context, apiKeyIds []uint) error {
	if len(apiKeyIds) == 0 {
		return nil
	}
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().DeletedAt: gtime.Now(),
	}).WhereIn(d.Columns().ApiKeyId, apiKeyIds).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "批量删除API密钥失败")
	}
	return nil
}

// GetApiKeyList 获取API密钥列表 (带分页、过滤、排序)
func (d *merchantApiKeysDao) GetApiKeyList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.MerchantApiKeys, total int, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt)

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询API密钥总数失败")
	}

	// 获取列表数据
	err = m.Page(page, pageSize).OrderDesc(d.Columns().ApiKeyId).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询API密钥列表失败")
	}

	return list, total, nil
}

// UpdateApiKey 更新API密钥信息
func (d *merchantApiKeysDao) UpdateApiKey(ctx context.Context, apiKeyId uint, data g.Map) error {
	_, err := d.Ctx(ctx).Data(data).Where(d.Columns().ApiKeyId, apiKeyId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "更新API密钥信息失败")
	}
	return nil
}

// UpdateApiKeyStatus 更新API密钥状态
func (d *merchantApiKeysDao) UpdateApiKeyStatus(ctx context.Context, apiKeyId uint, status string) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().Status: status,
	}).Where(d.Columns().ApiKeyId, apiKeyId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "更新API密钥状态失败")
	}
	return nil
}
