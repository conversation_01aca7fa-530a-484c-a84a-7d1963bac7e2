// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameTransactionsDao is the data access object for the table game_transactions.
// You can define custom methods on it to extend its functionality as needed.
type gameTransactionsDao struct {
	*internal.GameTransactionsDao
}

var (
	// GameTransactions is a globally accessible object for table game_transactions operations.
	GameTransactions = gameTransactionsDao{internal.NewGameTransactionsDao()}
)

// Add your custom methods and functionality below.
