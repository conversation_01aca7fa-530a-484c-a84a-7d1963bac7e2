// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"fmt"

	// v1 "admin-api/api/system/v1" // 移除未使用的导入
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// adminMemberDao is the data access object for the table admin_member.
// You can define custom methods on it to extend its functionality as needed.
type adminMemberDao struct {
	*internal.AdminMemberDao
}

var (
	// AdminMember is a globally accessible object for table admin_member operations.
	AdminMember = adminMemberDao{internal.NewAdminMemberDao()}
)

// UserInfoForNoticeSelect 公告选择器所需的用户信息
type UserInfoForNoticeSelect struct {
	Id       int64  `json:"id"`
	Username string `json:"username"`
	Avatar   string `json:"avatar"`
}

// GetMemberList 获取用户列表（分页，含筛选）
// 返回基础用户实体列表，关联信息由 Logic 层处理
func (d *adminMemberDao) GetMemberList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminMember, total int, err error) {
	m := d.Ctx(ctx).As("m").WhereNull(fmt.Sprintf("m.%s", d.Columns().DeletedAt)) // 添加软删除过滤条件

	// 构建基础查询条件
	if username, ok := condition["username"]; ok && username.(string) != "" {
		m = m.Where(fmt.Sprintf("m.%s LIKE ?", d.Columns().Username), "%"+username.(string)+"%")
	}
	if realName, ok := condition["realName"]; ok && realName.(string) != "" {
		m = m.Where(fmt.Sprintf("m.%s LIKE ?", d.Columns().RealName), "%"+realName.(string)+"%")
	}
	if mobile, ok := condition["mobile"]; ok && mobile.(string) != "" {
		m = m.Where(fmt.Sprintf("m.%s", d.Columns().Mobile), mobile.(string))
	}
	if status, ok := condition["status"]; ok && status.(*int) != nil {
		m = m.Where(fmt.Sprintf("m.%s", d.Columns().Status), *status.(*int))
	}
	// if deptId, ok := condition["deptId"]; ok && deptId.(*int64) != nil {
	// 	m = m.Where(fmt.Sprintf("m.%s", d.Columns().DeptId), *deptId.(*int64)) // 暂时只按部门ID精确查询
	// }

	// 处理角色和岗位筛选 (需要联表查询)
	needsRoleJoin := false
	if roleId, ok := condition["roleId"]; ok && roleId.(*int64) != nil {
		needsRoleJoin = true
	}
	needsPostJoin := false
	if postId, ok := condition["postId"]; ok && postId.(*int64) != nil {
		needsPostJoin = true
	}

	// if needsRoleJoin {
	// 	m = m.LeftJoin(AdminMemberRole.Table()+" mr", fmt.Sprintf("m.%s = mr.%s", d.Columns().Id, AdminMemberRole.Columns().MemberId))
	// 	// m = m.Where(fmt.Sprintf("mr.%s", AdminMemberRole.Columns().RoleId), *condition["roleId"].(*int64))
	// }
	// if needsPostJoin {
	// 	m = m.LeftJoin(AdminMemberPost.Table()+" mp", fmt.Sprintf("m.%s = mp.%s", d.Columns().Id, AdminMemberPost.Columns().MemberId))
	// 	m = m.Where(fmt.Sprintf("mp.%s", AdminMemberPost.Columns().PostId), *condition["postId"].(*int64))
	// }

	// 查询总数
	// 如果有角色或岗位筛选，需要 distinct 避免联表导致重复计数
	countModel := m
	if needsRoleJoin || needsPostJoin {
		countModel = countModel.Distinct()
	}
	total, err = countModel.Fields(fmt.Sprintf("m.%s", d.Columns().Id)).Count() // 只计数主表 ID
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户总数失败")
	}

	if total == 0 {
		return []*entity.AdminMember{}, 0, nil
	}

	// 查询列表数据
	err = m.Fields("m.*").Page(page, pageSize).Group(fmt.Sprintf("m.%s", d.Columns().Id)).OrderAsc(fmt.Sprintf("m.%s", d.Columns().Id)).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户列表失败")
	}

	return list, total, nil
}

// GetById 获取用户详情
func (d *adminMemberDao) GetById(ctx context.Context, id int64) (*entity.AdminMember, error) {
	var member *entity.AdminMember
	err := d.Ctx(ctx).Where(d.Columns().Id, id).Scan(&member)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeMemberNotFound)
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "数据库查询失败")
	}
	if member == nil {
		return nil, gerror.NewCode(codes.CodeMemberNotFound)
	}
	return member, nil
}

// Add 新增用户
func (d *adminMemberDao) Add(ctx context.Context, data *do.AdminMember) (lastInsertId int64, err error) {
	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "新增用户失败")
	}
	lastInsertId, err = res.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新增用户ID失败")
	}
	return lastInsertId, nil
}

// Update 更新用户信息 (不包括密码、角色、岗位)
func (d *adminMemberDao) Update(ctx context.Context, data g.Map, id int64) (err error) {
	// 确保不更新敏感或关联字段
	delete(data, d.Columns().PasswordHash)
	delete(data, d.Columns().Salt)
	// delete(data, d.Columns().RoleId)   // 主角色ID在 admin_member 表
	delete(data, d.Columns().Username) // 用户名通常不允许修改
	delete(data, d.Columns().CreatedAt)
	// delete(data, d.Columns().DeletedAt) // 移除 DeletedAt

	if len(data) == 0 {
		return nil
	}

	_, err = d.Ctx(ctx).Data(data).Where(d.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "更新用户信息失败")
	}
	return nil
}

// DeleteByIds 删除用户 (物理删除)
func (d *adminMemberDao) DeleteByIds(ctx context.Context, ids []int64) (err error) {
	if len(ids) == 0 {
		return nil
	}
	_, err = d.Ctx(ctx).WhereIn(d.Columns().Id, ids).Delete()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "删除用户失败")
	}
	return nil
}

// CheckUsernameExists 检查用户名是否存在
func (d *adminMemberDao) CheckUsernameExists(ctx context.Context, username string, excludeId ...int64) (bool, error) {
	query := d.Ctx(ctx).Where(d.Columns().Username, username)
	if len(excludeId) > 0 {
		query = query.WhereNotIn(d.Columns().Id, excludeId)
	}
	count, err := query.Count()
	if err != nil {
		return false, gerror.WrapCode(codes.CodeInternalError, err, "检查用户名是否存在失败")
	}
	return count > 0, nil
}

// CheckEmailExists 检查邮箱是否存在
func (d *adminMemberDao) CheckEmailExists(ctx context.Context, email string, excludeId ...int64) (bool, error) {
	query := d.Ctx(ctx).Where(d.Columns().Email, email)
	if len(excludeId) > 0 {
		query = query.WhereNotIn(d.Columns().Id, excludeId)
	}
	count, err := query.Count()
	if err != nil {
		return false, gerror.WrapCode(codes.CodeInternalError, err, "检查邮箱是否存在失败")
	}
	return count > 0, nil
}

// CheckMobileExists 检查手机号是否存在
func (d *adminMemberDao) CheckMobileExists(ctx context.Context, mobile string, excludeId ...int64) (bool, error) {
	query := d.Ctx(ctx).Where(d.Columns().Mobile, mobile)
	if len(excludeId) > 0 {
		query = query.WhereNotIn(d.Columns().Id, excludeId)
	}
	count, err := query.Count()
	if err != nil {
		return false, gerror.WrapCode(codes.CodeInternalError, err, "检查手机号是否存在失败")
	}
	return count > 0, nil
}

// CheckInviteCodeExists 检查邀请码是否存在 (如果需要唯一)
func (d *adminMemberDao) CheckInviteCodeExists(ctx context.Context, inviteCode string, excludeId ...int64) (bool, error) {
	if inviteCode == "" { // 邀请码可能为空
		return false, nil
	}
	query := d.Ctx(ctx).Where(d.Columns().InviteCode, inviteCode)
	if len(excludeId) > 0 {
		query = query.WhereNotIn(d.Columns().Id, excludeId)
	}
	count, err := query.Count()
	if err != nil {
		return false, gerror.WrapCode(codes.CodeInternalError, err, "检查邀请码是否存在失败")
	}
	return count > 0, nil
}

// UpdateStatus 更新用户状态
func (d *adminMemberDao) UpdateStatus(ctx context.Context, id int64, status int) error {
	_, err := d.Ctx(ctx).Data(g.Map{d.Columns().Status: status}).Where(d.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "更新用户状态失败")
	}
	return nil
}

// UpdatePassword 更新用户密码
func (d *adminMemberDao) UpdatePassword(ctx context.Context, id int64, passwordHash, salt string) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().PasswordHash: passwordHash,
		d.Columns().Salt:         salt,
	}).Where(d.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "更新用户密码失败")
	}
	return nil
}

// GetDirectSubordinatesCount 获取指定用户的直接下级数量
func (d *adminMemberDao) GetDirectSubordinatesCount(ctx context.Context, pid int64) (int, error) {
	count, err := d.Ctx(ctx).Where(d.Columns().Pid, pid).Count()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取下级用户数量失败")
	}
	return count, nil
}

// FindDescendantsByTree 查找指定用户的所有子孙节点 (通过 tree 字段)
func (d *adminMemberDao) FindDescendantsByTree(ctx context.Context, tree string) ([]*entity.AdminMember, error) {
	var descendants []*entity.AdminMember
	likePattern := tree + "%"
	err := d.Ctx(ctx).Where(fmt.Sprintf("%s LIKE ?", d.Columns().Tree), likePattern).
		Where(fmt.Sprintf("%s != ?", d.Columns().Tree), tree). // 排除自身
		Scan(&descendants)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查找子孙用户失败")
	}
	return descendants, nil
}

// BatchUpdateLevelTree 批量更新用户的 Level 和 Tree 字段 (在事务中使用)
func (d *adminMemberDao) BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error {
	if len(updates) == 0 {
		return nil
	}
	for id, data := range updates {
		_, err := tx.Model(d.Table()).Data(data).Where(d.Columns().Id, id).Update()
		if err != nil {
			return gerror.WrapCodef(codes.CodeInternalError, err, "批量更新用户 Tree 和 Level 失败 (ID: %d)", id)
		}
	}
	return nil
}

// BuildMemberTree 构建用户关系树路径
func BuildMemberTree(parentTree string, memberId int64) string {
	if parentTree == "" || parentTree == ",0," {
		return fmt.Sprintf(",%d,", memberId)
	}
	return fmt.Sprintf("%s%d,", parentTree, memberId)
}

// GetAllMemberIds 获取所有有效用户ID (移除 DeletedAt 条件)
func (d *adminMemberDao) GetAllMemberIds(ctx context.Context) ([]int64, error) {
	var ids []int64
	err := d.Ctx(ctx).WhereNull(d.Columns().DeletedAt).Fields(d.Columns().Id).Scan(&ids) // 添加软删除过滤条件
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取所有用户ID失败")
	}
	return ids, nil
}

// GetMemberListForNotice 获取用户列表（用于公告选择器）
// 支持按用户名、部门、角色、岗位筛选
func (d *adminMemberDao) GetMemberListForNotice(ctx context.Context, page, pageSize int, condition g.Map) (list []*UserInfoForNoticeSelect, total int, err error) {
	m := d.Ctx(ctx).As("m").WhereNull(fmt.Sprintf("m.%s", d.Columns().DeletedAt)) // 添加软删除过滤条件

	// 构建基础查询条件
	if username, ok := condition["username"]; ok && username.(string) != "" {
		m = m.Where(fmt.Sprintf("m.%s LIKE ?", d.Columns().Username), "%"+username.(string)+"%")
	}
	if deptId, ok := condition["deptId"]; ok && deptId.(*int64) != nil {
		// m = m.Where(fmt.Sprintf("m.%s", d.Columns().DeptId), *deptId.(*int64))
	}

	// 处理角色和岗位筛选 (需要联表查询)
	needsRoleJoin := false
	if roleId, ok := condition["roleId"]; ok && roleId.(*int64) != nil {
		needsRoleJoin = true
	}
	needsPostJoin := false
	if postId, ok := condition["postId"]; ok && postId.(*int64) != nil {
		needsPostJoin = true
	}

	// if needsRoleJoin {
	// 	m = m.LeftJoin(AdminMemberRole.Table()+" mr", fmt.Sprintf("m.%s = mr.%s", d.Columns().Id, AdminMemberRole.Columns().MemberId))
	// 	// m = m.Where(fmt.Sprintf("mr.%s", AdminMemberRole.Columns().RoleId), *condition["roleId"].(*int64))
	// }
	// if needsPostJoin {
	// 	m = m.LeftJoin(AdminMemberPost.Table()+" mp", fmt.Sprintf("m.%s = mp.%s", d.Columns().Id, AdminMemberPost.Columns().MemberId))
	// 	m = m.Where(fmt.Sprintf("mp.%s", AdminMemberPost.Columns().PostId), *condition["postId"].(*int64))
	// }

	// 查询总数 (需要 Group By 避免联表导致重复计数)
	countModel := m
	if needsRoleJoin || needsPostJoin {
		countModel = countModel.Distinct()
	}
	total, err = countModel.Fields(fmt.Sprintf("m.%s", d.Columns().Id)).Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户总数失败")
	}

	if total == 0 {
		return []*UserInfoForNoticeSelect{}, 0, nil
	}

	// 查询列表数据
	// 选择需要的字段，并使用 Group By 确保用户唯一性
	fields := fmt.Sprintf("m.%s as id, m.%s as username, m.%s as avatar",
		d.Columns().Id, d.Columns().Username, d.Columns().Avatar)
	err = m.Fields(fields).Page(page, pageSize).Group(fmt.Sprintf("m.%s", d.Columns().Id)).OrderAsc(fmt.Sprintf("m.%s", d.Columns().Id)).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户列表失败")
	}

	return list, total, nil
}
