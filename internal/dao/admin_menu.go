// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"
	"database/sql"
	"fmt"
	"sort"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// adminMenuDao is the data access object for the table admin_menu.
// You can define custom methods on it to extend its functionality as needed.
type adminMenuDao struct {
	*internal.AdminMenuDao
}

var (
	// AdminMenu is a globally accessible object for table admin_menu operations.
	AdminMenu = adminMenuDao{internal.NewAdminMenuDao()}
)

// GetAllMenus 获取所有菜单列表（可带条件过滤）
func (d *adminMenuDao) GetAllMenus(ctx context.Context, condition g.Map) ([]*entity.AdminMenu, error) {
	var menus []*entity.AdminMenu
	query := d.Ctx(ctx)
	if len(condition) > 0 {
		query = query.Where(condition)
	}
	// 按 Sort 升序，然后按 ID 升序，确保排序稳定
	err := query.OrderAsc(d.Columns().Sort).OrderAsc(d.Columns().Id).Scan(&menus)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取所有菜单列表失败")
	}
	return menus, nil
}

// 获取无限极菜单
func (d *adminMenuDao) GetAllMenusTree(ctx context.Context, condition g.Map) ([]*v1.MenuTreeNode, error) {
	menus, err := d.GetAllMenus(ctx, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取所有菜单列表失败")
	}
	// 构建无限极菜单树
	menuTree := buildMenuTree(menus)
	return menuTree, nil
}

// // 获取无限极菜单
// func (d *adminMenuDao) GetAllMenusTreeWithPermission(ctx context.Context, condition g.Map, permissions []cache.Permission) ([]*v1.MenuTreeNode, error) {
// 	menus, err := d.GetAllMenus(ctx, condition)
// 	if err != nil {
// 		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取所有菜单列表失败")
// 	}
// 	// 构建无限极菜单树
// 	menuTree := buildMenuTree(menus)
// 	return menuTree, nil
// }

// 构建无限极菜单树
func buildMenuTree(menus []*entity.AdminMenu) []*v1.MenuTreeNode {
	if len(menus) == 0 {
		return []*v1.MenuTreeNode{}
	}

	// 创建所有菜单节点映射
	menuMap := make(map[int64]*v1.MenuTreeNode)
	for _, menu := range menus {
		menuMap[menu.Id] = &v1.MenuTreeNode{
			AdminMenu: menu,
			Children:  make([]*v1.MenuTreeNode, 0),
		}
	}

	// 构建树形结构
	var rootNodes []*v1.MenuTreeNode
	for _, menu := range menus {
		if menu.Pid != 0 {
			// 将当前节点添加到父节点的子节点列表中
			if parent, exists := menuMap[menu.Pid]; exists {
				parent.Children = append(parent.Children, menuMap[menu.Id])
			}
		} else {
			// 如果是根节点（Pid=0），则添加到根节点列表
			rootNodes = append(rootNodes, menuMap[menu.Id])
		}
	}

	// 递归排序所有节点的子节点
	var sortChildrenRecursive func([]*v1.MenuTreeNode)
	sortChildrenRecursive = func(nodes []*v1.MenuTreeNode) {
		if len(nodes) == 0 {
			return
		}
		for _, node := range nodes {
			if len(node.Children) > 1 {
				sort.Slice(node.Children, func(i, j int) bool {
					// 主要按 Sort 字段升序排序
					if node.Children[i].AdminMenu.Sort != node.Children[j].AdminMenu.Sort {
						return node.Children[i].AdminMenu.Sort < node.Children[j].AdminMenu.Sort
					}
					// 如果 Sort 相同，则按 Id 升序排序以确保稳定性
					return node.Children[i].AdminMenu.Id < node.Children[j].AdminMenu.Id
				})
			}
			sortChildrenRecursive(node.Children) // 递归处理子节点的子节点
		}
	}
	sortChildrenRecursive(rootNodes)

	return rootNodes
}

// GetMenuById 根据ID获取菜单信息
func (d *adminMenuDao) GetMenuById(ctx context.Context, id int64) (*entity.AdminMenu, error) {
	var menu *entity.AdminMenu
	err := d.Ctx(ctx).Where(d.Columns().Id, id).Scan(&menu)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeMenuNotFound)
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "数据库查询失败")
	}
	if menu == nil {
		return nil, gerror.NewCode(codes.CodeMenuNotFound)
	}
	return menu, nil
}

// AddMenu 新增菜单
func (d *adminMenuDao) AddMenu(ctx context.Context, data *do.AdminMenu) (lastInsertId int64, err error) {
	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "新增菜单失败")
	}
	lastInsertId, err = res.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新增菜单ID失败")
	}
	return lastInsertId, nil
}

// UpdateMenu 更新菜单信息
func (d *adminMenuDao) UpdateMenu(ctx context.Context, data g.Map, id int64) (err error) {
	_, err = d.Ctx(ctx).Data(data).Where(d.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "更新菜单信息失败")
	}
	return nil
}

// DeleteMenuById 根据ID删除菜单 (物理删除)
func (d *adminMenuDao) DeleteMenuById(ctx context.Context, id int64) (err error) {
	_, err = d.Ctx(ctx).Where(d.Columns().Id, id).Delete()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "删除菜单失败")
	}
	return nil
}

// // CheckNameExists 检查菜单名称是否存在（在同一父目录下，可排除指定ID）
// func (d *adminMenuDao) CheckNameExists(ctx context.Context, title string, pid int64, excludeId ...int64) (bool, error) {
// 	query := d.Ctx(ctx).Where(d.Columns().Title, title).Where(d.Columns().Pid, pid)
// 	if len(excludeId) > 0 {
// 		query = query.WhereNotIn(d.Columns().Id, excludeId)
// 	}
// 	count, err := query.Count()
// 	if err != nil {
// 		return false, gerror.WrapCode(codes.CodeInternalError, err, "检查菜单名称是否存在失败")
// 	}
// 	return count > 0, nil
// }

// GetDirectChildrenCount 获取指定父ID下的直接子菜单数量
func (d *adminMenuDao) GetDirectChildrenCount(ctx context.Context, pid int64) (int, error) {
	count, err := d.Ctx(ctx).Where(d.Columns().Pid, pid).Count()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取子菜单数量失败")
	}
	return count, nil
}

// FindDescendantsByTree 查找指定菜单的所有子孙节点 (通过 tree 字段)
func (d *adminMenuDao) FindDescendantsByTree(ctx context.Context, tree string) ([]*entity.AdminMenu, error) {
	var descendants []*entity.AdminMenu
	likePattern := tree + "%"
	err := d.Ctx(ctx).Where(fmt.Sprintf("%s LIKE ?", d.Columns().Tree), likePattern).
		Where(fmt.Sprintf("%s != ?", d.Columns().Tree), tree). // 排除自身
		Scan(&descendants)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查找子孙菜单失败")
	}
	return descendants, nil
}

// BatchUpdateLevelTree 批量更新菜单的 Level 和 Tree 字段 (在事务中使用)
func (d *adminMenuDao) BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error {
	if len(updates) == 0 {
		return nil
	}
	// 使用事务执行批量更新
	for id, data := range updates {
		_, err := tx.Model(d.Table()).Data(data).Where(d.Columns().Id, id).Update()
		if err != nil {
			return gerror.WrapCodef(codes.CodeInternalError, err, "批量更新菜单 Tree 和 Level 失败 (ID: %d)", id)
		}
	}
	return nil
}

// BuildMenuTree 构建菜单关系树路径
func BuildMenuTree(parentTree string, menuId int64) string {
	if parentTree == "" || parentTree == ",0," { // 根节点或父节点是根
		return fmt.Sprintf(",%d,", menuId)
	}
	return fmt.Sprintf("%s%d,", parentTree, menuId)
}

// 移除不再需要的 GetMenuPermissionsByIds 方法
