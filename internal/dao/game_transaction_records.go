// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameTransactionRecordsDao is the data access object for the table game_transaction_records.
// You can define custom methods on it to extend its functionality as needed.
type gameTransactionRecordsDao struct {
	*internal.GameTransactionRecordsDao
}

var (
	// GameTransactionRecords is a globally accessible object for table game_transaction_records operations.
	GameTransactionRecords = gameTransactionRecordsDao{internal.NewGameTransactionRecordsDao()}
)

// Add your custom methods and functionality below.
