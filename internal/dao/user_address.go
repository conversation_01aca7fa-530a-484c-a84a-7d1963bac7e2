// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
	
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
)

// userAddressDao is the data access object for the table user_address.
// You can define custom methods on it to extend its functionality as needed.
type userAddressDao struct {
	*internal.UserAddressDao
}

var (
	// UserAddress is a globally accessible object for table user_address operations.
	UserAddress = userAddressDao{internal.NewUserAddressDao()}
)

// Add your custom methods and functionality below.

// ListAdminUserAddressesWithFullInfo 查询后台用户充值地址列表（含完整信息）
func (d *userAddressDao) ListAdminUserAddressesWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.UserAddressAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.UserAddressAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := d.Ctx(ctx).As("ua").
		LeftJoin("users u", "ua.user_id = u.id")
	
	// 添加用户的代理关联
	m = m.LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id")
	m = m.LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id")
	m = m.LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id")
	
	// 添加用户的 Telegram 信息关联
	m = m.LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1")

	// 处理查询条件（分类处理 LIKE、BETWEEN、精确匹配）
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})
	
	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}
	
	// 按顺序应用条件
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}
	
	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}
	
	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询充值地址总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"ua.user_address_id",
		"ua.token_id",
		"ua.user_id",
		"u.account as user_account",
		"ua.lable",
		"ua.name",
		"ua.chan",
		"ua.address",
		"ua.qr_code_url",
		"ua.type",
		"ua.created_at",
		"ua.updated_at",
	}
	
	// 用户的代理和Telegram字段
	userAgentAndTelegramFields := []string{
		"first_agent.username as first_agent_name",
		"second_agent.username as second_agent_name",
		"third_agent.username as third_agent_name",
		"uba.telegram_id",
		"uba.telegram_username",
		"uba.first_name",
	}
	
	allFields := append(baseFields, userAgentAndTelegramFields...)
	
	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}
	
	// 执行查询
	err = query.Page(page, pageSize).Order("ua.user_address_id DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询充值地址列表失败")
	}

	return list, total, nil
}
