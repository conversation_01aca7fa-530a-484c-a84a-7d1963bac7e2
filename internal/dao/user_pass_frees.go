// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// userPassFreesDao is the data access object for the table user_pass_frees.
// You can define custom methods on it to extend its functionality as needed.
type userPassFreesDao struct {
	*internal.UserPassFreesDao
}

var (
	// UserPassFrees is a globally accessible object for table user_pass_frees operations.
	UserPassFrees = userPassFreesDao{internal.NewUserPassFreesDao()}
)

// Add your custom methods and functionality below.
