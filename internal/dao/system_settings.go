// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// systemSettingsDao is the data access object for the table system_settings.
// You can define custom methods on it to extend its functionality as needed.
type systemSettingsDao struct {
	*internal.SystemSettingsDao
}

var (
	// SystemSettings is a globally accessible object for table system_settings operations.
	SystemSettings = systemSettingsDao{internal.NewSystemSettingsDao()}
)

// Add your custom methods and functionality below.
