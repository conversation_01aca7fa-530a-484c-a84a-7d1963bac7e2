// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity" // Added import
	"context"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror" // Added import
	"github.com/gogf/gf/v2/frame/g"
)

// ipAccessListDao is the data access object for the table ip_access_list.
// You can define custom methods on it to extend its functionality as needed.
type ipAccessListDao struct {
	*internal.IpAccessListDao
}

var (
	// IpAccessList is a globally accessible object for table ip_access_list operations.
	IpAccessList = ipAccessListDao{internal.NewIpAccessListDao()}
)

// ParseTimeRange 解析时间范围为开始时间和结束时间
func (d *ipAccessListDao) ParseTimeRange(dateRange, startTime, endTime string) (string, string) {
	// 如果有dateRange，优先使用dateRange
	if dateRange != "" {
		parts := strings.Split(dateRange, ",")
		if len(parts) >= 2 {
			startTime = parts[0]
			endTime = parts[1]
		}
	}

	// 确保时间格式正确
	if startTime != "" {
		startTime += " 00:00:00"
	}
	if endTime != "" {
		endTime += " 23:59:59"
	}

	return startTime, endTime
}

// GetIpAccessListList 获取IP访问列表
func (d *ipAccessListDao) GetIpAccessListList(ctx context.Context, page, pageSize int, condition g.Map) (list []*do.IpAccessList, total int, err error) {
	// 创建查询对象
	db := g.DB()
	m := db.Model(d.Table())

	// 应用查询条件
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	// 统计总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询数据
	list = make([]*do.IpAccessList, 0)
	err = m.Page(page, pageSize).Order("id DESC").Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// AddIpAccessList 添加IP到访问列表
func (d *ipAccessListDao) AddIpAccessList(ctx context.Context, data *do.IpAccessList) (id int64, err error) {
	r, err := g.DB().Model(d.Table()).Data(data).Insert()
	if err != nil {
		return 0, err
	}

	id, err = r.LastInsertId()
	return id, err
}

// DeleteIpAccessList 删除IP记录
func (d *ipAccessListDao) DeleteIpAccessList(ctx context.Context, id int64) (err error) {
	_, err = g.DB().Model(d.Table()).Where("id", id).Delete()
	return err
}

// UpdateIpAccessList 更新IP访问记录
func (d *ipAccessListDao) UpdateIpAccessList(ctx context.Context, id int64, data *do.IpAccessList) (err error) {
	_, err = g.DB().Model(d.Table()).Data(data).Where("id", id).Update()
	return err
}

// GetIpAccessListById 根据ID获取IP访问记录
func (d *ipAccessListDao) GetIpAccessListById(ctx context.Context, id int64) (ipAccess *do.IpAccessList, err error) {
	ipAccess = &do.IpAccessList{}
	err = g.DB().Model(d.Table()).Where("id", id).Scan(ipAccess)
	if err != nil {
		// 如果是没有找到记录，返回nil
		// Note: g.IsNil(err) is likely incorrect for checking sql.ErrNoRows.
		// Use errors.Is(err, sql.ErrNoRows) or check the error message if using older Go versions or specific DB drivers.
		// For simplicity based on original code, keeping the check but adding a comment.
		if err.Error() == "sql: no rows in result set" { // Consider using errors.Is(err, sql.ErrNoRows)
			return nil, nil
		}
		return nil, err
	}

	return ipAccess, nil
}

// EnableIpAccessList 启用IP访问控制
func (d *ipAccessListDao) EnableIpAccessList(ctx context.Context, id int64) (err error) {
	_, err = g.DB().Model(d.Table()).Data(g.Map{"is_enabled": 1}).Where("id", id).Update()
	return err
}

// DisableIpAccessList 禁用IP访问控制
func (d *ipAccessListDao) DisableIpAccessList(ctx context.Context, id int64) (err error) {
	_, err = g.DB().Model(d.Table()).Data(g.Map{"is_enabled": 0}).Where("id", id).Update()
	return err
}

// --- Methods required by Agent Logic ---

// AddIP 添加 IP 记录 (兼容 Logic 调用)
// 注意：Logic 层传入的是 do.IpAccessList，包含 AgentId
func (d *ipAccessListDao) AddIP(ctx context.Context, data *do.IpAccessList) (id int64, err error) {
	// 直接使用 Model 方法插入，它会处理 do 结构体
	r, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "添加IP记录失败")
	}
	id, err = r.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取新IP记录ID失败")
	}
	return id, nil
}

// DeleteIPById 根据 ID 删除 IP 记录 (兼容 Logic 调用)
func (d *ipAccessListDao) DeleteIPById(ctx context.Context, id int64) (err error) {
	_, err = d.Ctx(ctx).Where(d.Columns().Id, id).Delete()
	if err != nil {
		return gerror.Wrapf(err, "删除IP记录失败, ID: %d", id)
	}
	return nil
}

// GetIPsByAgentIdAndType 根据 Agent ID 和 Use Type 获取 IP 列表 (不分页)
func (d *ipAccessListDao) GetIPsByAgentIdAndType(ctx context.Context, agentId int64, useType string) (list []*entity.IpAccessList, err error) {
	err = d.Ctx(ctx).
		Where(d.Columns().AgentId, agentId).
		Where(d.Columns().UseType, useType).
		Where(d.Columns().ListType, "whitelist"). // 假设只查白名单
		Where(d.Columns().IsEnabled, 1).          // 假设只查启用的
		OrderDesc(d.Columns().Id).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrapf(err, "根据Agent ID和类型查询IP列表失败, AgentID: %d, Type: %s", agentId, useType)
	}
	return list, nil
}

// GetCount 根据条件获取 IP 记录数量 (兼容 Logic 调用)
func (d *ipAccessListDao) GetCount(ctx context.Context, condition g.Map) (total int, err error) {
	total, err = d.Ctx(ctx).Where(condition).Count()
	if err != nil {
		return 0, gerror.Wrap(err, "查询IP记录数量失败")
	}
	return total, nil
}

// GetList 根据条件获取 IP 记录列表 (兼容 Logic 调用)
func (d *ipAccessListDao) GetList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.IpAccessList, err error) {
	err = d.Ctx(ctx).Where(condition).Page(page, pageSize).OrderDesc(d.Columns().Id).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "查询IP记录列表失败")
	}
	return list, nil
}
