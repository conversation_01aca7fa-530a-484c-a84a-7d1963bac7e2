// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// commissionRecordsDao is the data access object for the table commission_records.
// You can define custom methods on it to extend its functionality as needed.
type commissionRecordsDao struct {
	*internal.CommissionRecordsDao
}

var (
	// CommissionRecords is a globally accessible object for table commission_records operations.
	CommissionRecords = commissionRecordsDao{internal.NewCommissionRecordsDao()}
)

// Add your custom methods and functionality below.
