// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// vPaybotAuthOrdersStatsDao is the data access object for the table v_paybot_auth_orders_stats.
// You can define custom methods on it to extend its functionality as needed.
type vPaybotAuthOrdersStatsDao struct {
	*internal.VPaybotAuthOrdersStatsDao
}

var (
	// VPaybotAuthOrdersStats is a globally accessible object for table v_paybot_auth_orders_stats operations.
	VPaybotAuthOrdersStats = vPaybotAuthOrdersStatsDao{internal.NewVPaybotAuthOrdersStatsDao()}
)

// Add your custom methods and functionality below.
