// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"

	"github.com/gogf/gf/v2/database/gdb"
)

// merchantCallbacksDao is the data access object for the table merchant_callbacks.
// You can define custom methods on it to extend its functionality as needed.
type merchantCallbacksDao struct {
	*internal.MerchantCallbacksDao
}

var (
	// MerchantCallbacks is a globally accessible object for table merchant_callbacks operations.
	MerchantCallbacks = merchantCallbacksDao{internal.NewMerchantCallbacksDao()}
)

// Add your custom methods and functionality below.

// List 查询回调记录列表
func (dao *merchantCallbacksDao) List(merchantId uint64, status, callbackType string, startTime, endTime string, page, size int) (list []map[string]interface{}, total int, err error) {
	db := dao.DB()

	// 构建查询条件的函数，确保每次调用都返回新的查询构建器
	buildWhere := func() *gdb.Model {
		whereBuilder := db.Model(dao.Table()).Where(dao.Columns().MerchantId, merchantId)

		if status != "" {
			whereBuilder = whereBuilder.Where(dao.Columns().Status, status)
		}

		if callbackType != "" {
			whereBuilder = whereBuilder.Where(dao.Columns().CallbackType, callbackType)
		}

		if startTime != "" && endTime != "" {
			whereBuilder = whereBuilder.WhereBetween(dao.Columns().CreatedAt, startTime, endTime)
		}

		return whereBuilder
	}

	// 查询总数 - 使用独立的查询构建器
	total, err = buildWhere().Count()
	if err != nil {
		return nil, 0, err
	}

	// 查询列表 - 使用新的查询构建器
	err = buildWhere().
		Order(dao.Columns().Id+" DESC").
		Page(page, size).
		Scan(&list)

	return
}

// GetByIdAndMerchantId 根据ID和商户ID查询详情
func (dao *merchantCallbacksDao) GetByIdAndMerchantId(id, merchantId uint64) (record map[string]interface{}, err error) {
	err = dao.DB().Model(dao.Table()).
		Where(dao.Columns().Id, id).
		Where(dao.Columns().MerchantId, merchantId).
		Scan(&record)
	return
}

// CountByCondition 根据条件统计记录数
func (dao *merchantCallbacksDao) CountByCondition(merchantId uint64, status string, callbackType string, startTime, endTime string) (count int, err error) {
	db := dao.DB().Model(dao.Table()).Where(dao.Columns().MerchantId, merchantId)

	if status != "" {
		db = db.Where(dao.Columns().Status, status)
	}

	if callbackType != "" {
		db = db.Where(dao.Columns().CallbackType, callbackType)
	}

	if startTime != "" && endTime != "" {
		db = db.WhereBetween(dao.Columns().CreatedAt, startTime, endTime)
	}

	count, err = db.Count()
	return
}
