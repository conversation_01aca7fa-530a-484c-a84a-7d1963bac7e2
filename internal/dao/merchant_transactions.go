// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// merchantTransactionsDao is the data access object for the table merchant_transactions.
// You can define custom methods on it to extend its functionality as needed.
type merchantTransactionsDao struct {
	*internal.MerchantTransactionsDao
}

var (
	// MerchantTransactions is a globally accessible object for table merchant_transactions operations.
	MerchantTransactions = merchantTransactionsDao{internal.NewMerchantTransactionsDao()}
)

// Add your custom methods and functionality below.

// List 分页查询交易记录
func (dao *merchantTransactionsDao) List(ctx context.Context, page, pageSize int, condition g.Map) ([]*entity.MerchantTransactions, int64, error) {
	var transactions []*entity.MerchantTransactions

	// 构建查询模型
	model := dao.Ctx(ctx).Where(condition).OrderDesc("transaction_id")

	// 获取总数
	total, err := model.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		model = model.Limit(pageSize).Offset(offset)
	}

	err = model.Scan(&transactions)
	if err != nil {
		return nil, 0, err
	}

	return transactions, int64(total), nil
}

// GetByTransactionIdAndMerchantId 获取特定交易详情（安全验证商户ID）
func (dao *merchantTransactionsDao) GetByTransactionIdAndMerchantId(ctx context.Context, transactionId uint64, merchantId uint64) (*entity.MerchantTransactions, error) {
	var transaction *entity.MerchantTransactions

	err := dao.Ctx(ctx).
		Where("transaction_id = ? AND merchant_id = ?", transactionId, merchantId).
		Scan(&transaction)

	if err != nil {
		return nil, err
	}

	return transaction, nil
}

// TransactionStats 交易统计结构
type TransactionStats struct {
	TotalTransactions   int64  `json:"totalTransactions"`
	SuccessTransactions int64  `json:"successTransactions"`
	FailedTransactions  int64  `json:"failedTransactions"`
	TotalAmountIn       string `json:"totalAmountIn"`
	TotalAmountOut      string `json:"totalAmountOut"`
	TotalFeeAmount      string `json:"totalFeeAmount"`
}

// GetStatsByMerchantId 获取资金统计信息
func (dao *merchantTransactionsDao) GetStatsByMerchantId(ctx context.Context, merchantId uint64, tokenSymbol string, dateRange string) (*TransactionStats, error) {
	model := dao.Ctx(ctx).Where("merchant_id = ?", merchantId)

	// 币种筛选
	if tokenSymbol != "" {
		model = model.Where("symbol = ?", tokenSymbol)
	}

	// 时间范围筛选
	if dateRange != "" {
		// 这里需要解析dateRange并添加时间条件
		// 简化实现，实际应用中需要解析"2025-01-01,2025-01-31"格式
	}

	// 基础统计
	var stats TransactionStats

	// 获取总交易数
	total, _ := model.Count()
	stats.TotalTransactions = int64(total)

	// 获取成功交易数
	successCount, _ := model.Where("status = ?", 1).Count()
	stats.SuccessTransactions = int64(successCount)

	// 失败交易数
	stats.FailedTransactions = stats.TotalTransactions - stats.SuccessTransactions

	// 金额统计需要使用聚合查询
	// 入账金额
	inAmountResult, _ := model.Where("direction = ? AND status = ?", "in", 1).Sum("amount")
	stats.TotalAmountIn = gconv.String(inAmountResult)

	// 出账金额
	outAmountResult, _ := model.Where("direction = ? AND status = ?", "out", 1).Sum("amount")
	stats.TotalAmountOut = gconv.String(outAmountResult)

	// 手续费统计
	feeAmountResult, _ := model.Where("status = ?", 1).Sum("fee_amount")
	stats.TotalFeeAmount = gconv.String(feeAmountResult)

	return &stats, nil
}

// BalanceHistoryItem 余额历史项
type BalanceHistoryItem struct {
	TransactionId uint64 `json:"transactionId"`
	Amount        string `json:"amount"`
	BalanceBefore string `json:"balanceBefore"`
	BalanceAfter  string `json:"balanceAfter"`
	Type          string `json:"type"`
	Direction     string `json:"direction"`
	WalletType    string `json:"walletType"`
	CreatedAt     string `json:"createdAt"`
}

// GetBalanceHistory 获取余额变动历史
func (dao *merchantTransactionsDao) GetBalanceHistory(ctx context.Context, merchantId uint64, tokenSymbol string, limit int) ([]*BalanceHistoryItem, error) {
	model := dao.Ctx(ctx).
		Where("merchant_id = ? AND symbol = ?", merchantId, tokenSymbol).
		OrderDesc("transaction_id")

	if limit > 0 {
		model = model.Limit(limit)
	}

	var items []*BalanceHistoryItem
	err := model.Fields("transaction_id, amount, balance_before, balance_after, type, direction, wallet_type, created_at").
		Scan(&items)

	return items, err
}

// GetTypeStatsByMerchantId 按交易类型获取统计信息
func (dao *merchantTransactionsDao) GetTypeStatsByMerchantId(ctx context.Context, merchantId uint64, tokenSymbol string) ([]g.Map, error) {
	model := dao.Ctx(ctx).Where("merchant_id = ?", merchantId)

	if tokenSymbol != "" {
		model = model.Where("symbol = ?", tokenSymbol)
	}

	var result []g.Map
	err := model.Fields(
		"type",
		"COUNT(*) as count",
		"SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count",
		"SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as failed_count",
		"SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as total_amount",
	).
		Group("type").
		Scan(&result)

	return result, err
}

// GetTokenStatsByMerchantId 按币种获取统计信息
func (dao *merchantTransactionsDao) GetTokenStatsByMerchantId(ctx context.Context, merchantId uint64) ([]g.Map, error) {
	var result []g.Map
	err := dao.Ctx(ctx).
		Where("merchant_id = ?", merchantId).
		Fields(
			"symbol as token_symbol",
			"COUNT(*) as count",
			"SUM(CASE WHEN direction = 'in' AND status = 1 THEN amount ELSE 0 END) as total_amount_in",
			"SUM(CASE WHEN direction = 'out' AND status = 1 THEN amount ELSE 0 END) as total_amount_out",
		).
		Group("symbol").
		Scan(&result)

	return result, err
}
