// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"context"

	v1 "admin-api/api/system/v1"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// exchangeOrdersDao is the data access object for the table exchange_orders.
// You can define custom methods on it to extend its functionality as needed.
type exchangeOrdersDao struct {
	*internal.ExchangeOrdersDao
}

var (
	// ExchangeOrders is a globally accessible object for table exchange_orders operations.
	ExchangeOrders = exchangeOrdersDao{internal.NewExchangeOrdersDao()}
)

// Add your custom methods and functionality below.

// ListWithAgentInfo 查询兑换订单列表（包含用户的代理和telegram信息）
func (d *exchangeOrdersDao) ListWithAgentInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*v1.ExchangeOrderItem, total int, err error) {
	list = make([]*v1.ExchangeOrderItem, 0)

	// 构建基础查询，关联用户表和代理信息
	m := d.Ctx(ctx).As("o").
		LeftJoin("users u", "o.user_id = u.id").
		LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id").
		LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id").
		LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1")

	// 应用过滤条件
	query := m

	// 为了避免SQL参数顺序混乱，分别处理不同类型的条件
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			// 提取字段名并存储BETWEEN条件
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE") {
			// 提取字段名（去掉 " LIKE" 部分）
			fieldName := gstr.Replace(key, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件，确保SQL参数正确对应
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}

	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}

	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询兑换订单总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 应用分页和排序
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 指定查询字段
	fields := []string{
		"o.order_id",
		"o.order_sn",
		"o.user_id",
		"o.product_id",
		"o.base_token",
		"o.quote_token",
		"o.symbol",
		"o.trade_type",
		"o.amount_base",
		"o.amount_quote",
		"o.original_from_amount",
		"o.original_to_amount",
		"o.price",
		"o.spread_amount",
		"o.spread_rate",
		"o.fee_amount",
		"o.fee_token_id",
		"o.output_amount_before_fee",
		"o.output_amount_after_fee",
		"o.fee_calculation_method",
		"o.transaction_hash",
		"o.sender_transaction_id",
		"o.receiver_transaction_id",
		"o.quote_id",
		"o.status",
		"o.error_message",
		"o.client_order_id",
		"o.created_at",
		"o.executed_at",
		"o.completed_at",
		"o.updated_at",
	}

	err = query.Fields(fields).
		Page(page, pageSize).
		OrderDesc("o.created_at").
		Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询兑换订单列表失败")
	}

	return list, total, nil
}

// ListAdminExchangeOrdersWithFullInfo 查询后台兑换订单列表（含完整信息）
func (d *exchangeOrdersDao) ListAdminExchangeOrdersWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.ExchangeOrderAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.ExchangeOrderAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := g.DB().Model("exchange_orders o").
		LeftJoin("users u", "o.user_id = u.id")

	// 添加用户的代理关联
	m = m.LeftJoin("agents u_first_agent", "u.first_id = u_first_agent.agent_id")
	m = m.LeftJoin("agents u_second_agent", "u.second_id = u_second_agent.agent_id")
	m = m.LeftJoin("agents u_third_agent", "u.third_id = u_third_agent.agent_id")

	// 添加用户的 Telegram 信息关联
	m = m.LeftJoin("user_backup_accounts u_uba", "u.id = u_uba.user_id AND u_uba.is_master = 1")

	// 添加软删除条件
	m = m.WhereNull("o.deleted_at")

	// 处理查询条件（分类处理 LIKE、BETWEEN、精确匹配）
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}

	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}

	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询兑换订单总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"o.order_id",
		"o.order_sn",
		"o.user_id",
		"o.product_id",
		"o.base_token",
		"o.quote_token",
		"o.symbol",
		"o.trade_type",
		"o.amount_base",
		"o.amount_quote",
		"o.original_from_amount",
		"o.original_to_amount",
		"o.price",
		"o.spread_amount",
		"o.spread_rate",
		"o.fee_amount",
		"o.fee_token_id",
		"o.output_amount_before_fee",
		"o.output_amount_after_fee",
		"o.fee_calculation_method",
		"o.transaction_hash",
		"o.sender_transaction_id",
		"o.receiver_transaction_id",
		"o.quote_id",
		"o.status",
		"o.error_message",
		"o.client_order_id",
		"o.created_at",
		"o.executed_at",
		"o.completed_at",
		"o.updated_at",
		"u.account as account",
	}

	// 用户的代理和Telegram字段
	userAgentAndTelegramFields := []string{
		"u_first_agent.username as first_agent_name",
		"u_second_agent.username as second_agent_name",
		"u_third_agent.username as third_agent_name",
		"u_uba.telegram_id",
		"u_uba.telegram_username",
		"u_uba.first_name",
	}

	allFields := append(baseFields, userAgentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	// 执行查询
	err = query.Page(page, pageSize).Order("o.created_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询兑换订单列表失败")
	}

	return list, total, nil
}

// GetOrderDetailWithFullInfo 获取包含完整信息的订单详情
func (d *exchangeOrdersDao) GetOrderDetailWithFullInfo(ctx context.Context, orderId uint64) (*model.ExchangeOrderAdminInfo, error) {
	var order model.ExchangeOrderAdminInfo
	
	// 构建查询，使用与列表查询相同的关联结构
	err := g.DB().Model("exchange_orders o").
		LeftJoin("users u", "o.user_id = u.id").
		LeftJoin("agents u_first_agent", "u.first_id = u_first_agent.agent_id").
		LeftJoin("agents u_second_agent", "u.second_id = u_second_agent.agent_id").
		LeftJoin("agents u_third_agent", "u.third_id = u_third_agent.agent_id").
		LeftJoin("user_backup_accounts u_uba", "u.id = u_uba.user_id AND u_uba.is_master = 1").
		WhereNull("o.deleted_at").
		Where("o.order_id", orderId).
		Fields(
			// 订单基础字段
			"o.order_id",
			"o.order_sn",
			"o.user_id",
			"o.product_id",
			"o.base_token",
			"o.quote_token",
			"o.symbol",
			"o.trade_type",
			"o.amount_base",
			"o.amount_quote",
			"o.original_from_amount",
			"o.original_to_amount",
			"o.price",
			"o.spread_amount",
			"o.spread_rate",
			"o.fee_amount",
			"o.fee_token_id",
			"o.output_amount_before_fee",
			"o.output_amount_after_fee",
			"o.fee_calculation_method",
			"o.transaction_hash",
			"o.sender_transaction_id",
			"o.receiver_transaction_id",
			"o.quote_id",
			"o.status",
			"o.error_message",
			"o.client_order_id",
			"o.created_at",
			"o.executed_at",
			"o.completed_at",
			"o.updated_at",
			// 用户账号
			"u.account as account",
			// 三级代理信息
			"u_first_agent.username as first_agent_name",
			"u_second_agent.username as second_agent_name",
			"u_third_agent.username as third_agent_name",
			// Telegram信息
			"u_uba.telegram_id",
			"u_uba.telegram_username",
			"u_uba.first_name",
		).
		Scan(&order)
	
	if err != nil {
		return nil, gerror.Wrap(err, "查询兑换订单详情失败")
	}
	
	if order.OrderId == 0 {
		return nil, gerror.New("兑换订单不存在")
	}
	
	return &order, nil
}
