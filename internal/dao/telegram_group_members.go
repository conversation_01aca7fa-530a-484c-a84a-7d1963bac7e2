// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// telegramGroupMembersDao is the data access object for the table telegram_group_members.
// You can define custom methods on it to extend its functionality as needed.
type telegramGroupMembersDao struct {
	*internal.TelegramGroupMembersDao
}

var (
	// TelegramGroupMembers is a globally accessible object for table telegram_group_members operations.
	TelegramGroupMembers = telegramGroupMembersDao{internal.NewTelegramGroupMembersDao()}
)

// Add your custom methods and functionality below.
