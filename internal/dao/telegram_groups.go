// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// telegramGroupsDao is the data access object for the table telegram_groups.
// You can define custom methods on it to extend its functionality as needed.
type telegramGroupsDao struct {
	*internal.TelegramGroupsDao
}

var (
	// TelegramGroups is a globally accessible object for table telegram_groups operations.
	TelegramGroups = telegramGroupsDao{internal.NewTelegramGroupsDao()}
)

// Add your custom methods and functionality below.
