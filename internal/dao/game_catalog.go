// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameCatalogDao is the data access object for the table game_catalog.
// You can define custom methods on it to extend its functionality as needed.
type gameCatalogDao struct {
	*internal.GameCatalogDao
}

var (
	// GameCatalog is a globally accessible object for table game_catalog operations.
	GameCatalog = gameCatalogDao{internal.NewGameCatalogDao()}
)

// Add your custom methods and functionality below.
