// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameBetDetailsApiCallsDao is the data access object for the table game_bet_details_api_calls.
// You can define custom methods on it to extend its functionality as needed.
type gameBetDetailsApiCallsDao struct {
	*internal.GameBetDetailsApiCallsDao
}

var (
	// GameBetDetailsApiCalls is a globally accessible object for table game_bet_details_api_calls operations.
	GameBetDetailsApiCalls = gameBetDetailsApiCallsDao{internal.NewGameBetDetailsApiCallsDao()}
)

// Add your custom methods and functionality below.
