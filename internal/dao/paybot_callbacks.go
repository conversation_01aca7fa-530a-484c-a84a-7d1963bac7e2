// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// paybotCallbacksDao is the data access object for the table paybot_callbacks.
// You can define custom methods on it to extend its functionality as needed.
type paybotCallbacksDao struct {
	*internal.PaybotCallbacksDao
}

var (
	// PaybotCallbacks is a globally accessible object for table paybot_callbacks operations.
	PaybotCallbacks = paybotCallbacksDao{internal.NewPaybotCallbacksDao()}
)

// Add your custom methods and functionality below.
