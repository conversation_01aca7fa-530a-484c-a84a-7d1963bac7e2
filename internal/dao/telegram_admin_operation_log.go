// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// telegramAdminOperationLogDao is the data access object for the table telegram_admin_operation_log.
// You can define custom methods on it to extend its functionality as needed.
type telegramAdminOperationLogDao struct {
	*internal.TelegramAdminOperationLogDao
}

var (
	// TelegramAdminOperationLog is a globally accessible object for table telegram_admin_operation_log operations.
	TelegramAdminOperationLog = telegramAdminOperationLogDao{internal.NewTelegramAdminOperationLogDao()}
)

// Add your custom methods and functionality below.
