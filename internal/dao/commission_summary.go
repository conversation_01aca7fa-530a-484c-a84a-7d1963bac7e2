// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// commissionSummaryDao is the data access object for the table commission_summary.
// You can define custom methods on it to extend its functionality as needed.
type commissionSummaryDao struct {
	*internal.CommissionSummaryDao
}

var (
	// CommissionSummary is a globally accessible object for table commission_summary operations.
	CommissionSummary = commissionSummaryDao{internal.NewCommissionSummaryDao()}
)

// Add your custom methods and functionality below.
