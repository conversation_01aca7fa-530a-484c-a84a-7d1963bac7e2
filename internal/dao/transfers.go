// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// transfersDao is the data access object for the table transfers.
// You can define custom methods on it to extend its functionality as needed.
type transfersDao struct {
	*internal.TransfersDao
}

var (
	// Transfers is a globally accessible object for table transfers operations.
	Transfers = transfersDao{internal.NewTransfersDao()}
)

// ListAdminTransfers
func (d *transfersDao) ListAdminTransfers(ctx context.Context, page, pageSize int, condition g.Map) (list []*model.TransferAdminInfo, total int, err error) {
	//
	m := d.Ctx(ctx)

	// -
	m = m.As("t")
	m = m.LeftJoin("users sender", "t.sender_user_id = sender.id")
	m = m.LeftJoin("users receiver", "t.receiver_user_id = receiver.id")
	m = m.LeftJoin("tokens token", "t.token_id = token.token_id")

	//
	fields := []string{
		"t.transfer_id",
		"t.sender_user_id",
		"t.sender_username",
		"sender.account as sender_account",
		"t.receiver_user_id",
		"t.receiver_username",
		"receiver.account as receiver_account",
		"t.token_id",
		"token.symbol as token_symbol", // This is from tokens table
		"token.decimals as token_decimals",
		"t.amount",
		"t.memo",
		"t.sender_transaction_id",
		"t.receiver_transaction_id",
		"t.created_at",
		"t.message_id",
		"t.chat_id",
		"t.status",
		"t.hold_id",
		"t.expires_at",
		"t.updated_at",
		"t.need_pass",
		"t.key",
		"t.symbol", // This is from transfers table itself
		"t.message",
		"t.inline_message_id",
	}
	m = m.Fields(fields)

	//
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	//
	//
	countModel := d.Ctx(ctx)
	countModel = countModel.As("t")
	countModel = countModel.LeftJoin("users sender", "t.sender_user_id = sender.id")
	countModel = countModel.LeftJoin("users receiver", "t.receiver_user_id = receiver.id")
	countModel = countModel.LeftJoin("tokens token", "t.token_id = token.token_id")

	//
	if len(condition) > 0 {
		countModel = countModel.Where(condition)
	}

	total, err = countModel.Count("1")
	if err != nil {
		return nil, 0, err
	}

	//
	if total == 0 {
		return make([]*model.TransferAdminInfo, 0), 0, nil
	}

	//
	m = m.Page(page, pageSize)

	//
	m = m.Order("t.created_at DESC")

	//
	list = make([]*model.TransferAdminInfo, 0)
	err = m.Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// GetAdminTransferDetail
func (d *transfersDao) GetAdminTransferDetail(ctx context.Context, transferId int64) (*model.TransferAdminInfo, error) {
	//
	m := d.Ctx(ctx)

	// -
	m = m.As("t")
	m = m.LeftJoin("users sender", "t.sender_user_id = sender.id")
	m = m.LeftJoin("users receiver", "t.receiver_user_id = receiver.id")
	m = m.LeftJoin("tokens token", "t.token_id = token.token_id")

	//
	fields := []string{
		"t.transfer_id",
		"t.sender_user_id",
		"sender.account as sender_username",
		"t.receiver_user_id",
		"receiver.account as receiver_username",
		"t.token_id",
		"token.symbol as token_symbol", // This is from tokens table
		"token.decimals as token_decimals",
		"t.amount",
		"t.memo",
		"t.sender_transaction_id",
		"t.receiver_transaction_id",
		"t.created_at",
		"t.message_id",
		"t.chat_id",
		"t.status",
		"t.hold_id",
		"t.expires_at",
		"t.updated_at",
		"t.need_pass",
		"t.key",
		"t.symbol", // This is from transfers table itself
		"t.message",
		"t.inline_message_id",
	}
	m = m.Fields(fields)

	//
	m = m.Where("t.transfer_id", transferId)

	//
	var transfer *model.TransferAdminInfo
	err := m.Scan(&transfer)
	if err != nil {
		return nil, err
	}

	return transfer, nil
}

// ListAdminTransfersWithAgentInfo 查询后台转账记录列表 (含用户信息、代币信息、代理和telegram信息)
func (d *transfersDao) ListAdminTransfersWithAgentInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.TransferAdminInfo, total int, err error) {
	// 使用我们定义的 TransferAdminInfo 模型来接收结果
	list = make([]*model.TransferAdminInfo, 0)

	// 构建基础查询，并指定别名以防字段冲突
	m := d.Ctx(ctx).As("t").
		LeftJoin("users sender", "t.sender_user_id = sender.id").
		LeftJoin("users receiver", "t.receiver_user_id = receiver.id").
		LeftJoin("tokens token", "t.token_id = token.token_id")
	
	// 添加发送方用户的代理和telegram表的关联查询
	// 发送方代理关联
	m = m.LeftJoin("agents sender_first_agent", "sender.first_id = sender_first_agent.agent_id")
	m = m.LeftJoin("agents sender_second_agent", "sender.second_id = sender_second_agent.agent_id") 
	m = m.LeftJoin("agents sender_third_agent", "sender.third_id = sender_third_agent.agent_id")
	// 发送方telegram信息关联
	m = m.LeftJoin("user_backup_accounts sender_uba", "sender.id = sender_uba.user_id AND sender_uba.is_master = 1")
	
	// 添加接收方用户的代理和telegram表的关联查询
	// 接收方代理关联
	m = m.LeftJoin("agents receiver_first_agent", "receiver.first_id = receiver_first_agent.agent_id")
	m = m.LeftJoin("agents receiver_second_agent", "receiver.second_id = receiver_second_agent.agent_id")
	m = m.LeftJoin("agents receiver_third_agent", "receiver.third_id = receiver_third_agent.agent_id")
	// 接收方telegram信息关联
	m = m.LeftJoin("user_backup_accounts receiver_uba", "receiver.id = receiver_uba.user_id AND receiver_uba.is_master = 1")

	// 应用过滤条件
	query := m
	
	// 为了避免SQL参数顺序混乱，分别处理不同类型的条件
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})
	
	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			// 提取字段名并存储BETWEEN条件
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE") {
			// 提取字段名（去掉 " LIKE" 部分）
			fieldName := gstr.Replace(key, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else {
			exactConditions[key] = value
		}
	}
	
	// 按顺序应用条件，确保SQL参数正确对应
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}
	
	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}
	
	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数 (避免影响后续分页查询)
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询转账记录总数失败")
	}

	if total == 0 {
		return list, 0, nil // 没有数据，直接返回空列表和0总数
	}

	// 应用分页和排序，并指定查询字段
	// 明确指定所有需要的字段，并使用别名避免冲突
	baseFields := []string{
		"t.transfer_id",
		"t.sender_user_id",
		"t.sender_username",
		"sender.account as sender_account",
		"t.receiver_user_id",
		"t.receiver_username", 
		"receiver.account as receiver_account",
		"t.token_id",
		"token.symbol as token_symbol",
		"token.decimals as token_decimals",
		"t.amount",
		"t.memo",
		"t.sender_transaction_id",
		"t.receiver_transaction_id",
		"t.created_at",
		"t.message_id",
		"t.chat_id",
		"t.status",
		"t.hold_id",
		"t.expires_at",
		"t.updated_at",
		"t.need_pass",
		"t.key",
		"t.symbol as transfer_symbol",
		"t.message",
		"t.inline_message_id",
	}
	
	// 添加发送方代理和telegram字段
	senderAgentAndTelegramFields := []string{
		"sender_first_agent.username as sender_first_agent_name",
		"sender_second_agent.username as sender_second_agent_name", 
		"sender_third_agent.username as sender_third_agent_name",
		"sender_uba.telegram_id as sender_telegram_id",
		"sender_uba.telegram_username as sender_telegram_username",
		"sender_uba.first_name as sender_first_name",
	}
	
	// 添加接收方代理和telegram字段
	receiverAgentAndTelegramFields := []string{
		"receiver_first_agent.username as receiver_first_agent_name",
		"receiver_second_agent.username as receiver_second_agent_name",
		"receiver_third_agent.username as receiver_third_agent_name",
		"receiver_uba.telegram_id as receiver_telegram_id",
		"receiver_uba.telegram_username as receiver_telegram_username",
		"receiver_uba.first_name as receiver_first_name",
	}
	
	allFields := append(baseFields, senderAgentAndTelegramFields...)
	allFields = append(allFields, receiverAgentAndTelegramFields...)
	
	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}
	
	err = query.Page(page, pageSize).Order("t.created_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询转账记录列表失败")
	}

	return list, total, nil
}

// Add your custom methods and functionality below.
