// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameCommissionRecordsDao is the data access object for the table game_commission_records.
// You can define custom methods on it to extend its functionality as needed.
type gameCommissionRecordsDao struct {
	*internal.GameCommissionRecordsDao
}

var (
	// GameCommissionRecords is a globally accessible object for table game_commission_records operations.
	GameCommissionRecords = gameCommissionRecordsDao{internal.NewGameCommissionRecordsDao()}
)

// Add your custom methods and functionality below.
