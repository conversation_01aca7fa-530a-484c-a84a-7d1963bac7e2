// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// paybotAuthOrdersDao is the data access object for the table paybot_auth_orders.
// You can define custom methods on it to extend its functionality as needed.
type paybotAuthOrdersDao struct {
	*internal.PaybotAuthOrdersDao
}

var (
	// PaybotAuthOrders is a globally accessible object for table paybot_auth_orders operations.
	PaybotAuthOrders = paybotAuthOrdersDao{internal.NewPaybotAuthOrdersDao()}
)

// Add your custom methods and functionality below.
