// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// userWithdrawsDao is the data access object for the table user_withdraws.
// You can define custom methods on it to extend its functionality as needed.
type userWithdrawsDao struct {
	*internal.UserWithdrawsDao
}

var (
	// UserWithdraws is a globally accessible object for table user_withdraws operations.
	UserWithdraws = userWithdrawsDao{internal.NewUserWithdrawsDao()}
)

// Add your custom methods and functionality below.
