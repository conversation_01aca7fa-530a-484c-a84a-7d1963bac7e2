// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"admin-api/internal/model/entity"
	"context"
	"database/sql"
	"errors"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
)

// paymentRequestsDao is the data access object for the table payment_requests.
// You can define custom methods on it to extend its functionality as needed.
type paymentRequestsDao struct {
	*internal.PaymentRequestsDao
}

var (
	// PaymentRequests is a globally accessible object for table payment_requests operations.
	PaymentRequests = paymentRequestsDao{internal.NewPaymentRequestsDao()}
)

// Add your custom methods and functionality below.


// GetPaymentRequestById 根据ID获取收款请求详情
func (d *paymentRequestsDao) GetPaymentRequestById(ctx context.Context, requestId int64) (*entity.PaymentRequests, error) {
	var paymentRequest *entity.PaymentRequests
	err := d.Ctx(ctx).
		Where(d.Columns().RequestId, requestId).
		WhereNull(d.Columns().DeletedAt). // 排除已删除的记录
		Scan(&paymentRequest)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 未找到记录返回nil
		}
		return nil, gerror.Wrap(err, "查询收款请求详情失败")
	}

	return paymentRequest, nil
}

// UpdatePaymentRequestStatus 更新收款请求状态
func (d *paymentRequestsDao) UpdatePaymentRequestStatus(ctx context.Context, requestId int64, status int) error {
	_, err := d.Ctx(ctx).
		Data(g.Map{
			d.Columns().Status: status,
			"updated_at":       gtime.Now(),
		}).
		Where(d.Columns().RequestId, requestId).
		WhereNull(d.Columns().DeletedAt). // 排除已删除的记录
		Update()

	if err != nil {
		return gerror.Wrap(err, "更新收款请求状态失败")
	}

	return nil
}

// GetPaymentRequestWithDetails 获取收款请求详情，并关联用户、代币和可能的交易信息
func (d *paymentRequestsDao) GetPaymentRequestWithDetails(ctx context.Context, requestId int64) (map[string]interface{}, error) {
	// 第一步：获取收款请求记录
	var paymentRequest *entity.PaymentRequests
	err := g.DB().Model("payment_requests").
		Where("request_id", requestId).
		WhereNull("deleted_at").
		Scan(&paymentRequest)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 未找到记录返回nil
		}
		return nil, gerror.Wrap(err, "查询收款请求详情失败")
	}

	if paymentRequest == nil {
		return nil, nil // 未找到记录
	}

	// 第二步：查询关联信息
	// 查询发起者用户信息
	var requester *entity.Users
	if paymentRequest.RequesterUserId > 0 {
		err = g.DB().Model("users").
			Where("id", paymentRequest.RequesterUserId).
			WhereNull("deleted_at").
			Scan(&requester)

		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			g.Log().Warningf(ctx, "查询发起者用户信息失败: %v", err)
		}
	}

	// 查询付款人用户信息
	var payer *entity.Users
	if paymentRequest.PayerUserId > 0 {
		err = g.DB().Model("users").
			Where("id", paymentRequest.PayerUserId).
			WhereNull("deleted_at").
			Scan(&payer)

		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			g.Log().Warningf(ctx, "查询付款人用户信息失败: %v", err)
		}
	}

	// 查询代币信息
	var token *entity.Tokens
	if paymentRequest.TokenId > 0 {
		err = g.DB().Model("tokens").
			Where("token_id", paymentRequest.TokenId).
			WhereNull("deleted_at").
			Scan(&token)

		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			g.Log().Warningf(ctx, "查询代币信息失败: %v", err)
		}
	}

	// 查询交易信息
	var transaction *entity.Transactions
	if paymentRequest.Status == 2 && paymentRequest.PaymentTransactionId > 0 {
		err = g.DB().Model("transactions").
			Where("transaction_id", paymentRequest.PaymentTransactionId).
			WhereNull("deleted_at").
			Scan(&transaction)

		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			g.Log().Warningf(ctx, "查询交易信息失败: %v", err)
		}
	}

	// 第三步：组装结果
	result := make(map[string]interface{})

	// 添加主表字段
	result["request_id"] = paymentRequest.RequestId
	result["requester_user_id"] = paymentRequest.RequesterUserId
	result["requester_username"] = paymentRequest.RequesterUsername
	result["payer_user_id"] = paymentRequest.PayerUserId
	result["payer_username"] = paymentRequest.PayerUsername
	result["token_id"] = paymentRequest.TokenId
	result["amount"] = paymentRequest.Amount
	result["memo"] = paymentRequest.Memo
	result["status"] = paymentRequest.Status
	result["payment_transaction_id"] = paymentRequest.PaymentTransactionId
	result["telegram_chat_id"] = paymentRequest.TelegramChatId
	result["telegram_message_id"] = paymentRequest.TelegramMessageId
	result["created_at"] = paymentRequest.CreatedAt
	result["expires_at"] = paymentRequest.ExpiresAt
	result["paid_at"] = paymentRequest.PaidAt
	result["cancelled_at"] = paymentRequest.CancelledAt
	result["updated_at"] = paymentRequest.UpdatedAt
	result["deleted_at"] = paymentRequest.DeletedAt
	result["symbol"] = paymentRequest.Symbol

	// 添加关联字段
	// 发起者信息
	if requester != nil {
		result["requester_nickname"] = requester.Account
		result["requester_account"] = requester.Account
	} else {
		result["requester_nickname"] = ""
		result["requester_account"] = ""
	}

	// 付款人信息
	if payer != nil {
		result["payer_nickname"] = payer.Account
		result["payer_account"] = payer.Account
	} else {
		result["payer_nickname"] = ""
		result["payer_account"] = ""
	}

	// 代币信息
	if token != nil {
		result["token_symbol"] = token.Symbol
		result["token_name"] = token.Name
	} else {
		result["token_symbol"] = paymentRequest.Symbol // 使用主表中的symbol字段
		result["token_name"] = ""
	}

	// 交易信息
	if transaction != nil {
		result["transaction_id"] = transaction.TransactionId
		result["transaction_status"] = transaction.Status
		result["transaction_time"] = transaction.CreatedAt
	} else {
		result["transaction_id"] = nil
		result["transaction_status"] = nil
		result["transaction_time"] = nil
	}

	g.Log().Infof(ctx, "GetPaymentRequestWithDetails 结果: %+v", result)

	return result, nil
}

// ListAdminPaymentRequestsWithFullInfo 查询后台收款请求列表 (含用户信息、代币信息、代理和telegram信息)
func (d *paymentRequestsDao) ListAdminPaymentRequestsWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.PaymentRequestAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.PaymentRequestAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := d.Ctx(ctx).As("pr").
		LeftJoin("users requester", "pr.requester_user_id = requester.id").
		LeftJoin("users payer", "pr.payer_user_id = payer.id").
		LeftJoin("tokens token", "pr.token_id = token.token_id")
	
	// 添加发起者的代理和telegram表的关联查询
	// 发起者代理关联
	m = m.LeftJoin("agents requester_first_agent", "requester.first_id = requester_first_agent.agent_id")
	m = m.LeftJoin("agents requester_second_agent", "requester.second_id = requester_second_agent.agent_id") 
	m = m.LeftJoin("agents requester_third_agent", "requester.third_id = requester_third_agent.agent_id")
	// 发起者telegram信息关联
	m = m.LeftJoin("user_backup_accounts requester_uba", "requester.id = requester_uba.user_id AND requester_uba.is_master = 1")
	
	// 添加付款人的代理和telegram表的关联查询
	// 付款人代理关联
	m = m.LeftJoin("agents payer_first_agent", "payer.first_id = payer_first_agent.agent_id")
	m = m.LeftJoin("agents payer_second_agent", "payer.second_id = payer_second_agent.agent_id")
	m = m.LeftJoin("agents payer_third_agent", "payer.third_id = payer_third_agent.agent_id")
	// 付款人telegram信息关联
	m = m.LeftJoin("user_backup_accounts payer_uba", "payer.id = payer_uba.user_id AND payer_uba.is_master = 1")

	// 添加软删除条件
	m = m.WhereNull("pr.deleted_at")
	
	// 处理LEFT JOIN表的软删除 - 需要检查NULL或未删除
	// 发起者用户必须存在且未删除
	m = m.WhereNull("requester.deleted_at")
	// 付款人可能不存在（未支付的情况），需要特殊处理
	m = m.Where("(payer.id IS NULL OR payer.deleted_at IS NULL)")
	// 代币表通常都有记录，但为了安全也处理
	m = m.Where("(token.token_id IS NULL OR token.deleted_at IS NULL)")
	// 发起者代理表可能不存在记录，需要特殊处理
	m = m.Where("(requester_first_agent.agent_id IS NULL OR requester_first_agent.deleted_at IS NULL)")
	m = m.Where("(requester_second_agent.agent_id IS NULL OR requester_second_agent.deleted_at IS NULL)")
	m = m.Where("(requester_third_agent.agent_id IS NULL OR requester_third_agent.deleted_at IS NULL)")
	// 发起者备份账户表可能不存在记录，需要特殊处理
	m = m.Where("(requester_uba.user_id IS NULL OR requester_uba.deleted_at IS NULL)")
	// 付款人代理表可能不存在记录，需要特殊处理
	m = m.Where("(payer_first_agent.agent_id IS NULL OR payer_first_agent.deleted_at IS NULL)")
	m = m.Where("(payer_second_agent.agent_id IS NULL OR payer_second_agent.deleted_at IS NULL)")
	m = m.Where("(payer_third_agent.agent_id IS NULL OR payer_third_agent.deleted_at IS NULL)")
	// 付款人备份账户表可能不存在记录，需要特殊处理
	m = m.Where("(payer_uba.user_id IS NULL OR payer_uba.deleted_at IS NULL)")

	// 应用过滤条件
	query := m
	
	// 为了避免SQL参数顺序混乱，分别处理不同类型的条件
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})
	
	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			// 提取字段名并存储BETWEEN条件
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			// 提取字段名
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			// 处理大于等于条件
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			// 处理小于等于条件
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}
	
	// 按顺序应用条件
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}
	
	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}
	
	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询收款请求总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 应用分页和排序，并指定查询字段
	// 基础字段
	baseFields := []string{
		"pr.request_id",
		"pr.requester_user_id",
		"pr.requester_username",
		"requester.account as requester_account",
		"pr.payer_user_id",
		"pr.payer_username", 
		"payer.account as payer_account",
		"pr.token_id",
		"COALESCE(token.symbol, pr.symbol) as token_symbol",
		"token.name as token_name",
		"pr.amount",
		"pr.memo",
		"pr.status",
		"pr.created_at",
		"pr.expires_at",
	}
	
	// 添加发起者代理和telegram字段
	requesterAgentAndTelegramFields := []string{
		"requester_first_agent.username as requester_first_agent_name",
		"requester_second_agent.username as requester_second_agent_name", 
		"requester_third_agent.username as requester_third_agent_name",
		"requester_uba.telegram_id as requester_telegram_id",
		"requester_uba.telegram_username as requester_telegram_username",
		"requester_uba.first_name as requester_first_name",
	}
	
	// 添加付款人代理和telegram字段
	payerAgentAndTelegramFields := []string{
		"payer_first_agent.username as payer_first_agent_name",
		"payer_second_agent.username as payer_second_agent_name",
		"payer_third_agent.username as payer_third_agent_name",
		"payer_uba.telegram_id as payer_telegram_id",
		"payer_uba.telegram_username as payer_telegram_username",
		"payer_uba.first_name as payer_first_name",
	}
	
	allFields := append(baseFields, requesterAgentAndTelegramFields...)
	allFields = append(allFields, payerAgentAndTelegramFields...)
	
	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}
	
	err = query.Page(page, pageSize).Order("pr.request_id DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询收款请求列表失败")
	}

	return list, total, nil
}

