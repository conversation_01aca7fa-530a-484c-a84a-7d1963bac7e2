package dao

import (
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// tokensDao is the data access object for table tokens.
// You can define custom methods on it to extend its functionality as you wish.
type tokensDao struct {
	*internal.TokensDao
}

var (
	// Tokens is globally public accessible object for table tokens operations.
	Tokens = tokensDao{
		internal.NewTokensDao(),
	}
)

// GetTokenList 获取代币列表 (包含软删除过滤)
func (d *tokensDao) GetTokenList(ctx context.Context, page, pageSize int, condition g.Map, orderBy, orderDirection string) (list []*entity.Tokens, total int, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt) // 强制过滤软删除

	// 查询总数
	total, err = m.Count()
	if err != nil || total == 0 {
		// 如果查询总数出错或总数为0，直接返回空列表和错误（如果有）
		// 修正：即使 total 为 0 也应该返回空列表而不是 nil, err
		if err != nil {
			return nil, 0, gerror.Wrap(err, "查询代币总数失败")
		}
		return make([]*entity.Tokens, 0), 0, nil // 返回空列表和 0 总数
	}

	// 查询数据
	list = make([]*entity.Tokens, 0)
	query := m.Page(page, pageSize)

	// 应用排序
	if orderBy != "" && orderDirection != "" {
		// 简单校验防止SQL注入 (虽然gf框架有处理，但多一层保险)
		validOrderBy := false
		columns := d.Columns()
		// 通过反射或手动列出允许排序的字段
		allowedOrderFields := map[string]string{
			"order":     columns.Order,
			"symbol":    columns.Symbol,
			"network":   columns.Network,
			"createdAt": columns.CreatedAt,
			"updatedAt": columns.UpdatedAt,
			"tokenId":   columns.TokenId, // 补充 tokenId 排序
			// 添加其他允许排序的字段...
		}
		if colName, ok := allowedOrderFields[orderBy]; ok {
			orderDirection = strings.ToLower(orderDirection)
			if orderDirection == "asc" || orderDirection == "desc" {
				query = query.Order(colName + " " + orderDirection)
				validOrderBy = true
			}
		}
		// 如果没有提供有效的排序字段或方向，可以设置默认排序
		if !validOrderBy {
			query = query.OrderDesc(d.Columns().Order).OrderDesc(d.Columns().TokenId) // 默认排序
		}
	} else {
		query = query.OrderDesc(d.Columns().Order).OrderDesc(d.Columns().TokenId) // 默认排序
	}

	err = query.Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询代币列表失败")
	}

	return list, total, nil
}

// GetTokenByID 根据 ID 获取代币信息 (包含软删除过滤)
func (d *tokensDao) GetTokenByID(ctx context.Context, tokenId uint) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := d.Ctx(ctx).
		Where(d.Columns().TokenId, tokenId).
		WhereNull(d.Columns().DeletedAt). // 强制过滤软删除
		Scan(&token)

	if err != nil {
		if err == sql.ErrNoRows {
			// 使用 gerror.WrapCode 包装错误，并附加业务错误码
			return nil, gerror.WrapCode(codes.CodeTokenNotFound, err, fmt.Sprintf("数据库未找到代币 ID: %d", tokenId))
		}
		// 其他数据库错误，包装为内部错误
		return nil, gerror.Wrap(err, fmt.Sprintf("查询代币失败, ID: %d", tokenId))
	}
	if token == nil { // 双重检查，虽然 Scan 在 ErrNoRows 时通常不会赋值
		return nil, gerror.NewCodef(codes.CodeTokenNotFound, "数据库未找到代币 ID: %d", tokenId)
	}
	return token, nil
}

// CreateToken 创建代币
func (d *tokensDao) CreateToken(ctx context.Context, data *do.Tokens) (lastInsertId int64, err error) {
	// 确保创建时不包含删除标记
	data.DeletedAt = nil
	// 确保创建时设置创建和更新时间
	now := gtime.Now()
	data.CreatedAt = now
	data.UpdatedAt = now

	result, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {

		// 其他错误包装为创建失败
		return 0, gerror.WrapCode(codes.CodeTokenCreateFailed, err, "创建代币数据库操作失败")
	}

	lastInsertId, err = result.LastInsertId()
	if err != nil {
		// 获取 LastInsertId 失败也算创建失败的一种情况
		return 0, gerror.WrapCode(codes.CodeTokenCreateFailed, err, "创建代币后获取 ID 失败")
	}
	return lastInsertId, nil
}

// UpdateToken 更新代币信息 (只更新传入的字段，且过滤软删除)
func (d *tokensDao) UpdateToken(ctx context.Context, tokenId uint, data g.Map) error {
	// 确保更新时不包含删除标记
	delete(data, d.Columns().DeletedAt)
	// 确保不更新主键
	delete(data, d.Columns().TokenId)
	// 确保不更新创建时间
	delete(data, d.Columns().CreatedAt)
	// 自动更新 updated_at
	data[d.Columns().UpdatedAt] = gtime.Now()

	// 如果 data 为空，则不执行更新
	if len(data) <= 1 { // 只包含 updated_at
		return nil
	}

	result, err := d.Ctx(ctx).
		Data(data).
		Where(d.Columns().TokenId, tokenId).
		WhereNull(d.Columns().DeletedAt). // 确保只更新未删除的记录
		Update()

	if err != nil {

		// 其他错误包装为更新失败
		return gerror.WrapCode(codes.CodeTokenUpdateFailed, err, "更新代币数据库操作失败")
	}

	// 检查是否有行受到影响，如果没有，可能记录不存在或已被删除
	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		// 再次检查记录是否存在（可能在更新操作执行期间被删除）
		count, checkErr := d.Ctx(ctx).Where(d.Columns().TokenId, tokenId).WhereNull(d.Columns().DeletedAt).Count()
		if checkErr != nil {
			// 检查存在性时出错，也返回更新失败
			return gerror.WrapCode(codes.CodeTokenUpdateFailed, checkErr, fmt.Sprintf("更新代币后检查存在性失败 (ID: %d)", tokenId))
		}
		if count == 0 {
			return gerror.NewCodef(codes.CodeTokenNotFound, "更新代币失败：代币信息不存在或已被删除 (ID: %d)", tokenId)
		}
		// 如果记录存在但未更新（例如数据未改变），则不视为错误
	}

	return nil
}

// SoftDeleteToken 软删除代币
func (d *tokensDao) SoftDeleteToken(ctx context.Context, tokenId uint) error {
	updateData := g.Map{
		d.Columns().DeletedAt: gtime.Now(),
		d.Columns().UpdatedAt: gtime.Now(), // 同时更新 updated_at
	}
	result, err := d.Ctx(ctx).
		Data(updateData).
		Where(d.Columns().TokenId, tokenId).
		WhereNull(d.Columns().DeletedAt). // 确保只删除未删除的记录
		Update()

	if err != nil {
		return gerror.WrapCode(codes.CodeTokenDeleteFailed, err, "删除代币数据库操作失败")
	}

	// 检查是否有行受到影响
	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		// 再次检查记录是否存在（可能在删除操作执行期间已被删除）
		count, checkErr := d.Ctx(ctx).Where(d.Columns().TokenId, tokenId).Count() // 检查包括已删除的
		if checkErr != nil {
			return gerror.WrapCode(codes.CodeTokenDeleteFailed, checkErr, fmt.Sprintf("删除代币后检查存在性失败 (ID: %d)", tokenId))
		}
		if count == 0 {
			// 记录不存在
			return gerror.NewCodef(codes.CodeTokenNotFound, "删除代币失败：代币信息不存在 (ID: %d)", tokenId)
		}
		// 如果记录存在但已被删除 (DeletedAt is not null)，也返回 Not Found
		token, _ := d.GetTokenByID(ctx, tokenId) // 尝试获取未删除的记录
		if token == nil {
			return gerror.NewCodef(codes.CodeTokenNotFound, "删除代币失败：代币信息已被删除 (ID: %d)", tokenId)
		}
		// 其他情况（例如并发删除），也视为未找到或删除失败
		return gerror.NewCodef(codes.CodeTokenDeleteFailed, "删除代币失败：记录未受影响 (ID: %d)", tokenId)

	}

	return nil
}

// CheckExistence 检查指定字段值是否存在 (排除指定 ID，过滤软删除)
// fieldName: 要检查的字段名 (例如 d.Columns().Symbol 或 d.Columns().ContractAddress)
// value: 要检查的值
// network: 网络标识
// excludeTokenId: 更新时需要排除的当前代币 ID (创建时传 0)
func (d *tokensDao) CheckExistence(ctx context.Context, network, value string, fieldName string, excludeTokenId uint) (bool, error) {
	if value == "" { // 如果检查的值为空字符串，通常不认为存在冲突
		return false, nil
	}

	query := d.Ctx(ctx).
		Where(d.Columns().Network, network).
		Where(fieldName, value).
		WhereNull(d.Columns().DeletedAt) // 强制过滤软删除

	if excludeTokenId > 0 {
		query = query.Where(d.Columns().TokenId, "!=", excludeTokenId)
	}

	count, err := query.Count()
	if err != nil {
		// 包装错误，方便上层判断
		return false, gerror.Wrapf(err, "检查代币存在性失败 (network: %s, field: %s, value: %s)", network, fieldName, value)
	}

	return count > 0, nil
}
