// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameProvidersDao is the data access object for the table game_providers.
// You can define custom methods on it to extend its functionality as needed.
type gameProvidersDao struct {
	*internal.GameProvidersDao
}

var (
	// GameProviders is a globally accessible object for table game_providers operations.
	GameProviders = gameProvidersDao{internal.NewGameProvidersDao()}
)

// Add your custom methods and functionality below.
