// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// luckyRedpacketParticipantsDao is the data access object for the table lucky_redpacket_participants.
// You can define custom methods on it to extend its functionality as needed.
type luckyRedpacketParticipantsDao struct {
	*internal.LuckyRedpacketParticipantsDao
}

var (
	// LuckyRedpacketParticipants is a globally accessible object for table lucky_redpacket_participants operations.
	LuckyRedpacketParticipants = luckyRedpacketParticipantsDao{internal.NewLuckyRedpacketParticipantsDao()}
)

// Add your custom methods and functionality below.
