// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"
	"database/sql"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
)

// adminNoticeReadDao is the data access object for the table admin_notice_read.
// You can define custom methods on it to extend its functionality as needed.
type adminNoticeReadDao struct {
	*internal.AdminNoticeReadDao
}

var (
	// AdminNoticeRead is a globally accessible object for table admin_notice_read operations.
	AdminNoticeRead = adminNoticeReadDao{internal.NewAdminNoticeReadDao()}
)

// GetByNoticeAndMember 根据公告ID和会员ID获取阅读记录
func (d *adminNoticeReadDao) GetByNoticeAndMember(ctx context.Context, noticeId, memberId int64) (*entity.AdminNoticeRead, error) {
	var record *entity.AdminNoticeRead
	err := d.Ctx(ctx).Where(gdb.Map{
		d.Columns().NoticeId: noticeId,
		d.Columns().MemberId: memberId,
	}).Scan(&record)
	if err != nil {
		// ErrNoRows 在这里是正常情况，表示用户尚未阅读
		if err == sql.ErrNoRows {
			return nil, nil // 返回 nil, nil 表示未找到记录
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询公告阅读记录失败")
	}
	return record, nil
}

// Add 新增阅读记录
func (d *adminNoticeReadDao) Add(ctx context.Context, noticeId, memberId int64) error {
	_, err := d.Ctx(ctx).Data(do.AdminNoticeRead{
		NoticeId: noticeId,
		MemberId: memberId,
		Clicks:   1, // 首次阅读，点击次数为 1
		// CreatedAt 和 UpdatedAt 由数据库或框架自动处理
	}).Insert()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "新增公告阅读记录失败")
	}
	return nil
}

// IncrementClicks 增加阅读次数
func (d *adminNoticeReadDao) IncrementClicks(ctx context.Context, id int64) error {
	_, err := d.Ctx(ctx).
		Data(gdb.Map{d.Columns().Clicks: gdb.Raw("clicks + 1"), d.Columns().UpdatedAt: gtime.Now()}).
		Where(d.Columns().Id, id).
		Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "增加公告阅读次数失败")
	}
	return nil
}

// GetReadMemberIdsByNoticeId 根据公告ID获取已读会员ID列表
func (d *adminNoticeReadDao) GetReadMemberIdsByNoticeId(ctx context.Context, noticeId int64) ([]int64, error) {
	var memberIds []int64
	err := d.Ctx(ctx).
		Where(d.Columns().NoticeId, noticeId).
		Fields(d.Columns().MemberId).
		Scan(&memberIds)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取已读会员ID列表失败")
	}
	return memberIds, nil
}

// GetReadCountByNoticeId 根据公告ID获取已读总人数
func (d *adminNoticeReadDao) GetReadCountByNoticeId(ctx context.Context, noticeId int64) (int, error) {
	count, err := d.Ctx(ctx).Where(d.Columns().NoticeId, noticeId).Count()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取公告已读总数失败")
	}
	return count, nil
}

// GetReadStatusForUser 获取指定用户对指定公告列表的阅读状态
// noticeIds: 需要查询状态的公告ID列表
// 返回 map[noticeId]readRecord，如果未读则 map 中不存在对应 noticeId
func (d *adminNoticeReadDao) GetReadStatusForUser(ctx context.Context, userId int64, noticeIds []int64) (map[int64]*entity.AdminNoticeRead, error) {
	if len(noticeIds) == 0 {
		return map[int64]*entity.AdminNoticeRead{}, nil
	}
	var records []*entity.AdminNoticeRead
	err := d.Ctx(ctx).
		Where(d.Columns().MemberId, userId).
		WhereIn(d.Columns().NoticeId, noticeIds).
		Scan(&records)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询用户公告阅读状态失败")
	}

	resultMap := make(map[int64]*entity.AdminNoticeRead, len(records))
	for _, record := range records {
		resultMap[record.NoticeId] = record
	}
	return resultMap, nil
}
