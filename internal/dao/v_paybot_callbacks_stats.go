// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// vPaybotCallbacksStatsDao is the data access object for the table v_paybot_callbacks_stats.
// You can define custom methods on it to extend its functionality as needed.
type vPaybotCallbacksStatsDao struct {
	*internal.VPaybotCallbacksStatsDao
}

var (
	// VPaybotCallbacksStats is a globally accessible object for table v_paybot_callbacks_stats operations.
	VPaybotCallbacksStats = vPaybotCallbacksStatsDao{internal.NewVPaybotCallbacksStatsDao()}
)

// Add your custom methods and functionality below.
