// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// userAssDao is the data access object for the table user_ass.
// You can define custom methods on it to extend its functionality as needed.
type userAssDao struct {
	*internal.UserAssDao
}

var (
	// UserAss is a globally accessible object for table user_ass operations.
	UserAss = userAssDao{internal.NewUserAssDao()}
)

// Add your custom methods and functionality below.
