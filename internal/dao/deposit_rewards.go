// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// depositRewardsDao is the data access object for the table deposit_rewards.
// You can define custom methods on it to extend its functionality as needed.
type depositRewardsDao struct {
	*internal.DepositRewardsDao
}

var (
	// DepositRewards is a globally accessible object for table deposit_rewards operations.
	DepositRewards = depositRewardsDao{internal.NewDepositRewardsDao()}
)

// Add your custom methods and functionality below.
