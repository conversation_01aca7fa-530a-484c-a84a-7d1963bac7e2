// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameInfoDao is the data access object for the table game_info.
// You can define custom methods on it to extend its functionality as needed.
type gameInfoDao struct {
	*internal.GameInfoDao
}

var (
	// GameInfo is a globally accessible object for table game_info operations.
	GameInfo = gameInfoDao{internal.NewGameInfoDao()}
)

// Add your custom methods and functionality below.
