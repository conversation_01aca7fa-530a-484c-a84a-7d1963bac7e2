// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameRngBetDetailsDao is the data access object for the table game_rng_bet_details.
// You can define custom methods on it to extend its functionality as needed.
type gameRngBetDetailsDao struct {
	*internal.GameRngBetDetailsDao
}

var (
	// GameRngBetDetails is a globally accessible object for table game_rng_bet_details operations.
	GameRngBetDetails = gameRngBetDetailsDao{internal.NewGameRngBetDetailsDao()}
)

// Add your custom methods and functionality below.
