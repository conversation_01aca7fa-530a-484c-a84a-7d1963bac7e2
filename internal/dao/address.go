// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// addressDao is the data access object for the table address.
// You can define custom methods on it to extend its functionality as needed.
type addressDao struct {
	*internal.AddressDao
}

var (
	// Address is a globally accessible object for table address operations.
	Address = addressDao{internal.NewAddressDao()}
)

// Add your custom methods and functionality below.
