// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"time"

	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/golang-jwt/jwt/v4"
)

// personalAccessTokensDao is the data access object for the table personal_access_tokens.
// You can define custom methods on it to extend its functionality as needed.
type personalAccessTokensDao struct {
	*internal.PersonalAccessTokensDao
}

var (
	// PersonalAccessTokens is a globally accessible object for table personal_access_tokens operations.
	PersonalAccessTokens = personalAccessTokensDao{internal.NewPersonalAccessTokensDao()}
)

// GenerateToken 生成访问令牌
func (d *personalAccessTokensDao) GenerateToken(ctx context.Context, userId uint64, username string, expiresIn time.Duration, tokenableType string) (string, int64, error) {

	// Fetch JWT configuration from config file
	jwtSecret := g.Cfg().MustGet(ctx, "jwt.secret", "defaultSecret").String() // Provide a default secret
	jwtExpiryHours := g.Cfg().MustGet(ctx, "jwt.expiryHours", 72).Int64()     // Default to 72 hours

	if jwtSecret == "defaultSecret" {
		g.Log().Warning(ctx, "Using default JWT secret. Please configure 'jwt.secret' in your config file.")
	}

	// Calculate expiration time
	expireDuration := time.Duration(jwtExpiryHours) * time.Hour
	expireTime := gtime.Now().Add(expireDuration)
	expireAt := expireTime.Unix()

	// Create JWT claims
	claims := jwt.MapClaims{
		"uid":      userId,
		"name":     username,
		"orig_iat": gtime.Now().Unix(), // Original issued at time
		"exp":      expireAt,           // Expiration time
		"iss":      "xpayadminapi",     // TODO: Define consts.JwtIssuer in internal/consts package
	}

	// Create token object with claims and signing method
	tokenObject := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign the token with the secret key
	signedToken, err := tokenObject.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", 0, gerror.Wrap(err, "Failed to sign JWT token")
	}

	_, err = d.Ctx(ctx).Data(g.Map{
		PersonalAccessTokens.Columns().TokenableType: tokenableType,
		PersonalAccessTokens.Columns().Token:         signedToken,
		PersonalAccessTokens.Columns().UserId:        userId,
		PersonalAccessTokens.Columns().ExpiresAt:     time.Unix(expireAt, 0),
	}).Save()
	if err != nil {
		return "", 0, gerror.Wrap(err, "Failed to save personal access token")
	}

	return signedToken, expireAt, nil
}

// VerifyToken 验证令牌是否有效
func (d *personalAccessTokensDao) VerifyToken(ctx context.Context, token string, tokenType string) (*entity.PersonalAccessTokens, error) {
	var tokenModel *entity.PersonalAccessTokens
	err := d.Ctx(ctx).Where(d.Columns().Token, token).Where(d.Columns().TokenableType, tokenType).Scan(&tokenModel)
	if err != nil {
		return nil, err
	}

	// 检查令牌是否存在
	if tokenModel == nil || tokenModel.Id == 0 {
		return nil, gerror.New("令牌无效")
	}

	// 检查令牌是否过期
	if tokenModel.ExpiresAt != nil && tokenModel.ExpiresAt.Before(gtime.Now()) {
		return nil, gerror.New("令牌已过期")
	}

	// 更新最后使用时间
	_, err = d.Ctx(ctx).
		Data(g.Map{
			d.Columns().LastUsedAt: gtime.Now(),
			d.Columns().UpdatedAt:  gtime.Now(),
		}).
		Where(d.Columns().Id, tokenModel.Id).
		Update()
	if err != nil {
		g.Log().Error(ctx, "更新令牌使用时间失败", err)
		return nil, gerror.New("更新令牌使用时间失败")
	}

	return tokenModel, nil
}

// GetUserTokens 获取用户的所有有效令牌
func (d *personalAccessTokensDao) GetUserTokens(ctx context.Context, userId uint64) ([]*entity.PersonalAccessTokens, error) {
	var tokens []*entity.PersonalAccessTokens
	err := d.Ctx(ctx).
		Where(d.Columns().UserId, userId).
		Where(d.Columns().ExpiresAt+" > ?", gtime.Now()).
		Scan(&tokens)
	if err != nil {
		return nil, err
	}
	return tokens, nil
}

// RevokeToken 撤销令牌
func (d *personalAccessTokensDao) RevokeToken(ctx context.Context, token string) error {
	_, err := d.Ctx(ctx).
		Where(d.Columns().Token, token).
		Delete()
	return err
}

// RevokeAllUserTokens 撤销用户的所有令牌
func (d *personalAccessTokensDao) RevokeAllUserTokens(ctx context.Context, userId uint64) error {
	_, err := d.Ctx(ctx).
		Where(d.Columns().UserId, userId).
		Delete()
	return err
}

// RefreshToken 刷新令牌有效期
func (d *personalAccessTokensDao) RefreshToken(ctx context.Context, token string, expiresIn time.Duration) error {
	expiresAt := gtime.Now().Add(expiresIn)
	_, err := d.Ctx(ctx).
		Data(g.Map{
			d.Columns().ExpiresAt:  expiresAt,
			d.Columns().UpdatedAt:  gtime.Now(),
			d.Columns().LastUsedAt: gtime.Now(),
		}).
		Where(d.Columns().Token, token).
		Update()
	return err
}
