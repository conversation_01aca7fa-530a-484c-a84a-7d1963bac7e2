// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameSessionsDao is the data access object for the table game_sessions.
// You can define custom methods on it to extend its functionality as needed.
type gameSessionsDao struct {
	*internal.GameSessionsDao
}

var (
	// GameSessions is a globally accessible object for table game_sessions operations.
	GameSessions = gameSessionsDao{internal.NewGameSessionsDao()}
)

// Add your custom methods and functionality below.
