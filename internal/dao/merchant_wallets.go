// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// merchantWalletsDao is the data access object for the table merchant_wallets.
// You can define custom methods on it to extend its functionality as needed.
type merchantWalletsDao struct {
	*internal.MerchantWalletsDao
}

var (
	// MerchantWallets is a globally accessible object for table merchant_wallets operations.
	MerchantWallets = merchantWalletsDao{internal.NewMerchantWalletsDao()}
)

// Add your custom methods and functionality below.
