// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// telegramGroupEventsDao is the data access object for the table telegram_group_events.
// You can define custom methods on it to extend its functionality as needed.
type telegramGroupEventsDao struct {
	*internal.TelegramGroupEventsDao
}

var (
	// TelegramGroupEvents is a globally accessible object for table telegram_group_events operations.
	TelegramGroupEvents = telegramGroupEventsDao{internal.NewTelegramGroupEventsDao()}
)

// Add your custom methods and functionality below.
