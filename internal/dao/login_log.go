// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"

	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
)

// loginLog<PERSON>ao is the data access object for the table login_log.
// You can define custom methods on it to extend its functionality as needed.
type loginLogDao struct {
	*internal.LoginLogDao
}

var (
	// LoginLog is a globally accessible object for table login_log operations.
	LoginLog = loginLogDao{internal.NewLoginLogDao()}
)

// RecordLoginLog
func (d *loginLogDao) RecordLoginLog(ctx context.Context, r *ghttp.Request, memberType string,
	memberId int64, username string, response interface{}, errMsg string, status int) error {
	//
	loginLog := &do.LoginLog{
		ReqId:      r.GetCtxVar("requestId").String(), //
		MemberType: memberType,                        //
		MemberId:   memberId,                          //
		Username:   username,                          //
		LoginAt:    gtime.Now(),                       //
		LoginIp:    r.GetClientIp(),                   //
		UserAgent:  r.GetHeader("User-Agent"),         // UA
		ErrMsg:     errMsg,                            //
		Status:     status,                            // :1,0
		CreatedAt:  gtime.Now(),                       //
		UpdatedAt:  gtime.Now(),                       //
	}

	//
	if response != nil {
		responseJson := gjson.New(response)
		loginLog.Response = responseJson
	}

	//
	_, err := d.Ctx(ctx).Data(loginLog).Insert()
	return err
}

// GetLoginLogList
func (d *loginLogDao) GetLoginLogList(ctx context.Context, page, pageSize int, condition g.Map) (list []*do.LoginLog, total int, err error) {
	//
	m := d.Ctx(ctx).Where(condition)

	//
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	//
	if total == 0 {
		return nil, 0, nil
	}

	//
	list = make([]*do.LoginLog, 0)
	err = m.Page(page, pageSize).Order("id DESC").Scan(&list)
	return list, total, err
}

// GetLoginLogById ID
func (d *loginLogDao) GetLoginLogById(ctx context.Context, id int64) (*do.LoginLog, error) {
	var loginLog *do.LoginLog
	err := d.Ctx(ctx).Where(d.Columns().Id, id).Scan(&loginLog)
	return loginLog, err
}

// ParseTimeRange 解析时间范围
func (d *loginLogDao) ParseTimeRange(dateRange, startTime, endTime string) (string, string) {
	// 确保参数不为nil，处理特殊字符串值
	if dateRange != "" && dateRange != "undefined" && dateRange != "null" {
		// 处理日期范围格式
		timeParts := strings.Split(dateRange, ",")
		if len(timeParts) == 2 {
			startTime = strings.TrimSpace(timeParts[0])
			endTime = strings.TrimSpace(timeParts[1])
		} else if len(timeParts) == 1 && timeParts[0] != "" {
			// 单个日期
			startTime = strings.TrimSpace(timeParts[0])
			endTime = startTime
		}
	}

	// 处理特殊值
	if startTime == "undefined" || startTime == "null" {
		startTime = ""
	}
	if endTime == "undefined" || endTime == "null" {
		endTime = ""
	}

	// 处理结束时间，添加时分秒
	if endTime != "" {
		// 如果只提供了日期，添加时分秒
		if !strings.Contains(endTime, " ") {
			endTime = endTime + " 23:59:59"
		}
	}

	return startTime, endTime
}
