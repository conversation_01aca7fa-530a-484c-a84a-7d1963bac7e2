// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// adminConfigItemsDao is the data access object for the table admin_config_items.
// You can define custom methods on it to extend its functionality as needed.
type adminConfigItemsDao struct {
	*internal.AdminConfigItemsDao
}

var (
	// AdminConfigItems is a globally accessible object for table admin_config_items operations.
	AdminConfigItems = adminConfigItemsDao{internal.NewAdminConfigItemsDao()}
)

// Add your custom methods and functionality below.
