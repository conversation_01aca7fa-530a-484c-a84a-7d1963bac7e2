// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gamePlayerBetSummaryDao is the data access object for the table game_player_bet_summary.
// You can define custom methods on it to extend its functionality as needed.
type gamePlayerBetSummaryDao struct {
	*internal.GamePlayerBetSummaryDao
}

var (
	// GamePlayerBetSummary is a globally accessible object for table game_player_bet_summary operations.
	GamePlayerBetSummary = gamePlayerBetSummaryDao{internal.NewGamePlayerBetSummaryDao()}
)

// Add your custom methods and functionality below.
