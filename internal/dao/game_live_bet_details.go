// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameLiveBetDetailsDao is the data access object for the table game_live_bet_details.
// You can define custom methods on it to extend its functionality as needed.
type gameLiveBetDetailsDao struct {
	*internal.GameLiveBetDetailsDao
}

var (
	// GameLiveBetDetails is a globally accessible object for table game_live_bet_details operations.
	GameLiveBetDetails = gameLiveBetDetailsDao{internal.NewGameLiveBetDetailsDao()}
)

// Add your custom methods and functionality below.
