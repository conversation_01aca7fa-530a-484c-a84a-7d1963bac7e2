// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// adminConfigItemsCopy1Dao is the data access object for the table admin_config_items_copy1.
// You can define custom methods on it to extend its functionality as needed.
type adminConfigItemsCopy1Dao struct {
	*internal.AdminConfigItemsCopy1Dao
}

var (
	// AdminConfigItemsCopy1 is a globally accessible object for table admin_config_items_copy1 operations.
	AdminConfigItemsCopy1 = adminConfigItemsCopy1Dao{internal.NewAdminConfigItemsCopy1Dao()}
)

// Add your custom methods and functionality below.
