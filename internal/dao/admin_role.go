// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"
	"database/sql"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// adminRoleDao is the data access object for the table admin_role.
// You can define custom methods on it to extend its functionality as needed.
type adminRoleDao struct {
	*internal.AdminRoleDao
}

var (
	// AdminRole is a globally accessible object for table admin_role operations.
	AdminRole = adminRoleDao{internal.NewAdminRoleDao()}
)

// GetRoleList 获取角色列表（分页）
func (d *adminRoleDao) GetRoleList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminRole, total int, err error) {
	m := d.Ctx(ctx)

	// 构建查询条件
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	// 查询总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询角色总数失败")
	}
	if total == 0 {
		return []*entity.AdminRole{}, 0, nil
	}

	// 查询列表数据
	// 按 Sort 升序，然后按 ID 升序
	err = m.Page(page, pageSize).OrderAsc(d.Columns().Sort).OrderAsc(d.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询角色列表失败")
	}

	return list, total, nil
}

// GetRoleById 根据ID获取角色信息
func (d *adminRoleDao) GetRoleById(ctx context.Context, id int64) (*entity.AdminRole, error) {
	var role *entity.AdminRole
	err := d.Ctx(ctx).Where(d.Columns().Id, id).Scan(&role)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeRoleNotFound)
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "数据库查询失败")
	}
	if role == nil {
		return nil, gerror.NewCode(codes.CodeRoleNotFound)
	}
	return role, nil
}

// AddRole 新增角色
func (d *adminRoleDao) AddRole(ctx context.Context, data *do.AdminRole) (lastInsertId int64, err error) {
	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "新增角色失败")
	}
	lastInsertId, err = res.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新增角色ID失败")
	}
	return lastInsertId, nil
}

// UpdateRole 更新角色信息 (不含权限相关字段)
func (d *adminRoleDao) UpdateRole(ctx context.Context, data g.Map, id int64) (err error) {
	// 确保不更新权限相关字段，这些字段通过专门的接口更新
	delete(data, d.Columns().DataScope)
	delete(data, d.Columns().CustomDept)
	delete(data, d.Columns().Pid) // Pid, Level, Tree 通常不在此处更新，除非有明确需求
	delete(data, d.Columns().Level)
	delete(data, d.Columns().Tree)

	if len(data) == 0 {
		return nil // 没有需要更新的字段
	}

	_, err = d.Ctx(ctx).Data(data).Where(d.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "更新角色信息失败")
	}
	return nil
}

// DeleteRoleByIds 根据ID列表删除角色 (物理删除)
func (d *adminRoleDao) DeleteRoleByIds(ctx context.Context, ids []int64) (err error) {
	if len(ids) == 0 {
		return nil
	}
	_, err = d.Ctx(ctx).WhereIn(d.Columns().Id, ids).Delete()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "删除角色失败")
	}
	return nil
}

// CheckKeyExists 检查角色权限标识是否存在（全局检查，可排除指定ID）
func (d *adminRoleDao) CheckKeyExists(ctx context.Context, key string, excludeId ...int64) (bool, error) {
	query := d.Ctx(ctx).Where(d.Columns().Key, key)
	if len(excludeId) > 0 {
		query = query.WhereNotIn(d.Columns().Id, excludeId)
	}
	count, err := query.Count()
	if err != nil {
		return false, gerror.WrapCode(codes.CodeInternalError, err, "检查角色权限标识是否存在失败")
	}
	return count > 0, nil
}

// CheckNameExists 检查角色名称是否存在（全局检查，可排除指定ID） - 可选
func (d *adminRoleDao) CheckNameExists(ctx context.Context, name string, excludeId ...int64) (bool, error) {
	query := d.Ctx(ctx).Where(d.Columns().Name, name)
	if len(excludeId) > 0 {
		query = query.WhereNotIn(d.Columns().Id, excludeId)
	}
	count, err := query.Count()
	if err != nil {
		return false, gerror.WrapCode(codes.CodeInternalError, err, "检查角色名称是否存在失败")
	}
	return count > 0, nil
}

// UpdateRoleDataScope 更新角色数据范围
func (d *adminRoleDao) UpdateRoleDataScope(ctx context.Context, id int64, dataScope int, customDeptJson []byte) error {
	updateData := g.Map{
		d.Columns().DataScope: dataScope,
	}
	// 只有当 dataScope 为自定义时，才更新 custom_dept
	if dataScope == 5 { // 假设 5 是自定义数据范围的常量值
		updateData[d.Columns().CustomDept] = customDeptJson // 直接传递 JSON 字节
	} else {
		// 如果不是自定义，清空 custom_dept 字段
		updateData[d.Columns().CustomDept] = nil
	}

	_, err := d.Ctx(ctx).Data(updateData).Where(d.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "更新角色数据范围失败")
	}
	return nil
}

// GetRoleMenuIds 获取角色关联的菜单ID列表
func (d *adminRoleDao) GetRoleMenuIds(ctx context.Context, roleId int64) ([]int64, error) {
	var menuIds []int64
	// err := AdminRoleMenu.Ctx(ctx). // 直接使用 dao.AdminRoleMenu
	// 				Where(AdminRoleMenu.Columns().RoleId, roleId).
	// 				Fields(AdminRoleMenu.Columns().MenuId).
	// 				Scan(&menuIds)
	// if err != nil {
	// 	return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询角色菜单关联失败")
	// }
	return menuIds, nil
}

// DeleteRoleMenusByRoleId 删除指定角色的所有菜单关联
func (d *adminRoleDao) DeleteRoleMenusByRoleId(ctx context.Context, tx gdb.TX, roleId int64) error {
	// _, err := tx.Model(AdminRoleMenu.Table()).Where(AdminRoleMenu.Columns().RoleId, roleId).Delete()
	// if err != nil {
	// 	return gerror.WrapCode(codes.CodeInternalError, err, "删除角色菜单关联失败")
	// }
	return nil
}

// BatchAddRoleMenus 批量添加角色菜单关联
func (d *adminRoleDao) BatchAddRoleMenus(ctx context.Context, tx gdb.TX, roleId int64, menuIds []int64) error {
	if len(menuIds) == 0 {
		return nil
	}
	// data := make([]g.Map, len(menuIds))
	// for i, menuId := range menuIds {
	// 	data[i] = g.Map{
	// 		AdminRoleMenu.Columns().RoleId: roleId,
	// 		AdminRoleMenu.Columns().MenuId: menuId,
	// 	}
	// }
	// _, err := tx.Model(AdminRoleMenu.Table()).Data(data).Insert()
	// if err != nil {
	// 	return gerror.WrapCode(codes.CodeInternalError, err, "批量添加角色菜单关联失败")
	// }
	return nil
}

// DeleteMemberRolesByRoleIds 删除指定角色的所有用户关联
func (d *adminRoleDao) DeleteMemberRolesByRoleIds(ctx context.Context, tx gdb.TX, roleIds []int64) error {
	// if len(roleIds) == 0 {
	// 	return nil
	// }
	// _, err := tx.Model(AdminMemberRole.Table()).WhereIn(AdminMemberRole.Columns().RoleId, roleIds).Delete()
	// if err != nil {
	// 	return gerror.WrapCode(codes.CodeInternalError, err, "删除用户角色关联失败")
	// }
	return nil
}

// BuildRoleTree 构建角色关系树路径 (如果需要层级管理)
func BuildRoleTree(parentTree string, roleId int64) string {
	if parentTree == "" || parentTree == ",0," {
		return fmt.Sprintf(",%d,", roleId)
	}
	return fmt.Sprintf("%s%d,", parentTree, roleId)
}

// GetRoleMapByIds 根据 ID 列表获取角色 Map
func (d *adminRoleDao) GetRoleMapByIds(ctx context.Context, ids []int64) (map[int64]*entity.AdminRole, error) {
	roleMap := make(map[int64]*entity.AdminRole)
	if len(ids) == 0 {
		return roleMap, nil
	}

	var roles []*entity.AdminRole
	err := d.Ctx(ctx).WhereIn(d.Columns().Id, ids).Scan(&roles)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "根据ID列表获取角色失败")
	}

	for _, role := range roles {
		roleMap[role.Id] = role
	}
	return roleMap, nil
}
