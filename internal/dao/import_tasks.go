// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// importTasksDao is the data access object for the table import_tasks.
// You can define custom methods on it to extend its functionality as needed.
type importTasksDao struct {
	*internal.ImportTasksDao
}

var (
	// ImportTasks is a globally accessible object for table import_tasks operations.
	ImportTasks = importTasksDao{internal.NewImportTasksDao()}
)

// Add your custom methods and functionality below.
