// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// redPacketsDao is the data access object for the table red_packets.
// You can define custom methods on it to extend its functionality as needed.
type redPacketsDao struct {
	*internal.RedPacketsDao
}

var (
	// RedPackets is a globally accessible object for table red_packets operations.
	RedPackets = redPacketsDao{internal.NewRedPacketsDao()}
)

// Add your custom methods and functionality below.

// GetAdminRedPacketDetail 获取红包详情
func (d *redPacketsDao) GetAdminRedPacketDetail(ctx context.Context, redPacketId int64) (*model.RedPacketAdminInfo, error) {
	var result *model.RedPacketAdminInfo

	err := d.DB().Model(d.Table()).
		LeftJoin("users u", "u.id = red_packets.sender_user_id").
		LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id").
		LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id").
		LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1").
		LeftJoin("tokens t", "t.token_id = red_packets.token_id").
		Fields(
			"red_packets.*",
			"u.name as creator_username",
			"first_agent.username as first_agent_name",
			"second_agent.username as second_agent_name",
			"third_agent.username as third_agent_name",
			"uba.telegram_id as telegram_id",
			"uba.telegram_username as telegram_username",
			"uba.first_name as first_name",
			"t.symbol as token_symbol",
			"t.name as token_name",
			"t.logo_url as token_logo",
		).
		Where("red_packets.red_packet_id", redPacketId).
		WhereNull("red_packets.deleted_at").
		Where("(u.id IS NULL OR u.deleted_at IS NULL)").
		Where("(first_agent.agent_id IS NULL OR first_agent.deleted_at IS NULL)").
		Where("(second_agent.agent_id IS NULL OR second_agent.deleted_at IS NULL)").
		Where("(third_agent.agent_id IS NULL OR third_agent.deleted_at IS NULL)").
		Where("(uba.user_id IS NULL OR uba.deleted_at IS NULL)").
		Where("(t.token_id IS NULL OR t.deleted_at IS NULL)").
		Scan(&result)

	return result, err
}

// GetRedPacketForUpdate 获取红包详情并加锁(用于事务)
func (d *redPacketsDao) GetRedPacketForUpdate(ctx context.Context, tx gdb.TX, redPacketId int64) (*model.RedPacketAdminInfo, error) {
	var result *model.RedPacketAdminInfo

	// 使用FOR UPDATE语句进行行锁定
	err := tx.Model(d.Table()).As("red_packets").
		LeftJoin("users u", "u.id = red_packets.sender_user_id").
		LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id").
		LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id").
		LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1").
		LeftJoin("tokens t", "t.token_id = red_packets.token_id").
		Fields(
			"red_packets.*",
			"u.nickname as creator_username",
			"first_agent.username as first_agent_name",
			"second_agent.username as second_agent_name",
			"third_agent.username as third_agent_name",
			"uba.telegram_id as telegram_id",
			"uba.telegram_username as telegram_username",
			"uba.first_name as first_name",
			"t.symbol as token_symbol",
			"t.name as token_name",
			"t.logo_url as token_logo",
		).
		Where("red_packets.red_packet_id", redPacketId).
		WhereNull("red_packets.deleted_at").
		Where("(u.id IS NULL OR u.deleted_at IS NULL)").
		Where("(first_agent.agent_id IS NULL OR first_agent.deleted_at IS NULL)").
		Where("(second_agent.agent_id IS NULL OR second_agent.deleted_at IS NULL)").
		Where("(third_agent.agent_id IS NULL OR third_agent.deleted_at IS NULL)").
		Where("(uba.user_id IS NULL OR uba.deleted_at IS NULL)").
		Where("(t.token_id IS NULL OR t.deleted_at IS NULL)").
		Limit(1).
		Scan(&result, "SELECT * FROM (?) AS t FOR UPDATE")

	return result, err
}

// UpdateRedPacketStatus 更新红包状态
func (d *redPacketsDao) UpdateRedPacketStatus(ctx context.Context, tx gdb.TX, redPacketId int64, newStatus string, oldStatus string) (int64, error) {
	// 在事务中更新红包状态，通常需要检查旧状态以防止并发问题
	condition := g.Map{
		"red_packet_id": redPacketId,
	}

	// 如果指定了旧状态，则添加到条件中
	if oldStatus != "" {
		condition["status"] = oldStatus
	}

	result, err := tx.Model(d.Table()).
		Data(g.Map{
			"status": newStatus,
		}).
		Where(condition).
		Update()

	if err != nil {
		return 0, err
	}

	// 返回受影响行数
	return result.RowsAffected()
}

// ListAdminRedPacketsWithFullInfo 查询后台红包列表（含完整信息）
func (d *redPacketsDao) ListAdminRedPacketsWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.RedPacketAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.RedPacketAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := d.Ctx(ctx).As("rp").
		LeftJoin("users creator", "rp.sender_user_id = creator.id").
		LeftJoin("tokens token", "rp.token_id = token.token_id")

	// 添加创建者的代理和telegram表的关联查询
	m = m.LeftJoin("agents creator_first_agent", "creator.first_id = creator_first_agent.agent_id")
	m = m.LeftJoin("agents creator_second_agent", "creator.second_id = creator_second_agent.agent_id")
	m = m.LeftJoin("agents creator_third_agent", "creator.third_id = creator_third_agent.agent_id")
	m = m.LeftJoin("user_backup_accounts creator_uba", "creator.id = creator_uba.user_id AND creator_uba.is_master = 1")

	// 添加软删除条件
	m = m.WhereNull("rp.deleted_at")

	// 处理LEFT JOIN表的软删除 - 需要检查NULL或未删除
	// 创建者用户必须存在且未删除
	m = m.WhereNull("creator.deleted_at")
	// 代理表可能不存在记录，需要特殊处理
	m = m.Where("(creator_first_agent.agent_id IS NULL OR creator_first_agent.deleted_at IS NULL)")
	m = m.Where("(creator_second_agent.agent_id IS NULL OR creator_second_agent.deleted_at IS NULL)")
	m = m.Where("(creator_third_agent.agent_id IS NULL OR creator_third_agent.deleted_at IS NULL)")
	// 备份账户表可能不存在记录，需要特殊处理
	m = m.Where("(creator_uba.user_id IS NULL OR creator_uba.deleted_at IS NULL)")
	// 代币表通常都有记录，但为了安全也处理
	m = m.Where("(token.token_id IS NULL OR token.deleted_at IS NULL)")

	// 处理查询条件
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询红包总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"rp.red_packet_id",
		"rp.uuid",
		"rp.sender_user_id",
		"rp.creator_username",
		"creator.account as creator_account",
		"rp.token_id",
		"COALESCE(token.symbol, rp.symbol) as token_symbol",
		"token.name as token_name",
		"token.logo_url as token_logo",
		"rp.type",
		"rp.total_amount",
		"rp.quantity as total_quantity",
		"rp.remaining_amount",
		"rp.remaining_quantity",
		"rp.status",
		"rp.memo",
		"rp.symbol",
		"rp.red_packet_images_id as image_id",
		"rp.thumb_url as image_url",
		"rp.message_id",
		"rp.expires_at",
		"rp.created_at",
	}

	// 创建者代理和telegram字段
	creatorAgentAndTelegramFields := []string{
		"creator_first_agent.username as first_agent_name",
		"creator_second_agent.username as second_agent_name",
		"creator_third_agent.username as third_agent_name",
		"creator_uba.telegram_id",
		"creator_uba.telegram_username",
		"creator_uba.first_name",
	}

	allFields := append(baseFields, creatorAgentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	err = query.Page(page, pageSize).Order("rp.created_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询红包列表失败")
	}

	return list, total, nil
}
