// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameTransactionsDao is the data access object for the table game_transactions.
type GameTransactionsDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  GameTransactionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// GameTransactionsColumns defines and stores column names for the table game_transactions.
type GameTransactionsColumns struct {
	Id                  string //
	UserId              string // 用户ID
	Amount              string // 投注金额
	Symbol              string // 币种
	GameType            string // 游戏类型
	GameResult          string // 游戏结果（win/lose/draw）
	CommissionProcessed string // 佣金是否已处理
	CreatedAt           string //
	UpdatedAt           string //
}

// gameTransactionsColumns holds the columns for the table game_transactions.
var gameTransactionsColumns = GameTransactionsColumns{
	Id:                  "id",
	UserId:              "user_id",
	Amount:              "amount",
	Symbol:              "symbol",
	GameType:            "game_type",
	GameResult:          "game_result",
	CommissionProcessed: "commission_processed",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
}

// NewGameTransactionsDao creates and returns a new DAO object for table data access.
func NewGameTransactionsDao(handlers ...gdb.ModelHandler) *GameTransactionsDao {
	return &GameTransactionsDao{
		group:    "default",
		table:    "game_transactions",
		columns:  gameTransactionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameTransactionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameTransactionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameTransactionsDao) Columns() GameTransactionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameTransactionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameTransactionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameTransactionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
