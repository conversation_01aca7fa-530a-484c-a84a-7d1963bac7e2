// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ExchangeOrdersDao is the data access object for the table exchange_orders.
type ExchangeOrdersDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  ExchangeOrdersColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// ExchangeOrdersColumns defines and stores column names for the table exchange_orders.
type ExchangeOrdersColumns struct {
	OrderId               string // 订单内部 ID (主键)
	OrderSn               string // 订单唯一编号 (业务层生成, 便于跟踪和对外展示)
	UserId                string // 执行兑换的用户 ID (外键关联 users.user_id)
	TenantId              string // 租户 id
	ProductId             string // 关联的兑换产品 ID (外键关联 exchange_products.product_id)
	BaseToken             string // 基础代币 ID (来自 exchange_products)
	QuoteToken            string // 计价代币 ID (来自 exchange_products)
	Symbol                string // 交易对符号 (来自 exchange_products)
	TradeType             string // 交易类型: buy-用户买入基础代币(花费计价代币), sell-用户卖出基础代币(获得计价代币)
	AmountBase            string // 涉及的基础代币数量（截断后）
	AmountQuote           string // 涉及的计价代币数量（截断后）
	OriginalFromAmount    string // 原始兑换金额（未截断）
	OriginalToAmount      string // 原始收到金额（未截断）
	Price                 string // 本次交易实际成交价格 (计价代币数量 / 基础代币数量)
	SpreadAmount          string // 点差金额
	SpreadRate            string // 点差率
	FeeAmount             string // 收取的手续费金额
	FeeTokenId            string // 手续费收取的币种 ID (通常是 base_token_id 或 quote_token_id, 根据产品配置决定)
	TransactionHash       string // 交易哈希（如果有）
	SenderTransactionId   string // Wallet transaction ID for debit operation
	ReceiverTransactionId string // Wallet transaction ID for credit operation
	QuoteId               string // 关联的报价ID
	Status                string // 订单状态
	ErrorMessage          string // 订单失败或取消时的错误信息
	ClientOrderId         string // 客户端提供的订单 ID (用于幂等性检查或客户端关联)
	CreatedAt             string // 订单创建时间
	ExecutedAt            string // 实际执行时间
	CompletedAt           string // 完成时间
	UpdatedAt             string // 订单最后更新时间
	DeletedAt             string // 软删除时间
	OutputAmountBeforeFee string // 扣费前输出金额
	OutputAmountAfterFee  string // 扣费后输出金额
	FeeCalculationMethod  string // 手续费计算方法
}

// exchangeOrdersColumns holds the columns for the table exchange_orders.
var exchangeOrdersColumns = ExchangeOrdersColumns{
	OrderId:               "order_id",
	OrderSn:               "order_sn",
	UserId:                "user_id",
	TenantId:              "tenant_id",
	ProductId:             "product_id",
	BaseToken:             "base_token",
	QuoteToken:            "quote_token",
	Symbol:                "symbol",
	TradeType:             "trade_type",
	AmountBase:            "amount_base",
	AmountQuote:           "amount_quote",
	OriginalFromAmount:    "original_from_amount",
	OriginalToAmount:      "original_to_amount",
	Price:                 "price",
	SpreadAmount:          "spread_amount",
	SpreadRate:            "spread_rate",
	FeeAmount:             "fee_amount",
	FeeTokenId:            "fee_token_id",
	TransactionHash:       "transaction_hash",
	SenderTransactionId:   "sender_transaction_id",
	ReceiverTransactionId: "receiver_transaction_id",
	QuoteId:               "quote_id",
	Status:                "status",
	ErrorMessage:          "error_message",
	ClientOrderId:         "client_order_id",
	CreatedAt:             "created_at",
	ExecutedAt:            "executed_at",
	CompletedAt:           "completed_at",
	UpdatedAt:             "updated_at",
	DeletedAt:             "deleted_at",
	OutputAmountBeforeFee: "output_amount_before_fee",
	OutputAmountAfterFee:  "output_amount_after_fee",
	FeeCalculationMethod:  "fee_calculation_method",
}

// NewExchangeOrdersDao creates and returns a new DAO object for table data access.
func NewExchangeOrdersDao(handlers ...gdb.ModelHandler) *ExchangeOrdersDao {
	return &ExchangeOrdersDao{
		group:    "default",
		table:    "exchange_orders",
		columns:  exchangeOrdersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ExchangeOrdersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ExchangeOrdersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ExchangeOrdersDao) Columns() ExchangeOrdersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ExchangeOrdersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ExchangeOrdersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ExchangeOrdersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
