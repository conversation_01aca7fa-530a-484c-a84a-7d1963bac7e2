// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CommissionSummaryDao is the data access object for the table commission_summary.
type CommissionSummaryDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  CommissionSummaryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// CommissionSummaryColumns defines and stores column names for the table commission_summary.
type CommissionSummaryColumns struct {
	Id                 string //
	UserId             string // 用户ID
	TenantId           string // 租户 id
	Date               string // 日期
	DirectCommission   string // 直接邀请佣金
	IndirectCommission string // 间接邀请佣金
	TotalCommission    string // 总佣金
	DirectCount        string // 直接邀请佣金笔数
	IndirectCount      string // 间接邀请佣金笔数
	Symbol             string // 币种
	CreatedAt          string //
	UpdatedAt          string //
}

// commissionSummaryColumns holds the columns for the table commission_summary.
var commissionSummaryColumns = CommissionSummaryColumns{
	Id:                 "id",
	UserId:             "user_id",
	TenantId:           "tenant_id",
	Date:               "date",
	DirectCommission:   "direct_commission",
	IndirectCommission: "indirect_commission",
	TotalCommission:    "total_commission",
	DirectCount:        "direct_count",
	IndirectCount:      "indirect_count",
	Symbol:             "symbol",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
}

// NewCommissionSummaryDao creates and returns a new DAO object for table data access.
func NewCommissionSummaryDao(handlers ...gdb.ModelHandler) *CommissionSummaryDao {
	return &CommissionSummaryDao{
		group:    "default",
		table:    "commission_summary",
		columns:  commissionSummaryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CommissionSummaryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CommissionSummaryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CommissionSummaryDao) Columns() CommissionSummaryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CommissionSummaryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CommissionSummaryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CommissionSummaryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
