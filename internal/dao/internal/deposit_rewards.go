// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DepositRewardsDao is the data access object for the table deposit_rewards.
type DepositRewardsDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  DepositRewardsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// DepositRewardsColumns defines and stores column names for the table deposit_rewards.
type DepositRewardsColumns struct {
	Id               string //
	UserId           string //
	TenantId         string // 租户 id
	RechargeId       string //
	DepositAmount    string //
	RewardPercentage string //
	RewardAmount     string //
	FlowMultiplier   string //
	RequiredTurnover string //
	CreatedAt        string //
}

// depositRewardsColumns holds the columns for the table deposit_rewards.
var depositRewardsColumns = DepositRewardsColumns{
	Id:               "id",
	UserId:           "user_id",
	TenantId:         "tenant_id",
	RechargeId:       "recharge_id",
	DepositAmount:    "deposit_amount",
	RewardPercentage: "reward_percentage",
	RewardAmount:     "reward_amount",
	FlowMultiplier:   "flow_multiplier",
	RequiredTurnover: "required_turnover",
	CreatedAt:        "created_at",
}

// NewDepositRewardsDao creates and returns a new DAO object for table data access.
func NewDepositRewardsDao(handlers ...gdb.ModelHandler) *DepositRewardsDao {
	return &DepositRewardsDao{
		group:    "default",
		table:    "deposit_rewards",
		columns:  depositRewardsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DepositRewardsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DepositRewardsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DepositRewardsDao) Columns() DepositRewardsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DepositRewardsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DepositRewardsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DepositRewardsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
