// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameTransferRecordsDao is the data access object for the table game_transfer_records.
type GameTransferRecordsDao struct {
	table    string                     // table is the underlying table name of the DAO.
	group    string                     // group is the database configuration group name of the current DAO.
	columns  GameTransferRecordsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler         // handlers for customized model modification.
}

// GameTransferRecordsColumns defines and stores column names for the table game_transfer_records.
type GameTransferRecordsColumns struct {
	Id                  string //
	ReferenceNo         string // 唯一参考号，用于幂等性控制
	UserId              string // 用户ID
	GameCode            string // 游戏代码（如evolution, pp, pg）
	ProductCode         string // 游戏产品代码（如191, 39, 98）
	GameName            string // 游戏名称快照
	TransferType        string // 转账类型（transfer_in:转入游戏, transfer_out:转出游戏）
	Amount              string // 转账金额
	Currency            string // 币种
	LocalStatus         string // 本地资金操作状态（pending:待处理, processing:处理中, success:成功, failed:失败, rollback:已回滚）
	LocalTransactionId  string // 本地钱包扣款交易ID
	LocalErrorMsg       string // 本地操作错误信息
	RemoteStatus        string // 远程API调用状态（pending:待调用, processing:调用中, success:成功, failed:失败）
	RemoteTransactionId string // 远程平台返回的交易ID
	RemoteErrorMsg      string // 远程调用错误信息
	RemoteResponse      string // 远程API完整响应（JSON格式）
	WalletBalanceBefore string // 转账前钱包余额
	WalletBalanceAfter  string // 转账后钱包余额
	GameBalanceBefore   string // 转账前游戏余额
	GameBalanceAfter    string // 转账后游戏余额
	CreatedAt           string //
	UpdatedAt           string //
	CompletedAt         string // 完成时间
}

// gameTransferRecordsColumns holds the columns for the table game_transfer_records.
var gameTransferRecordsColumns = GameTransferRecordsColumns{
	Id:                  "id",
	ReferenceNo:         "reference_no",
	UserId:              "user_id",
	GameCode:            "game_code",
	ProductCode:         "product_code",
	GameName:            "game_name",
	TransferType:        "transfer_type",
	Amount:              "amount",
	Currency:            "currency",
	LocalStatus:         "local_status",
	LocalTransactionId:  "local_transaction_id",
	LocalErrorMsg:       "local_error_msg",
	RemoteStatus:        "remote_status",
	RemoteTransactionId: "remote_transaction_id",
	RemoteErrorMsg:      "remote_error_msg",
	RemoteResponse:      "remote_response",
	WalletBalanceBefore: "wallet_balance_before",
	WalletBalanceAfter:  "wallet_balance_after",
	GameBalanceBefore:   "game_balance_before",
	GameBalanceAfter:    "game_balance_after",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	CompletedAt:         "completed_at",
}

// NewGameTransferRecordsDao creates and returns a new DAO object for table data access.
func NewGameTransferRecordsDao(handlers ...gdb.ModelHandler) *GameTransferRecordsDao {
	return &GameTransferRecordsDao{
		group:    "default",
		table:    "game_transfer_records",
		columns:  gameTransferRecordsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameTransferRecordsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameTransferRecordsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameTransferRecordsDao) Columns() GameTransferRecordsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameTransferRecordsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameTransferRecordsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameTransferRecordsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
