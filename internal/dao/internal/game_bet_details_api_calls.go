// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameBetDetailsApiCallsDao is the data access object for the table game_bet_details_api_calls.
type GameBetDetailsApiCallsDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  GameBetDetailsApiCallsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// GameBetDetailsApiCallsColumns defines and stores column names for the table game_bet_details_api_calls.
type GameBetDetailsApiCallsColumns struct {
	Id               string //
	ApiMethod        string // API方法 (lbdm=Live, bdm=RNG)
	TenantId         string // 租户 id
	Username         string // 查询的玩家用户名
	StartDate        string // 查询开始日期
	EndDate          string // 查询结束日期
	PageNumber       string // 请求的页码
	TotalPages       string // 总页数
	CurrentPage      string // 当前页码
	TotalCount       string // 总记录数
	ApiStatus        string // API响应状态码 (0=成功)
	ApiErrorDesc     string // API错误描述
	ResponseTimeMs   string // API响应时间(毫秒)
	RecordsProcessed string // 成功处理的记录数
	ProcessingStatus string // 处理状态
	ProcessingError  string // 处理错误信息
	CreatedAt        string // 调用时间
	CompletedAt      string // 处理完成时间
}

// gameBetDetailsApiCallsColumns holds the columns for the table game_bet_details_api_calls.
var gameBetDetailsApiCallsColumns = GameBetDetailsApiCallsColumns{
	Id:               "id",
	ApiMethod:        "api_method",
	TenantId:         "tenant_id",
	Username:         "username",
	StartDate:        "start_date",
	EndDate:          "end_date",
	PageNumber:       "page_number",
	TotalPages:       "total_pages",
	CurrentPage:      "current_page",
	TotalCount:       "total_count",
	ApiStatus:        "api_status",
	ApiErrorDesc:     "api_error_desc",
	ResponseTimeMs:   "response_time_ms",
	RecordsProcessed: "records_processed",
	ProcessingStatus: "processing_status",
	ProcessingError:  "processing_error",
	CreatedAt:        "created_at",
	CompletedAt:      "completed_at",
}

// NewGameBetDetailsApiCallsDao creates and returns a new DAO object for table data access.
func NewGameBetDetailsApiCallsDao(handlers ...gdb.ModelHandler) *GameBetDetailsApiCallsDao {
	return &GameBetDetailsApiCallsDao{
		group:    "default",
		table:    "game_bet_details_api_calls",
		columns:  gameBetDetailsApiCallsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameBetDetailsApiCallsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameBetDetailsApiCallsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameBetDetailsApiCallsDao) Columns() GameBetDetailsApiCallsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameBetDetailsApiCallsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameBetDetailsApiCallsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameBetDetailsApiCallsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
