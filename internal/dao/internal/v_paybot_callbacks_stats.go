// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VPaybotCallbacksStatsDao is the data access object for the table v_paybot_callbacks_stats.
type VPaybotCallbacksStatsDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  VPaybotCallbacksStatsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// VPaybotCallbacksStatsColumns defines and stores column names for the table v_paybot_callbacks_stats.
type VPaybotCallbacksStatsColumns struct {
	EventType       string // 事件类型（deposit_confirmed/withdraw_completed等）
	ProcessedStatus string // 处理状态
	CallbackCount   string //
	AvgAttempts     string //
	FirstCallbackAt string // 记录创建时间
	LastCallbackAt  string // 记录创建时间
}

// vPaybotCallbacksStatsColumns holds the columns for the table v_paybot_callbacks_stats.
var vPaybotCallbacksStatsColumns = VPaybotCallbacksStatsColumns{
	EventType:       "event_type",
	ProcessedStatus: "processed_status",
	CallbackCount:   "callback_count",
	AvgAttempts:     "avg_attempts",
	FirstCallbackAt: "first_callback_at",
	LastCallbackAt:  "last_callback_at",
}

// NewVPaybotCallbacksStatsDao creates and returns a new DAO object for table data access.
func NewVPaybotCallbacksStatsDao(handlers ...gdb.ModelHandler) *VPaybotCallbacksStatsDao {
	return &VPaybotCallbacksStatsDao{
		group:    "default",
		table:    "v_paybot_callbacks_stats",
		columns:  vPaybotCallbacksStatsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VPaybotCallbacksStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VPaybotCallbacksStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VPaybotCallbacksStatsDao) Columns() VPaybotCallbacksStatsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VPaybotCallbacksStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VPaybotCallbacksStatsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VPaybotCallbacksStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
