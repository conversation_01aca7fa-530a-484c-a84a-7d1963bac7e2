// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameCommissionSummaryDao is the data access object for the table game_commission_summary.
type GameCommissionSummaryDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  GameCommissionSummaryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// GameCommissionSummaryColumns defines and stores column names for the table game_commission_summary.
type GameCommissionSummaryColumns struct {
	Id                     string //
	UserId                 string // 用户ID
	TenantId               string // 租户 id
	Date                   string // 日期
	DirectCommission       string // 直接邀请佣金
	IndirectCommission     string // 间接邀请佣金
	BettingBonusCommission string // 反水佣金
	TotalCommission        string // 总佣金
	DirectCount            string // 直接邀请佣金笔数
	IndirectCount          string // 间接邀请佣金笔数
	BonusCommissionCount   string // 反水笔数
	Symbol                 string // 币种
	CreatedAt              string //
	UpdatedAt              string //
}

// gameCommissionSummaryColumns holds the columns for the table game_commission_summary.
var gameCommissionSummaryColumns = GameCommissionSummaryColumns{
	Id:                     "id",
	UserId:                 "user_id",
	TenantId:               "tenant_id",
	Date:                   "date",
	DirectCommission:       "direct_commission",
	IndirectCommission:     "indirect_commission",
	BettingBonusCommission: "betting_bonus_commission",
	TotalCommission:        "total_commission",
	DirectCount:            "direct_count",
	IndirectCount:          "indirect_count",
	BonusCommissionCount:   "bonus_commission_count",
	Symbol:                 "symbol",
	CreatedAt:              "created_at",
	UpdatedAt:              "updated_at",
}

// NewGameCommissionSummaryDao creates and returns a new DAO object for table data access.
func NewGameCommissionSummaryDao(handlers ...gdb.ModelHandler) *GameCommissionSummaryDao {
	return &GameCommissionSummaryDao{
		group:    "default",
		table:    "game_commission_summary",
		columns:  gameCommissionSummaryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameCommissionSummaryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameCommissionSummaryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameCommissionSummaryDao) Columns() GameCommissionSummaryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameCommissionSummaryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameCommissionSummaryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameCommissionSummaryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
