// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PersonalAccessTokensDao is the data access object for the table personal_access_tokens.
type PersonalAccessTokensDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  PersonalAccessTokensColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// PersonalAccessTokensColumns defines and stores column names for the table personal_access_tokens.
type PersonalAccessTokensColumns struct {
	Id            string //
	TokenableType string //
	UserId        string //
	Token         string //
	LastUsedAt    string //
	ExpiresAt     string //
	CreatedAt     string //
	UpdatedAt     string //
	DeletedAt     string // 软删除的时间戳
}

// personalAccessTokensColumns holds the columns for the table personal_access_tokens.
var personalAccessTokensColumns = PersonalAccessTokensColumns{
	Id:            "id",
	TokenableType: "tokenable_type",
	UserId:        "user_id",
	Token:         "token",
	LastUsedAt:    "last_used_at",
	ExpiresAt:     "expires_at",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewPersonalAccessTokensDao creates and returns a new DAO object for table data access.
func NewPersonalAccessTokensDao(handlers ...gdb.ModelHandler) *PersonalAccessTokensDao {
	return &PersonalAccessTokensDao{
		group:    "default",
		table:    "personal_access_tokens",
		columns:  personalAccessTokensColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *PersonalAccessTokensDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *PersonalAccessTokensDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *PersonalAccessTokensDao) Columns() PersonalAccessTokensColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *PersonalAccessTokensDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *PersonalAccessTokensDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *PersonalAccessTokensDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
