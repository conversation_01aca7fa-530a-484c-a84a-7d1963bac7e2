// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ReferralCommissionsDao is the data access object for the table referral_commissions.
type ReferralCommissionsDao struct {
	table    string                     // table is the underlying table name of the DAO.
	group    string                     // group is the database configuration group name of the current DAO.
	columns  ReferralCommissionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler         // handlers for customized model modification.
}

// ReferralCommissionsColumns defines and stores column names for the table referral_commissions.
type ReferralCommissionsColumns struct {
	CommissionId     string // 佣金记录 ID (主键)
	TenantId         string // 租户 id
	TransactionId    string // 关联的触发佣金的交易 ID (例如: 下级的某笔操作, 外键指向 transactions.transaction_id)
	ReferrerId       string // 获得佣金的用户 ID (外键, 指向 users.user_id)
	InviteeId        string // 产生佣金的被推荐人用户 ID (外键, 指向 users.user_id)
	Level            string // 佣金产生的推荐层级
	CommissionAmount string // 佣金金额
	CommissionRate   string // 佣金比率 (例如: 0.01 表示 1%)
	TokenId          string // 佣金代币 ID (外键, 指向 tokens.token_id)
	Status           string // 佣金状态: pending-待发放, paid-已发放, cancelled-已取消
	CreatedAt        string // 创建时间
	UpdatedAt        string // 最后更新时间
	DeletedAt        string // 软删除时间
}

// referralCommissionsColumns holds the columns for the table referral_commissions.
var referralCommissionsColumns = ReferralCommissionsColumns{
	CommissionId:     "commission_id",
	TenantId:         "tenant_id",
	TransactionId:    "transaction_id",
	ReferrerId:       "referrer_id",
	InviteeId:        "invitee_id",
	Level:            "level",
	CommissionAmount: "commission_amount",
	CommissionRate:   "commission_rate",
	TokenId:          "token_id",
	Status:           "status",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
}

// NewReferralCommissionsDao creates and returns a new DAO object for table data access.
func NewReferralCommissionsDao(handlers ...gdb.ModelHandler) *ReferralCommissionsDao {
	return &ReferralCommissionsDao{
		group:    "default",
		table:    "referral_commissions",
		columns:  referralCommissionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ReferralCommissionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ReferralCommissionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ReferralCommissionsDao) Columns() ReferralCommissionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ReferralCommissionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ReferralCommissionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ReferralCommissionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
