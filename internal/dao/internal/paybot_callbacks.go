// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PaybotCallbacksDao is the data access object for the table paybot_callbacks.
type PaybotCallbacksDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  PaybotCallbacksColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// PaybotCallbacksColumns defines and stores column names for the table paybot_callbacks.
type PaybotCallbacksColumns struct {
	Id              string // 主键ID
	TenantId        string // 租户 id
	EventType       string // 事件类型（deposit_confirmed/withdraw_completed等）
	OrderNo         string // 关联的订单号
	RelatedTable    string // 关联的表名
	RelatedId       string // 关联表的记录ID
	MerchantId      string // 商户ID
	Payload         string // 回调数据（JSON格式）
	Signature       string // 回调签名
	CallbackTime    string // 回调接收时间
	ProcessedStatus string // 处理状态
	ProcessAttempts string // 处理尝试次数
	ErrorMessage    string // 处理错误信息
	NextRetryAt     string // 下次重试时间
	CreatedAt       string // 记录创建时间
	UpdatedAt       string // 记录更新时间
}

// paybotCallbacksColumns holds the columns for the table paybot_callbacks.
var paybotCallbacksColumns = PaybotCallbacksColumns{
	Id:              "id",
	TenantId:        "tenant_id",
	EventType:       "event_type",
	OrderNo:         "order_no",
	RelatedTable:    "related_table",
	RelatedId:       "related_id",
	MerchantId:      "merchant_id",
	Payload:         "payload",
	Signature:       "signature",
	CallbackTime:    "callback_time",
	ProcessedStatus: "processed_status",
	ProcessAttempts: "process_attempts",
	ErrorMessage:    "error_message",
	NextRetryAt:     "next_retry_at",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
}

// NewPaybotCallbacksDao creates and returns a new DAO object for table data access.
func NewPaybotCallbacksDao(handlers ...gdb.ModelHandler) *PaybotCallbacksDao {
	return &PaybotCallbacksDao{
		group:    "default",
		table:    "paybot_callbacks",
		columns:  paybotCallbacksColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *PaybotCallbacksDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *PaybotCallbacksDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *PaybotCallbacksDao) Columns() PaybotCallbacksColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *PaybotCallbacksDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *PaybotCallbacksDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *PaybotCallbacksDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
