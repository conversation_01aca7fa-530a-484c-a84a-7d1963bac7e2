// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameCommissionRecordsDao is the data access object for the table game_commission_records.
type GameCommissionRecordsDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  GameCommissionRecordsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// GameCommissionRecordsColumns defines and stores column names for the table game_commission_records.
type GameCommissionRecordsColumns struct {
	Id                string //
	BeneficiaryId     string // 受益人ID（收到佣金的用户）
	SourceUserId      string // 来源用户ID（产生投注的用户）
	TenantId          string // 租户 id
	GameTransactionId string // 游戏流水ID
	BetAmount         string // 投注金额
	CommissionRate    string // 佣金比例（0.003表示0.3%）
	CommissionAmount  string // 佣金金额
	CommissionType    string // 佣金类型：direct   :直接邀请 indirect :间接邀请 betting_bonus :反水佣金
	Level             string // 推荐层级（1:直接，2+:间接）
	Symbol            string // 币种
	Status            string // 状态（0:待发放，1:已发放，2:发放失败）
	TransactionId     string // 关联的资金交易ID
	Notified          string // 是否已发送通知
	CreatedAt         string //
	UpdatedAt         string //
	DistributedAt     string // 发放时间
}

// gameCommissionRecordsColumns holds the columns for the table game_commission_records.
var gameCommissionRecordsColumns = GameCommissionRecordsColumns{
	Id:                "id",
	BeneficiaryId:     "beneficiary_id",
	SourceUserId:      "source_user_id",
	TenantId:          "tenant_id",
	GameTransactionId: "game_transaction_id",
	BetAmount:         "bet_amount",
	CommissionRate:    "commission_rate",
	CommissionAmount:  "commission_amount",
	CommissionType:    "commission_type",
	Level:             "level",
	Symbol:            "symbol",
	Status:            "status",
	TransactionId:     "transaction_id",
	Notified:          "notified",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DistributedAt:     "distributed_at",
}

// NewGameCommissionRecordsDao creates and returns a new DAO object for table data access.
func NewGameCommissionRecordsDao(handlers ...gdb.ModelHandler) *GameCommissionRecordsDao {
	return &GameCommissionRecordsDao{
		group:    "default",
		table:    "game_commission_records",
		columns:  gameCommissionRecordsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameCommissionRecordsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameCommissionRecordsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameCommissionRecordsDao) Columns() GameCommissionRecordsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameCommissionRecordsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameCommissionRecordsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameCommissionRecordsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
