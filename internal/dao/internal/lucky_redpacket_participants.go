// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LuckyRedpacketParticipantsDao is the data access object for the table lucky_redpacket_participants.
type LuckyRedpacketParticipantsDao struct {
	table    string                            // table is the underlying table name of the DAO.
	group    string                            // group is the database configuration group name of the current DAO.
	columns  LuckyRedpacketParticipantsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler                // handlers for customized model modification.
}

// LuckyRedpacketParticipantsColumns defines and stores column names for the table lucky_redpacket_participants.
type LuckyRedpacketParticipantsColumns struct {
	Id             string // 主键ID
	RedPacketId    string // 红包 ID (外键, 指向 red_packets.red_packet_id)
	TenantId       string // 租户 ID
	UserId         string // Telegram 用户 ID
	Username       string // 用户名
	ParticipatedAt string // 参与时间
	IsWinner       string // 是否中奖: 0-未中奖, 1-中奖
	WonAmount      string // 中奖金额
	WonAt          string // 中奖时间
	ClaimId        string // 关联的红包领取记录 ID (外键, 指向 red_packet_claims.claim_id)
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
}

// luckyRedpacketParticipantsColumns holds the columns for the table lucky_redpacket_participants.
var luckyRedpacketParticipantsColumns = LuckyRedpacketParticipantsColumns{
	Id:             "id",
	RedPacketId:    "red_packet_id",
	TenantId:       "tenant_id",
	UserId:         "user_id",
	Username:       "username",
	ParticipatedAt: "participated_at",
	IsWinner:       "is_winner",
	WonAmount:      "won_amount",
	WonAt:          "won_at",
	ClaimId:        "claim_id",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
}

// NewLuckyRedpacketParticipantsDao creates and returns a new DAO object for table data access.
func NewLuckyRedpacketParticipantsDao(handlers ...gdb.ModelHandler) *LuckyRedpacketParticipantsDao {
	return &LuckyRedpacketParticipantsDao{
		group:    "default",
		table:    "lucky_redpacket_participants",
		columns:  luckyRedpacketParticipantsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LuckyRedpacketParticipantsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LuckyRedpacketParticipantsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LuckyRedpacketParticipantsDao) Columns() LuckyRedpacketParticipantsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LuckyRedpacketParticipantsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LuckyRedpacketParticipantsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LuckyRedpacketParticipantsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
