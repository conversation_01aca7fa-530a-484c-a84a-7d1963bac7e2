// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TelegramGroupsDao is the data access object for the table telegram_groups.
type TelegramGroupsDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  TelegramGroupsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// TelegramGroupsColumns defines and stores column names for the table telegram_groups.
type TelegramGroupsColumns struct {
	Id                          string // 主键ID
	ChatId                      string // Telegram群组ID(负数)
	TenantId                    string // 租户ID
	Type                        string // 聊天类型:    group, supergroup, channel
	Title                       string // 群组名称
	Username                    string // 群组用户名(如果是公开群组)
	Description                 string // 群组描述
	InviteLink                  string // 群组邀请链接
	PhotoFileId                 string // 群组头像文件ID
	PhotoUrl                    string // 群组头像URL
	MemberCount                 string // 群组成员数量
	AllMembersAreAdministrators string // 是否所有成员都是管理员
	HasProtectedContent         string // 是否启用内容保护
	HasVisibleHistory           string // 新成员是否可见历史消息
	CanSetStickerSet            string // 是否可以设置贴纸集
	StickerSetName              string // 群组贴纸集名称
	LinkedChatId                string // 链接的频道/群组ID
	Location                    string // 群组位置信息(JSON格式)
	SlowModeDelay               string // 慢速模式延迟(秒)
	MessageAutoDeleteTime       string // 消息自动删除时间(秒)
	Permissions                 string // 群组权限设置(JSON格式)
	BotAddedByUserId            string // 添加机器人的用户ID
	BotAddedByUsername          string // 添加机器人的用户名
	BotAddedAt                  string // 机器人加入时间
	BotIsAdmin                  string // 机器人是否是管理员
	BotCanManageChat            string // 机器人是否可以管理群组
	BotCanDeleteMessages        string // 机器人是否可以删除消息
	BotCanManageVideoChats      string // 机器人是否可以管理视频聊天
	BotCanRestrictMembers       string // 机器人是否可以限制成员
	BotCanPromoteMembers        string // 机器人是否可以提升成员
	BotCanChangeInfo            string // 机器人是否可以更改群组信息
	BotCanInviteUsers           string // 机器人是否可以邀请用户
	BotCanPinMessages           string // 机器人是否可以置顶消息
	BotCanManageTopics          string // 机器人是否可以管理话题
	BotStatus                   string // 机器人状态:    active, left, kicked
	BotLeftAt                   string // 机器人离开时间
	FirstMessageId              string // 第一条消息ID
	FirstMessageDate            string // 第一条消息时间
	LastMessageId               string // 最后一条消息ID
	LastMessageDate             string // 最后一条消息时间
	TotalMessages               string // 总消息数
	ActiveUsersCount            string // 活跃用户数
	IsForum                     string // 是否是论坛群组
	ActiveTopics                string // 活跃话题列表(JSON格式)
	ExtraData                   string // 额外数据(JSON格式)
	CreatedAt                   string // 记录创建时间
	UpdatedAt                   string // 记录更新时间
}

// telegramGroupsColumns holds the columns for the table telegram_groups.
var telegramGroupsColumns = TelegramGroupsColumns{
	Id:                          "id",
	ChatId:                      "chat_id",
	TenantId:                    "tenant_id",
	Type:                        "type",
	Title:                       "title",
	Username:                    "username",
	Description:                 "description",
	InviteLink:                  "invite_link",
	PhotoFileId:                 "photo_file_id",
	PhotoUrl:                    "photo_url",
	MemberCount:                 "member_count",
	AllMembersAreAdministrators: "all_members_are_administrators",
	HasProtectedContent:         "has_protected_content",
	HasVisibleHistory:           "has_visible_history",
	CanSetStickerSet:            "can_set_sticker_set",
	StickerSetName:              "sticker_set_name",
	LinkedChatId:                "linked_chat_id",
	Location:                    "location",
	SlowModeDelay:               "slow_mode_delay",
	MessageAutoDeleteTime:       "message_auto_delete_time",
	Permissions:                 "permissions",
	BotAddedByUserId:            "bot_added_by_user_id",
	BotAddedByUsername:          "bot_added_by_username",
	BotAddedAt:                  "bot_added_at",
	BotIsAdmin:                  "bot_is_admin",
	BotCanManageChat:            "bot_can_manage_chat",
	BotCanDeleteMessages:        "bot_can_delete_messages",
	BotCanManageVideoChats:      "bot_can_manage_video_chats",
	BotCanRestrictMembers:       "bot_can_restrict_members",
	BotCanPromoteMembers:        "bot_can_promote_members",
	BotCanChangeInfo:            "bot_can_change_info",
	BotCanInviteUsers:           "bot_can_invite_users",
	BotCanPinMessages:           "bot_can_pin_messages",
	BotCanManageTopics:          "bot_can_manage_topics",
	BotStatus:                   "bot_status",
	BotLeftAt:                   "bot_left_at",
	FirstMessageId:              "first_message_id",
	FirstMessageDate:            "first_message_date",
	LastMessageId:               "last_message_id",
	LastMessageDate:             "last_message_date",
	TotalMessages:               "total_messages",
	ActiveUsersCount:            "active_users_count",
	IsForum:                     "is_forum",
	ActiveTopics:                "active_topics",
	ExtraData:                   "extra_data",
	CreatedAt:                   "created_at",
	UpdatedAt:                   "updated_at",
}

// NewTelegramGroupsDao creates and returns a new DAO object for table data access.
func NewTelegramGroupsDao(handlers ...gdb.ModelHandler) *TelegramGroupsDao {
	return &TelegramGroupsDao{
		group:    "default",
		table:    "telegram_groups",
		columns:  telegramGroupsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TelegramGroupsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TelegramGroupsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TelegramGroupsDao) Columns() TelegramGroupsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TelegramGroupsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TelegramGroupsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TelegramGroupsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
