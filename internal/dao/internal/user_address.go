// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserAddressDao is the data access object for the table user_address.
type UserAddressDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserAddressColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserAddressColumns defines and stores column names for the table user_address.
type UserAddressColumns struct {
	UserAddressId   string //
	TokenId         string // 币种ID
	TenantId        string // 租户 id
	UserId          string // 用户id
	Lable           string // 备注
	Name            string // 币种
	Chan            string // 链
	Address         string // 地址
	QrCodeUrl       string // 二维码url
	CreatedAt       string //
	UpdatedAt       string //
	Type            string //
	PaybotAddressId string // PayBot系统中的地址ID
	IsReused        string // 是否复用地址：0-新生成，1-复用
	QrCodeData      string // 二维码数据（Base64格式）
	Status          string // 地址状态
	DepositCount    string // 充值次数统计
	LastDepositAt   string // 最后一次充值时间
}

// userAddressColumns holds the columns for the table user_address.
var userAddressColumns = UserAddressColumns{
	UserAddressId:   "user_address_id",
	TokenId:         "token_id",
	TenantId:        "tenant_id",
	UserId:          "user_id",
	Lable:           "lable",
	Name:            "name",
	Chan:            "chan",
	Address:         "address",
	QrCodeUrl:       "qr_code_url",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	Type:            "type",
	PaybotAddressId: "paybot_address_id",
	IsReused:        "is_reused",
	QrCodeData:      "qr_code_data",
	Status:          "status",
	DepositCount:    "deposit_count",
	LastDepositAt:   "last_deposit_at",
}

// NewUserAddressDao creates and returns a new DAO object for table data access.
func NewUserAddressDao(handlers ...gdb.ModelHandler) *UserAddressDao {
	return &UserAddressDao{
		group:    "default",
		table:    "user_address",
		columns:  userAddressColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserAddressDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserAddressDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserAddressDao) Columns() UserAddressColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserAddressDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserAddressDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserAddressDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
