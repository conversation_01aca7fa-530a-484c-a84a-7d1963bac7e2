// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RedPacketsInforsDao is the data access object for the table red_packets_infors.
type RedPacketsInforsDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  RedPacketsInforsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// RedPacketsInforsColumns defines and stores column names for the table red_packets_infors.
type RedPacketsInforsColumns struct {
	RedPacketId     string // 红包 ID (主键)
	TenantId        string // 租户 id
	NforId          string // 红包唯一id
	InlineMessageId string // 内联消息ID（内联模式）
	CreatedAt       string // 创建时间
}

// redPacketsInforsColumns holds the columns for the table red_packets_infors.
var redPacketsInforsColumns = RedPacketsInforsColumns{
	RedPacketId:     "red_packet_id",
	TenantId:        "tenant_id",
	NforId:          "nfor_id",
	InlineMessageId: "inline_message_id",
	CreatedAt:       "created_at",
}

// NewRedPacketsInforsDao creates and returns a new DAO object for table data access.
func NewRedPacketsInforsDao(handlers ...gdb.ModelHandler) *RedPacketsInforsDao {
	return &RedPacketsInforsDao{
		group:    "default",
		table:    "red_packets_infors",
		columns:  redPacketsInforsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RedPacketsInforsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RedPacketsInforsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RedPacketsInforsDao) Columns() RedPacketsInforsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RedPacketsInforsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RedPacketsInforsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RedPacketsInforsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
