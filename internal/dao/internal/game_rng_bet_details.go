// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameRngBetDetailsDao is the data access object for the table game_rng_bet_details.
type GameRngBetDetailsDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  GameRngBetDetailsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// GameRngBetDetailsColumns defines and stores column names for the table game_rng_bet_details.
type GameRngBetDetailsColumns struct {
	Id                string //
	TenantId          string // 租户 id
	Username          string // 游戏账号的登录名
	UserId            string // 用户ID
	BetAmount         string // 投注金额
	ValidBetAmount    string // 有效投注金额
	WinAmount         string // 赢金额
	NetPnl            string // 净输赢 (正数为赢，负数为输)
	Currency          string // 币别 (例如: CNY, USD)
	GameCode          string // 游戏代码
	GameName          string // 游戏名称 (RNG/FISH特有字段)
	ProductType       string // 产品类别 (16=RNG, 其他=FISH)
	GameCategory      string // 游戏类别 (RNG, FISH)
	BetOrderNo        string // 投注订单编号
	SessionId         string // 会话标识
	BetStatus         string // 投注状态 (来自三方的status, e.g., C, V, R)
	BetType           string // 投注类型 (来自三方的type, e.g., R for Regular)
	BetTime           string // 投注时间
	TransactionTime   string // 交易时间
	SettlementTime    string // 结算时间 (来自三方的endDate)
	AdditionalDetails string // 产品追加投注详细信息 (gamehall, gamePlat, status, createTime, endRoundTime, balance)
	ApiStatus         string // API响应状态码
	ApiErrorDesc      string // API错误描述
	CreatedAt         string // 记录创建时间
	UpdatedAt         string // 最后更新时间
}

// gameRngBetDetailsColumns holds the columns for the table game_rng_bet_details.
var gameRngBetDetailsColumns = GameRngBetDetailsColumns{
	Id:                "id",
	TenantId:          "tenant_id",
	Username:          "username",
	UserId:            "user_id",
	BetAmount:         "bet_amount",
	ValidBetAmount:    "valid_bet_amount",
	WinAmount:         "win_amount",
	NetPnl:            "net_pnl",
	Currency:          "currency",
	GameCode:          "game_code",
	GameName:          "game_name",
	ProductType:       "product_type",
	GameCategory:      "game_category",
	BetOrderNo:        "bet_order_no",
	SessionId:         "session_id",
	BetStatus:         "bet_status",
	BetType:           "bet_type",
	BetTime:           "bet_time",
	TransactionTime:   "transaction_time",
	SettlementTime:    "settlement_time",
	AdditionalDetails: "additional_details",
	ApiStatus:         "api_status",
	ApiErrorDesc:      "api_error_desc",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
}

// NewGameRngBetDetailsDao creates and returns a new DAO object for table data access.
func NewGameRngBetDetailsDao(handlers ...gdb.ModelHandler) *GameRngBetDetailsDao {
	return &GameRngBetDetailsDao{
		group:    "default",
		table:    "game_rng_bet_details",
		columns:  gameRngBetDetailsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameRngBetDetailsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameRngBetDetailsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameRngBetDetailsDao) Columns() GameRngBetDetailsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameRngBetDetailsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameRngBetDetailsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameRngBetDetailsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
