// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameInfoDao is the data access object for the table game_info.
type GameInfoDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  GameInfoColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// GameInfoColumns defines and stores column names for the table game_info.
type GameInfoColumns struct {
	Id                  string // 游戏信息ID
	ProviderCode        string // 游戏提供商代码
	GameCode            string // 游戏代码
	GameName            string // 游戏名称
	GameNameEn          string // 游戏英文名称
	Category            string // 游戏分类
	Subcategory         string // 游戏子分类
	Status              string // 游戏状态
	MinBet              string // 最小投注金额
	MaxBet              string // 最大投注金额
	MaxWin              string // 最大赢取金额
	RtpPercentage       string // RTP百分比
	Volatility          string // 波动性
	SupportedCurrencies string // 支持的货币（JSON数组）
	SupportedLanguages  string // 支持的语言（JSON数组）
	GameThumbnail       string // 游戏缩略图URL
	GameIcon            string // 游戏图标URL
	GameDescription     string // 游戏描述
	Features            string // 游戏特色功能（JSON数组）
	Tags                string // 游戏标签（JSON数组）
	IsMobileSupported   string // 是否支持移动端
	IsDemoAvailable     string // 是否提供试玩模式
	SortOrder           string // 排序顺序
	IsFeatured          string // 是否为推荐游戏
	IsNew               string // 是否为新游戏
	LaunchCount         string // 启动次数
	LastLaunchedAt      string // 最后启动时间
	Metadata            string // 额外元数据
	CreatedAt           string // 创建时间
	UpdatedAt           string // 更新时间
	DeletedAt           string // 删除时间
}

// gameInfoColumns holds the columns for the table game_info.
var gameInfoColumns = GameInfoColumns{
	Id:                  "id",
	ProviderCode:        "provider_code",
	GameCode:            "game_code",
	GameName:            "game_name",
	GameNameEn:          "game_name_en",
	Category:            "category",
	Subcategory:         "subcategory",
	Status:              "status",
	MinBet:              "min_bet",
	MaxBet:              "max_bet",
	MaxWin:              "max_win",
	RtpPercentage:       "rtp_percentage",
	Volatility:          "volatility",
	SupportedCurrencies: "supported_currencies",
	SupportedLanguages:  "supported_languages",
	GameThumbnail:       "game_thumbnail",
	GameIcon:            "game_icon",
	GameDescription:     "game_description",
	Features:            "features",
	Tags:                "tags",
	IsMobileSupported:   "is_mobile_supported",
	IsDemoAvailable:     "is_demo_available",
	SortOrder:           "sort_order",
	IsFeatured:          "is_featured",
	IsNew:               "is_new",
	LaunchCount:         "launch_count",
	LastLaunchedAt:      "last_launched_at",
	Metadata:            "metadata",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	DeletedAt:           "deleted_at",
}

// NewGameInfoDao creates and returns a new DAO object for table data access.
func NewGameInfoDao(handlers ...gdb.ModelHandler) *GameInfoDao {
	return &GameInfoDao{
		group:    "default",
		table:    "game_info",
		columns:  gameInfoColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameInfoDao) Columns() GameInfoColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameInfoDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
