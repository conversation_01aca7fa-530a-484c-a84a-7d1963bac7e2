// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameCatalogDao is the data access object for the table game_catalog.
type GameCatalogDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  GameCatalogColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// GameCatalogColumns defines and stores column names for the table game_catalog.
type GameCatalogColumns struct {
	Id            string //
	TcgGameCode   string // TC Gaming游戏代码
	GameName      string // 游戏显示名称
	ProductCode   string // 产品代码
	ProductType   string // 产品类型
	Platform      string // 支持的平台
	GameType      string // 游戏类型分类
	DisplayStatus string // 显示状态 (1=激活, 0=非激活)
	TrialSupport  string // 是否支持试玩模式
	Sort          string // 排序
	CreatedAt     string //
	UpdatedAt     string //
}

// gameCatalogColumns holds the columns for the table game_catalog.
var gameCatalogColumns = GameCatalogColumns{
	Id:            "id",
	TcgGameCode:   "tcg_game_code",
	GameName:      "game_name",
	ProductCode:   "product_code",
	ProductType:   "product_type",
	Platform:      "platform",
	GameType:      "game_type",
	DisplayStatus: "display_status",
	TrialSupport:  "trial_support",
	Sort:          "sort",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewGameCatalogDao creates and returns a new DAO object for table data access.
func NewGameCatalogDao(handlers ...gdb.ModelHandler) *GameCatalogDao {
	return &GameCatalogDao{
		group:    "default",
		table:    "game_catalog",
		columns:  gameCatalogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameCatalogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameCatalogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameCatalogDao) Columns() GameCatalogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameCatalogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameCatalogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameCatalogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
