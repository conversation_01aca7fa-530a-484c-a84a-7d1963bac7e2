// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdminConfigCategoriesDao is the data access object for the table admin_config_categories.
type AdminConfigCategoriesDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  AdminConfigCategoriesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// AdminConfigCategoriesColumns defines and stores column names for the table admin_config_categories.
type AdminConfigCategoriesColumns struct {
	Id          string // 分类唯一标识符 (例如 UUID)
	Name        string // 分类显示名称
	CategoryKey string // 分类键 (唯一, 创建后不可修改)
	SortOrder   string // 排序顺序
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// adminConfigCategoriesColumns holds the columns for the table admin_config_categories.
var adminConfigCategoriesColumns = AdminConfigCategoriesColumns{
	Id:          "id",
	Name:        "name",
	CategoryKey: "category_key",
	SortOrder:   "sort_order",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewAdminConfigCategoriesDao creates and returns a new DAO object for table data access.
func NewAdminConfigCategoriesDao(handlers ...gdb.ModelHandler) *AdminConfigCategoriesDao {
	return &AdminConfigCategoriesDao{
		group:    "default",
		table:    "admin_config_categories",
		columns:  adminConfigCategoriesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdminConfigCategoriesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdminConfigCategoriesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdminConfigCategoriesDao) Columns() AdminConfigCategoriesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdminConfigCategoriesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdminConfigCategoriesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdminConfigCategoriesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
