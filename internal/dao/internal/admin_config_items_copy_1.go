// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdminConfigItemsCopy1Dao is the data access object for the table admin_config_items_copy1.
type AdminConfigItemsCopy1Dao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  AdminConfigItemsCopy1Columns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// AdminConfigItemsCopy1Columns defines and stores column names for the table admin_config_items_copy1.
type AdminConfigItemsCopy1Columns struct {
	Id          string // 配置项唯一标识符 (例如 UUID)
	CategoryId  string // 所属分类 ID
	Key         string // 配置键 (全局唯一, 包含分类前缀)
	Value       string // 配置值 (以字符串存储)
	ValueType   string // 配置值类型 (text, number, boolean, json etc.)
	Description string // 描述 (可选)
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// adminConfigItemsCopy1Columns holds the columns for the table admin_config_items_copy1.
var adminConfigItemsCopy1Columns = AdminConfigItemsCopy1Columns{
	Id:          "id",
	CategoryId:  "category_id",
	Key:         "key",
	Value:       "value",
	ValueType:   "value_type",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewAdminConfigItemsCopy1Dao creates and returns a new DAO object for table data access.
func NewAdminConfigItemsCopy1Dao(handlers ...gdb.ModelHandler) *AdminConfigItemsCopy1Dao {
	return &AdminConfigItemsCopy1Dao{
		group:    "default",
		table:    "admin_config_items_copy1",
		columns:  adminConfigItemsCopy1Columns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdminConfigItemsCopy1Dao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdminConfigItemsCopy1Dao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdminConfigItemsCopy1Dao) Columns() AdminConfigItemsCopy1Columns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdminConfigItemsCopy1Dao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdminConfigItemsCopy1Dao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdminConfigItemsCopy1Dao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
