// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TenantsDao is the data access object for the table tenants.
type TenantsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TenantsColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TenantsColumns defines and stores column names for the table tenants.
type TenantsColumns struct {
	TenantId                  string // 租户唯一ID
	Username                  string // 租户登录用户名
	PasswordHash              string // 加密后的登录密码
	TenantName                string // 姓名或昵称
	Email                     string // 电子邮箱
	BusinessName              string // 公司/业务注册名称 (可选)
	TelegramAccount           string // telegram 账户
	TelegramBotName           string // 机器人名称
	TelegramBotToken          string // 机器人 token
	Level                     string // 代理级别
	Status                    string // 账户状态 (0-禁用, 1-启用)
	InvitationCode            string // 专属邀请码 (用于下级注册)
	GoogleAuthenticatorSecret string // Google Authenticator 的秘钥 (用于2FA)
	DatabaseCreatedAt         string // 数据库创建时间
	CreatedAt                 string // 创建时间
	UpdatedAt                 string // 最后更新时间
	DeletedAt                 string // 软删除时间
	Group                     string // 官方群组
	Customer                  string // 客服
	BettingBonusRate          string // 下注反水比例
	TelegramGroupsId          string // telegram_groups
}

// tenantsColumns holds the columns for the table tenants.
var tenantsColumns = TenantsColumns{
	TenantId:                  "tenant_id",
	Username:                  "username",
	PasswordHash:              "password_hash",
	TenantName:                "tenant_name",
	Email:                     "email",
	BusinessName:              "business_name",
	TelegramAccount:           "telegram_account",
	TelegramBotName:           "telegram_bot_name",
	TelegramBotToken:          "telegram_bot_token",
	Level:                     "level",
	Status:                    "status",
	InvitationCode:            "invitation_code",
	GoogleAuthenticatorSecret: "google_authenticator_secret",
	DatabaseCreatedAt:         "database_created_at",
	CreatedAt:                 "created_at",
	UpdatedAt:                 "updated_at",
	DeletedAt:                 "deleted_at",
	Group:                     "group",
	Customer:                  "customer",
	BettingBonusRate:          "betting_bonus_rate",
	TelegramGroupsId:          "telegram_groups_id",
}

// NewTenantsDao creates and returns a new DAO object for table data access.
func NewTenantsDao(handlers ...gdb.ModelHandler) *TenantsDao {
	return &TenantsDao{
		group:    "default",
		table:    "tenants",
		columns:  tenantsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TenantsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TenantsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TenantsDao) Columns() TenantsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TenantsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TenantsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TenantsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
