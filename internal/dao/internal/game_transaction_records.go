// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameTransactionRecordsDao is the data access object for the table game_transaction_records.
type GameTransactionRecordsDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  GameTransactionRecordsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// GameTransactionRecordsColumns defines and stores column names for the table game_transaction_records.
type GameTransactionRecordsColumns struct {
	Id                    string // 交易记录ID
	TenantId              string // 租户 id
	TransactionId         string // 交易唯一标识符
	UserId                string // 用户ID
	SnapshotGameName      string // 冗余字段：游戏名称快照
	SnapshotGameType      string // 冗余字段：游戏类型快照
	ProviderCode          string // 游戏提供商代码
	GameCode              string // 提供商返回的游戏代码 (e.g., tcg_game_code)
	SessionId             string // 会话ID
	Type                  string // 交易类型
	Status                string // 交易状态
	Currency              string // 货币类型
	Amount                string // 交易金额
	WinAmount             string // 赢取金额
	NetAmount             string // 净金额（输赢差值）
	ProviderTransactionId string // 提供商交易ID
	ReferenceId           string // 关联交易ID
	RoundId               string // 游戏轮次ID
	BetId                 string // 投注ID
	CommissionStatus      string // 佣金处理状态
	BettingBonusStatus    string // 投注反水状态
	CreatedAt             string // 创建时间
	UpdatedAt             string // 更新时间
}

// gameTransactionRecordsColumns holds the columns for the table game_transaction_records.
var gameTransactionRecordsColumns = GameTransactionRecordsColumns{
	Id:                    "id",
	TenantId:              "tenant_id",
	TransactionId:         "transaction_id",
	UserId:                "user_id",
	SnapshotGameName:      "snapshot_game_name",
	SnapshotGameType:      "snapshot_game_type",
	ProviderCode:          "provider_code",
	GameCode:              "game_code",
	SessionId:             "session_id",
	Type:                  "type",
	Status:                "status",
	Currency:              "currency",
	Amount:                "amount",
	WinAmount:             "win_amount",
	NetAmount:             "net_amount",
	ProviderTransactionId: "provider_transaction_id",
	ReferenceId:           "reference_id",
	RoundId:               "round_id",
	BetId:                 "bet_id",
	CommissionStatus:      "commission_status",
	BettingBonusStatus:    "betting_bonus_status",
	CreatedAt:             "created_at",
	UpdatedAt:             "updated_at",
}

// NewGameTransactionRecordsDao creates and returns a new DAO object for table data access.
func NewGameTransactionRecordsDao(handlers ...gdb.ModelHandler) *GameTransactionRecordsDao {
	return &GameTransactionRecordsDao{
		group:    "default",
		table:    "game_transaction_records",
		columns:  gameTransactionRecordsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameTransactionRecordsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameTransactionRecordsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameTransactionRecordsDao) Columns() GameTransactionRecordsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameTransactionRecordsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameTransactionRecordsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameTransactionRecordsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
