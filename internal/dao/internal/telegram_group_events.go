// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TelegramGroupEventsDao is the data access object for the table telegram_group_events.
type TelegramGroupEventsDao struct {
	table    string                     // table is the underlying table name of the DAO.
	group    string                     // group is the database configuration group name of the current DAO.
	columns  TelegramGroupEventsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler         // handlers for customized model modification.
}

// TelegramGroupEventsColumns defines and stores column names for the table telegram_group_events.
type TelegramGroupEventsColumns struct {
	Id        string // 主键ID
	GroupId   string // 关联telegram_groups表ID
	ChatId    string // Telegram群组ID
	TenantId  string // 租户ID
	EventType string // 事件类型: bot_added,    bot_removed, bot_promoted, member_joined, member_left, etc
	UserId    string // 相关用户ID
	Username  string // 相关用户名
	EventData string // 事件详细数据(JSON格式)
	CreatedAt string // 事件时间
}

// telegramGroupEventsColumns holds the columns for the table telegram_group_events.
var telegramGroupEventsColumns = TelegramGroupEventsColumns{
	Id:        "id",
	GroupId:   "group_id",
	ChatId:    "chat_id",
	TenantId:  "tenant_id",
	EventType: "event_type",
	UserId:    "user_id",
	Username:  "username",
	EventData: "event_data",
	CreatedAt: "created_at",
}

// NewTelegramGroupEventsDao creates and returns a new DAO object for table data access.
func NewTelegramGroupEventsDao(handlers ...gdb.ModelHandler) *TelegramGroupEventsDao {
	return &TelegramGroupEventsDao{
		group:    "default",
		table:    "telegram_group_events",
		columns:  telegramGroupEventsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TelegramGroupEventsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TelegramGroupEventsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TelegramGroupEventsDao) Columns() TelegramGroupEventsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TelegramGroupEventsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TelegramGroupEventsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TelegramGroupEventsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
