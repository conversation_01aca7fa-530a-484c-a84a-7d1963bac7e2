// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserAssDao is the data access object for the table user_ass.
type UserAssDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserAssColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserAssColumns defines and stores column names for the table user_ass.
type UserAssColumns struct {
	Id         string // 备用账户主键ID
	AUserId    string // 主账户
	BUserId    string // 备账户
	TenantId   string // 租户 id
	VerifiedAt string // 验证时间
	CreatedAt  string // 创建时间
	UpdatedAt  string // 最后更新时间
	DeletedAt  string // 软删除的时间戳
}

// userAssColumns holds the columns for the table user_ass.
var userAssColumns = UserAssColumns{
	Id:         "id",
	AUserId:    "a_user_id",
	BUserId:    "b_user_id",
	TenantId:   "tenant_id",
	VerifiedAt: "verified_at",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
	DeletedAt:  "deleted_at",
}

// NewUserAssDao creates and returns a new DAO object for table data access.
func NewUserAssDao(handlers ...gdb.ModelHandler) *UserAssDao {
	return &UserAssDao{
		group:    "default",
		table:    "user_ass",
		columns:  userAssColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserAssDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserAssDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserAssDao) Columns() UserAssColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserAssDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserAssDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserAssDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
