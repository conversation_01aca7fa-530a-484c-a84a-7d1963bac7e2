// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VPaybotAuthOrdersStatsDao is the data access object for the table v_paybot_auth_orders_stats.
type VPaybotAuthOrdersStatsDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  VPaybotAuthOrdersStatsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// VPaybotAuthOrdersStatsColumns defines and stores column names for the table v_paybot_auth_orders_stats.
type VPaybotAuthOrdersStatsColumns struct {
	UserAccount  string // 用户账户标识符（关联users.account）
	OrderType    string // 订单类型：add-加款，deduct-扣款
	TokenSymbol  string // 代币符号（如USDT）
	Status       string // 订单状态
	OrderCount   string //
	TotalAmount  string //
	AvgAmount    string //
	FirstOrderAt string // 创建时间
	LastOrderAt  string // 创建时间
}

// vPaybotAuthOrdersStatsColumns holds the columns for the table v_paybot_auth_orders_stats.
var vPaybotAuthOrdersStatsColumns = VPaybotAuthOrdersStatsColumns{
	UserAccount:  "user_account",
	OrderType:    "order_type",
	TokenSymbol:  "token_symbol",
	Status:       "status",
	OrderCount:   "order_count",
	TotalAmount:  "total_amount",
	AvgAmount:    "avg_amount",
	FirstOrderAt: "first_order_at",
	LastOrderAt:  "last_order_at",
}

// NewVPaybotAuthOrdersStatsDao creates and returns a new DAO object for table data access.
func NewVPaybotAuthOrdersStatsDao(handlers ...gdb.ModelHandler) *VPaybotAuthOrdersStatsDao {
	return &VPaybotAuthOrdersStatsDao{
		group:    "default",
		table:    "v_paybot_auth_orders_stats",
		columns:  vPaybotAuthOrdersStatsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VPaybotAuthOrdersStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VPaybotAuthOrdersStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VPaybotAuthOrdersStatsDao) Columns() VPaybotAuthOrdersStatsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VPaybotAuthOrdersStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VPaybotAuthOrdersStatsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VPaybotAuthOrdersStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
