// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PaybotAuthOrdersDao is the data access object for the table paybot_auth_orders.
type PaybotAuthOrdersDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  PaybotAuthOrdersColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// PaybotAuthOrdersColumns defines and stores column names for the table paybot_auth_orders.
type PaybotAuthOrdersColumns struct {
	Id                 string // 主键ID
	PaybotOrderNo      string // PayBot系统订单号
	TenantId           string // 租户 id
	MerchantOrderNo    string // 商户订单号
	UserAccount        string // 用户账户标识符（关联users.account）
	UserId             string // 用户ID（关联users.id）
	OrderType          string // 订单类型：add-加款，deduct-扣款
	TokenSymbol        string // 代币符号（如USDT）
	Amount             string // 交易金额
	AuthReason         string // 授权原因
	Status             string // 订单状态
	CallbackBot        string // 回调机器人
	CallbackStatus     string // 回调状态
	ExpireAt           string // 过期时间
	CompletedAt        string // 完成时间
	ErrorMessage       string // 错误信息
	MessageId          string // 内联消息 ID，用于后续编辑
	CreatedAt          string // 创建时间
	UpdatedAt          string // 更新时间
	NotificationSent   string // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt string // 通知发送时间
}

// paybotAuthOrdersColumns holds the columns for the table paybot_auth_orders.
var paybotAuthOrdersColumns = PaybotAuthOrdersColumns{
	Id:                 "id",
	PaybotOrderNo:      "paybot_order_no",
	TenantId:           "tenant_id",
	MerchantOrderNo:    "merchant_order_no",
	UserAccount:        "user_account",
	UserId:             "user_id",
	OrderType:          "order_type",
	TokenSymbol:        "token_symbol",
	Amount:             "amount",
	AuthReason:         "auth_reason",
	Status:             "status",
	CallbackBot:        "callback_bot",
	CallbackStatus:     "callback_status",
	ExpireAt:           "expire_at",
	CompletedAt:        "completed_at",
	ErrorMessage:       "error_message",
	MessageId:          "message_id",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	NotificationSent:   "notification_sent",
	NotificationSentAt: "notification_sent_at",
}

// NewPaybotAuthOrdersDao creates and returns a new DAO object for table data access.
func NewPaybotAuthOrdersDao(handlers ...gdb.ModelHandler) *PaybotAuthOrdersDao {
	return &PaybotAuthOrdersDao{
		group:    "default",
		table:    "paybot_auth_orders",
		columns:  paybotAuthOrdersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *PaybotAuthOrdersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *PaybotAuthOrdersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *PaybotAuthOrdersDao) Columns() PaybotAuthOrdersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *PaybotAuthOrdersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *PaybotAuthOrdersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *PaybotAuthOrdersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
