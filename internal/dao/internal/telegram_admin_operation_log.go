// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TelegramAdminOperationLogDao is the data access object for the table telegram_admin_operation_log.
type TelegramAdminOperationLogDao struct {
	table    string                           // table is the underlying table name of the DAO.
	group    string                           // group is the database configuration group name of the current DAO.
	columns  TelegramAdminOperationLogColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler               // handlers for customized model modification.
}

// TelegramAdminOperationLogColumns defines and stores column names for the table telegram_admin_operation_log.
type TelegramAdminOperationLogColumns struct {
	Id                            string // 日志ID
	TenantId                      string // 租户ID
	AdminTelegramId               string // 管理员Telegram ID
	AdminName                     string // 管理员名称
	AdminUsername                 string // 管理员用户名
	OperationType                 string // 操作类型(balance/flow/withdrawal/user/system)
	OperationAction               string // 具体操作动作(balance_increase/balance_decrease/flow_increase/flow_decrease/withdraw_approve/withdraw_reject/withdraw_ban/user_ban/user_unban)
	TargetUserId                  string // 目标用户ID
	TargetTelegramId              string // 目标用户Telegram ID
	TargetUsername                string // 目标用户名
	OperationData                 string // 操作数据(JSON格式)
	BeforeValue                   string // 操作前的值
	AfterValue                    string // 操作后的值
	Amount                        string // 涉及金额
	Currency                      string // 币种
	Description                   string // 操作描述/备注
	IpAddress                     string // 操作IP地址
	UserAgent                     string // 用户代理
	Status                        string // 操作状态(1-成功 0-失败)
	ErrorMessage                  string // 错误信息
	Duration                      string // 操作耗时(毫秒)
	CreatedAt                     string // 创建时间
	UpdatedAt                     string // 更新时间
	IsCustomerServiceNotification string // 是否对客服群进行通知
}

// telegramAdminOperationLogColumns holds the columns for the table telegram_admin_operation_log.
var telegramAdminOperationLogColumns = TelegramAdminOperationLogColumns{
	Id:                            "id",
	TenantId:                      "tenant_id",
	AdminTelegramId:               "admin_telegram_id",
	AdminName:                     "admin_name",
	AdminUsername:                 "admin_username",
	OperationType:                 "operation_type",
	OperationAction:               "operation_action",
	TargetUserId:                  "target_user_id",
	TargetTelegramId:              "target_telegram_id",
	TargetUsername:                "target_username",
	OperationData:                 "operation_data",
	BeforeValue:                   "before_value",
	AfterValue:                    "after_value",
	Amount:                        "amount",
	Currency:                      "currency",
	Description:                   "description",
	IpAddress:                     "ip_address",
	UserAgent:                     "user_agent",
	Status:                        "status",
	ErrorMessage:                  "error_message",
	Duration:                      "duration",
	CreatedAt:                     "created_at",
	UpdatedAt:                     "updated_at",
	IsCustomerServiceNotification: "is_customer_service_notification",
}

// NewTelegramAdminOperationLogDao creates and returns a new DAO object for table data access.
func NewTelegramAdminOperationLogDao(handlers ...gdb.ModelHandler) *TelegramAdminOperationLogDao {
	return &TelegramAdminOperationLogDao{
		group:    "default",
		table:    "telegram_admin_operation_log",
		columns:  telegramAdminOperationLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TelegramAdminOperationLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TelegramAdminOperationLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TelegramAdminOperationLogDao) Columns() TelegramAdminOperationLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TelegramAdminOperationLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TelegramAdminOperationLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TelegramAdminOperationLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
