// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameSessionsDao is the data access object for the table game_sessions.
type GameSessionsDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  GameSessionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// GameSessionsColumns defines and stores column names for the table game_sessions.
type GameSessionsColumns struct {
	Id             string // 会话ID
	SessionId      string // 会话唯一标识符
	UserId         string // 用户ID
	ProviderCode   string // 游戏提供商代码
	GameCode       string // 游戏代码
	Currency       string // 货币类型
	InitialBalance string // 初始余额
	CurrentBalance string // 当前余额
	Status         string // 会话状态
	IpAddress      string // 用户IP地址
	UserAgent      string // 用户代理
	LoginUrl       string // 游戏登录URL
	SessionToken   string // 会话令牌
	ExpiresAt      string // 过期时间
	LastActivityAt string // 最后活动时间
	Metadata       string // 会话元数据
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
}

// gameSessionsColumns holds the columns for the table game_sessions.
var gameSessionsColumns = GameSessionsColumns{
	Id:             "id",
	SessionId:      "session_id",
	UserId:         "user_id",
	ProviderCode:   "provider_code",
	GameCode:       "game_code",
	Currency:       "currency",
	InitialBalance: "initial_balance",
	CurrentBalance: "current_balance",
	Status:         "status",
	IpAddress:      "ip_address",
	UserAgent:      "user_agent",
	LoginUrl:       "login_url",
	SessionToken:   "session_token",
	ExpiresAt:      "expires_at",
	LastActivityAt: "last_activity_at",
	Metadata:       "metadata",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
}

// NewGameSessionsDao creates and returns a new DAO object for table data access.
func NewGameSessionsDao(handlers ...gdb.ModelHandler) *GameSessionsDao {
	return &GameSessionsDao{
		group:    "default",
		table:    "game_sessions",
		columns:  gameSessionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameSessionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameSessionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameSessionsDao) Columns() GameSessionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameSessionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameSessionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameSessionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
