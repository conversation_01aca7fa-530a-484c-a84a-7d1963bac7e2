// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GamePlayerBetSummaryDao is the data access object for the table game_player_bet_summary.
type GamePlayerBetSummaryDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  GamePlayerBetSummaryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// GamePlayerBetSummaryColumns defines and stores column names for the table game_player_bet_summary.
type GamePlayerBetSummaryColumns struct {
	Id                 string //
	TenantId           string // 租户 id
	Username           string // 玩家用户名
	SummaryDate        string // 汇总日期
	GameCategory       string // 游戏类别 (LIVE, RNG, FISH)
	LiveBetCount       string // Live游戏投注次数
	LiveTotalBetAmount string // Live游戏总投注金额
	LiveTotalValidBet  string // Live游戏总有效投注
	LiveTotalWinAmount string // Live游戏总赢金额
	LiveTotalNetPnl    string // Live游戏总净输赢
	RngBetCount        string // RNG游戏投注次数
	RngTotalBetAmount  string // RNG游戏总投注金额
	RngTotalValidBet   string // RNG游戏总有效投注
	RngTotalWinAmount  string // RNG游戏总赢金额
	RngTotalNetPnl     string // RNG游戏总净输赢
	TotalBetCount      string // 总投注次数
	TotalBetAmount     string // 总投注金额
	TotalValidBet      string // 总有效投注
	TotalWinAmount     string // 总赢金额
	TotalNetPnl        string // 总净输赢
	CreatedAt          string // 记录创建时间
	UpdatedAt          string // 最后更新时间
}

// gamePlayerBetSummaryColumns holds the columns for the table game_player_bet_summary.
var gamePlayerBetSummaryColumns = GamePlayerBetSummaryColumns{
	Id:                 "id",
	TenantId:           "tenant_id",
	Username:           "username",
	SummaryDate:        "summary_date",
	GameCategory:       "game_category",
	LiveBetCount:       "live_bet_count",
	LiveTotalBetAmount: "live_total_bet_amount",
	LiveTotalValidBet:  "live_total_valid_bet",
	LiveTotalWinAmount: "live_total_win_amount",
	LiveTotalNetPnl:    "live_total_net_pnl",
	RngBetCount:        "rng_bet_count",
	RngTotalBetAmount:  "rng_total_bet_amount",
	RngTotalValidBet:   "rng_total_valid_bet",
	RngTotalWinAmount:  "rng_total_win_amount",
	RngTotalNetPnl:     "rng_total_net_pnl",
	TotalBetCount:      "total_bet_count",
	TotalBetAmount:     "total_bet_amount",
	TotalValidBet:      "total_valid_bet",
	TotalWinAmount:     "total_win_amount",
	TotalNetPnl:        "total_net_pnl",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
}

// NewGamePlayerBetSummaryDao creates and returns a new DAO object for table data access.
func NewGamePlayerBetSummaryDao(handlers ...gdb.ModelHandler) *GamePlayerBetSummaryDao {
	return &GamePlayerBetSummaryDao{
		group:    "default",
		table:    "game_player_bet_summary",
		columns:  gamePlayerBetSummaryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GamePlayerBetSummaryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GamePlayerBetSummaryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GamePlayerBetSummaryDao) Columns() GamePlayerBetSummaryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GamePlayerBetSummaryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GamePlayerBetSummaryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GamePlayerBetSummaryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
