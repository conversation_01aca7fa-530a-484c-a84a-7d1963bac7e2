// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ImportTasksDao is the data access object for the table import_tasks.
type ImportTasksDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ImportTasksColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ImportTasksColumns defines and stores column names for the table import_tasks.
type ImportTasksColumns struct {
	TaskId        string //
	Status        string //
	Progress      string //
	ProcessedRows string //
	TotalRows     string //
	ErrorMessage  string //
	CreatedAt     string //
	UpdatedAt     string //
}

// importTasksColumns holds the columns for the table import_tasks.
var importTasksColumns = ImportTasksColumns{
	TaskId:        "task_id",
	Status:        "status",
	Progress:      "progress",
	ProcessedRows: "processed_rows",
	TotalRows:     "total_rows",
	ErrorMessage:  "error_message",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewImportTasksDao creates and returns a new DAO object for table data access.
func NewImportTasksDao(handlers ...gdb.ModelHandler) *ImportTasksDao {
	return &ImportTasksDao{
		group:    "default",
		table:    "import_tasks",
		columns:  importTasksColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ImportTasksDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ImportTasksDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ImportTasksDao) Columns() ImportTasksColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ImportTasksDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ImportTasksDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ImportTasksDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
