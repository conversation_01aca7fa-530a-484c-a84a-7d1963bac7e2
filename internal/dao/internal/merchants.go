// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantsDao is the data access object for the table merchants.
type MerchantsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  MerchantsColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// MerchantsColumns defines and stores column names for the table merchants.
type MerchantsColumns struct {
	MerchantId         string // 商户内部ID (主键)
	MerchantName       string // 商户名称 (对外展示/内部识别)
	BusinessName       string // 公司/业务注册名称 (可选)
	Email              string // 商户邮箱 (用于登录和通知)
	EmailVerifiedAt    string // 邮箱验证时间
	PaymentPassword    string // 支付密码 (经过哈希处理)
	PaymentPasswordSet string // 是否设置支付密码
	AreaCode           string // 电话区号
	Phone              string // 联系电话
	ContactEmail       string // 备用联系邮箱
	WebsiteUrl         string // 商户网站URL
	Google2FaSecret    string // Google 2FA密钥
	Google2FaEnabled   string // Google 2FA是否启用
	RecoveryCodes      string // 恢复代码
	WithdrawPermission string // 提现权限
	RechargePermission string // 充值权限
	ApiPermission      string // API调用权限
	Status             string // 商户状态 (-1:待审核, 0:禁用, 1:启用)
	IsStop             string // 账户暂停状态 (0:活跃, 1:已暂停)
	Reason             string // 暂停或状态变更原因
	CallbackUrl        string // 回调URL
	WebhookSecret      string // 回调密钥
	Language           string // 语言偏好
	Timezone           string // 时区设置
	Avatar             string // 头像URL
	CurrentToken       string // 当前认证令牌
	LastLoginTime      string // 最后登录时间
	LastLoginIp        string // 最后登录IP
	LoginAttempts      string // 登录尝试次数
	LockedUntil        string // 账户锁定到期时间
	Notes              string // 内部备注/审核意见
	ApprovedAt         string // 审核通过时间
	ApprovedBy         string // 审核人ID
	CreatedAt          string // 创建时间
	UpdatedAt          string // 更新时间
	DeletedAt          string // 软删除时间
}

// merchantsColumns holds the columns for the table merchants.
var merchantsColumns = MerchantsColumns{
	MerchantId:         "merchant_id",
	MerchantName:       "merchant_name",
	BusinessName:       "business_name",
	Email:              "email",
	EmailVerifiedAt:    "email_verified_at",
	PaymentPassword:    "payment_password",
	PaymentPasswordSet: "payment_password_set",
	AreaCode:           "area_code",
	Phone:              "phone",
	ContactEmail:       "contact_email",
	WebsiteUrl:         "website_url",
	Google2FaSecret:    "google2fa_secret",
	Google2FaEnabled:   "google2fa_enabled",
	RecoveryCodes:      "recovery_codes",
	WithdrawPermission: "withdraw_permission",
	RechargePermission: "recharge_permission",
	ApiPermission:      "api_permission",
	Status:             "status",
	IsStop:             "is_stop",
	Reason:             "reason",
	CallbackUrl:        "callback_url",
	WebhookSecret:      "webhook_secret",
	Language:           "language",
	Timezone:           "timezone",
	Avatar:             "avatar",
	CurrentToken:       "current_token",
	LastLoginTime:      "last_login_time",
	LastLoginIp:        "last_login_ip",
	LoginAttempts:      "login_attempts",
	LockedUntil:        "locked_until",
	Notes:              "notes",
	ApprovedAt:         "approved_at",
	ApprovedBy:         "approved_by",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	DeletedAt:          "deleted_at",
}

// NewMerchantsDao creates and returns a new DAO object for table data access.
func NewMerchantsDao(handlers ...gdb.ModelHandler) *MerchantsDao {
	return &MerchantsDao{
		group:    "default",
		table:    "merchants",
		columns:  merchantsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantsDao) Columns() MerchantsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
