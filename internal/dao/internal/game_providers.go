// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// GameProvidersDao is the data access object for the table game_providers.
type GameProvidersDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  GameProvidersColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// GameProvidersColumns defines and stores column names for the table game_providers.
type GameProvidersColumns struct {
	Id               string // 游戏提供商唯一ID
	Code             string // 提供商代码（如TCG、PP、PG）
	Name             string // 提供商名称
	ApiUrl           string // API接口地址
	Status           string // 提供商状态
	MerchantCode     string // 商户代码
	SecretKey        string // 签名密钥
	EncryptKey       string // 加密密钥
	EncryptionMethod string // 加密方式
	TimeoutSeconds   string // API超时时间（秒）
	RetryCount       string // 重试次数
	RateLimit        string // 速率限制（每分钟请求数）
	IpWhitelist      string // IP白名单（JSON数组）
	WebhookUrl       string // 回调地址
	WebhookSecret    string // 回调验签密钥
	ConfigData       string // 额外配置数据
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
	DeletedAt        string // 删除时间
}

// gameProvidersColumns holds the columns for the table game_providers.
var gameProvidersColumns = GameProvidersColumns{
	Id:               "id",
	Code:             "code",
	Name:             "name",
	ApiUrl:           "api_url",
	Status:           "status",
	MerchantCode:     "merchant_code",
	SecretKey:        "secret_key",
	EncryptKey:       "encrypt_key",
	EncryptionMethod: "encryption_method",
	TimeoutSeconds:   "timeout_seconds",
	RetryCount:       "retry_count",
	RateLimit:        "rate_limit",
	IpWhitelist:      "ip_whitelist",
	WebhookUrl:       "webhook_url",
	WebhookSecret:    "webhook_secret",
	ConfigData:       "config_data",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
}

// NewGameProvidersDao creates and returns a new DAO object for table data access.
func NewGameProvidersDao(handlers ...gdb.ModelHandler) *GameProvidersDao {
	return &GameProvidersDao{
		group:    "default",
		table:    "game_providers",
		columns:  gameProvidersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *GameProvidersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *GameProvidersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *GameProvidersDao) Columns() GameProvidersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *GameProvidersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *GameProvidersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *GameProvidersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
