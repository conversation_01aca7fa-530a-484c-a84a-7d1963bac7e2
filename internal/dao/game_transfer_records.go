// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameTransferRecordsDao is the data access object for the table game_transfer_records.
// You can define custom methods on it to extend its functionality as needed.
type gameTransferRecordsDao struct {
	*internal.GameTransferRecordsDao
}

var (
	// GameTransferRecords is a globally accessible object for table game_transfer_records operations.
	GameTransferRecords = gameTransferRecordsDao{internal.NewGameTransferRecordsDao()}
)

// Add your custom methods and functionality below.
