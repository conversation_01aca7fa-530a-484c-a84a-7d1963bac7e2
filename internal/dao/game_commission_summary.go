// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// gameCommissionSummaryDao is the data access object for the table game_commission_summary.
// You can define custom methods on it to extend its functionality as needed.
type gameCommissionSummaryDao struct {
	*internal.GameCommissionSummaryDao
}

var (
	// GameCommissionSummary is a globally accessible object for table game_commission_summary operations.
	GameCommissionSummary = gameCommissionSummaryDao{internal.NewGameCommissionSummaryDao()}
)

// Add your custom methods and functionality below.
