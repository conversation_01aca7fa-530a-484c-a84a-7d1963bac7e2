// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// tenantsDao is the data access object for the table tenants.
// You can define custom methods on it to extend its functionality as needed.
type tenantsDao struct {
	*internal.TenantsDao
}

var (
	// Tenants is a globally accessible object for table tenants operations.
	Tenants = tenantsDao{internal.NewTenantsDao()}
)

// Add your custom methods and functionality below.

// GetTenantList 获取租户列表 (带分页、过滤、排序)
func (d *tenantsDao) GetTenantList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.Tenants, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt)
	err = m.Page(page, pageSize).OrderDesc(d.Columns().TenantId).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "查询租户列表失败")
	}
	return list, nil
}

// GetTenantCount 获取租户数量 (带过滤)
func (d *tenantsDao) GetTenantCount(ctx context.Context, condition g.Map) (total int, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt)
	total, err = m.Count()
	if err != nil {
		return 0, gerror.Wrap(err, "查询租户数量失败")
	}
	return total, nil
}

// GetTenantById 根据 ID 获取未删除的租户信息
func (d *tenantsDao) GetTenantById(ctx context.Context, tenantId int64) (tenant *entity.Tenants, err error) {
	err = d.Ctx(ctx).Where(d.Columns().TenantId, tenantId).WhereNull(d.Columns().DeletedAt).Scan(&tenant)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询租户失败, ID: %d", tenantId)
	}
	return tenant, nil
}

// GetTenantsByIds 根据 ID 列表获取未删除的租户信息
func (d *tenantsDao) GetTenantsByIds(ctx context.Context, tenantIds []int64) (tenants []*entity.Tenants, err error) {
	if len(tenantIds) == 0 {
		return []*entity.Tenants{}, nil // Return empty slice if no IDs provided
	}
	err = d.Ctx(ctx).WhereIn(d.Columns().TenantId, tenantIds).WhereNull(d.Columns().DeletedAt).Scan(&tenants)
	if err != nil {
		return nil, gerror.Wrapf(err, "根据ID列表查询租户失败, IDs: %v", tenantIds)
	}
	return tenants, nil
}

// GetTenantByUsername 根据用户名获取未删除的租户信息
func (d *tenantsDao) GetTenantByUsername(ctx context.Context, username string) (tenant *entity.Tenants, err error) {
	err = d.Ctx(ctx).Where(d.Columns().Username, username).WhereNull(d.Columns().DeletedAt).Scan(&tenant)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询租户失败, Username: %s", username)
	}
	return tenant, nil
}

// GetTenantByEmail 根据邮箱获取未删除的租户信息
func (d *tenantsDao) GetTenantByEmail(ctx context.Context, email string) (tenant *entity.Tenants, err error) {
	err = d.Ctx(ctx).Where(d.Columns().Email, email).WhereNull(d.Columns().DeletedAt).Scan(&tenant)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询租户失败, Email: %s", email)
	}
	return tenant, nil
}

// UpdateTenant 更新租户信息 (使用 g.Map)
// 注意：此方法不处理密码、层级关系等特殊字段的更新逻辑，仅更新传入 map 中的字段
func (d *tenantsDao) UpdateTenant(ctx context.Context, tenantId int64, data g.Map) (err error) {
	// 确保更新时间和排除空值
	data[d.Columns().UpdatedAt] = gtime.Now()
	_, err = d.Ctx(ctx).Data(data).Where(d.Columns().TenantId, tenantId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrapf(err, "更新租户信息失败, ID: %d", tenantId)
	}
	return nil
}

// SoftDeleteTenantsByIds 批量软删除租户 (传入事务可选)
func (d *tenantsDao) SoftDeleteTenantsByIds(ctx context.Context, tx gdb.TX, tenantIds []int64) (err error) {
	if len(tenantIds) == 0 {
		return nil // No IDs to delete
	}
	// 使用事务或直接执行
	m := d.Ctx(ctx)
	if tx != nil {
		m = m.TX(tx)
	}
	_, err = m.Data(g.Map{d.Columns().DeletedAt: gtime.Now()}).
		WhereIn(d.Columns().TenantId, tenantIds).
		WhereNull(d.Columns().DeletedAt). // Ensure we only soft-delete records not already deleted
		Update()
	if err != nil {
		return gerror.Wrapf(err, "批量软删除租户失败, IDs: %v", tenantIds)
	}
	return nil
}
