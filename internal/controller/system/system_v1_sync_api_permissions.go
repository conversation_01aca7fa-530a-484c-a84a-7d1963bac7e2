package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) SyncApiPermissions(ctx context.Context, req *v1.SyncApiPermissionsReq) (res *v1.SyncApiPermissionsRes, err error) {
	// 调用服务层方法
	result, err := service.SystemServiceInstance.SyncApiPermissions(ctx, req)

	// 如果出错，记录错误但返回空结果
	if err != nil {
		g.Log().Errorf(ctx, "同步API权限失败: %v", err)
		return &v1.SyncApiPermissionsRes{
			CreatedCount:  0,
			ExistingCount: 0,
			TotalScanned:  0,
		}, nil
	}

	return result, nil
}
