package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// EditAdminNotice implements the logic for the EditAdminNotice controller.
func (c *ControllerV1) EditAdminNotice(ctx context.Context, req *v1.EditAdminNoticeReq) (res *v1.EditAdminNoticeRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.EditAdminNotice(ctx, req)
	return
}
