package system

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
)

const (
	maxFileSize = 50 * 1024 * 1024 // 50 MB limit for general files
)

var (
	allowedFileTypes = map[string]bool{
		// Documents
		".mp4":  true,
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
		".bmp":  true,
		".tiff": true,
		".webp": true,
		".pdf":  true,
		".doc":  true,
		".docx": true,
		".xls":  true,
		".xlsx": true,
		".ppt":  true,
		".pptx": true,
		".txt":  true,
		".csv":  true,
		// Archives
		".zip": true,
		".rar": true,
		".7z":  true,
		".tar": true,
		".gz":  true,
		// Others
		".json": true,
		".xml":  true,
	}
)

// UploadFile handles the generic file upload request.
func (c *ControllerV1) UploadFile(ctx context.Context, req *v1.UploadFileReq) (res *v1.UploadFileRes, err error) {
	r := g.RequestFromCtx(ctx)
	file := r.GetUploadFile("file")
	if file == nil {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "请上传文件，使用'file'字段名称")
	}

	// 1. Validate File Extension
	fileExt := strings.ToLower(filepath.Ext(file.Filename))
	if !allowedFileTypes[fileExt] {
		allowed := make([]string, 0, len(allowedFileTypes))
		for k := range allowedFileTypes {
			allowed = append(allowed, k)
		}
		errMsg := fmt.Sprintf("文件类型 '%s' 不允许上传。允许的类型有: %s", fileExt, strings.Join(allowed, ", "))
		return nil, gerror.NewCode(gcode.CodeValidationFailed, errMsg)
	}

	// 2. Validate File Size
	if file.Size > maxFileSize {
		errMsg := fmt.Sprintf("文件大小超过限制，最大允许 %d MB，当前文件大小: %.2f MB", maxFileSize/(1024*1024), float64(file.Size)/(1024*1024))
		return nil, gerror.NewCode(gcode.CodeValidationFailed, errMsg)
	}

	// 3. Generate Object Key
	dateStr := time.Now().Format("2006-01-02")
	randomUUID := uuid.New().String()
	objectKey := fmt.Sprintf("files/%s/%s%s", dateStr, randomUUID, fileExt)

	// 4. Get Storage Service Client
	storageSvc := service.Storage()
	if storageSvc == nil {
		g.Log().Errorf(ctx, "Failed to get storage service")
		return nil, gerror.NewCode(gcode.CodeInternalError, "存储服务连接失败")
	}

	// 5. Call Upload Service
	fileReader, err := file.Open()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to open uploaded file stream: %v", err)
		return nil, gerror.NewCode(gcode.CodeInternalError, "处理上传文件失败")
	}
	defer fileReader.Close()

	// Determine content type
	contentType := file.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	fileURL, err := storageSvc.UploadObject(ctx, objectKey, fileReader, file.Size, contentType)
	if err != nil {
		return nil, gerror.NewCode(gcode.CodeInternalError, "上传到存储服务失败")
	}

	// 6. Build Success Response
	res = &v1.UploadFileRes{
		FileUrl:  fileURL,
		FileName: file.Filename,
		FileSize: file.Size,
	}

	return res, nil
}
