// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package system

import (
	"admin-api/api/system"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
	"context"
)

type ControllerV1 struct{}

func NewV1() system.ISystemV1 {
	return &ControllerV1{}
}

// ExchangeProductList 获取兑换产品列表
func (c *ControllerV1) ExchangeProductList(ctx context.Context, req *v1.ExchangeProductListReq) (res *v1.ExchangeProductListRes, err error) {
	return service.Exchange().GetProductList(ctx, req)
}

// ExchangeProductDetail 获取兑换产品详情
func (c *ControllerV1) ExchangeProductDetail(ctx context.Context, req *v1.ExchangeProductDetailReq) (res *v1.ExchangeProductDetailRes, err error) {
	return service.Exchange().GetProductDetail(ctx, req)
}

// ExchangeProductCreate 创建兑换产品
func (c *ControllerV1) ExchangeProductCreate(ctx context.Context, req *v1.ExchangeProductCreateReq) (res *v1.ExchangeProductCreateRes, err error) {
	return service.Exchange().CreateProduct(ctx, req)
}

// ExchangeProductUpdate 更新兑换产品
func (c *ControllerV1) ExchangeProductUpdate(ctx context.Context, req *v1.ExchangeProductUpdateReq) (res *v1.ExchangeProductUpdateRes, err error) {
	return service.Exchange().UpdateProduct(ctx, req)
}

// ExchangeProductDelete 删除兑换产品
func (c *ControllerV1) ExchangeProductDelete(ctx context.Context, req *v1.ExchangeProductDeleteReq) (res *v1.ExchangeProductDeleteRes, err error) {
	return service.Exchange().DeleteProduct(ctx, req)
}

// ExchangeProductStatus 更新兑换产品状态
func (c *ControllerV1) ExchangeProductStatus(ctx context.Context, req *v1.ExchangeProductStatusReq) (res *v1.ExchangeProductStatusRes, err error) {
	return service.Exchange().UpdateProductStatus(ctx, req)
}

// ExchangeOrderList 获取兑换订单列表
func (c *ControllerV1) ExchangeOrderList(ctx context.Context, req *v1.ExchangeOrderListReq) (res *v1.ExchangeOrderListRes, err error) {
	return service.ExchangeOrder().GetOrderList(ctx, req)
}

// ExchangeOrderDetail 获取兑换订单详情
func (c *ControllerV1) ExchangeOrderDetail(ctx context.Context, req *v1.ExchangeOrderDetailReq) (res *v1.ExchangeOrderDetailRes, err error) {
	return service.ExchangeOrder().GetOrderDetail(ctx, req)
}

// ExchangeProductVolume 获取兑换产品交易量
func (c *ControllerV1) ExchangeProductVolume(ctx context.Context, req *v1.ExchangeProductVolumeReq) (res *v1.ExchangeProductVolumeRes, err error) {
	return service.Exchange().GetProductVolume(ctx, req)
}