package system

import (
	"bytes"
	"context"
	"fmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"path/filepath"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	_ "golang.org/x/image/webp"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
)

const (
	maxImageSize = 10 * 1024 * 1024 // 10 MB limit for images
)

var (
	allowedImageTypes = map[string]bool{
		"image/jpeg": true,
		"image/png":  true,
		"image/gif":  true,
		"image/webp": true,
	}
)

// UploadImage handles the image upload request with dimension detection.
func (c *ControllerV1) UploadImage(ctx context.Context, req *v1.UploadImageReq) (res *v1.UploadImageRes, err error) {
	r := g.RequestFromCtx(ctx)
	file := r.GetUploadFile("file")
	if file == nil {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "请上传图片文件，使用'file'字段名称")
	}

	// 1. Validate File Type
	contentType := file.Header.Get("Content-Type")
	if !allowedImageTypes[contentType] {
		allowed := make([]string, 0, len(allowedImageTypes))
		for k := range allowedImageTypes {
			allowed = append(allowed, k)
		}
		errMsg := fmt.Sprintf("图片类型 '%s' 不允许上传。允许的类型有: %s", contentType, strings.Join(allowed, ", "))
		return nil, gerror.NewCode(gcode.CodeValidationFailed, errMsg)
	}

	// 2. Validate File Size
	if file.Size > maxImageSize {
		errMsg := fmt.Sprintf("图片大小超过限制，最大允许 %d MB，当前文件大小: %.2f MB", maxImageSize/(1024*1024), float64(file.Size)/(1024*1024))
		return nil, gerror.NewCode(gcode.CodeValidationFailed, errMsg)
	}

	// 3. Read file to detect image dimensions
	fileReader, err := file.Open()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to open uploaded file stream: %v", err)
		return nil, gerror.NewCode(gcode.CodeInternalError, "处理上传文件失败")
	}
	defer fileReader.Close()

	// Read file content for dimension detection
	buf := new(bytes.Buffer)
	if _, err := buf.ReadFrom(fileReader); err != nil {
		g.Log().Errorf(ctx, "Failed to read file content: %v", err)
		return nil, gerror.NewCode(gcode.CodeInternalError, "读取图片文件失败")
	}
	fileContent := buf.Bytes()

	// Detect image dimensions
	var width, height int
	img, _, err := image.DecodeConfig(bytes.NewReader(fileContent))
	if err != nil {
		g.Log().Warningf(ctx, "Failed to decode image config: %v", err)
		// Continue without dimensions
	} else {
		width = img.Width
		height = img.Height
	}

	// 4. Generate Object Key
	dateStr := time.Now().Format("2006-01-02")
	randomUUID := uuid.New().String()
	fileExt := filepath.Ext(file.Filename)
	if fileExt == "" {
		switch contentType {
		case "image/jpeg":
			fileExt = ".jpg"
		case "image/png":
			fileExt = ".png"
		case "image/gif":
			fileExt = ".gif"
		case "image/webp":
			fileExt = ".webp"
		default:
			fileExt = ".jpg"
		}
	}
	objectKey := fmt.Sprintf("images/%s/%s%s", dateStr, randomUUID, strings.ToLower(fileExt))

	// 5. Get Storage Service Client
	storageSvc := service.Storage()
	if storageSvc == nil {
		g.Log().Errorf(ctx, "Failed to get storage service")
		return nil, gerror.NewCode(gcode.CodeInternalError, "存储服务连接失败")
	}

	// 6. Upload to storage
	imageURL, err := storageSvc.UploadObject(ctx, objectKey, bytes.NewReader(fileContent), file.Size, contentType)
	if err != nil {
		return nil, gerror.NewCode(gcode.CodeInternalError, "上传到存储服务失败")
	}

	// 7. Build Success Response
	res = &v1.UploadImageRes{
		ImageUrl: imageURL,
		Width:    width,
		Height:   height,
	}

	return res, nil
}