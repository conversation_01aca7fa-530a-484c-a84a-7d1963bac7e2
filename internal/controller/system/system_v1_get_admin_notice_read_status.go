package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GetAdminNoticeReadStatus implements the logic for the GetAdminNoticeReadStatus controller.
func (c *ControllerV1) GetAdminNoticeReadStatus(ctx context.Context, req *v1.GetAdminNoticeReadStatusReq) (res *v1.GetAdminNoticeReadStatusRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.GetAdminNoticeReadStatus(ctx, req)
	return
}
