package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GenerateMerchantApiKey implements the logic for the GenerateMerchantApiKey controller.
func (c *ControllerV1) GenerateMerchantApiKey(ctx context.Context, req *v1.GenerateMerchantApiKeyReq) (res *v1.GenerateMerchantApiKeyRes, err error) {
	// 从路径参数中获取 merchantId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	res, err = service.SystemServiceInstance.GenerateMerchantApiKey(ctx, req)
	return
}
