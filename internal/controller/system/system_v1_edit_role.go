package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// EditRole implements the logic for the EditRole controller.
func (c *ControllerV1) EditRole(ctx context.Context, req *v1.EditRoleReq) (res *v1.EditRoleRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.EditRole(ctx, req)
	return
}
