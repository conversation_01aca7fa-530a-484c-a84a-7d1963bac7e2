package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GetRoleMenuIds implements the logic for the GetRoleMenuIds controller.
func (c *ControllerV1) GetRoleMenuIds(ctx context.Context, req *v1.GetRoleMenuIdsReq) (res *v1.GetRoleMenuIdsRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.GetRoleMenuIds(ctx, req)
	return
}
