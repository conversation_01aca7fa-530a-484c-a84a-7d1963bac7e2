package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
)

// GetGameTransactionRecordsDetail 获取游戏交易记录详情
func (c *ControllerV1) GetGameTransactionRecordsDetail(ctx context.Context, req *v1.GetGameTransactionRecordsDetailReq) (res *v1.GetGameTransactionRecordsDetailRes, err error) {
	return service.SystemServiceInstance.GetGameTransactionRecordsDetail(ctx, req)
}