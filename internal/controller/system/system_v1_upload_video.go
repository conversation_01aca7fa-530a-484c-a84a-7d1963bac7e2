package system

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
)

const (
	maxVideoSize = 200 * 1024 * 1024 // 200 MB limit for videos
)

var (
	allowedVideoTypes = map[string]bool{
		"video/mp4":       true,
		"video/mpeg":      true,
		"video/quicktime": true, // .mov
		"video/x-msvideo": true, // .avi
		"video/x-matroska": true, // .mkv
		"video/webm":      true,
		"video/ogg":       true,
		"video/3gpp":      true,
		"video/3gpp2":     true,
	}

	// Map content types to file extensions
	videoExtensions = map[string]string{
		"video/mp4":        ".mp4",
		"video/mpeg":       ".mpeg",
		"video/quicktime":  ".mov",
		"video/x-msvideo":  ".avi",
		"video/x-matroska": ".mkv",
		"video/webm":       ".webm",
		"video/ogg":        ".ogv",
		"video/3gpp":       ".3gp",
		"video/3gpp2":      ".3g2",
	}
)

// UploadVideo handles the video upload request.
func (c *ControllerV1) UploadVideo(ctx context.Context, req *v1.UploadVideoReq) (res *v1.UploadVideoRes, err error) {
	r := g.RequestFromCtx(ctx)
	file := r.GetUploadFile("file")
	if file == nil {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "请上传视频文件，使用'file'字段名称")
	}

	// 1. Validate File Type
	contentType := file.Header.Get("Content-Type")
	if !allowedVideoTypes[contentType] {
		allowed := make([]string, 0, len(allowedVideoTypes))
		for k := range allowedVideoTypes {
			allowed = append(allowed, k)
		}
		errMsg := fmt.Sprintf("视频类型 '%s' 不允许上传。允许的类型有: %s", contentType, strings.Join(allowed, ", "))
		return nil, gerror.NewCode(gcode.CodeValidationFailed, errMsg)
	}

	// 2. Validate File Size
	if file.Size > maxVideoSize {
		errMsg := fmt.Sprintf("视频大小超过限制，最大允许 %d MB，当前文件大小: %.2f MB", 
			maxVideoSize/(1024*1024), float64(file.Size)/(1024*1024))
		return nil, gerror.NewCode(gcode.CodeValidationFailed, errMsg)
	}

	// 3. Generate Object Key
	dateStr := time.Now().Format("2006-01-02")
	randomUUID := uuid.New().String()
	
	// Get file extension
	fileExt := filepath.Ext(file.Filename)
	if fileExt == "" {
		// Try to determine extension from content type
		if ext, ok := videoExtensions[contentType]; ok {
			fileExt = ext
		} else {
			fileExt = ".mp4" // Default to mp4
		}
	}
	objectKey := fmt.Sprintf("videos/%s/%s%s", dateStr, randomUUID, strings.ToLower(fileExt))

	// 4. Get Storage Service Client
	storageSvc := service.Storage()
	if storageSvc == nil {
		g.Log().Errorf(ctx, "Failed to get storage service")
		return nil, gerror.NewCode(gcode.CodeInternalError, "存储服务连接失败")
	}

	// 5. Call Upload Service
	fileReader, err := file.Open()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to open uploaded file stream: %v", err)
		return nil, gerror.NewCode(gcode.CodeInternalError, "处理上传文件失败")
	}
	defer fileReader.Close()

	videoURL, err := storageSvc.UploadObject(ctx, objectKey, fileReader, file.Size, contentType)
	if err != nil {
		return nil, gerror.NewCode(gcode.CodeInternalError, "上传到存储服务失败")
	}

	// 6. Build Success Response
	res = &v1.UploadVideoRes{
		VideoUrl: videoURL,
		FileName: file.Filename,
		FileSize: file.Size,
		// Duration would require video processing library to extract
	}

	return res, nil
}