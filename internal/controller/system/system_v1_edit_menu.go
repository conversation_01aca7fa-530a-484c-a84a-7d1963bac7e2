package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// EditMenu implements the logic for the EditMenu controller.
func (c *ControllerV1) EditMenu(ctx context.Context, req *v1.EditMenuReq) (res *v1.EditMenuRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.EditMenu(ctx, req)
	return
}
