package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"
)

func (c *ControllerV1) ReviewUserWithdraw(ctx context.Context, req *v1.ReviewUserWithdrawReq) (res *v1.ReviewUserWithdrawRes, err error) {
	// 从路径参数中获取 withdrawId
	r := ghttp.RequestFromCtx(ctx)
	req.UserWithdrawsId = r.Get("withdrawId").Uint()
	return service.SystemServiceInstance.ReviewUserWithdraw(ctx, req)
}
