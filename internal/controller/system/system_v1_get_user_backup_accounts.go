package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"
)

func (c *ControllerV1) GetUserBackupAccounts(ctx context.Context, req *v1.GetUserBackupAccountsReq) (res *v1.GetUserBackupAccountsRes, err error) {
	// 从路径参数中获取 userId
	r := ghttp.RequestFromCtx(ctx)
	req.UserId = r.Get("userId").Int64()
	res, err = service.SystemServiceInstance.GetUserBackupAccounts(ctx, req)
	return
}
