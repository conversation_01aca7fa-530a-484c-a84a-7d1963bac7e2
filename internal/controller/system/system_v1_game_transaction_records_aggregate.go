package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
)

// GetGameTransactionRecordsAggregate 获取游戏交易记录聚合数据
func (c *ControllerV1) GetGameTransactionRecordsAggregate(ctx context.Context, req *v1.GetGameTransactionRecordsAggregateReq) (res *v1.GetGameTransactionRecordsAggregateRes, err error) {
	return service.SystemServiceInstance.GetGameTransactionRecordsAggregate(ctx, req)
}