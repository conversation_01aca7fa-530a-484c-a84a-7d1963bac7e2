package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// UpdateApiKeyStatus implements the logic for the UpdateApiKeyStatus controller.
func (c *ControllerV1) UpdateApiKeyStatus(ctx context.Context, req *v1.UpdateApiKeyStatusReq) (res *v1.UpdateApiKeyStatusRes, err error) {
	// 从路径参数中获取 apiKeyId
	r := ghttp.RequestFromCtx(ctx)
	req.ApiKeyId = r.Get("apiKeyId").Uint()
	res, err = service.SystemServiceInstance.UpdateApiKeyStatus(ctx, req)
	return
}
