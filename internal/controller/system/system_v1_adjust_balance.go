package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
	amountUtil "admin-api/utility/amount"
)

// AdjustBalance implements the logic for the AdjustBalance controller.
func (c *ControllerV1) AdjustBalance(ctx context.Context, req *v1.AdjustBalanceReq) (res *v1.AdjustBalanceRes, err error) {
	if _, err := amountUtil.ValidateFloat64DecimalPlaces(req.Amount, 6); err != nil {
		return nil, err
	}
	return service.SystemServiceInstance.AdjustBalance(ctx, req)
}
