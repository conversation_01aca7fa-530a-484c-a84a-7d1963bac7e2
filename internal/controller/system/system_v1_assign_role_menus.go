package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// AssignRoleMenus implements the logic for the AssignRoleMenus controller.
func (c *ControllerV1) AssignRoleMenus(ctx context.Context, req *v1.AssignRoleMenusReq) (res *v1.AssignRoleMenusRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.AssignRoleMenus(ctx, req)
	return
}
