package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"
)

func (c *ControllerV1) AddUserBackupAccount(ctx context.Context, req *v1.AddUserBackupAccountReq) (res *v1.AddUserBackupAccountRes, err error) {
	// 从路径参数中获取 userId
	r := ghttp.RequestFromCtx(ctx)
	req.UserId = r.Get("userId").Int64()

	// 确保 BUserId 字段已提供
	if req.BUserId == 0 {
		// 尝试从请求体中获取
		bUserId := r.Get("bUserId").Int64()
		if bUserId != 0 {
			req.BUserId = bUserId
		}
	}

	res, err = service.SystemServiceInstance.AddUserBackupAccount(ctx, req)
	return
}
