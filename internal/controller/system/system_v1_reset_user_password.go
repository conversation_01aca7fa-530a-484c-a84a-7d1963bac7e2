package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// ResetUserPassword implements the logic for the ResetUserPassword controller.
func (c *ControllerV1) ResetUserPassword(ctx context.Context, req *v1.ResetUserPasswordReq) (res *v1.ResetUserPasswordRes, err error) {
	// 从路径参数中获取 id
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Uint64()
	res, err = service.SystemServiceInstance.ResetUserPassword(ctx, req)
	return
}
