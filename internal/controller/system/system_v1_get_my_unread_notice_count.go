package system

import (
	"context"

	"admin-api/internal/service"

	v1 "admin-api/api/system/v1"
)

// GetMyUnreadNoticeCount implements the logic for the GetMyUnreadNoticeCount controller.
func (c *ControllerV1) GetMyUnreadNoticeCount(ctx context.Context, req *v1.GetMyUnreadNoticeCountReq) (res *v1.GetMyUnreadNoticeCountRes, err error) {
	res, err = service.SystemServiceInstance.GetMyUnreadNoticeCount(ctx, req)
	return
}
