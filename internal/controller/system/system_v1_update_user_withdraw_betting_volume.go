package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// UpdateUserWithdrawBettingVolume implements the logic for the UpdateUserWithdrawBettingVolume controller.
func (c *ControllerV1) UpdateUserWithdrawBettingVolume(ctx context.Context, req *v1.UpdateUserWithdrawBettingVolumeReq) (res *v1.UpdateUserWithdrawBettingVolumeRes, err error) {
	// 从路径参数中获取 id
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Uint64()
	res, err = service.SystemServiceInstance.UpdateUserWithdrawBettingVolume(ctx, req)
	return
}