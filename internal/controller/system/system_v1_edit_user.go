package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// EditUser implements the logic for the EditUser controller.
func (c *ControllerV1) EditUser(ctx context.Context, req *v1.EditUserReq) (res *v1.EditUserRes, err error) {
	// 从路径参数中获取 id
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Uint64()
	res, err = service.SystemServiceInstance.EditUser(ctx, req)
	return
}
