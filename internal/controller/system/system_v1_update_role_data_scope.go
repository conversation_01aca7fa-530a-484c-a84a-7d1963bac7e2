package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// UpdateRoleDataScope implements the logic for the UpdateRoleDataScope controller.
func (c *ControllerV1) UpdateRoleDataScope(ctx context.Context, req *v1.UpdateRoleDataScopeReq) (res *v1.UpdateRoleDataScopeRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.UpdateRoleDataScope(ctx, req)
	return
}
