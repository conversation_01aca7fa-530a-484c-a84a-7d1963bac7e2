package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GetMerchantApiKeyList implements the logic for the GetMerchantApiKeyList controller.
func (c *ControllerV1) GetMerchantApiKeyList(ctx context.Context, req *v1.GetMerchantApiKeyListReq) (res *v1.GetMerchantApiKeyListRes, err error) {
	// 从路径参数中获取 merchantId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	res, err = service.SystemServiceInstance.GetMerchantApiKeyList(ctx, req)
	return
}
