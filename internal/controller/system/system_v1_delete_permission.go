package system

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
	"context"

	"github.com/gogf/gf/v2/net/ghttp" // 导入 ghttp
)

func (c *ControllerV1) DeletePermission(ctx context.Context, req *v1.DeletePermissionReq) (res *v1.DeletePermissionRes, err error) {
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()                                        // 从路径获取 ID
	res, err = service.SystemServiceInstance.DeletePermission(ctx, req) // 调用 Service 层
	return
}
