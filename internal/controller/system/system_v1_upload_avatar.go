package system

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"

	v1 "admin-api/api/system/v1"      // Import API definitions
	"admin-api/internal/service"      // Import service layer
)

const (
	maxAvatarSize = 5 * 1024 * 1024 // 5 MB limit for avatars
)

var (
	allowedAvatarTypes = map[string]bool{
		"image/jpeg": true,
		"image/png":  true,
		"image/gif":  true,
	}
)

// UploadAvatar handles the avatar upload request.
// It's part of the ControllerV1 struct now.
func (c *ControllerV1) UploadAvatar(ctx context.Context, req *v1.UploadAvatarReq) (res *v1.UploadAvatarRes, err error) {
	r := g.RequestFromCtx(ctx)
	file := r.GetUploadFile("file") // "file" is the default name, adjust if your form field name is different
	if file == nil {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "请上传图片文件，使用'file'字段名称")
	}
	// Note: No need for file.Close() on ghttp.UploadFile

	// 1. Validate File Type
	contentType := file.Header.Get("Content-Type")
	if !allowedAvatarTypes[contentType] {
		allowed := make([]string, 0, len(allowedAvatarTypes))
		for k := range allowedAvatarTypes {
			allowed = append(allowed, k)
		}
		errMsg := fmt.Sprintf("文件类型 '%s' 不允许上传。允许的类型有: %s", contentType, strings.Join(allowed, ", "))
		return nil, gerror.NewCodef(gcode.CodeValidationFailed, errMsg)
	}

	// 2. Validate File Size
	if file.Size > maxAvatarSize {
		errMsg := fmt.Sprintf("文件大小超过限制，最大允许 %d MB，当前文件大小: %.2f MB", maxAvatarSize/(1024*1024), float64(file.Size)/(1024*1024))
		return nil, gerror.NewCodef(gcode.CodeValidationFailed, errMsg)
	}

	// 3. Generate Object Key
	dateStr := time.Now().Format("2006-01-02") // YYYY-MM-DD format
	randomUUID := uuid.New().String()
	fileExt := filepath.Ext(file.Filename) // Get extension like ".jpg"
	// Ensure extension is lower case and handle cases with no extension if necessary
	if fileExt == "" {
		g.Log().Warningf(ctx, "Uploaded file '%s' has no extension.", file.Filename)
		switch contentType {
		case "image/jpeg":
			fileExt = ".jpg"
		case "image/png":
			fileExt = ".png"
		case "image/gif":
			fileExt = ".gif"
		default:
			fileExt = "" // Or return error
		}
		if fileExt == "" {
			return nil, gerror.NewCodef(gcode.CodeValidationFailed, "无法确定内容类型为'%s'的文件扩展名", contentType)
		}
	}
	objectKey := fmt.Sprintf("avatars/%s/%s%s", dateStr, randomUUID, strings.ToLower(fileExt))

	// 4. Get Storage Service Client
	storageSvc := service.Storage()
	if storageSvc == nil {
		g.Log().Errorf(ctx, "Failed to get storage service")
		// Return a user-friendly error, hide internal details
		return nil, gerror.NewCode(gcode.CodeInternalError, "存储服务连接失败")
	}

	// 5. Call Upload Service
	fileReader, err := file.Open()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to open uploaded file stream: %v", err)
		return nil, gerror.NewCode(gcode.CodeInternalError, "处理上传文件失败")
	}
	defer fileReader.Close() // Ensure the reader is closed

	avatarURL, err := storageSvc.UploadObject(ctx, objectKey, fileReader, file.Size, contentType)
	if err != nil {
		// Error is already logged in the service, return a user-friendly error
		// Check if it's a specific type of error if needed, otherwise generic internal error
		return nil, gerror.NewCode(gcode.CodeInternalError, "上传到存储服务失败")
	}

	// 6. Build Success Response
	res = &v1.UploadAvatarRes{
		AvatarUrl: avatarURL,
	}

	return res, nil
}
