package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"
)

func (c *ControllerV1) UpdateConfigCategory(ctx context.Context, req *v1.UpdateConfigCategoryReq) (res *v1.UpdateConfigCategoryRes, err error) {

	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	return service.SystemServiceInstance.UpdateConfigCategory(ctx, req)
}
