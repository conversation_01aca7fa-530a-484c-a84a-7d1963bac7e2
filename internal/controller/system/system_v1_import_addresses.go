package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
)

// ImportAddresses implements the logic for the ImportAddresses controller.
func (c *ControllerV1) ImportAddresses(ctx context.Context, req *v1.ImportAddressesReq) (res *v1.ImportAddressesRes, err error) {
	return service.SystemServiceInstance.ImportAddresses(ctx, req)
}

// GetImportProgress implements the logic for the GetImportProgress controller.
func (c *ControllerV1) GetImportProgress(ctx context.Context, req *v1.GetImportProgressReq) (res *v1.GetImportProgressRes, err error) {
	return service.SystemServiceInstance.GetImportProgress(ctx, req)
}
