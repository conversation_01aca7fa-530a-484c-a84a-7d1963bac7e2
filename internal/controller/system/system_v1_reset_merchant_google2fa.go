package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// ResetMerchantGoogle2FA implements the logic for the ResetMerchantGoogle2FA controller.
func (c *ControllerV1) ResetMerchantGoogle2FA(ctx context.Context, req *v1.ResetMerchantGoogle2FAReq) (res *v1.ResetMerchantGoogle2FARes, err error) {
	// 从路径参数中获取 merchantId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	res, err = service.SystemServiceInstance.ResetMerchantGoogle2FA(ctx, req)
	return
}
