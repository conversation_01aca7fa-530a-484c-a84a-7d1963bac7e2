package system

// import (
// 	"context"

// 	"admin-api/internal/service"

// 	"github.com/gogf/gf/v2/net/ghttp"

// 	v1 "admin-api/api/system/v1"
// )

// // GetMenu implements the logic for the GetMenu controller.
// func (c *ControllerV1) GetMenu(ctx context.Context, req *v1.GetMenuReq) (res *v1.GetMenuRes, err error) {
// 	// 从路径参数中获取 ID
// 	r := ghttp.RequestFromCtx(ctx)
// 	req.Id = r.Get("id").Int64()
// 	res, err = service.SystemServiceInstance.GetMenu(ctx, req)
// 	return
// }
