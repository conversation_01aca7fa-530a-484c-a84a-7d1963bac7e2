package config

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"context"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

var (
	// 初始化同步控制
	once sync.Once
)

// Initialize 初始化配置管理器
func Initialize(ctx context.Context) error {
	var err error
	once.Do(func() {
		glog.Info(ctx, "开始初始化配置管理器...")

		// 初始化 Consul 同步器
		err = InitConsulSync(ctx)
		if err != nil {
			glog.Errorf(ctx, "初始化 Consul 同步器失败: %v", err)
			//同步失败意味整个系统无法继续使用 应该检查问题
			panic(err)
		}

		// 初始化Redis缓存（从数据库加载配置到Redis和Consul）
		err = initializeCache(ctx)
		if err != nil {
			glog.Errorf(ctx, "初始化缓存失败: %v", err)
			panic(err)
		}

		glog.Info(ctx, "配置管理器初始化完成")
	})

	return err
}

// initializeCache 从数据库加载所有配置到Redis缓存和Consul
func initializeCache(ctx context.Context) error {
	// 获取所有配置项
	var configItems []*entity.AdminConfigItems
	err := dao.AdminConfigItems.Ctx(ctx).Scan(&configItems)
	if err != nil {
		return err
	}

	// 将配置项同步到Redis
	redis := g.Redis()
	consulItems := make(map[string]string)

	for _, item := range configItems {
		// 存储单个配置项 - 使用原始键名，不添加前缀
		redis.Set(ctx, item.Key, item.Value)

		// 同时存储带前缀的版本（向后兼容）
		configKey := "xpay:config:" + item.Key
		redis.Set(ctx, configKey, item.Value)

		// 存储到分类哈希
		parts := strings.Split(item.Key, ".")
		if len(parts) > 1 {
			categoryKey := parts[0]
			subKey := strings.Join(parts[1:], ".")
			categoryHashKey := "xpay:config:category:" + categoryKey
			redis.HSet(ctx, categoryHashKey, map[string]interface{}{
				subKey: item.Value,
			})
		}

		// 收集配置项用于同步到 Consul
		consulItems[item.Key] = item.Value
	}

	// 同步到 Consul（使用新的 JSON 格式）
	if GetConsulSync() != nil {
		// 重新查询一次获取完整的类型信息
		var configItemsWithType []*entity.AdminConfigItems
		err := dao.AdminConfigItems.Ctx(ctx).Scan(&configItemsWithType)
		if err == nil {
			for _, item := range configItemsWithType {
				configData := &ConfigData{
					Value:     item.Value,
					ValueType: ConfigValueType(item.ValueType),
				}
				if err := GetConsulSync().SyncConfigData(ctx, item.Key, configData); err != nil {
					glog.Errorf(ctx, "同步配置到 Consul 失败: key=%s, error=%v", item.Key, err)
				}
			}
		}
	}

	return nil
}

// Get 获取配置值
func Get(ctx context.Context, key string) (interface{}, error) {
	redis := g.Redis()
	value, err := redis.Get(ctx, key)
	if err != nil {
		return nil, err
	}
	if value == nil {
		return nil, gerror.Newf("config key '%s' not found", key)
	}
	return value.Val(), nil
}

// GetString 获取字符串配置值
func GetString(ctx context.Context, key string) (string, error) {
	metrics.ReadCount.Add(1)

	// 临时解决方案：直接从 Redis 读取，绕过 xpay-config 客户端
	redis := g.Redis()
	value, err := redis.Get(ctx, key)
	if err != nil {
		metrics.ReadFailureCount.Add(1)
		glog.Debugf(ctx, "GetString - Redis 读取失败: key='%s', error='%v'", key, err)
		return "", err
	}
	if value == nil {
		metrics.ReadFailureCount.Add(1)
		glog.Debugf(ctx, "GetString - 配置键不存在: key='%s'", key)
		return "", gerror.Newf("config key '%s' not found", key)
	}
	result := value.String()
	glog.Debugf(ctx, "GetString - 读取成功: key='%s', value='%s'", key, result)
	return result, nil
}

// GetInt 获取整数配置值
func GetInt(ctx context.Context, key string) (int, error) {
	value, err := GetString(ctx, key)
	if err != nil {
		return 0, err
	}
	return strconv.Atoi(value)
}

// GetBool 获取布尔配置值
func GetBool(ctx context.Context, key string) (bool, error) {
	value, err := GetString(ctx, key)
	if err != nil {
		return false, err
	}
	return gvar.New(value).Bool(), nil
}

// GetByCategory 获取分类配置
func GetByCategory(ctx context.Context, categoryKey string) (map[string]interface{}, error) {
	redis := g.Redis()
	categoryHashKey := "xpay:config:category:" + categoryKey

	// 从 Redis 哈希中获取所有字段
	result, err := redis.HGetAll(ctx, categoryHashKey)
	if err != nil {
		return nil, err
	}

	// 转换为 map[string]interface{}
	data := make(map[string]interface{})
	for k, v := range result.Map() {
		data[k] = v
	}

	return data, nil
}

// GetStringWithDefault 获取字符串配置值，如果不存在则返回默认值
func GetStringWithDefault(ctx context.Context, key, defaultValue string) string {
	value, err := GetString(ctx, key)
	if err != nil {
		glog.Debugf(ctx, "GetStringWithDefault - 获取配置失败: key='%s', error='%v', 使用默认值='%s'", key, err, defaultValue)
		return defaultValue
	}
	glog.Debugf(ctx, "GetStringWithDefault - 获取配置成功: key='%s', value='%s'", key, value)
	return value
}

// GetIntWithDefault 获取整数配置值，如果不存在则返回默认值
func GetIntWithDefault(ctx context.Context, key string, defaultValue int) int {
	value, err := GetInt(ctx, key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetBoolWithDefault 获取布尔配置值，如果不存在则返回默认值
func GetBoolWithDefault(ctx context.Context, key string, defaultValue bool) bool {
	value, err := GetBool(ctx, key)
	if err != nil {
		return defaultValue
	}
	return value
}

// UpdateCache 更新缓存（用于配置项变更后的钩子）
func UpdateCache(ctx context.Context, item *entity.AdminConfigItems) error {
	redis := g.Redis()

	// 获取旧值（用于变更通知）
	oldValue, _ := redis.Get(ctx, item.Key)
	oldValueStr := ""
	if oldValue != nil {
		oldValueStr = oldValue.String()
	}

	// 更新单个配置项 - 使用原始键名
	redis.Set(ctx, item.Key, item.Value)

	// 同时更新带前缀的版本（向后兼容）
	configKey := "xpay:config:" + item.Key
	redis.Set(ctx, configKey, item.Value)

	// 更新分类哈希
	parts := strings.Split(item.Key, ".")
	if len(parts) > 1 {
		categoryKey := parts[0]
		subKey := strings.Join(parts[1:], ".")
		categoryHashKey := "xpay:config:category:" + categoryKey
		redis.HSet(ctx, categoryHashKey, map[string]interface{}{
			subKey: item.Value,
		})
	}

	// 同步到 Consul（使用 JSON 格式）
	if cs := GetConsulSync(); cs != nil {
		configData := &ConfigData{
			Value:     item.Value,
			ValueType: ConfigValueType(item.ValueType),
		}
		if err := cs.SyncConfigData(ctx, item.Key, configData); err != nil {
			metrics.ConsulSyncFailure.Add(1)
			glog.Errorf(ctx, "同步配置到 Consul 失败: key=%s, error=%v", item.Key, err)
			// 不返回错误，Consul 同步失败不影响主流程
		} else {
			metrics.ConsulSyncSuccess.Add(1)
			metrics.LastSyncTime.Store(time.Now())
		}
	}

	// 通知配置变更
	if oldValueStr != item.Value {
		notifyChange(ctx, item.Key, oldValueStr, item.Value)
	}

	return nil
}

// RemoveFromCache 从缓存中移除配置项
func RemoveFromCache(ctx context.Context, key string) error {
	redis := g.Redis()

	// 删除单个配置项
	redis.Del(ctx, key)

	// 删除带前缀的版本
	configKey := "xpay:config:" + key
	redis.Del(ctx, configKey)

	// 从分类哈希中删除
	parts := strings.Split(key, ".")
	if len(parts) > 1 {
		categoryKey := parts[0]
		subKey := strings.Join(parts[1:], ".")
		categoryHashKey := "xpay:config:category:" + categoryKey
		redis.HDel(ctx, categoryHashKey, subKey)
	}

	// 从 Consul 删除
	if cs := GetConsulSync(); cs != nil {
		if err := cs.DeleteFromConsul(ctx, key); err != nil {
			glog.Errorf(ctx, "从 Consul 删除配置失败: key=%s, error=%v", key, err)
			// 不返回错误，Consul 同步失败不影响主流程
		}
	}

	return nil
}

// RefreshCache 刷新缓存
func RefreshCache(ctx context.Context) error {
	return initializeCache(ctx)
}
