package config

import (
	"context"
	"sync"
	
	"github.com/gogf/gf/v2/os/glog"
)

// ConfigChangeHandler 配置变更处理函数
type ConfigChangeHandler func(ctx context.Context, key string, oldValue, newValue string)

// Watcher 配置变更监听器
type Watcher struct {
	mu       sync.RWMutex
	handlers map[string][]ConfigChangeHandler
}

var watcher = &Watcher{
	handlers: make(map[string][]ConfigChangeHandler),
}

// Watch 监听配置变更
// pattern 支持通配符，如 "telegram_bot_setting.*" 或 "*"
func Watch(pattern string, handler ConfigChangeHandler) {
	watcher.mu.Lock()
	defer watcher.mu.Unlock()
	
	watcher.handlers[pattern] = append(watcher.handlers[pattern], handler)
}

// notifyChange 通知配置变更
func notifyChange(ctx context.Context, key, oldValue, newValue string) {
	watcher.mu.RLock()
	defer watcher.mu.RUnlock()
	
	// 精确匹配
	if handlers, ok := watcher.handlers[key]; ok {
		for _, h := range handlers {
			go func(handler ConfigChangeHandler) {
				defer func() {
					if r := recover(); r != nil {
						glog.Errorf(ctx, "配置变更处理器 panic: key=%s, error=%v", key, r)
					}
				}()
				handler(ctx, key, oldValue, newValue)
			}(h)
		}
	}
	
	// 通配符匹配
	if handlers, ok := watcher.handlers["*"]; ok {
		for _, h := range handlers {
			go func(handler ConfigChangeHandler) {
				defer func() {
					if r := recover(); r != nil {
						glog.Errorf(ctx, "配置变更处理器 panic: key=%s, error=%v", key, r)
					}
				}()
				handler(ctx, key, oldValue, newValue)
			}(h)
		}
	}
}

// 使用示例：
// config.Watch("telegram_bot_setting.token", func(ctx context.Context, key, oldValue, newValue string) {
//     // 重新初始化 Telegram Bot
//     bot.Reinitialize(newValue)
// })