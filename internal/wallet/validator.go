package wallet

import (
	"context"
	"net"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// Validator 验证器接口
type Validator interface {
	// 基础验证
	ValidateMerchantID(merchantID uint64) error
	ValidateTokenSymbol(symbol string) error
	ValidateAmount(amount decimal.Decimal, symbol string) error
	ValidateBusinessID(businessID string) error
	ValidateOperation(operation Operation) error
	ValidateDirection(direction Direction) error
	ValidateWalletType(walletType WalletType) error

	// 业务验证
	ValidateTransactionRequest(req *TransactionRequest) error
	ValidateTransferRequest(req *TransferRequest) error
	ValidateDepositRequest(req *DepositRequest) error
	ValidateWithdrawRequest(req *WithdrawRequest) error
	ValidateFreezeRequest(req *FreezeRequest) error
	ValidateAdjustRequest(req *AdjustRequest) error
	ValidateApproveWithdrawRequest(req *ApproveWithdrawRequest) error

	// 复合验证
	ValidateBalanceAvailability(ctx context.Context, tx gdb.TX, merchantID uint64, symbol string, amount decimal.Decimal, walletType WalletType) error
	ValidateTokenSupport(symbol string) error
	ValidateAmountLimits(amount decimal.Decimal, symbol string) error

	// IP和安全验证
	ValidateIPAddress(ip string) error
	ValidateUserAgent(userAgent string) error
}

// validator 验证器实现
type validator struct {
	tokenConfigs map[string]*TokenConfig
	configMgr    *ConfigManager
}

// NewValidator 创建验证器
func NewValidator(tokenConfigs map[string]*TokenConfig, configMgr *ConfigManager) Validator {
	return &validator{
		tokenConfigs: tokenConfigs,
		configMgr:    configMgr,
	}
}

// ValidateMerchantID 验证商户ID
func (v *validator) ValidateMerchantID(merchantID uint64) error {
	if merchantID == 0 {
		return ErrInvalidParam("merchantID", "商户ID不能为空").
			WithRecoverySuggestion("请提供有效的商户ID").
			WithUserFriendlyMessage("商户ID无效")
	}
	
	// 检查商户ID范围
	if merchantID > 999999999999 { // 12位数限制
		return ErrInvalidParam("merchantID", "商户ID超出有效范围").
			WithRecoverySuggestion("请检查商户ID格式").
			WithUserFriendlyMessage("商户ID格式错误").
			WithContext("merchant_id", merchantID)
	}
	
	return nil
}

// ValidateTokenSymbol 验证代币符号
func (v *validator) ValidateTokenSymbol(symbol string) error {
	if symbol == "" {
		return ErrInvalidParam("tokenSymbol", "代币符号不能为空").
			WithRecoverySuggestion("请提供有效的代币符号").
			WithUserFriendlyMessage("代币类型不能为空")
	}
	
	// 标准化符号
	symbol = strings.ToUpper(strings.TrimSpace(symbol))
	
	// 检查符号格式
	if !v.isValidTokenSymbol(symbol) {
		return ErrInvalidParam("tokenSymbol", "代币符号格式无效").
			WithRecoverySuggestion("代币符号应为2-10位大写字母").
			WithUserFriendlyMessage("代币符号格式不正确").
			WithContext("token_symbol", symbol)
	}
	
	return nil
}

// ValidateAmount 验证金额
func (v *validator) ValidateAmount(amount decimal.Decimal, symbol string) error {
	// 检查负数
	if amount.IsNegative() {
		return ErrNegativeAmount().
			WithRecoverySuggestion("请输入正数金额").
			WithUserFriendlyMessage("金额不能为负数").
			WithContext("amount", amount.String())
	}
	
	// 检查零值
	if amount.IsZero() {
		return ErrZeroAmount().
			WithRecoverySuggestion("请输入大于0的金额").
			WithUserFriendlyMessage("金额必须大于0").
			WithContext("amount", amount.String())
	}
	
	// 检查精度
	if err := v.validateAmountPrecision(amount, symbol); err != nil {
		return err
	}
	
	return nil
}

// ValidateBusinessID 验证业务ID
func (v *validator) ValidateBusinessID(businessID string) error {
	if businessID == "" {
		return ErrInvalidParam("businessID", "业务ID不能为空").
			WithRecoverySuggestion("请提供有效的业务ID").
			WithUserFriendlyMessage("业务ID不能为空")
	}
	
	// 检查长度
	if len(businessID) < 8 || len(businessID) > 128 {
		return ErrInvalidParam("businessID", "业务ID长度无效").
			WithRecoverySuggestion("业务ID长度应在8-128字符之间").
			WithUserFriendlyMessage("业务ID长度不符合要求").
			WithContext("business_id", businessID).
			WithContext("length", len(businessID))
	}
	
	// 检查字符集（字母数字和下划线）
	if !v.isValidBusinessID(businessID) {
		return ErrInvalidParam("businessID", "业务ID包含无效字符").
			WithRecoverySuggestion("业务ID只能包含字母、数字和下划线").
			WithUserFriendlyMessage("业务ID格式不正确").
			WithContext("business_id", businessID)
	}
	
	return nil
}

// ValidateOperation 验证操作类型
func (v *validator) ValidateOperation(operation Operation) error {
	validOps := map[Operation]bool{
		OperationDeposit:    true,
		OperationWithdraw:   true,
		OperationTransfer:   true,
		OperationFreeze:     true,
		OperationUnfreeze:   true,
		OperationAdjust:     true,
		OperationCommission: true,
	}
	
	if !validOps[operation] {
		return ErrInvalidParam("operation", "无效的操作类型").
			WithRecoverySuggestion("请使用有效的操作类型").
			WithUserFriendlyMessage("操作类型不支持").
			WithContext("operation", string(operation))
	}
	
	return nil
}

// ValidateDirection 验证资金方向
func (v *validator) ValidateDirection(direction Direction) error {
	if direction != DirectionIn && direction != DirectionOut {
		return ErrInvalidParam("direction", "无效的资金方向").
			WithRecoverySuggestion("资金方向只能是'in'或'out'").
			WithUserFriendlyMessage("资金方向无效").
			WithContext("direction", string(direction))
	}
	
	return nil
}

// ValidateWalletType 验证钱包类型
func (v *validator) ValidateWalletType(walletType WalletType) error {
	if walletType != WalletTypeAvailable && walletType != WalletTypeFrozen {
		return ErrInvalidWalletType(string(walletType)).
			WithRecoverySuggestion("钱包类型只能是'available'或'frozen'").
			WithUserFriendlyMessage("钱包类型无效").
			WithContext("wallet_type", string(walletType))
	}
	
	return nil
}

// ValidateTransactionRequest 验证交易请求
func (v *validator) ValidateTransactionRequest(req *TransactionRequest) error {
	if req == nil {
		return ErrInvalidParam("request", "交易请求不能为空").
			WithRecoverySuggestion("请提供有效的交易请求").
			WithUserFriendlyMessage("请求参数错误")
	}
	
	// 基础字段验证
	if err := v.ValidateMerchantID(req.MerchantID); err != nil {
		return err
	}
	
	if err := v.ValidateTokenSymbol(req.TokenSymbol); err != nil {
		return err
	}
	
	if err := v.ValidateAmount(req.Amount, req.TokenSymbol); err != nil {
		return err
	}
	
	if err := v.ValidateOperation(req.Operation); err != nil {
		return err
	}
	
	if err := v.ValidateDirection(req.Direction); err != nil {
		return err
	}
	
	if err := v.ValidateWalletType(req.WalletType); err != nil {
		return err
	}
	
	if err := v.ValidateBusinessID(req.BusinessID); err != nil {
		return err
	}
	
	// 业务逻辑验证
	if err := v.validateOperationDirection(req.Operation, req.Direction); err != nil {
		return err
	}
	
	// 可选字段验证
	if req.RequestIP != nil && *req.RequestIP != "" {
		if err := v.ValidateIPAddress(*req.RequestIP); err != nil {
			return err
		}
	}
	
	if req.RequestUserAgent != nil && *req.RequestUserAgent != "" {
		if err := v.ValidateUserAgent(*req.RequestUserAgent); err != nil {
			return err
		}
	}
	
	return nil
}

// ValidateTransferRequest 验证转账请求
func (v *validator) ValidateTransferRequest(req *TransferRequest) error {
	if req == nil {
		return ErrInvalidParam("request", "转账请求不能为空").
			WithRecoverySuggestion("请提供有效的转账请求").
			WithUserFriendlyMessage("转账请求参数错误")
	}
	
	if err := v.ValidateMerchantID(req.FromMerchantID); err != nil {
		return err
	}
	
	if err := v.ValidateMerchantID(req.ToMerchantID); err != nil {
		return err
	}
	
	// 检查是否自转账
	if req.FromMerchantID == req.ToMerchantID {
		return ErrOperationNotAllowed("自转账", "不能向自己转账").
			WithRecoverySuggestion("请选择不同的接收方").
			WithUserFriendlyMessage("不能向自己转账").
			WithContext("from_merchant_id", req.FromMerchantID).
			WithContext("to_merchant_id", req.ToMerchantID)
	}
	
	if err := v.ValidateTokenSymbol(req.TokenSymbol); err != nil {
		return err
	}
	
	if err := v.ValidateAmount(req.Amount, req.TokenSymbol); err != nil {
		return err
	}
	
	// 验证备注长度
	if len(req.Memo) > 500 {
		return ErrInvalidParam("memo", "备注长度超出限制").
			WithRecoverySuggestion("备注长度不能超过500字符").
			WithUserFriendlyMessage("备注内容过长").
			WithContext("memo_length", len(req.Memo))
	}
	
	return nil
}

// ValidateDepositRequest 验证充值请求
func (v *validator) ValidateDepositRequest(req *DepositRequest) error {
	if req == nil {
		return ErrInvalidParam("request", "充值请求不能为空").
			WithRecoverySuggestion("请提供有效的充值请求").
			WithUserFriendlyMessage("充值请求参数错误")
	}
	
	if err := v.ValidateMerchantID(req.MerchantID); err != nil {
		return err
	}
	
	if err := v.ValidateTokenSymbol(req.TokenSymbol); err != nil {
		return err
	}
	
	if err := v.ValidateAmount(req.Amount, req.TokenSymbol); err != nil {
		return err
	}
	
	// 验证交易哈希
	if req.TxHash != "" && !v.isValidTxHash(req.TxHash) {
		return ErrInvalidParam("txHash", "交易哈希格式无效").
			WithRecoverySuggestion("请提供有效的交易哈希").
			WithUserFriendlyMessage("交易哈希格式错误").
			WithContext("tx_hash", req.TxHash)
	}
	
	return nil
}

// ValidateWithdrawRequest 验证提现请求
func (v *validator) ValidateWithdrawRequest(req *WithdrawRequest) error {
	if req == nil {
		return ErrInvalidParam("request", "提现请求不能为空").
			WithRecoverySuggestion("请提供有效的提现请求").
			WithUserFriendlyMessage("提现请求参数错误")
	}
	
	if err := v.ValidateMerchantID(req.MerchantID); err != nil {
		return err
	}
	
	if err := v.ValidateTokenSymbol(req.TokenSymbol); err != nil {
		return err
	}
	
	if err := v.ValidateAmount(req.Amount, req.TokenSymbol); err != nil {
		return err
	}
	
	// 验证提现地址
	if req.Address == "" {
		return ErrInvalidParam("address", "提现地址不能为空").
			WithRecoverySuggestion("请提供有效的提现地址").
			WithUserFriendlyMessage("提现地址不能为空")
	}
	
	if !v.isValidAddress(req.Address, req.TokenSymbol) {
		return ErrInvalidParam("address", "提现地址格式无效").
			WithRecoverySuggestion("请检查地址格式").
			WithUserFriendlyMessage("提现地址格式错误").
			WithContext("address", req.Address).
			WithContext("token_symbol", req.TokenSymbol)
	}
	
	return nil
}

// ValidateFreezeRequest 验证冻结请求
func (v *validator) ValidateFreezeRequest(req *FreezeRequest) error {
	if req == nil {
		return ErrInvalidParam("request", "冻结请求不能为空").
			WithRecoverySuggestion("请提供有效的冻结请求").
			WithUserFriendlyMessage("冻结请求参数错误")
	}
	
	if err := v.ValidateMerchantID(req.MerchantID); err != nil {
		return err
	}
	
	if err := v.ValidateTokenSymbol(req.TokenSymbol); err != nil {
		return err
	}
	
	if err := v.ValidateAmount(req.Amount, req.TokenSymbol); err != nil {
		return err
	}
	
	return nil
}

// ValidateAdjustRequest 验证调账请求
func (v *validator) ValidateAdjustRequest(req *AdjustRequest) error {
	if req == nil {
		return ErrInvalidParam("request", "调账请求不能为空").
			WithRecoverySuggestion("请提供有效的调账请求").
			WithUserFriendlyMessage("调账请求参数错误")
	}
	
	if err := v.ValidateMerchantID(req.MerchantID); err != nil {
		return err
	}
	
	if err := v.ValidateTokenSymbol(req.TokenSymbol); err != nil {
		return err
	}
	
	// 调账可以是负数，但不能是零
	if req.Amount.IsZero() {
		return ErrZeroAmount().
			WithRecoverySuggestion("调账金额不能为零").
			WithUserFriendlyMessage("调账金额不能为零")
	}
	
	if req.AdminID == 0 {
		return ErrInvalidParam("adminID", "管理员ID不能为空").
			WithRecoverySuggestion("请提供管理员ID").
			WithUserFriendlyMessage("管理员ID无效")
	}
	
	if req.Reason == "" {
		return ErrInvalidParam("reason", "调账原因不能为空").
			WithRecoverySuggestion("请提供调账原因").
			WithUserFriendlyMessage("请填写调账原因")
	}
	
	// 验证原因长度
	if utf8.RuneCountInString(req.Reason) > 200 {
		return ErrInvalidParam("reason", "调账原因长度超出限制").
			WithRecoverySuggestion("调账原因不能超过200字符").
			WithUserFriendlyMessage("调账原因过长").
			WithContext("reason_length", utf8.RuneCountInString(req.Reason))
	}
	
	return nil
}

// ValidateApproveWithdrawRequest 验证提现审批请求
func (v *validator) ValidateApproveWithdrawRequest(req *ApproveWithdrawRequest) error {
	if req == nil {
		return ErrInvalidParam("request", "审批请求不能为空").
			WithRecoverySuggestion("请提供有效的审批请求").
			WithUserFriendlyMessage("审批请求参数错误")
	}
	
	if err := v.ValidateMerchantID(req.MerchantID); err != nil {
		return err
	}
	
	if err := v.ValidateTokenSymbol(req.TokenSymbol); err != nil {
		return err
	}
	
	if req.WithdrawID == 0 {
		return ErrInvalidParam("withdrawID", "提现记录ID不能为空").
			WithRecoverySuggestion("请提供有效的提现记录ID").
			WithUserFriendlyMessage("提现记录ID无效")
	}
	
	if req.ApproverID == 0 {
		return ErrInvalidParam("approverID", "审批人ID不能为空").
			WithRecoverySuggestion("请提供审批人ID").
			WithUserFriendlyMessage("审批人ID无效")
	}
	
	// 验证审批备注长度
	if utf8.RuneCountInString(req.ApprovalNotes) > 200 {
		return ErrInvalidParam("approvalNotes", "审批备注长度超出限制").
			WithRecoverySuggestion("审批备注不能超过200字符").
			WithUserFriendlyMessage("审批备注过长").
			WithContext("notes_length", utf8.RuneCountInString(req.ApprovalNotes))
	}
	
	return nil
}

// ValidateBalanceAvailability 验证余额可用性
func (v *validator) ValidateBalanceAvailability(ctx context.Context, tx gdb.TX, merchantID uint64, symbol string, amount decimal.Decimal, walletType WalletType) error {
	// 这里需要实际查询余额，暂时返回nil
	// 实际实现中应该调用manager的GetBalanceInTx方法
	return nil
}

// ValidateTokenSupport 验证代币支持
func (v *validator) ValidateTokenSupport(symbol string) error {
	symbol = strings.ToUpper(symbol)
	
	// 检查本地配置
	if _, exists := v.tokenConfigs[symbol]; exists {
		return nil
	}
	
	// 检查配置管理器
	if v.configMgr != nil {
		if _, err := v.configMgr.GetTokenConfig(symbol); err == nil {
			return nil
		}
	}
	
	return ErrTokenNotSupported(symbol).
		WithRecoverySuggestion("请检查支持的代币列表或联系管理员").
		WithUserFriendlyMessage("当前不支持该代币类型").
		WithContext("token_symbol", symbol)
}

// ValidateAmountLimits 验证金额限制
func (v *validator) ValidateAmountLimits(amount decimal.Decimal, symbol string) error {
	symbol = strings.ToUpper(symbol)
	
	var config *TokenConfig
	
	// 获取代币配置
	if localConfig, exists := v.tokenConfigs[symbol]; exists {
		config = localConfig
	} else if v.configMgr != nil {
		if extConfig, err := v.configMgr.GetTokenConfig(symbol); err == nil {
			config = &TokenConfig{
				Symbol:        extConfig.Symbol,
				DecimalPlaces: extConfig.DecimalPlaces,
				MinAmount:     extConfig.MinAmount,
				MaxAmount:     extConfig.MaxAmount,
			}
		}
	}
	
	if config == nil {
		return ErrTokenNotSupported(symbol)
	}
	
	// 检查最小金额
	if amount.LessThan(config.MinAmount) {
		return ErrAmountTooSmall(amount.String(), config.MinAmount.String()).
			WithRecoverySuggestion("请增加交易金额").
			WithUserFriendlyMessage("交易金额低于最小限额").
			WithContext("amount", amount.String()).
			WithContext("min_amount", config.MinAmount.String())
	}
	
	// 检查最大金额
	if amount.GreaterThan(config.MaxAmount) {
		return ErrAmountTooLarge(amount.String(), config.MaxAmount.String()).
			WithRecoverySuggestion("请减少交易金额").
			WithUserFriendlyMessage("交易金额超过最大限额").
			WithContext("amount", amount.String()).
			WithContext("max_amount", config.MaxAmount.String())
	}
	
	return nil
}

// ValidateIPAddress 验证IP地址
func (v *validator) ValidateIPAddress(ip string) error {
	if ip == "" {
		return nil // IP地址可选
	}
	
	// 清理IP地址：去除端口号和空白字符
	cleanedIP := strings.TrimSpace(ip)
	if host, _, err := net.SplitHostPort(cleanedIP); err == nil {
		cleanedIP = host
	}
	
	// 使用Go标准库进行IP地址验证，支持IPv4和IPv6
	parsed := net.ParseIP(cleanedIP)
	if parsed == nil {
		return ErrInvalidParam("requestIP", "IP地址格式无效").
			WithRecoverySuggestion("请提供有效的IPv4或IPv6地址").
			WithUserFriendlyMessage("IP地址格式错误").
			WithContext("ip_address", ip).
			WithContext("cleaned_ip", cleanedIP)
	}
	
	return nil
}

// ValidateUserAgent 验证用户代理
func (v *validator) ValidateUserAgent(userAgent string) error {
	if userAgent == "" {
		return nil // 用户代理可选
	}
	
	// 检查长度
	if len(userAgent) > 500 {
		return ErrInvalidParam("requestUserAgent", "用户代理字符串过长").
			WithRecoverySuggestion("用户代理字符串不能超过500字符").
			WithUserFriendlyMessage("用户代理信息过长").
			WithContext("user_agent_length", len(userAgent))
	}
	
	return nil
}

// 辅助验证方法

func (v *validator) isValidTokenSymbol(symbol string) bool {
	matched, _ := regexp.MatchString(`^[A-Z]{2,10}$`, symbol)
	return matched
}

func (v *validator) isValidBusinessID(businessID string) bool {
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_]+$`, businessID)
	return matched
}

func (v *validator) isValidTxHash(txHash string) bool {
	// 支持常见的交易哈希格式（64位十六进制）
	matched, _ := regexp.MatchString(`^(0x)?[a-fA-F0-9]{64}$`, txHash)
	return matched
}

func (v *validator) isValidAddress(address, tokenSymbol string) bool {
	// 验证地址不能为空且长度在合理范围内
	if len(address) < 10 || len(address) > 100 {
		return false
	}
	
	// 检查是否在开发模式下，如果是则允许更宽松的地址格式
	if v.isDevMode() {
		// 开发模式下，允许纯数字地址（用于测试）
		if matched, _ := regexp.MatchString(`^[0-9]+$`, address); matched {
			return true
		}
		// 开发模式下也允许包含@等特殊字符的测试地址
		if matched, _ := regexp.MatchString(`^[a-zA-Z0-9@._-]+$`, address); matched {
			return true
		}
	}
	
	// 根据不同的代币类型验证地址格式
	tokenSymbol = strings.ToUpper(tokenSymbol)
	
	switch tokenSymbol {
	case "ETH", "USDT-ERC20", "USDC", "DAI":
		// EVM兼容链地址：0x开头，后跟40个十六进制字符
		matched, _ := regexp.MatchString(`^0x[a-fA-F0-9]{40}$`, address)
		return matched
		
	case "BTC", "LTC":
		// Bitcoin地址格式：P2PKH (1开头), P2SH (3开头), Bech32 (bc1开头)
		// 简化验证：26-35个字符，Base58或Bech32格式
		matched, _ := regexp.MatchString(`^(1[a-km-zA-HJ-NP-Z1-9]{25,34}|3[a-km-zA-HJ-NP-Z1-9]{25,34}|bc1[a-z0-9]{39,59})$`, address)
		return matched
		
	case "TRX", "USDT-TRC20":
		// TRON地址：T开头，后跟33个Base58字符
		matched, _ := regexp.MatchString(`^T[a-km-zA-HJ-NP-Z1-9]{33}$`, address)
		// 开发模式下也接受更宽松的格式
		if !matched && v.isDevMode() {
			matched, _ = regexp.MatchString(`^[a-zA-Z0-9][a-zA-Z0-9\-_]*$`, address)
		}
		return matched
		
	case "BNB", "USDT-BEP20":
		// BSC地址：与ETH相同格式
		matched, _ := regexp.MatchString(`^0x[a-fA-F0-9]{40}$`, address)
		return matched
		
	case "MATIC", "POLYGON":
		// Polygon地址：与ETH相同格式
		matched, _ := regexp.MatchString(`^0x[a-fA-F0-9]{40}$`, address)
		return matched
		
	default:
		// 默认情况：支持更宽松的地址格式
		// 允许字母数字字符，以及一些特殊字符（用于未知的链）
		// 这里包括数字序列（如测试地址）
		matched, _ := regexp.MatchString(`^[a-zA-Z0-9][a-zA-Z0-9\-_]*$`, address)
		return matched
	}
}

// isDevMode 检查是否在开发模式
func (v *validator) isDevMode() bool {
	// 从配置中读取开发模式标志
	devMode := g.Cfg().MustGet(context.Background(), "dev_mode", false).Bool()
	return devMode
}

func (v *validator) validateAmountPrecision(amount decimal.Decimal, symbol string) error {
	symbol = strings.ToUpper(symbol)
	
	var decimalPlaces uint = 18 // 默认精度
	
	// 获取代币精度配置
	if config, exists := v.tokenConfigs[symbol]; exists {
		decimalPlaces = config.DecimalPlaces
	} else if v.configMgr != nil {
		if extConfig, err := v.configMgr.GetTokenConfig(symbol); err == nil {
			decimalPlaces = extConfig.DecimalPlaces
		}
	}
	
	// 检查小数位数
	amountStr := amount.String()
	if dotIndex := strings.Index(amountStr, "."); dotIndex != -1 {
		actualDecimals := len(amountStr) - dotIndex - 1
		if uint(actualDecimals) > decimalPlaces {
			return ErrInvalidAmount(amountStr, "小数位数超出限制").
				WithRecoverySuggestion("请减少小数位数").
				WithUserFriendlyMessage("金额精度超出限制").
				WithContext("amount", amountStr).
				WithContext("max_decimal_places", decimalPlaces).
				WithContext("actual_decimal_places", actualDecimals)
		}
	}
	
	return nil
}

func (v *validator) validateOperationDirection(operation Operation, direction Direction) error {
	// 验证操作和方向的组合是否合理
	validCombinations := map[Operation]map[Direction]bool{
		OperationDeposit:    {DirectionIn: true},
		OperationWithdraw:   {DirectionOut: true},
		OperationTransfer:   {DirectionIn: true, DirectionOut: true},
		OperationFreeze:     {DirectionOut: true},
		OperationUnfreeze:   {DirectionIn: true},
		OperationAdjust:     {DirectionIn: true, DirectionOut: true},
		OperationCommission: {DirectionIn: true, DirectionOut: true},
	}
	
	if validDirs, exists := validCombinations[operation]; exists {
		if !validDirs[direction] {
			return ErrInvalidParam("operation_direction", "操作类型和资金方向不匹配").
				WithRecoverySuggestion("请检查操作类型和资金方向的组合").
				WithUserFriendlyMessage("操作类型和资金方向不匹配").
				WithContext("operation", string(operation)).
				WithContext("direction", string(direction))
		}
	}
	
	return nil
}