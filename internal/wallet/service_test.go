package wallet

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
	"github.com/shopspring/decimal"
)

// TestService_NewService 测试创建服务
func TestService_NewService(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试默认配置
		service := NewService()
		t.AssertNE(service, nil)

		// 测试自定义配置
		service2 := NewService(
			WithDBGroup("test"),
			WithAudit(true, "debug"),
		)
		t.AssertNE(service2, nil)
	})
}

// TestService_GetBalance 测试获取余额
func TestService_GetBalance(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := NewService()
		ctx := context.Background()

		// 注意：这个测试需要数据库连接，在实际环境中可能会失败
		// 这里主要测试接口调用是否正常
		_, err := service.GetBalance(ctx, 1, "USDT")
		// 由于没有真实的数据库连接，这里可能会返回错误
		// 我们主要验证方法调用不会panic
		t.Log("GetBalance error (expected in test environment):", err)
	})
}

// TestService_ValidateDepositRequest 测试充值请求验证
func TestService_ValidateDepositRequest(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := NewService()

		// 测试有效请求
		validReq := &DepositRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			TxHash:        "0x123456789",
			Memo:          "测试充值",
			RequestSource: "api",
		}
		err := service.validateDepositRequest(validReq)
		t.AssertNil(err)

		// 测试空请求
		err = service.validateDepositRequest(nil)
		t.AssertNE(err, nil)
		t.Assert(IsWalletError(err), true)

		// 测试无效商户ID
		invalidReq := &DepositRequest{
			MerchantID:    0,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			TxHash:        "0x123456789",
			RequestSource: "api",
		}
		err = service.validateDepositRequest(invalidReq)
		t.AssertNE(err, nil)

		// 测试无效代币符号
		invalidReq2 := &DepositRequest{
			MerchantID:    1,
			TokenSymbol:   "",
			Amount:        decimal.NewFromFloat(100),
			TxHash:        "0x123456789",
			RequestSource: "api",
		}
		err = service.validateDepositRequest(invalidReq2)
		t.AssertNE(err, nil)

		// 测试无效金额
		invalidReq3 := &DepositRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(-100),
			TxHash:        "0x123456789",
			RequestSource: "api",
		}
		err = service.validateDepositRequest(invalidReq3)
		t.AssertNE(err, nil)

		// 测试零金额
		invalidReq4 := &DepositRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.Zero,
			TxHash:        "0x123456789",
			RequestSource: "api",
		}
		err = service.validateDepositRequest(invalidReq4)
		t.AssertNE(err, nil)
	})
}

// TestService_ValidateWithdrawRequest 测试提现请求验证
func TestService_ValidateWithdrawRequest(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := NewService()

		// 测试有效请求
		validReq := &WithdrawRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			Address:       "TTest123456789",
			OrderID:       "WD123456",
			Memo:          "测试提现",
			RequestSource: "api",
		}
		err := service.validateWithdrawRequest(validReq)
		t.AssertNil(err)

		// 测试空请求
		err = service.validateWithdrawRequest(nil)
		t.AssertNE(err, nil)

		// 测试无效商户ID
		invalidReq := &WithdrawRequest{
			MerchantID:    0,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			Address:       "TTest123456789",
			RequestSource: "api",
		}
		err = service.validateWithdrawRequest(invalidReq)
		t.AssertNE(err, nil)

		// 测试无效地址
		invalidReq2 := &WithdrawRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			Address:       "",
			RequestSource: "api",
		}
		err = service.validateWithdrawRequest(invalidReq2)
		t.AssertNE(err, nil)
	})
}

// TestService_ValidateTransferRequest 测试转账请求验证
func TestService_ValidateTransferRequest(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := NewService()

		// 测试有效请求
		validReq := &TransferRequest{
			FromMerchantID: 1,
			ToMerchantID:   2,
			TokenSymbol:    "USDT",
			Amount:         decimal.NewFromFloat(100),
			Memo:           "测试转账",
			RequestSource:  "api",
		}
		err := service.validateTransferRequest(validReq)
		t.AssertNil(err)

		// 测试空请求
		err = service.validateTransferRequest(nil)
		t.AssertNE(err, nil)

		// 测试相同商户ID
		invalidReq := &TransferRequest{
			FromMerchantID: 1,
			ToMerchantID:   1,
			TokenSymbol:    "USDT",
			Amount:         decimal.NewFromFloat(100),
			RequestSource:  "api",
		}
		err = service.validateTransferRequest(invalidReq)
		t.AssertNE(err, nil)

		// 测试无效发送方ID
		invalidReq2 := &TransferRequest{
			FromMerchantID: 0,
			ToMerchantID:   2,
			TokenSymbol:    "USDT",
			Amount:         decimal.NewFromFloat(100),
			RequestSource:  "api",
		}
		err = service.validateTransferRequest(invalidReq2)
		t.AssertNE(err, nil)

		// 测试无效接收方ID
		invalidReq3 := &TransferRequest{
			FromMerchantID: 1,
			ToMerchantID:   0,
			TokenSymbol:    "USDT",
			Amount:         decimal.NewFromFloat(100),
			RequestSource:  "api",
		}
		err = service.validateTransferRequest(invalidReq3)
		t.AssertNE(err, nil)
	})
}

// TestService_ValidateAdjustRequest 测试调账请求验证
func TestService_ValidateAdjustRequest(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := NewService()

		// 测试有效请求（增加余额）
		validReq := &AdjustRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			Reason:        "管理员调账",
			AdminID:       2,
			RequestSource: "admin",
		}
		err := service.validateAdjustRequest(validReq)
		t.AssertNil(err)

		// 测试有效请求（减少余额）
		validReq2 := &AdjustRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(-50),
			Reason:        "管理员调账",
			AdminID:       2,
			RequestSource: "admin",
		}
		err = service.validateAdjustRequest(validReq2)
		t.AssertNil(err)

		// 测试空请求
		err = service.validateAdjustRequest(nil)
		t.AssertNE(err, nil)

		// 测试零金额
		invalidReq := &AdjustRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.Zero,
			Reason:        "管理员调账",
			AdminID:       2,
			RequestSource: "admin",
		}
		err = service.validateAdjustRequest(invalidReq)
		t.AssertNE(err, nil)

		// 测试无效管理员ID
		invalidReq2 := &AdjustRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			Reason:        "管理员调账",
			AdminID:       0,
			RequestSource: "admin",
		}
		err = service.validateAdjustRequest(invalidReq2)
		t.AssertNE(err, nil)

		// 测试空原因
		invalidReq3 := &AdjustRequest{
			MerchantID:    1,
			TokenSymbol:   "USDT",
			Amount:        decimal.NewFromFloat(100),
			Reason:        "",
			AdminID:       2,
			RequestSource: "admin",
		}
		err = service.validateAdjustRequest(invalidReq3)
		t.AssertNE(err, nil)
	})
}

// TestService_ValidateFreezeRequest 测试冻结请求验证
func TestService_ValidateFreezeRequest(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := NewService()

		// 测试有效请求
		validReq := &FreezeRequest{
			MerchantID:      1,
			TokenSymbol:     "USDT",
			Amount:          decimal.NewFromFloat(100),
			RelatedEntityID: 123,
			Memo:            "风控冻结",
		}
		err := service.validateFreezeRequest(validReq)
		t.AssertNil(err)

		// 测试空请求
		err = service.validateFreezeRequest(nil)
		t.AssertNE(err, nil)

		// 测试无效金额
		invalidReq := &FreezeRequest{
			MerchantID:      1,
			TokenSymbol:     "USDT",
			Amount:          decimal.NewFromFloat(-100),
			RelatedEntityID: 123,
			Memo:            "风控冻结",
		}
		err = service.validateFreezeRequest(invalidReq)
		t.AssertNE(err, nil)

		// 测试零金额
		invalidReq2 := &FreezeRequest{
			MerchantID:      1,
			TokenSymbol:     "USDT",
			Amount:          decimal.Zero,
			RelatedEntityID: 123,
			Memo:            "风控冻结",
		}
		err = service.validateFreezeRequest(invalidReq2)
		t.AssertNE(err, nil)
	})
}

// TestService_GenerateBusinessID 测试业务ID生成
func TestService_GenerateBusinessID(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := NewService()

		// 测试生成不同前缀的业务ID
		id1 := service.generateBusinessID("deposit")
		id2 := service.generateBusinessID("withdraw")
		id3 := service.generateBusinessID("transfer")

		// 验证ID不为空
		t.AssertNE(id1, "")
		t.AssertNE(id2, "")
		t.AssertNE(id3, "")

		// 验证ID包含前缀
		t.Assert(len(id1) > len("deposit"), true)
		t.Assert(len(id2) > len("withdraw"), true)
		t.Assert(len(id3) > len("transfer"), true)

		// 验证ID唯一性
		t.AssertNE(id1, id2)
		t.AssertNE(id2, id3)
		t.AssertNE(id1, id3)

		// 验证多次生成的ID不同
		id4 := service.generateBusinessID("deposit")
		t.AssertNE(id1, id4)
	})
}

// BenchmarkService_GenerateBusinessID 性能测试
func BenchmarkService_GenerateBusinessID(b *testing.B) {
	service := NewService()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.generateBusinessID("test")
	}
}
