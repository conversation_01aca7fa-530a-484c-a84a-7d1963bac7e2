package wallet

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// TestEnsuredWalletManager_LockBalance 测试装饰器在锁定余额时自动创建钱包
func TestEnsuredWalletManager_LockBalance(t *testing.T) {
	// 创建管理器，启用自动确保钱包
	manager := NewManager(
		WithDBGroup("default"),
		WithAutoEnsureWallet(true),
	)

	ctx := context.Background()
	merchantID := uint64(99999) // 使用一个不太可能存在的商户ID
	tokenSymbol := "TEST"
	amount := decimal.NewFromFloat(100)

	// 在事务中执行
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 第一次调用 LockBalance，应该自动创建钱包
		err := manager.LockBalance(ctx, tx, merchantID, tokenSymbol, amount)

		// 期望失败，因为新创建的钱包余额为0
		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "余额不足")

		// 但钱包应该已经被创建了
		walletInfo, err := manager.GetBalance(ctx, tx, merchantID, tokenSymbol)
		assert.Nil(t, err)
		assert.NotNil(t, walletInfo)
		assert.Equal(t, merchantID, walletInfo.MerchantID)
		assert.Equal(t, tokenSymbol, walletInfo.TokenSymbol)
		assert.Equal(t, "0", walletInfo.Available.String())

		return nil // 回滚事务
	})

	assert.Nil(t, err)
}

// TestEnsuredWalletManager_DisableAutoEnsure 测试禁用自动确保钱包
func TestEnsuredWalletManager_DisableAutoEnsure(t *testing.T) {
	// 创建管理器，禁用自动确保钱包
	manager := NewManager(
		WithDBGroup("default"),
		WithAutoEnsureWallet(false),
	)

	ctx := context.Background()
	merchantID := uint64(99998) // 使用一个不太可能存在的商户ID
	tokenSymbol := "TEST2"
	amount := decimal.NewFromFloat(50)

	// 在事务中执行
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 调用 LockBalance，应该失败因为钱包不存在
		err := manager.LockBalance(ctx, tx, merchantID, tokenSymbol, amount)

		// 期望失败，因为钱包不存在
		assert.NotNil(t, err)

		return nil // 回滚事务
	})

	assert.Nil(t, err)
}

// TestEnsuredWalletManager_ValidateBalance 测试验证余额时自动创建钱包
func TestEnsuredWalletManager_ValidateBalance(t *testing.T) {
	// 创建管理器，启用自动确保钱包
	manager := NewManager(
		WithDBGroup("default"),
		WithAutoEnsureWallet(true),
	)

	ctx := context.Background()
	merchantID := uint64(99997) // 使用一个不太可能存在的商户ID
	tokenSymbol := "TEST3"
	amount := decimal.NewFromFloat(10)

	// ValidateBalance 应该自动创建钱包
	err := manager.ValidateBalance(ctx, nil, merchantID, tokenSymbol, amount, WalletTypeAvailable)

	// 期望失败，因为新创建的钱包余额为0
	assert.NotNil(t, err)

	// 验证钱包已经被创建
	walletInfo, err := manager.GetBalance(ctx, nil, merchantID, tokenSymbol)
	assert.Nil(t, err)
	assert.NotNil(t, walletInfo)
	assert.Equal(t, "0", walletInfo.Available.String())
}
