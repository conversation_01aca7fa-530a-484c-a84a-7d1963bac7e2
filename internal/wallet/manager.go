package wallet

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"admin-api/internal/dao"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// manager 钱包管理器实现
type manager struct {
	config        *Config
	tokenConfig   map[string]*TokenConfig
	configManager *ConfigManager
	validator     Validator
	mu            sync.RWMutex // 保护tokenConfig的并发访问
}

// NewManager 创建新的钱包管理器
func NewManager(options ...Option) Manager {
	config := &Config{
		DBGroup:             "default",
		MaxConcurrent:       100,
		MaxRetries:          3,
		RetryDelay:          100 * time.Millisecond,
		EnableAudit:         true,
		AuditLevel:          "info",
		DefaultTokenConfigs: getDefaultTokenConfigs(),
		ConfigPath:          "manifest/config/wallet.yaml", // 默认配置文件路径
		AutoEnsureWallet:    true,                          // 默认启用自动确保钱包存在
	}

	for _, option := range options {
		option(config)
	}

	m := &manager{
		config:      config,
		tokenConfig: make(map[string]*TokenConfig),
	}

	// 初始化验证器（在配置加载后更新）
	m.validator = NewValidator(m.tokenConfig, m.configManager)

	// 初始化配置管理器
	if config.ConfigPath != "" {
		configManager, err := NewConfigManager(config.ConfigPath)
		if err != nil {
			g.Log().Warningf(context.Background(), "初始化配置管理器失败，使用默认配置: %v", err)
		} else {
			m.configManager = configManager
			// 添加配置变化监听器
			configManager.AddConfigWatcher(m.onConfigChanged)
			// 从配置文件加载代币配置
			m.loadTokenConfigsFromFile()
		}
	}

	// 如果没有配置管理器或加载失败，使用默认配置
	if len(m.tokenConfig) == 0 {
		for symbol, cfg := range config.DefaultTokenConfigs {
			m.tokenConfig[strings.ToUpper(symbol)] = cfg
		}
	}

	// 根据配置决定是否使用装饰器
	if config.AutoEnsureWallet {
		g.Log().Infof(context.Background(), "启用自动确保钱包存在功能")
		return NewEnsuredWalletManager(m, config.DBGroup)
	}

	return m
}

// loadTokenConfigsFromFile 从配置文件加载代币配置
func (m *manager) loadTokenConfigsFromFile() {
	if m.configManager == nil {
		return
	}

	walletConfig := m.configManager.GetConfig()
	if walletConfig == nil || walletConfig.Tokens == nil {
		return
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 清空现有配置
	m.tokenConfig = make(map[string]*TokenConfig)

	// 加载新配置
	for symbol, extConfig := range walletConfig.Tokens {
		if !extConfig.Enabled {
			continue
		}

		tokenConfig := &TokenConfig{
			Symbol:        extConfig.Symbol,
			DecimalPlaces: extConfig.DecimalPlaces,
			MinAmount:     extConfig.MinAmount,
			MaxAmount:     extConfig.MaxAmount,
		}

		m.tokenConfig[strings.ToUpper(symbol)] = tokenConfig
	}

	g.Log().Infof(context.Background(), "从配置文件加载了 %d 个代币配置", len(m.tokenConfig))
}

// onConfigChanged 配置变化回调
func (m *manager) onConfigChanged(config *WalletConfig) {
	g.Log().Infof(context.Background(), "检测到配置文件变化，重新加载代币配置")
	m.loadTokenConfigsFromFile()
	// 更新验证器
	m.validator = NewValidator(m.tokenConfig, m.configManager)
}

// getDefaultTokenConfigs 获取默认代币配置
func getDefaultTokenConfigs() map[string]*TokenConfig {
	return map[string]*TokenConfig{
		"BTC": {
			Symbol:        "BTC",
			DecimalPlaces: 8,
			MinAmount:     decimal.NewFromFloat(0.00000001),
			MaxAmount:     decimal.NewFromFloat(1000000),
		},
		"ETH": {
			Symbol:        "ETH",
			DecimalPlaces: 18,
			MinAmount:     decimal.NewFromFloat(0.000000000000000001),
			MaxAmount:     decimal.NewFromFloat(1000000),
		},
		"TRX": {
			Symbol:        "TRX",
			DecimalPlaces: 6,
			MinAmount:     decimal.NewFromFloat(0.000001),
			MaxAmount:     decimal.NewFromFloat(1000000),
		},
		"USDT": {
			Symbol:        "USDT",
			DecimalPlaces: 6,
			MinAmount:     decimal.NewFromFloat(0.000001),
			MaxAmount:     decimal.NewFromFloat(1000000),
		},
		"USDC": {
			Symbol:        "USDC",
			DecimalPlaces: 6,
			MinAmount:     decimal.NewFromFloat(0.000001),
			MaxAmount:     decimal.NewFromFloat(1000000),
		},
	}
}

// GetBalance 获取钱包余额信息
// 如果 tx 为 nil，则不使用事务；否则在指定事务中执行
func (m *manager) GetBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	// 先尝试获取现有钱包
	wallet, err := m.getWalletWithoutError(ctx, tx, merchantID, tokenSymbol)
	if err != nil {
		return nil, ErrDatabaseError("获取钱包信息", err.Error())
	}

	if wallet != nil {
		// 钱包已存在，返回余额信息
		available := m.convertIntToDecimal(wallet.AvailableBalance, wallet.DecimalPlaces)
		frozen := m.convertIntToDecimal(wallet.FrozenBalance, wallet.DecimalPlaces)

		return &WalletInfo{
			WalletID:      int64(wallet.WalletId),
			MerchantID:    uint64(wallet.MerchantId),
			TokenSymbol:   wallet.Symbol,
			Available:     available,
			Frozen:        frozen,
			DecimalPlaces: wallet.DecimalPlaces,
		}, nil
	}

	// 钱包不存在，自动创建新钱包
	g.Log().Infof(ctx, "钱包不存在，自动创建新钱包: merchantID=%d, tokenSymbol=%s", merchantID, tokenSymbol)

	if tx != nil {
		// 在现有事务中创建钱包
		return m.EnsureWallet(ctx, tx, merchantID, tokenSymbol)
	} else {
		// 创建新事务来创建钱包
		var result *WalletInfo
		err = g.DB(m.config.DBGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			result, err = m.EnsureWallet(ctx, tx, merchantID, tokenSymbol)
			return err
		})
		if err != nil {
			return nil, err
		}
		return result, nil
	}
}

// LockBalance 锁定余额
// tx 参数不能为 nil，必须在事务中执行
func (m *manager) LockBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal) error {
	// 检查事务参数
	if tx == nil {
		return ErrTransactionRequired("LockBalance")
	}
	if amount.IsNegative() {
		return ErrNegativeAmount()
	}
	if amount.IsZero() {
		return ErrZeroAmount()
	}

	// 获取当前钱包信息（行锁定）
	wallet := &entity.MerchantWallets{}
	err := dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("merchant_id = ? AND symbol = ?", merchantID, strings.ToUpper(tokenSymbol)).
		LockUpdate().
		Scan(wallet)
	if err != nil {
		// 如果是 "no rows" 错误，说明钱包不存在
		if strings.Contains(err.Error(), "no rows") {
			return ErrWalletNotFound(merchantID, tokenSymbol)
		}
		return ErrDatabaseError("获取钱包信息", err.Error())
	}

	if wallet.WalletId == 0 {
		return ErrWalletNotFound(merchantID, tokenSymbol)
	}

	// 检查可用余额是否足够
	available := m.convertIntToDecimal(wallet.AvailableBalance, wallet.DecimalPlaces)
	frozen := m.convertIntToDecimal(wallet.FrozenBalance, wallet.DecimalPlaces)
	realAvailable := available // 修复：available_balance就是真实可用余额

	g.Log().Debugf(ctx, "余额检查 - 商户ID: %d, 代币: %s, 数据库可用余额: %d, 数据库冻结余额: %d, 小数位: %d",
		merchantID, tokenSymbol, wallet.AvailableBalance, wallet.FrozenBalance, wallet.DecimalPlaces)
	g.Log().Debugf(ctx, "余额转换 - 转换后可用余额: %s, 转换后冻结余额: %s, 真实可用余额: %s, 请求金额: %s",
		available.String(), frozen.String(), realAvailable.String(), amount.String())

	if realAvailable.LessThan(amount) {
		g.Log().Errorf(ctx, "余额不足 - 需要: %s, 可用: %s", amount.String(), realAvailable.String())
		return ErrInsufficientBalance(amount.String(), realAvailable.String())
	}

	// 计算新的余额：从可用余额转移到冻结余额
	newAvailableAmount := m.convertDecimalToInt(available.Sub(amount), wallet.DecimalPlaces)
	newFrozenAmount := m.convertDecimalToInt(frozen.Add(amount), wallet.DecimalPlaces)

	g.Log().Debugf(ctx, "钱包更新 - 钱包ID: %d, 原可用余额: %d->%d, 原冻结余额: %d->%d",
		wallet.WalletId, wallet.AvailableBalance, newAvailableAmount, wallet.FrozenBalance, newFrozenAmount)

	// 更新钱包：同时更新可用余额和冻结余额
	result, err := dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("wallet_id = ?", wallet.WalletId).
		Update(g.Map{
			"available_balance": newAvailableAmount,
			"frozen_balance":    newFrozenAmount,
			"updated_at":        time.Now(),
		})

	if err != nil {
		g.Log().Errorf(ctx, "更新钱包余额失败: %v", err)
		return ErrDatabaseError("更新钱包余额", err.Error())
	}

	rowsAffected, _ := result.RowsAffected()
	g.Log().Debugf(ctx, "钱包更新结果 - 影响行数: %d", rowsAffected)

	// 验证更新后的余额
	walletAfter := &entity.MerchantWallets{}
	err = dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("wallet_id = ?", wallet.WalletId).
		Scan(walletAfter)
	if err != nil {
		g.Log().Errorf(ctx, "验证更新后余额失败: %v", err)
	} else {
		g.Log().Debugf(ctx, "更新后验证 - 可用余额: %d, 冻结余额: %d",
			walletAfter.AvailableBalance, walletAfter.FrozenBalance)
	}

	return nil
}

// UnlockBalance 解锁余额
// tx 参数不能为 nil，必须在事务中执行
func (m *manager) UnlockBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal) error {
	// 检查事务参数
	if tx == nil {
		return ErrTransactionRequired("UnlockBalance")
	}
	if amount.IsNegative() {
		return ErrNegativeAmount()
	}
	if amount.IsZero() {
		return ErrZeroAmount()
	}

	// 获取当前钱包信息（行锁定）
	wallet := &entity.MerchantWallets{}
	err := dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("merchant_id = ? AND symbol = ?", merchantID, strings.ToUpper(tokenSymbol)).
		LockUpdate().
		Scan(wallet)
	if err != nil {
		// 如果是 "no rows" 错误，说明钱包不存在
		if strings.Contains(err.Error(), "no rows") {
			return ErrWalletNotFound(merchantID, tokenSymbol)
		}
		return ErrDatabaseError("获取钱包信息", err.Error())
	}

	if wallet.WalletId == 0 {
		return ErrWalletNotFound(merchantID, tokenSymbol)
	}

	// 检查冻结余额是否足够
	available := m.convertIntToDecimal(wallet.AvailableBalance, wallet.DecimalPlaces)
	frozen := m.convertIntToDecimal(wallet.FrozenBalance, wallet.DecimalPlaces)

	if frozen.LessThan(amount) {
		return ErrInsufficientFrozen(amount.String(), frozen.String())
	}

	// 计算新的余额：从冻结余额转移到可用余额
	newAvailableAmount := m.convertDecimalToInt(available.Add(amount), wallet.DecimalPlaces)
	newFrozenAmount := m.convertDecimalToInt(frozen.Sub(amount), wallet.DecimalPlaces)

	g.Log().Debugf(ctx, "钱包解冻 - 钱包ID: %d, 原可用余额: %d->%d, 原冻结余额: %d->%d",
		wallet.WalletId, wallet.AvailableBalance, newAvailableAmount, wallet.FrozenBalance, newFrozenAmount)

	// 更新钱包：同时更新可用余额和冻结余额
	_, err = dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("wallet_id = ?", wallet.WalletId).
		Update(g.Map{
			"available_balance": newAvailableAmount,
			"frozen_balance":    newFrozenAmount,
			"updated_at":        time.Now(),
		})

	if err != nil {
		return ErrDatabaseError("更新钱包余额", err.Error())
	}

	return nil
}

// UpdateBalance 更新余额
// tx 参数不能为 nil，必须在事务中执行
func (m *manager) UpdateBalance(ctx context.Context, tx gdb.TX, req *TransactionRequest) (*TransactionResult, error) {
	// 检查事务参数
	if tx == nil {
		return nil, ErrTransactionRequired("UpdateBalance")
	}
	// 参数验证
	if err := m.validateTransactionRequest(req); err != nil {
		return nil, err
	}

	// 获取交易前余额
	balanceBefore, err := m.getBalanceSnapshot(ctx, tx, req.MerchantID, req.TokenSymbol)
	if err != nil {
		return nil, ErrDatabaseError("获取交易前余额", err.Error())
	}

	// 执行余额更新
	if err := m.executeBalanceUpdate(ctx, tx, req, balanceBefore); err != nil {
		return nil, err
	}

	// 获取交易后余额
	balanceAfter, err := m.getBalanceSnapshot(ctx, tx, req.MerchantID, req.TokenSymbol)
	if err != nil {
		return nil, ErrDatabaseError("获取交易后余额", err.Error())
	}

	// 记录交易
	recorder := NewTransactionRecorder()
	result, err := recorder.RecordTransactionInTx(ctx, tx, req, balanceBefore, balanceAfter)
	if err != nil {
		return nil, ErrDatabaseError("记录交易", err.Error())
	}

	return result, nil
}

// executeBalanceUpdate 执行余额更新
func (m *manager) executeBalanceUpdate(ctx context.Context, tx gdb.TX, req *TransactionRequest, balanceBefore *BalanceSnapshot) error {
	// 先确保钱包存在（如果不存在则自动创建）
	walletInfo, err := m.EnsureWallet(ctx, tx, req.MerchantID, req.TokenSymbol)
	if err != nil {
		return ErrDatabaseError("确保钱包存在", err.Error())
	}

	// 获取当前钱包信息（行锁定）
	wallet := &entity.MerchantWallets{}
	err = dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("merchant_id = ? AND symbol = ?", req.MerchantID, strings.ToUpper(req.TokenSymbol)).
		LockUpdate().
		Scan(wallet)
	if err != nil {
		return ErrDatabaseError("获取钱包信息", err.Error())
	}

	if wallet.WalletId == 0 {
		return ErrWalletNotFound(req.MerchantID, req.TokenSymbol)
	}

	// 验证钱包ID匹配
	if int64(wallet.WalletId) != walletInfo.WalletID {
		g.Log().Warningf(ctx, "钱包ID不匹配: expected=%d, actual=%d", walletInfo.WalletID, wallet.WalletId)
	}

	// 计算新余额
	var newAvailableBalance, newFrozenBalance int64

	switch req.WalletType {
	case WalletTypeAvailable:
		newAvailableBalance, newFrozenBalance, err = m.calculateAvailableBalanceUpdate(wallet, req)
	case WalletTypeFrozen:
		newAvailableBalance, newFrozenBalance, err = m.calculateFrozenBalanceUpdate(wallet, req)
	default:
		return gerror.Wrap(ErrInvalidWalletType(string(req.WalletType)), "无效钱包类型")
	}

	if err != nil {
		return err
	}

	// 更新钱包
	_, err = dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("wallet_id = ?", wallet.WalletId).
		Update(g.Map{
			"available_balance": newAvailableBalance,
			"frozen_balance":    newFrozenBalance,
			"updated_at":        time.Now(),
		})

	if err != nil {
		return ErrDatabaseError("更新钱包余额", err.Error())
	}

	return nil
}

// calculateAvailableBalanceUpdate 计算可用余额更新
func (m *manager) calculateAvailableBalanceUpdate(wallet *entity.MerchantWallets, req *TransactionRequest) (int64, int64, error) {
	available := m.convertIntToDecimal(wallet.AvailableBalance, wallet.DecimalPlaces)
	// frozen := m.convertIntToDecimal(wallet.FrozenBalance, wallet.DecimalPlaces)

	var newAvailable decimal.Decimal

	switch req.Direction {
	case DirectionIn:
		newAvailable = available.Add(req.Amount)
	case DirectionOut:
		newAvailable = available.Sub(req.Amount)
		if newAvailable.IsNegative() {
			return 0, 0, gerror.Newf("余额不足, 当前: %s, 需要: %s", available.String(), req.Amount.String())
		}
	default:
		return 0, 0, gerror.Newf("不支持的资金方向: %s", req.Direction)
	}

	newAvailableBalance := m.convertDecimalToInt(newAvailable, wallet.DecimalPlaces)
	newFrozenBalance := wallet.FrozenBalance

	return newAvailableBalance, newFrozenBalance, nil
}

// calculateFrozenBalanceUpdate 计算冻结余额更新
func (m *manager) calculateFrozenBalanceUpdate(wallet *entity.MerchantWallets, req *TransactionRequest) (int64, int64, error) {
	// available := m.convertIntToDecimal(wallet.AvailableBalance, wallet.DecimalPlaces)
	frozen := m.convertIntToDecimal(wallet.FrozenBalance, wallet.DecimalPlaces)

	var newFrozen decimal.Decimal

	switch req.Direction {
	case DirectionIn:
		newFrozen = frozen.Add(req.Amount)
	case DirectionOut:
		newFrozen = frozen.Sub(req.Amount)
		if newFrozen.IsNegative() {
			return 0, 0, ErrInsufficientFrozen(req.Amount.String(), frozen.String())
		}
	default:
		return 0, 0, ErrInvalidParam("direction", "不支持的资金方向: "+string(req.Direction))
	}

	newAvailableBalance := wallet.AvailableBalance
	newFrozenBalance := m.convertDecimalToInt(newFrozen, wallet.DecimalPlaces)

	return newAvailableBalance, newFrozenBalance, nil
}

// getBalanceSnapshot 获取余额快照
func (m *manager) getBalanceSnapshot(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*BalanceSnapshot, error) {
	wallet, err := m.GetBalance(ctx, tx, merchantID, tokenSymbol)
	if err != nil {
		return nil, err
	}

	return &BalanceSnapshot{
		Available: wallet.Available,
		Frozen:    wallet.Frozen,
		Total:     wallet.Available.Add(wallet.Frozen),
	}, nil
}

// validateTransactionRequest 验证交易请求
func (m *manager) validateTransactionRequest(req *TransactionRequest) error {
	return m.validator.ValidateTransactionRequest(req)
}

// 精度转换函数
func (m *manager) convertIntToDecimal(amount int64, decimalPlaces uint) decimal.Decimal {
	if decimalPlaces == 0 {
		return decimal.NewFromInt(amount)
	}

	divisor := decimal.NewFromInt(1)
	for i := uint(0); i < decimalPlaces; i++ {
		divisor = divisor.Mul(decimal.NewFromInt(10))
	}

	return decimal.NewFromInt(amount).Div(divisor)
}

func (m *manager) convertDecimalToInt(amount decimal.Decimal, decimalPlaces uint) int64 {
	if decimalPlaces == 0 {
		return amount.IntPart()
	}

	multiplier := decimal.NewFromInt(1)
	for i := uint(0); i < decimalPlaces; i++ {
		multiplier = multiplier.Mul(decimal.NewFromInt(10))
	}

	return amount.Mul(multiplier).IntPart()
}

// CreateWallet 创建新钱包
// tx 参数不能为 nil，必须在事务中执行
func (m *manager) CreateWallet(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	// 检查事务参数
	if tx == nil {
		return nil, ErrTransactionRequired("CreateWallet")
	}
	tokenSymbol = strings.ToUpper(tokenSymbol)

	// 获取代币配置
	tokenConfig, err := m.GetTokenConfig(tokenSymbol)
	if err != nil {
		return nil, ErrTokenNotSupported(tokenSymbol)
	}

	// 检查钱包是否已经存在
	existingWallet := &entity.MerchantWallets{}
	err = dao.MerchantWallets.Ctx(ctx).TX(tx).
		Where("merchant_id = ? AND symbol = ?", merchantID, tokenSymbol).
		Scan(existingWallet)

	if err == nil && existingWallet.WalletId > 0 {
		// 钱包已存在，返回现有钱包信息
		available := m.convertIntToDecimal(existingWallet.AvailableBalance, existingWallet.DecimalPlaces)
		frozen := m.convertIntToDecimal(existingWallet.FrozenBalance, existingWallet.DecimalPlaces)

		return &WalletInfo{
			WalletID:      int64(existingWallet.WalletId),
			MerchantID:    uint64(existingWallet.MerchantId),
			TokenSymbol:   existingWallet.Symbol,
			Available:     available,
			Frozen:        frozen,
			DecimalPlaces: existingWallet.DecimalPlaces,
		}, nil
	}

	// 生成唯一的 ledger_id
	ledgerID := fmt.Sprintf("wallet_%d_%s_%d", merchantID, tokenSymbol, time.Now().UnixNano())

	// 创建新钱包记录
	newWallet := &entity.MerchantWallets{
		LedgerId:         ledgerID,
		MerchantId:       uint(merchantID),
		TokenId:          0, // 可以根据需要设置
		AvailableBalance: 0, // 初始余额为0
		FrozenBalance:    0, // 初始冻结余额为0
		DecimalPlaces:    tokenConfig.DecimalPlaces,
		Type:             "wallet", // 可以根据需要调整
		Symbol:           tokenSymbol,
	}

	res, err := dao.MerchantWallets.Ctx(ctx).TX(tx).Data(newWallet).Insert()
	if err != nil {
		return nil, ErrDatabaseError("创建钱包", err.Error())
	}

	walletID, _ := res.LastInsertId()

	g.Log().Infof(ctx, "成功创建钱包: merchantID=%d, tokenSymbol=%s, walletID=%d",
		merchantID, tokenSymbol, walletID)

	return &WalletInfo{
		WalletID:      walletID,
		MerchantID:    merchantID,
		TokenSymbol:   tokenSymbol,
		Available:     decimal.Zero,
		Frozen:        decimal.Zero,
		DecimalPlaces: tokenConfig.DecimalPlaces,
	}, nil
}

// EnsureWallet 确保钱包存在，不存在则创建
// tx 参数不能为 nil，必须在事务中执行
func (m *manager) EnsureWallet(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	// 检查事务参数
	if tx == nil {
		return nil, ErrTransactionRequired("EnsureWallet")
	}
	// 先尝试获取现有钱包
	wallet, err := m.getWalletWithoutError(ctx, tx, merchantID, tokenSymbol)
	if err == nil && wallet != nil {
		// 钱包已存在
		available := m.convertIntToDecimal(wallet.AvailableBalance, wallet.DecimalPlaces)
		frozen := m.convertIntToDecimal(wallet.FrozenBalance, wallet.DecimalPlaces)

		return &WalletInfo{
			WalletID:      int64(wallet.WalletId),
			MerchantID:    uint64(wallet.MerchantId),
			TokenSymbol:   wallet.Symbol,
			Available:     available,
			Frozen:        frozen,
			DecimalPlaces: wallet.DecimalPlaces,
		}, nil
	}

	// 钱包不存在，创建新钱包
	return m.CreateWallet(ctx, tx, merchantID, tokenSymbol)
}

// getWalletWithoutError 获取钱包记录，不存在时返回 nil 而不是错误
func (m *manager) getWalletWithoutError(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*entity.MerchantWallets, error) {
	model := dao.MerchantWallets.Ctx(ctx)
	if tx != nil {
		model = dao.MerchantWallets.Ctx(ctx).TX(tx)
	}

	wallet := &entity.MerchantWallets{}
	err := model.Where("merchant_id = ? AND symbol = ?", merchantID, strings.ToUpper(tokenSymbol)).
		Scan(wallet)

	if err != nil {
		// 如果是 "no rows" 错误，说明钱包不存在，返回 nil 而不是错误
		if strings.Contains(err.Error(), "no rows") {
			return nil, nil
		}
		return nil, err
	}

	if wallet.WalletId == 0 {
		return nil, nil // 钱包不存在，返回 nil
	}

	return wallet, nil
}
