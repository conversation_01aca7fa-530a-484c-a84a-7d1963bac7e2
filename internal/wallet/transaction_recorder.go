package wallet

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"admin-api/internal/dao"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
)

// transactionRecorder 交易记录器实现
type transactionRecorder struct {
	dbGroup string
}

// NewTransactionRecorder 创建新的交易记录器
func NewTransactionRecorder(options ...Option) TransactionRecorder {
	config := &Config{
		DBGroup: "default",
	}

	for _, option := range options {
		option(config)
	}

	return &transactionRecorder{
		dbGroup: config.DBGroup,
	}
}

// RecordTransaction 记录交易
func (r *transactionRecorder) RecordTransaction(ctx context.Context, req *TransactionRequest, balanceBefore, balanceAfter *BalanceSnapshot) (*TransactionResult, error) {
	var result *TransactionResult
	err := g.DB(r.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err error
		result, err = r.RecordTransactionInTx(ctx, tx, req, balanceBefore, balanceAfter)
		return err
	})
	return result, err
}

// RecordTransactionInTx 在事务中记录交易
func (r *transactionRecorder) RecordTransactionInTx(ctx context.Context, tx gdb.TX, req *TransactionRequest, balanceBefore, balanceAfter *BalanceSnapshot) (*TransactionResult, error) {
	if err := r.validateRecordRequest(req, balanceBefore, balanceAfter); err != nil {
		return nil, err
	}

	// 构建交易记录
	transaction := &entity.MerchantTransactions{
		MerchantId:    uint(req.MerchantID),
		TokenId:       0, // 可以根据需要关联token表
		Type:          string(req.Operation),
		WalletType:    string(req.WalletType),
		Direction:     string(req.Direction),
		Amount:        req.Amount,
		BalanceBefore: r.getBalanceByWalletType(balanceBefore, req.WalletType),
		BalanceAfter:  r.getBalanceByWalletType(balanceAfter, req.WalletType),
		Status:        uint(StatusSuccess),
		Symbol:        strings.ToUpper(req.TokenSymbol),
		BusinessId:    req.BusinessID,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
		ProcessedAt:   gtime.Now(),
	}

	// 设置可选字段
	r.setOptionalFields(transaction, req)

	// 插入交易记录
	result, err := dao.MerchantTransactions.Ctx(ctx).TX(tx).InsertAndGetId(transaction)
	if err != nil {
		return nil, gerror.Wrapf(err, "插入交易记录失败")
	}

	transactionID := gconv.Uint64(result)

	return &TransactionResult{
		TransactionID: transactionID,
		BalanceBefore: balanceBefore,
		BalanceAfter:  balanceAfter,
		ProcessedAt:   time.Now(),
		Status:        StatusSuccess,
	}, nil
}

// GetTransaction 获取交易记录
func (r *transactionRecorder) GetTransaction(ctx context.Context, transactionID uint64) (*TransactionResult, error) {
	transaction := &entity.MerchantTransactions{}
	err := dao.MerchantTransactions.Ctx(ctx).
		Where("transaction_id = ?", transactionID).
		Scan(transaction)
	if err != nil {
		return nil, gerror.Wrapf(err, "获取交易记录失败, transactionID: %d", transactionID)
	}

	if transaction.TransactionId == 0 {
		return nil, gerror.Newf("交易记录不存在, transactionID: %d", transactionID)
	}

	return r.convertToTransactionResult(transaction), nil
}

// GetTransactionsByMerchant 获取商户的交易记录
func (r *transactionRecorder) GetTransactionsByMerchant(ctx context.Context, merchantID uint64, limit, offset int) ([]*TransactionResult, error) {
	var transactions []*entity.MerchantTransactions

	model := dao.MerchantTransactions.Ctx(ctx).
		Where("merchant_id = ?", merchantID).
		OrderDesc("transaction_id")

	if limit > 0 {
		model = model.Limit(limit)
	}
	if offset > 0 {
		model = model.Offset(offset)
	}

	err := model.Scan(&transactions)
	if err != nil {
		return nil, gerror.Wrapf(err, "获取商户交易记录失败, merchantID: %d", merchantID)
	}

	results := make([]*TransactionResult, 0, len(transactions))
	for _, transaction := range transactions {
		results = append(results, r.convertToTransactionResult(transaction))
	}

	return results, nil
}

// GetTransactionsByBusiness 根据业务ID获取交易记录
func (r *transactionRecorder) GetTransactionsByBusiness(ctx context.Context, businessID string) (*TransactionResult, error) {
	transaction := &entity.MerchantTransactions{}
	err := dao.MerchantTransactions.Ctx(ctx).
		Where("business_id = ?", businessID).
		Scan(transaction)
	if err != nil {
		return nil, gerror.Wrapf(err, "获取业务交易记录失败, businessID: %s", businessID)
	}

	if transaction.TransactionId == 0 {
		return nil, gerror.Newf("业务交易记录不存在, businessID: %s", businessID)
	}

	return r.convertToTransactionResult(transaction), nil
}

// validateRecordRequest 验证记录请求
func (r *transactionRecorder) validateRecordRequest(req *TransactionRequest, balanceBefore, balanceAfter *BalanceSnapshot) error {
	if req == nil {
		return ErrInvalidParam("request", "交易请求不能为空")
	}

	if balanceBefore == nil {
		return ErrInvalidParam("balanceBefore", "交易前余额快照不能为空")
	}

	if balanceAfter == nil {
		return ErrInvalidParam("balanceAfter", "交易后余额快照不能为空")
	}

	if req.BusinessID == "" {
		return ErrInvalidBusinessID(req.BusinessID, "业务ID不能为空")
	}

	return nil
}

// setOptionalFields 设置可选字段
func (r *transactionRecorder) setOptionalFields(transaction *entity.MerchantTransactions, req *TransactionRequest) {
	if req.RelatedEntityID != nil {
		transaction.RelatedEntityId = *req.RelatedEntityID
	}

	if req.RelatedEntityType != nil {
		transaction.RelatedEntityType = *req.RelatedEntityType
	}

	if req.Memo != nil {
		transaction.Memo = *req.Memo
	}

	if req.RequestAmount != nil {
		transaction.RequestAmount = *req.RequestAmount
	}

	if req.RequestReference != nil {
		transaction.RequestReference = *req.RequestReference
	}

	if req.RequestMetadata != nil && len(req.RequestMetadata) > 0 {
		if metadataBytes, err := json.Marshal(req.RequestMetadata); err == nil {
			transaction.RequestMetadata = gjson.New(metadataBytes)
		}
	}

	if req.RequestSource != nil {
		transaction.RequestSource = *req.RequestSource
	}

	if req.RequestIP != nil {
		transaction.RequestIp = *req.RequestIP
	}

	if req.RequestUserAgent != nil {
		transaction.RequestUserAgent = *req.RequestUserAgent
	}

	if req.FeeAmount != nil {
		transaction.FeeAmount = *req.FeeAmount
	}

	if req.FeeType != nil {
		transaction.FeeType = *req.FeeType
	}

	if req.TargetMerchantID != nil {
		transaction.TargetUserId = uint(*req.TargetMerchantID)
	}

	if req.TargetUsername != nil {
		transaction.TargetUsername = *req.TargetUsername
	}

	// 设置请求时间戳
	transaction.RequestTimestamp = gtime.Now()
}

// getBalanceByWalletType 根据钱包类型获取对应余额
func (r *transactionRecorder) getBalanceByWalletType(snapshot *BalanceSnapshot, walletType WalletType) decimal.Decimal {
	switch walletType {
	case WalletTypeAvailable:
		return snapshot.Available
	case WalletTypeFrozen:
		return snapshot.Frozen
	default:
		return snapshot.Total
	}
}

// convertToTransactionResult 转换为交易结果
func (r *transactionRecorder) convertToTransactionResult(transaction *entity.MerchantTransactions) *TransactionResult {
	// 根据钱包类型重建余额快照
	balanceBefore := &BalanceSnapshot{}
	balanceAfter := &BalanceSnapshot{}

	switch WalletType(transaction.WalletType) {
	case WalletTypeAvailable:
		balanceBefore.Available = transaction.BalanceBefore
		balanceAfter.Available = transaction.BalanceAfter
	case WalletTypeFrozen:
		balanceBefore.Frozen = transaction.BalanceBefore
		balanceAfter.Frozen = transaction.BalanceAfter
	}

	processedAt := time.Now()
	if transaction.ProcessedAt != nil {
		processedAt = transaction.ProcessedAt.Time
	}

	return &TransactionResult{
		TransactionID: transaction.TransactionId,
		BalanceBefore: balanceBefore,
		BalanceAfter:  balanceAfter,
		ProcessedAt:   processedAt,
		Status:        Status(transaction.Status),
	}
}

// RecordTransferTransaction 记录转账交易（双方记录）
func (r *transactionRecorder) RecordTransferTransaction(ctx context.Context, tx gdb.TX,
	fromReq, toReq *TransactionRequest,
	fromBalanceBefore, fromBalanceAfter, toBalanceBefore, toBalanceAfter *BalanceSnapshot) ([]*TransactionResult, error) {

	// 记录发送方交易
	fromResult, err := r.RecordTransactionInTx(ctx, tx, fromReq, fromBalanceBefore, fromBalanceAfter)
	if err != nil {
		return nil, gerror.Wrap(err, "记录发送方交易失败")
	}

	// 记录接收方交易
	toResult, err := r.RecordTransactionInTx(ctx, tx, toReq, toBalanceBefore, toBalanceAfter)
	if err != nil {
		return nil, gerror.Wrap(err, "记录接收方交易失败")
	}

	// 更新关联交易ID
	_, err = dao.MerchantTransactions.Ctx(ctx).TX(tx).
		Where("transaction_id = ?", fromResult.TransactionID).
		Update(g.Map{"related_transaction_id": toResult.TransactionID})
	if err != nil {
		return nil, gerror.Wrap(err, "更新发送方关联交易ID失败")
	}

	_, err = dao.MerchantTransactions.Ctx(ctx).TX(tx).
		Where("transaction_id = ?", toResult.TransactionID).
		Update(g.Map{"related_transaction_id": fromResult.TransactionID})
	if err != nil {
		return nil, gerror.Wrap(err, "更新接收方关联交易ID失败")
	}

	return []*TransactionResult{fromResult, toResult}, nil
}

// BatchRecordTransactions 批量记录交易
func (r *transactionRecorder) BatchRecordTransactions(ctx context.Context, tx gdb.TX,
	reqs []*TransactionRequest, balanceSnapshots [][]*BalanceSnapshot) ([]*TransactionResult, error) {

	if len(reqs) != len(balanceSnapshots) {
		return nil, gerror.New("请求数量与余额快照数量不匹配")
	}

	results := make([]*TransactionResult, 0, len(reqs))

	for i, req := range reqs {
		if len(balanceSnapshots[i]) != 2 {
			return nil, gerror.Newf("余额快照数量错误, 期望2个, 实际%d个", len(balanceSnapshots[i]))
		}

		result, err := r.RecordTransactionInTx(ctx, tx, req, balanceSnapshots[i][0], balanceSnapshots[i][1])
		if err != nil {
			return nil, gerror.Wrapf(err, "批量记录交易失败, businessID: %s", req.BusinessID)
		}

		results = append(results, result)
	}

	return results, nil
}

// RecordFreezeTransaction 记录冻结/解冻交易（双记录）
// 冻结操作会生成两条交易记录：
// 1. 可用余额减少记录
// 2. 冻结余额增加记录
func (r *transactionRecorder) RecordFreezeTransaction(ctx context.Context, tx gdb.TX,
	availableReq, frozenReq *TransactionRequest,
	balanceBefore, balanceAfter *BalanceSnapshot) ([]*TransactionResult, error) {

	// 构建可用余额的快照（只包含可用余额的变化）
	availableBefore := &BalanceSnapshot{
		Available: balanceBefore.Available,
		Frozen:    decimal.Zero, // 只关注可用余额
		Total:     balanceBefore.Available,
	}
	availableAfter := &BalanceSnapshot{
		Available: balanceAfter.Available,
		Frozen:    decimal.Zero,
		Total:     balanceAfter.Available,
	}

	// 构建冻结余额的快照（只包含冻结余额的变化）
	frozenBefore := &BalanceSnapshot{
		Available: decimal.Zero, // 只关注冻结余额
		Frozen:    balanceBefore.Frozen,
		Total:     balanceBefore.Frozen,
	}
	frozenAfter := &BalanceSnapshot{
		Available: decimal.Zero,
		Frozen:    balanceAfter.Frozen,
		Total:     balanceAfter.Frozen,
	}

	// 记录可用余额变化
	availableResult, err := r.RecordTransactionInTx(ctx, tx, availableReq, availableBefore, availableAfter)
	if err != nil {
		return nil, gerror.Wrap(err, "记录可用余额变化失败")
	}

	// 记录冻结余额变化
	frozenResult, err := r.RecordTransactionInTx(ctx, tx, frozenReq, frozenBefore, frozenAfter)
	if err != nil {
		return nil, gerror.Wrap(err, "记录冻结余额变化失败")
	}

	// 更新关联交易ID，将两条记录关联起来
	_, err = dao.MerchantTransactions.Ctx(ctx).TX(tx).
		Where("transaction_id = ?", availableResult.TransactionID).
		Update(g.Map{"related_transaction_id": frozenResult.TransactionID})
	if err != nil {
		return nil, gerror.Wrap(err, "更新可用余额交易关联ID失败")
	}

	_, err = dao.MerchantTransactions.Ctx(ctx).TX(tx).
		Where("transaction_id = ?", frozenResult.TransactionID).
		Update(g.Map{"related_transaction_id": availableResult.TransactionID})
	if err != nil {
		return nil, gerror.Wrap(err, "更新冻结余额交易关联ID失败")
	}

	return []*TransactionResult{availableResult, frozenResult}, nil
}
