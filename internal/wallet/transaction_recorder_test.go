package wallet

import (
	"context"
	"fmt"
	"testing"
	"time"

	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/test/gtest"
	"github.com/shopspring/decimal"
)

// TestTransactionRecorder_NewTransactionRecorder 测试创建交易记录器
func TestTransactionRecorder_NewTransactionRecorder(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试默认配置
		recorder := NewTransactionRecorder()
		t.AssertNE(recorder, nil)

		// 测试自定义配置
		recorder2 := NewTransactionRecorder(
			WithDBGroup("test"),
		)
		t.AssertNE(recorder2, nil)
	})
}

// TestTransactionRecorder_ValidateRecordRequest 测试记录请求验证
func TestTransactionRecorder_ValidateRecordRequest(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		recorder := NewTransactionRecorder()

		// 创建有效的交易请求
		validReq := &TransactionRequest{
			MerchantID:  1,
			TokenSymbol: "USDT",
			Amount:      decimal.NewFromFloat(100),
			Operation:   OperationDeposit,
			Direction:   DirectionIn,
			WalletType:  WalletTypeAvailable,
			BusinessID:  "test_business_id",
		}

		// 创建有效的余额快照
		balanceBefore := &BalanceSnapshot{
			Available: decimal.NewFromFloat(0),
			Frozen:    decimal.NewFromFloat(0),
			Total:     decimal.NewFromFloat(0),
		}

		balanceAfter := &BalanceSnapshot{
			Available: decimal.NewFromFloat(100),
			Frozen:    decimal.NewFromFloat(0),
			Total:     decimal.NewFromFloat(100),
		}

		// 测试有效请求
		err := recorder.(*transactionRecorder).validateRecordRequest(validReq, balanceBefore, balanceAfter)
		t.AssertNil(err)

		// 测试空交易请求
		err = recorder.(*transactionRecorder).validateRecordRequest(nil, balanceBefore, balanceAfter)
		t.AssertNE(err, nil)
		if !IsWalletError(err) {
			t.Fatalf("Expected WalletError, got: %v", err)
		}

		// 测试空余额快照（交易前）
		err = recorder.(*transactionRecorder).validateRecordRequest(validReq, nil, balanceAfter)
		t.AssertNE(err, nil)

		// 测试空余额快照（交易后）
		err = recorder.(*transactionRecorder).validateRecordRequest(validReq, balanceBefore, nil)
		t.AssertNE(err, nil)

		// 测试空业务ID
		invalidReq := &TransactionRequest{
			MerchantID:  1,
			TokenSymbol: "USDT",
			Amount:      decimal.NewFromFloat(100),
			Operation:   OperationDeposit,
			Direction:   DirectionIn,
			WalletType:  WalletTypeAvailable,
			BusinessID:  "",
		}
		err = recorder.(*transactionRecorder).validateRecordRequest(invalidReq, balanceBefore, balanceAfter)
		t.AssertNE(err, nil)
	})
}

// TestTransactionRecorder_GetBalanceByWalletType 测试根据钱包类型获取余额
func TestTransactionRecorder_GetBalanceByWalletType(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		recorder := NewTransactionRecorder()

		snapshot := &BalanceSnapshot{
			Available: decimal.NewFromFloat(100),
			Frozen:    decimal.NewFromFloat(50),
			Total:     decimal.NewFromFloat(150),
		}

		// 测试获取可用余额
		available := recorder.(*transactionRecorder).getBalanceByWalletType(snapshot, WalletTypeAvailable)
		t.Assert(available.Equal(decimal.NewFromFloat(100)), true)

		// 测试获取冻结余额
		frozen := recorder.(*transactionRecorder).getBalanceByWalletType(snapshot, WalletTypeFrozen)
		t.Assert(frozen.Equal(decimal.NewFromFloat(50)), true)

		// 测试获取总余额（默认情况）
		total := recorder.(*transactionRecorder).getBalanceByWalletType(snapshot, "unknown")
		t.Assert(total.Equal(decimal.NewFromFloat(150)), true)
	})
}

// TestTransactionRecorder_SetOptionalFields 测试设置可选字段
func TestTransactionRecorder_SetOptionalFields(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		recorder := NewTransactionRecorder()

		// 创建模拟的交易记录实体
		transaction := &entity.MerchantTransactions{}

		// 创建包含所有可选字段的请求
		relatedEntityID := uint64(123)
		relatedEntityType := "test_entity"
		memo := "测试备注"
		requestAmount := decimal.NewFromFloat(100)
		requestReference := "test_reference"
		requestMetadata := map[string]interface{}{
			"key1": "value1",
			"key2": 123,
		}
		requestSource := "api"
		requestIP := "127.0.0.1"
		requestUserAgent := "test-agent"
		feeAmount := decimal.NewFromFloat(1)
		feeType := "fixed"
		targetMerchantID := uint64(456)
		targetUsername := "target_user"

		req := &TransactionRequest{
			MerchantID:        1,
			TokenSymbol:       "USDT",
			Amount:            decimal.NewFromFloat(100),
			Operation:         OperationTransfer,
			Direction:         DirectionOut,
			WalletType:        WalletTypeAvailable,
			BusinessID:        "test_business_id",
			RelatedEntityID:   &relatedEntityID,
			RelatedEntityType: &relatedEntityType,
			Memo:              &memo,
			RequestAmount:     &requestAmount,
			RequestReference:  &requestReference,
			RequestMetadata:   requestMetadata,
			RequestSource:     &requestSource,
			RequestIP:         &requestIP,
			RequestUserAgent:  &requestUserAgent,
			FeeAmount:         &feeAmount,
			FeeType:           &feeType,
			TargetMerchantID:  &targetMerchantID,
			TargetUsername:    &targetUsername,
		}

		// 设置可选字段
		recorder.(*transactionRecorder).setOptionalFields(transaction, req)

		// 验证字段设置
		t.Assert(transaction.RelatedEntityId, relatedEntityID)
		t.Assert(transaction.RelatedEntityType, relatedEntityType)
		t.Assert(transaction.Memo, memo)
		t.Assert(transaction.RequestAmount.Equal(requestAmount), true)
		t.Assert(transaction.RequestReference, requestReference)
		t.Assert(transaction.RequestSource, requestSource)
		t.Assert(transaction.RequestIp, requestIP)
		t.Assert(transaction.RequestUserAgent, requestUserAgent)
		t.Assert(transaction.FeeAmount.Equal(feeAmount), true)
		t.Assert(transaction.FeeType, feeType)
		t.Assert(transaction.TargetUserId, uint(targetMerchantID))
		t.Assert(transaction.TargetUsername, targetUsername)
		t.AssertNE(transaction.RequestTimestamp, nil)
	})
}

// TestTransactionRecorder_ConvertToTransactionResult 测试转换为交易结果
func TestTransactionRecorder_ConvertToTransactionResult(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		recorder := NewTransactionRecorder()

		// 创建模拟的交易记录实体
		transaction := &entity.MerchantTransactions{
			TransactionId: 123,
			WalletType:    string(WalletTypeAvailable),
			BalanceBefore: decimal.NewFromFloat(100),
			BalanceAfter:  decimal.NewFromFloat(200),
			Status:        uint(StatusSuccess),
		}

		// 转换为交易结果
		result := recorder.(*transactionRecorder).convertToTransactionResult(transaction)

		// 验证转换结果
		t.AssertNE(result, nil)
		t.Assert(result.TransactionID, uint64(123))
		t.Assert(result.Status, StatusSuccess)
		t.AssertNE(result.BalanceBefore, nil)
		t.AssertNE(result.BalanceAfter, nil)
		t.Assert(result.BalanceBefore.Available.Equal(decimal.NewFromFloat(100)), true)
		t.Assert(result.BalanceAfter.Available.Equal(decimal.NewFromFloat(200)), true)
	})
}

// MockTransactionRecorder 用于测试的模拟交易记录器
type MockTransactionRecorder struct {
	transactions map[uint64]*TransactionResult
	businessMap  map[string]*TransactionResult
	nextID       uint64
}

// NewMockTransactionRecorder 创建模拟交易记录器
func NewMockTransactionRecorder() *MockTransactionRecorder {
	return &MockTransactionRecorder{
		transactions: make(map[uint64]*TransactionResult),
		businessMap:  make(map[string]*TransactionResult),
		nextID:       1,
	}
}

// RecordTransaction 模拟记录交易
func (m *MockTransactionRecorder) RecordTransaction(ctx context.Context, req *TransactionRequest, balanceBefore, balanceAfter *BalanceSnapshot) (*TransactionResult, error) {
	if req.BusinessID == "" {
		return nil, ErrInvalidBusinessID(req.BusinessID)
	}

	// 检查业务ID是否重复
	if _, exists := m.businessMap[req.BusinessID]; exists {
		return nil, ErrDuplicateBusinessID(req.BusinessID)
	}

	result := &TransactionResult{
		TransactionID: m.nextID,
		BalanceBefore: balanceBefore,
		BalanceAfter:  balanceAfter,
		ProcessedAt:   time.Now(),
		Status:        StatusSuccess,
	}

	m.transactions[m.nextID] = result
	m.businessMap[req.BusinessID] = result
	m.nextID++

	return result, nil
}

// GetTransaction 模拟获取交易
func (m *MockTransactionRecorder) GetTransaction(ctx context.Context, transactionID uint64) (*TransactionResult, error) {
	result, exists := m.transactions[transactionID]
	if !exists {
		return nil, ErrInvalidParam("transactionID", "交易不存在")
	}
	return result, nil
}

// GetTransactionsByBusiness 模拟根据业务ID获取交易
func (m *MockTransactionRecorder) GetTransactionsByBusiness(ctx context.Context, businessID string) (*TransactionResult, error) {
	result, exists := m.businessMap[businessID]
	if !exists {
		return nil, ErrInvalidBusinessID(businessID)
	}
	return result, nil
}

// TestMockTransactionRecorder 测试模拟交易记录器
func TestMockTransactionRecorder(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		recorder := NewMockTransactionRecorder()
		ctx := context.Background()

		// 创建测试请求
		req := &TransactionRequest{
			MerchantID:  1,
			TokenSymbol: "USDT",
			Amount:      decimal.NewFromFloat(100),
			Operation:   OperationDeposit,
			Direction:   DirectionIn,
			WalletType:  WalletTypeAvailable,
			BusinessID:  "test_business_001",
		}

		balanceBefore := &BalanceSnapshot{
			Available: decimal.Zero,
			Frozen:    decimal.Zero,
			Total:     decimal.Zero,
		}

		balanceAfter := &BalanceSnapshot{
			Available: decimal.NewFromFloat(100),
			Frozen:    decimal.Zero,
			Total:     decimal.NewFromFloat(100),
		}

		// 测试记录交易
		result, err := recorder.RecordTransaction(ctx, req, balanceBefore, balanceAfter)
		t.AssertNil(err)
		t.AssertNE(result, nil)
		t.Assert(result.TransactionID, uint64(1))
		t.Assert(result.Status, StatusSuccess)

		// 测试获取交易
		getResult, err := recorder.GetTransaction(ctx, 1)
		t.AssertNil(err)
		t.Assert(getResult.TransactionID, uint64(1))

		// 测试根据业务ID获取交易
		businessResult, err := recorder.GetTransactionsByBusiness(ctx, "test_business_001")
		t.AssertNil(err)
		t.Assert(businessResult.TransactionID, uint64(1))

		// 测试重复业务ID
		_, err = recorder.RecordTransaction(ctx, req, balanceBefore, balanceAfter)
		t.AssertNE(err, nil)
		t.Assert(IsWalletError(err), true)

		// 测试获取不存在的交易
		_, err = recorder.GetTransaction(ctx, 999)
		t.AssertNE(err, nil)

		// 测试获取不存在的业务交易
		_, err = recorder.GetTransactionsByBusiness(ctx, "non_existent")
		t.AssertNE(err, nil)
	})
}

// BenchmarkTransactionRecorder_RecordTransaction 性能测试
func BenchmarkTransactionRecorder_RecordTransaction(b *testing.B) {
	recorder := NewMockTransactionRecorder()
	ctx := context.Background()

	balanceBefore := &BalanceSnapshot{
		Available: decimal.Zero,
		Frozen:    decimal.Zero,
		Total:     decimal.Zero,
	}

	balanceAfter := &BalanceSnapshot{
		Available: decimal.NewFromFloat(100),
		Frozen:    decimal.Zero,
		Total:     decimal.NewFromFloat(100),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := &TransactionRequest{
			MerchantID:  1,
			TokenSymbol: "USDT",
			Amount:      decimal.NewFromFloat(100),
			Operation:   OperationDeposit,
			Direction:   DirectionIn,
			WalletType:  WalletTypeAvailable,
			BusinessID:  fmt.Sprintf("bench_business_%d", i),
		}

		_, err := recorder.RecordTransaction(ctx, req, balanceBefore, balanceAfter)
		if err != nil {
			b.Fatal(err)
		}
	}
}
