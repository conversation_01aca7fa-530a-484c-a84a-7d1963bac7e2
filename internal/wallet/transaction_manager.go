package wallet

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// TransactionManager 事务管理器，提供统一的事务管理功能
type TransactionManager struct {
	dbGroup string
}

// NewTransactionManager 创建新的事务管理器
func NewTransactionManager(dbGroup string) *TransactionManager {
	if dbGroup == "" {
		dbGroup = "default"
	}
	return &TransactionManager{
		dbGroup: dbGroup,
	}
}

// ReadOnly 在只读事务中执行操作
// 注意：某些数据库可能会优化只读事务的性能
func (tm *TransactionManager) ReadOnly(ctx context.Context, fn func(tx gdb.TX) error) error {
	return g.DB(tm.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// TODO: 未来可以设置事务为只读模式以优化性能
		// tx.SetReadOnly(true)
		return fn(tx)
	})
}

// ReadWrite 在读写事务中执行操作
func (tm *TransactionManager) ReadWrite(ctx context.Context, fn func(tx gdb.TX) error) error {
	return g.DB(tm.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		return fn(tx)
	})
}

// WithTransaction 在事务中执行操作（读写事务的别名）
func (tm *TransactionManager) WithTransaction(ctx context.Context, fn func(tx gdb.TX) error) error {
	return tm.ReadWrite(ctx, fn)
}

// RunInTransaction 在现有事务中执行，如果没有事务则创建新事务
func (tm *TransactionManager) RunInTransaction(ctx context.Context, tx gdb.TX, fn func(tx gdb.TX) error) error {
	if tx != nil {
		// 如果已经在事务中，直接使用现有事务
		return fn(tx)
	}
	// 否则创建新事务
	return tm.ReadWrite(ctx, fn)
}

// EnsureTransaction 确保操作在事务中执行，如果没有事务则返回错误
func (tm *TransactionManager) EnsureTransaction(tx gdb.TX, operation string) error {
	if tx == nil {
		return ErrTransactionRequired(operation)
	}
	return nil
}

// ErrTransactionRequired 创建事务必需错误
func ErrTransactionRequired(operation string) error {
	return gerror.New(fmt.Sprintf("操作 '%s' 必须在事务中执行", operation))
}

// TransactionHelper 提供事务相关的辅助方法
type TransactionHelper struct {
	dbGroup string
}

// NewTransactionHelper 创建事务辅助器
func NewTransactionHelper(dbGroup string) *TransactionHelper {
	return &TransactionHelper{dbGroup: dbGroup}
}

// Execute 执行事务操作，自动处理事务的提交和回滚
func (th *TransactionHelper) Execute(ctx context.Context, fn func(ctx context.Context, tx gdb.TX) error) error {
	return g.DB(th.dbGroup).Transaction(ctx, fn)
}

// ExecuteWithResult 执行事务操作并返回结果
func (th *TransactionHelper) ExecuteWithResult(ctx context.Context, fn func(ctx context.Context, tx gdb.TX) (interface{}, error)) (interface{}, error) {
	var result interface{}
	err := g.DB(th.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err error
		result, err = fn(ctx, tx)
		return err
	})
	return result, err
}