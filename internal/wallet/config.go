package wallet

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gfsnotify"
	"github.com/shopspring/decimal"
)

// WalletConfig 钱包配置结构
type WalletConfig struct {
	// 数据库配置
	Database DatabaseConfig `json:"database" yaml:"database"`
	
	// 代币配置
	Tokens map[string]TokenConfigExt `json:"tokens" yaml:"tokens"`
	
	// 并发控制配置
	Concurrency ConcurrencyConfig `json:"concurrency" yaml:"concurrency"`
	
	// 重试配置
	Retry RetryConfig `json:"retry" yaml:"retry"`
	
	// 审计配置
	Audit AuditConfig `json:"audit" yaml:"audit"`
	
	// 监控配置
	Monitoring MonitoringConfig `json:"monitoring" yaml:"monitoring"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Group string `json:"group" yaml:"group"`
}

// TokenConfigExt 扩展的代币配置
type TokenConfigExt struct {
	Symbol        string          `json:"symbol" yaml:"symbol"`
	DecimalPlaces uint            `json:"decimal_places" yaml:"decimal_places"`
	MinAmount     decimal.Decimal `json:"min_amount" yaml:"min_amount"`
	MaxAmount     decimal.Decimal `json:"max_amount" yaml:"max_amount"`
	Enabled       bool            `json:"enabled" yaml:"enabled"`
	Description   string          `json:"description,omitempty" yaml:"description,omitempty"`
}

// ConcurrencyConfig 并发控制配置
type ConcurrencyConfig struct {
	MaxConcurrent int `json:"max_concurrent" yaml:"max_concurrent"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries int           `json:"max_retries" yaml:"max_retries"`
	RetryDelay time.Duration `json:"retry_delay" yaml:"retry_delay"`
}

// AuditConfig 审计配置
type AuditConfig struct {
	Enabled bool   `json:"enabled" yaml:"enabled"`
	Level   string `json:"level" yaml:"level"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled     bool          `json:"enabled" yaml:"enabled"`
	MetricsPort int           `json:"metrics_port" yaml:"metrics_port"`
	Interval    time.Duration `json:"interval" yaml:"interval"`
}

// ConfigManager 配置管理器
type ConfigManager struct {
	config     *WalletConfig
	configPath string
	mu         sync.RWMutex
	watchers   []func(*WalletConfig)
}

// NewConfigManager 创建配置管理器
func NewConfigManager(configPath string) (*ConfigManager, error) {
	cm := &ConfigManager{
		configPath: configPath,
		watchers:   make([]func(*WalletConfig), 0),
	}
	
	if err := cm.loadConfig(); err != nil {
		return nil, err
	}
	
	// 启动配置文件监听
	if err := cm.startWatching(); err != nil {
		g.Log().Warningf(context.Background(), "启动配置文件监听失败: %v", err)
	}
	
	return cm, nil
}

// loadConfig 加载配置
func (cm *ConfigManager) loadConfig() error {
	if !gfile.Exists(cm.configPath) {
		// 如果配置文件不存在，创建默认配置
		defaultConfig := cm.getDefaultConfig()
		cm.config = defaultConfig
		return cm.saveConfig()
	}
	
	content := gfile.GetContents(cm.configPath)
	if content == "" {
		return ErrConfigInvalid("配置文件", "配置文件为空")
	}
	
	config := &WalletConfig{}
	if err := gjson.New(content).Scan(config); err != nil {
		return ErrConfigInvalid("配置文件", err.Error())
	}
	
	if err := cm.validateConfig(config); err != nil {
		return err
	}
	
	cm.mu.Lock()
	cm.config = config
	cm.mu.Unlock()
	
	return nil
}

// saveConfig 保存配置
func (cm *ConfigManager) saveConfig() error {
	cm.mu.RLock()
	config := cm.config
	cm.mu.RUnlock()
	
	content := gjson.New(config).MustToJsonString()
	return gfile.PutContents(cm.configPath, content)
}

// validateConfig 验证配置
func (cm *ConfigManager) validateConfig(config *WalletConfig) error {
	if config == nil {
		return ErrConfigInvalid("配置", "配置不能为空")
	}
	
	// 验证数据库配置
	if err := cm.validateDatabaseConfig(&config.Database); err != nil {
		return err
	}
	
	// 验证代币配置
	if err := cm.validateTokenConfigs(config.Tokens); err != nil {
		return err
	}
	
	// 验证并发配置
	if err := cm.validateConcurrencyConfig(&config.Concurrency); err != nil {
		return err
	}
	
	// 验证重试配置
	if err := cm.validateRetryConfig(&config.Retry); err != nil {
		return err
	}
	
	// 验证审计配置
	if err := cm.validateAuditConfig(&config.Audit); err != nil {
		return err
	}
	
	// 验证监控配置
	if err := cm.validateMonitoringConfig(&config.Monitoring); err != nil {
		return err
	}
	
	// 验证依赖关系
	if err := cm.validateDependencies(config); err != nil {
		return err
	}
	
	// 验证业务规则
	if err := cm.validateBusinessRules(config); err != nil {
		return err
	}
	
	return nil
}

// startWatching 开始监听配置文件变化
func (cm *ConfigManager) startWatching() error {
	callback := func(event *gfsnotify.Event) {
		if event.IsWrite() || event.IsCreate() {
			g.Log().Infof(context.Background(), "配置文件发生变化，重新加载: %s", event.Path)
			if err := cm.loadConfig(); err != nil {
				g.Log().Errorf(context.Background(), "重新加载配置失败: %v", err)
				return
			}
			
			// 通知所有监听器
			cm.mu.RLock()
			config := cm.config
			watchers := cm.watchers
			cm.mu.RUnlock()
			
			for _, watcher := range watchers {
				go watcher(config)
			}
		}
	}
	
	_, err := gfsnotify.Add(cm.configPath, callback)
	return err
}

// GetConfig 获取当前配置
func (cm *ConfigManager) GetConfig() *WalletConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	// 返回配置的副本
	configCopy := *cm.config
	return &configCopy
}

// GetTokenConfig 获取代币配置
func (cm *ConfigManager) GetTokenConfig(symbol string) (*TokenConfigExt, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	config, exists := cm.config.Tokens[symbol]
	if !exists {
		return nil, ErrTokenNotSupported(symbol)
	}
	
	if !config.Enabled {
		return nil, ErrTokenNotSupported(symbol, "代币已禁用")
	}
	
	// 返回配置的副本
	configCopy := config
	return &configCopy, nil
}

// UpdateTokenConfig 更新代币配置
func (cm *ConfigManager) UpdateTokenConfig(symbol string, config TokenConfigExt) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	if cm.config.Tokens == nil {
		cm.config.Tokens = make(map[string]TokenConfigExt)
	}
	
	cm.config.Tokens[symbol] = config
	return cm.saveConfig()
}

// AddConfigWatcher 添加配置变化监听器
func (cm *ConfigManager) AddConfigWatcher(watcher func(*WalletConfig)) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	cm.watchers = append(cm.watchers, watcher)
}

// getDefaultConfig 获取默认配置
func (cm *ConfigManager) getDefaultConfig() *WalletConfig {
	return &WalletConfig{
		Database: DatabaseConfig{
			Group: "default",
		},
		Tokens: map[string]TokenConfigExt{
			"USDT": {
				Symbol:        "USDT",
				DecimalPlaces: 6,
				MinAmount:     decimal.NewFromFloat(0.000001),
				MaxAmount:     decimal.NewFromFloat(1000000),
				Enabled:       true,
				Description:   "Tether USD",
			},
			"TRX": {
				Symbol:        "TRX",
				DecimalPlaces: 6,
				MinAmount:     decimal.NewFromFloat(0.000001),
				MaxAmount:     decimal.NewFromFloat(1000000),
				Enabled:       true,
				Description:   "TRON",
			},
			"ETH": {
				Symbol:        "ETH",
				DecimalPlaces: 18,
				MinAmount:     decimal.NewFromFloat(0.000000000000000001),
				MaxAmount:     decimal.NewFromFloat(10000),
				Enabled:       true,
				Description:   "Ethereum",
			},
			"BTC": {
				Symbol:        "BTC",
				DecimalPlaces: 8,
				MinAmount:     decimal.NewFromFloat(0.00000001),
				MaxAmount:     decimal.NewFromFloat(100),
				Enabled:       true,
				Description:   "Bitcoin",
			},
		},
		Concurrency: ConcurrencyConfig{
			MaxConcurrent: 100,
		},
		Retry: RetryConfig{
			MaxRetries: 3,
			RetryDelay: 100 * time.Millisecond,
		},
		Audit: AuditConfig{
			Enabled: true,
			Level:   "info",
		},
		Monitoring: MonitoringConfig{
			Enabled:     false,
			MetricsPort: 9090,
			Interval:    30 * time.Second,
		},
	}
}

// validateDatabaseConfig 验证数据库配置
func (cm *ConfigManager) validateDatabaseConfig(config *DatabaseConfig) error {
	if config.Group == "" {
		return ErrConfigInvalid("数据库配置", "数据库组不能为空")
	}
	
	// 检查数据库组名称格式
	if len(config.Group) > 50 {
		return ErrConfigInvalid("数据库配置", "数据库组名称过长")
	}
	
	return nil
}

// validateTokenConfigs 验证代币配置
func (cm *ConfigManager) validateTokenConfigs(tokens map[string]TokenConfigExt) error {
	if len(tokens) == 0 {
		return ErrConfigInvalid("代币配置", "至少需要配置一个代币")
	}
	
	// 验证每个代币配置
	for symbol, tokenConfig := range tokens {
		if err := cm.validateSingleTokenConfig(symbol, &tokenConfig); err != nil {
			return err
		}
	}
	
	// 验证代币间依赖
	return cm.validateTokenDependencies(tokens)
}

// validateSingleTokenConfig 验证单个代币配置
func (cm *ConfigManager) validateSingleTokenConfig(symbol string, config *TokenConfigExt) error {
	if config.Symbol == "" {
		return ErrConfigInvalid("代币配置", "代币符号不能为空: "+symbol)
	}
	
	// 符号一致性检查
	if config.Symbol != symbol {
		return ErrConfigInvalid("代币配置", "key和symbol不一致: "+symbol)
	}
	
	// 精度验证
	if config.DecimalPlaces > 18 {
		return ErrConfigInvalid("代币配置", "小数位数不能超过18位: "+symbol)
	}
	
	// 金额验证
	if config.MinAmount.IsNegative() {
		return ErrConfigInvalid("代币配置", "最小金额不能为负数: "+symbol)
	}
	
	if config.MaxAmount.IsNegative() || config.MaxAmount.LessThanOrEqual(config.MinAmount) {
		return ErrConfigInvalid("代币配置", "最大金额必须大于最小金额: "+symbol)
	}
	
	// 检查金额范围合理性
	if config.MaxAmount.GreaterThan(decimal.NewFromFloat(1e18)) {
		return ErrConfigInvalid("代币配置", "最大金额过大: "+symbol)
	}
	
	// 精度与金额的一致性检查
	if err := cm.validateAmountPrecision(config.MinAmount, config.DecimalPlaces); err != nil {
		return ErrConfigInvalid("代币配置", "最小金额精度不匹配: "+symbol+", "+err.Error())
	}
	
	if err := cm.validateAmountPrecision(config.MaxAmount, config.DecimalPlaces); err != nil {
		return ErrConfigInvalid("代币配置", "最大金额精度不匹配: "+symbol+", "+err.Error())
	}
	
	return nil
}

// validateTokenDependencies 验证代币间依赖
func (cm *ConfigManager) validateTokenDependencies(tokens map[string]TokenConfigExt) error {
	// 检查是否有基础代币（如ETH, TRX）
	hasMainTokens := false
	for symbol, config := range tokens {
		if config.Enabled && (symbol == "ETH" || symbol == "TRX" || symbol == "BTC") {
			hasMainTokens = true
			break
		}
	}
	
	if !hasMainTokens {
		return ErrConfigInvalid("代币配置", "必须启用至少一个主链代币（ETH, TRX, BTC）")
	}
	
	// 检查稳定币配置
	for symbol, config := range tokens {
		if config.Enabled && (symbol == "USDT" || symbol == "USDC") {
			// 稳定币的最小金额不应过小
			if config.MinAmount.LessThan(decimal.NewFromFloat(0.000001)) {
				return ErrConfigInvalid("代币配置", "稳定币最小金额不应小于0.000001: "+symbol)
			}
		}
	}
	
	return nil
}

// validateConcurrencyConfig 验证并发配置
func (cm *ConfigManager) validateConcurrencyConfig(config *ConcurrencyConfig) error {
	if config.MaxConcurrent <= 0 {
		return ErrConfigInvalid("并发配置", "最大并发数必须大于0")
	}
	
	if config.MaxConcurrent > 10000 {
		return ErrConfigInvalid("并发配置", "最大并发数不应超过10000")
	}
	
	return nil
}

// validateRetryConfig 验证重试配置
func (cm *ConfigManager) validateRetryConfig(config *RetryConfig) error {
	if config.MaxRetries < 0 {
		return ErrConfigInvalid("重试配置", "最大重试次数不能为负数")
	}
	
	if config.MaxRetries > 100 {
		return ErrConfigInvalid("重试配置", "最大重试次数不应超过100")
	}
	
	if config.RetryDelay < 0 {
		return ErrConfigInvalid("重试配置", "重试延迟不能为负数")
	}
	
	if config.RetryDelay > 60*time.Second {
		return ErrConfigInvalid("重试配置", "重试延迟不应超过60秒")
	}
	
	return nil
}

// validateAuditConfig 验证审计配置
func (cm *ConfigManager) validateAuditConfig(config *AuditConfig) error {
	if config.Level != "" {
		validLevels := map[string]bool{
			"debug": true, "info": true, "warn": true, "error": true,
		}
		if !validLevels[config.Level] {
			return ErrConfigInvalid("审计配置", "无效的日志级别: "+config.Level)
		}
	}
	
	return nil
}

// validateMonitoringConfig 验证监控配置
func (cm *ConfigManager) validateMonitoringConfig(config *MonitoringConfig) error {
	if config.Enabled {
		if config.MetricsPort <= 0 || config.MetricsPort > 65535 {
			return ErrConfigInvalid("监控配置", "指标端口范围无效")
		}
		
		if config.Interval <= 0 {
			return ErrConfigInvalid("监控配置", "监控间隔必须大于0")
		}
		
		if config.Interval < 5*time.Second {
			return ErrConfigInvalid("监控配置", "监控间隔不应小于5秒")
		}
	}
	
	return nil
}

// validateDependencies 验证依赖关系
func (cm *ConfigManager) validateDependencies(config *WalletConfig) error {
	// 如果启用监控，必须启用审计
	if config.Monitoring.Enabled && !config.Audit.Enabled {
		return ErrConfigInvalid("依赖关系", "启用监控时必须启用审计")
	}
	
	// 高并发配置需要对应的重试配置
	if config.Concurrency.MaxConcurrent > 1000 && config.Retry.MaxRetries < 3 {
		return ErrConfigInvalid("依赖关系", "高并发场景下建议设置更多重试次数")
	}
	
	return nil
}

// validateBusinessRules 验证业务规则
func (cm *ConfigManager) validateBusinessRules(config *WalletConfig) error {
	// 业务规则验证：稳定币和主链代币的精度匹配
	for symbol, tokenConfig := range config.Tokens {
		if !tokenConfig.Enabled {
			continue
		}
		
		// USDT和USDC在不同链上的精度验证
		if symbol == "USDT" || symbol == "USDC" {
			// Tron网络上是6位，Ethereum网络上是6位
			if tokenConfig.DecimalPlaces != 6 && tokenConfig.DecimalPlaces != 18 {
				return ErrConfigInvalid("业务规则", symbol+"精度应为6或18位")
			}
		}
		
		// 主链代币精度验证
		if symbol == "BTC" && tokenConfig.DecimalPlaces != 8 {
			return ErrConfigInvalid("业务规则", "BTC精度应为8位")
		}
		
		if symbol == "ETH" && tokenConfig.DecimalPlaces != 18 {
			return ErrConfigInvalid("业务规则", "ETH精度应为18位")
		}
		
		if symbol == "TRX" && tokenConfig.DecimalPlaces != 6 {
			return ErrConfigInvalid("业务规则", "TRX精度应为6位")
		}
	}
	
	// 验证金额范围的合理性
	for symbol, tokenConfig := range config.Tokens {
		if !tokenConfig.Enabled {
			continue
		}
		
		// 稳定币的最大金额不应过大
		if (symbol == "USDT" || symbol == "USDC") && tokenConfig.MaxAmount.GreaterThan(decimal.NewFromFloat(10000000)) {
			return ErrConfigInvalid("业务规则", "稳定币最大金额不应超过1000万")
		}
		
		// 主链代币的最大金额限制
		if symbol == "BTC" && tokenConfig.MaxAmount.GreaterThan(decimal.NewFromFloat(1000)) {
			return ErrConfigInvalid("业务规则", "BTC最大金额不应超过1000")
		}
	}
	
	return nil
}

// validateAmountPrecision 验证金额精度
func (cm *ConfigManager) validateAmountPrecision(amount decimal.Decimal, decimalPlaces uint) error {
	amountStr := amount.String()
	if dotIndex := strings.Index(amountStr, "."); dotIndex != -1 {
		actualDecimals := len(amountStr) - dotIndex - 1
		if uint(actualDecimals) > decimalPlaces {
			return ErrConfigInvalid("精度验证", "金额小数位数超出配置")
		}
	}
	return nil
}
