package wallet

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/shopspring/decimal"
)

// Examples 演示如何使用wallet库的示例代码
// 注意：这些是示例代码，实际使用时应该根据具体业务需求进行调整

// ExampleWithdrawService 使用wallet库重构提现服务的示例
type ExampleWithdrawService struct {
	walletService *Service
}

// NewExampleWithdrawService 创建示例提现服务
func NewExampleWithdrawService() *ExampleWithdrawService {
	// 创建钱包服务实例
	walletService := NewService(
		WithDBGroup("merchant"),
		WithAudit(true, "info"),
	)

	return &ExampleWithdrawService{
		walletService: walletService,
	}
}

// CreateWithdrawal 创建提现订单 - 使用wallet库的示例
func (s *ExampleWithdrawService) CreateWithdrawal(ctx context.Context, merchantID uint64, tokenSymbol, address string, amount decimal.Decimal) (*TransactionResult, error) {
	// 1. 验证提现参数
	if merchantID == 0 {
		return nil, gerror.New("商户ID不能为空")
	}
	if tokenSymbol == "" {
		return nil, gerror.New("代币符号不能为空")
	}
	if address == "" {
		return nil, gerror.New("提现地址不能为空")
	}
	if amount.IsNegative() || amount.IsZero() {
		return nil, gerror.New("提现金额必须大于0")
	}

	// 2. 检查钱包余额
	walletInfo, err := s.walletService.GetBalance(ctx, merchantID, tokenSymbol)
	if err != nil {
		return nil, gerror.Wrap(err, "获取钱包余额失败")
	}

	realAvailable := walletInfo.Available.Sub(walletInfo.Frozen)
	if realAvailable.LessThan(amount) {
		return nil, gerror.Newf("余额不足，可用余额: %s, 提现金额: %s",
			realAvailable.String(), amount.String())
	}

	// 3. 准备提现（冻结余额）
	withdrawReq := &WithdrawRequest{
		MerchantID:    merchantID,
		TokenSymbol:   tokenSymbol,
		Amount:        amount,
		Address:       address,
		OrderID:       generateOrderID(),
		Memo:          "用户提现申请",
		RequestSource: "api",
		RequestIP:     getClientIP(ctx),
	}

	result, err := s.walletService.PrepareWithdraw(ctx, withdrawReq)
	if err != nil {
		// Let the wallet service error propagate directly with its specific message
		return nil, err
	}

	g.Log().Infof(ctx, "提现申请创建成功, merchantID: %d, amount: %s %s, transactionID: %d",
		merchantID, amount.String(), tokenSymbol, result.TransactionID)

	return result, nil
}

// CompleteWithdrawal 完成提现 - 使用wallet库的示例
func (s *ExampleWithdrawService) CompleteWithdrawal(ctx context.Context, merchantID uint64, tokenSymbol string, amount decimal.Decimal, txHash string) (*TransactionResult, error) {
	withdrawReq := &WithdrawRequest{
		MerchantID:    merchantID,
		TokenSymbol:   tokenSymbol,
		Amount:        amount,
		RequestSource: "system",
	}

	result, err := s.walletService.CompleteWithdraw(ctx, withdrawReq, txHash)
	if err != nil {
		return nil, gerror.Wrap(err, "完成提现失败")
	}

	g.Log().Infof(ctx, "提现完成, merchantID: %d, amount: %s %s, txHash: %s, transactionID: %d",
		merchantID, amount.String(), tokenSymbol, txHash, result.TransactionID)

	return result, nil
}

// CancelWithdrawal 取消提现 - 使用wallet库的示例
func (s *ExampleWithdrawService) CancelWithdrawal(ctx context.Context, merchantID uint64, tokenSymbol string, amount decimal.Decimal, reason string) error {
	withdrawReq := &WithdrawRequest{
		MerchantID:    merchantID,
		TokenSymbol:   tokenSymbol,
		Amount:        amount,
		Memo:          reason,
		RequestSource: "admin",
	}

	results, err := s.walletService.CancelWithdraw(ctx, withdrawReq)
	if err != nil {
		return gerror.Wrap(err, "取消提现失败")
	}

	g.Log().Infof(ctx, "提现已取消, merchantID: %d, amount: %s %s, reason: %s, 交易记录: [可用余额增加ID: %d, 冻结余额减少ID: %d]",
		merchantID, amount.String(), tokenSymbol, reason, results[0].TransactionID, results[1].TransactionID)

	return nil
}

// ExampleTransferService 转账服务示例
type ExampleTransferService struct {
	walletService *Service
}

// NewExampleTransferService 创建示例转账服务
func NewExampleTransferService() *ExampleTransferService {
	walletService := NewService()
	return &ExampleTransferService{
		walletService: walletService,
	}
}

// Transfer 转账操作 - 使用wallet库的示例
func (s *ExampleTransferService) Transfer(ctx context.Context, fromMerchantID, toMerchantID uint64, tokenSymbol string, amount decimal.Decimal, memo string) ([]*TransactionResult, error) {
	// 构建转账请求
	transferReq := &TransferRequest{
		FromMerchantID: fromMerchantID,
		ToMerchantID:   toMerchantID,
		TokenSymbol:    tokenSymbol,
		Amount:         amount,
		Memo:           memo,
		RequestSource:  "api",
		RequestIP:      getClientIP(ctx),
	}

	// 执行转账
	results, err := s.walletService.Transfer(ctx, transferReq)
	if err != nil {
		return nil, gerror.Wrap(err, "转账失败")
	}

	g.Log().Infof(ctx, "转账成功, from: %d, to: %d, amount: %s %s, 发送方交易ID: %d, 接收方交易ID: %d",
		fromMerchantID, toMerchantID, amount.String(), tokenSymbol,
		results[0].TransactionID, results[1].TransactionID)

	return results, nil
}

// ExampleDepositService 充值服务示例
type ExampleDepositService struct {
	walletService *Service
}

// NewExampleDepositService 创建示例充值服务
func NewExampleDepositService() *ExampleDepositService {
	walletService := NewService()
	return &ExampleDepositService{
		walletService: walletService,
	}
}

// ProcessDeposit 处理充值 - 使用wallet库的示例
func (s *ExampleDepositService) ProcessDeposit(ctx context.Context, merchantID uint64, tokenSymbol string, amount decimal.Decimal, txHash string) (*TransactionResult, error) {
	// 构建充值请求
	depositReq := &DepositRequest{
		MerchantID:    merchantID,
		TokenSymbol:   tokenSymbol,
		Amount:        amount,
		TxHash:        txHash,
		Memo:          "区块链充值",
		RequestSource: "blockchain",
	}

	// 执行充值
	result, err := s.walletService.Deposit(ctx, depositReq)
	if err != nil {
		return nil, gerror.Wrap(err, "充值失败")
	}

	g.Log().Infof(ctx, "充值成功, merchantID: %d, amount: %s %s, txHash: %s, transactionID: %d",
		merchantID, amount.String(), tokenSymbol, txHash, result.TransactionID)

	return result, nil
}

// ExampleAdminService 管理后台服务示例
type ExampleAdminService struct {
	walletService *Service
}

// NewExampleAdminService 创建示例管理服务
func NewExampleAdminService() *ExampleAdminService {
	walletService := NewService()
	return &ExampleAdminService{
		walletService: walletService,
	}
}

// AdjustMerchantBalance 调整商户余额 - 使用wallet库的示例
func (s *ExampleAdminService) AdjustMerchantBalance(ctx context.Context, merchantID uint64, tokenSymbol string, amount decimal.Decimal, reason string, adminID uint64) (*TransactionResult, error) {
	// 构建调账请求
	adjustReq := &AdjustRequest{
		MerchantID:    merchantID,
		TokenSymbol:   tokenSymbol,
		Amount:        amount,
		Reason:        reason,
		AdminID:       adminID,
		RequestSource: "admin",
	}

	// 执行调账
	result, err := s.walletService.AdjustBalance(ctx, adjustReq)
	if err != nil {
		return nil, gerror.Wrap(err, "调账失败")
	}

	direction := "增加"
	if amount.IsNegative() {
		direction = "减少"
	}

	g.Log().Infof(ctx, "调账成功, merchantID: %d, %s: %s %s, 原因: %s, 管理员: %d, transactionID: %d",
		merchantID, direction, amount.Abs().String(), tokenSymbol, reason, adminID, result.TransactionID)

	return result, nil
}

// FreezeMerchantBalance 冻结商户余额 - 使用wallet库的示例
func (s *ExampleAdminService) FreezeMerchantBalance(ctx context.Context, merchantID uint64, tokenSymbol string, amount decimal.Decimal, relatedEntityID uint64, reason string) (*TransactionResult, error) {
	// 构建冻结请求
	freezeReq := &FreezeRequest{
		MerchantID:      merchantID,
		TokenSymbol:     tokenSymbol,
		Amount:          amount,
		RelatedEntityID: relatedEntityID,
		Memo:            reason,
	}

	// 执行冻结
	result, err := s.walletService.FreezeBalance(ctx, freezeReq)
	if err != nil {
		return nil, gerror.Wrap(err, "冻结余额失败")
	}

	g.Log().Infof(ctx, "余额冻结成功, merchantID: %d, amount: %s %s, 原因: %s, transactionID: %d",
		merchantID, amount.String(), tokenSymbol, reason, result.TransactionID)

	return result, nil
}

// ExampleQueryService 查询服务示例
type ExampleQueryService struct {
	walletService *Service
}

// NewExampleQueryService 创建示例查询服务
func NewExampleQueryService() *ExampleQueryService {
	walletService := NewService()
	return &ExampleQueryService{
		walletService: walletService,
	}
}

// GetMerchantBalance 获取商户余额信息 - 使用wallet库的示例
func (s *ExampleQueryService) GetMerchantBalance(ctx context.Context, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	walletInfo, err := s.walletService.GetBalance(ctx, merchantID, tokenSymbol)
	if err != nil {
		return nil, gerror.Wrap(err, "获取余额失败")
	}

	g.Log().Debugf(ctx, "查询余额, merchantID: %d, token: %s, available: %s, frozen: %s",
		merchantID, tokenSymbol, walletInfo.Available.String(), walletInfo.Frozen.String())

	return walletInfo, nil
}

// GetTransactionHistory 获取交易历史 - 使用wallet库的示例
func (s *ExampleQueryService) GetTransactionHistory(ctx context.Context, merchantID uint64, limit, offset int) ([]*TransactionResult, error) {
	results, err := s.walletService.GetTransactionHistory(ctx, merchantID, limit, offset)
	if err != nil {
		return nil, gerror.Wrap(err, "获取交易历史失败")
	}

	g.Log().Debugf(ctx, "查询交易历史, merchantID: %d, 记录数: %d", merchantID, len(results))

	return results, nil
}

// ExampleMigrationFromExistingCode 从现有代码迁移的示例
// 展示如何将现有的提现逻辑替换为wallet库调用
func ExampleMigrationFromExistingCode(ctx context.Context, merchantID uint64, amount string, tokenSymbol, address string) error {
	// 原有代码（复杂的手动事务管理）:
	// err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
	//     // 1. 锁定余额
	//     if err := lockMerchantBalanceInTx(ctx, tx, merchantID, chain, token, amount); err != nil {
	//         return err
	//     }
	//     // 2. 创建提现记录
	//     result, err := dao.MerchantWithdraws.Ctx(ctx).TX(tx).Insert(...)
	//     return err
	// })

	// 新代码（使用wallet库）:
	walletService := NewService()

	amountDecimal, err := decimal.NewFromString(amount)
	if err != nil {
		return gerror.Wrap(err, "金额格式错误")
	}

	withdrawReq := &WithdrawRequest{
		MerchantID:    merchantID,
		TokenSymbol:   tokenSymbol,
		Amount:        amountDecimal,
		Address:       address,
		OrderID:       generateOrderID(),
		Memo:          "用户提现",
		RequestSource: "api",
		RequestIP:     getClientIP(ctx),
	}

	// 一行代码完成原来复杂的事务操作
	_, err = walletService.PrepareWithdraw(ctx, withdrawReq)
	if err != nil {
		return gerror.Wrap(err, "准备提现失败")
	}

	return nil
}

// 辅助函数（示例实现）
func generateOrderID() string {
	return "WD" + guid.S()[:16]
}

func getClientIP(ctx context.Context) string {
	// 从请求上下文中获取客户端IP
	// 这里是示例实现，实际应根据框架获取
	return "127.0.0.1"
}

// 全局wallet服务实例（推荐方式）
var (
	globalWalletService *Service
)

// InitWalletService 初始化全局钱包服务
func InitWalletService() {
	globalWalletService = NewService(
		WithDBGroup("merchant"),
		WithAudit(true, "info"),
		WithMaxConcurrent(100),
	)
}

// GetWalletService 获取全局钱包服务实例
func GetWalletService() *Service {
	if globalWalletService == nil {
		InitWalletService()
	}
	return globalWalletService
}

// ExampleUsageInController 在控制器中使用的示例
func ExampleUsageInController(ctx context.Context, fromMerchantID, toMerchantID uint64, tokenSymbol string, amount decimal.Decimal) error {
	// 获取全局钱包服务
	walletService := GetWalletService()

	// 执行转账
	transferReq := &TransferRequest{
		FromMerchantID: fromMerchantID,
		ToMerchantID:   toMerchantID,
		TokenSymbol:    tokenSymbol,
		Amount:         amount,
		Memo:           "API转账",
		RequestSource:  "api",
		RequestIP:      getClientIP(ctx),
	}

	results, err := walletService.Transfer(ctx, transferReq)
	if err != nil {
		return err
	}

	// 返回结果给前端
	g.Log().Infof(ctx, "转账成功, 发送方交易: %d, 接收方交易: %d",
		results[0].TransactionID, results[1].TransactionID)

	return nil
}
