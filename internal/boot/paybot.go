package boot

import (
	"admin-api/internal/service"
	"admin-api/internal/service/paybot"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// InitializePayBotService 初始化PayBot服务
func InitializePayBotService(ctx context.Context) {
	g.Log().Info(ctx, "Initializing PayBot Service...")

	// 创建价格客户端适配器
	priceClientAdapter := &priceClientAdapter{
		serviceClient: service.PriceClientInstance(),
	}

	// 初始化PayBot服务，传入价格客户端
	paybotService := paybot.InitPayBotService(ctx, priceClientAdapter)
	if paybotService == nil {
		g.Log().Warning(ctx, "PayBot service is disabled")
		return
	}

	// 创建服务包装器来实现service.IPayBot接口
	wrapper := &payBotServiceWrapper{svc: paybotService}

	// 注册服务
	service.RegisterPayBot(wrapper)

	g.Log().Info(ctx, "PayBot Service initialized successfully")
	g.Log().Info(ctx, "PayBot sync tasks will be managed by the scheduled task system")
}

// payBotServiceWrapper 包装器，用于适配service.IPayBot接口
type payBotServiceWrapper struct {
	svc *paybot.SPayBot
}

// 实现service.IPayBot接口的所有方法
func (w *payBotServiceWrapper) CreateAuthPayment(ctx context.Context, req *paybot.CreateAuthPaymentRequest) (*paybot.AuthPaymentResult, error) {
	return w.svc.CreateAuthPayment(ctx, req)
}

func (w *payBotServiceWrapper) QueryAuthPayment(ctx context.Context, req *paybot.QueryAuthPaymentRequest) (*paybot.AuthPaymentResult, error) {
	return w.svc.QueryAuthPayment(ctx, req)
}

func (w *payBotServiceWrapper) ListAuthPayments(ctx context.Context, req *paybot.ListAuthPaymentsRequest) (*paybot.AuthPaymentListResult, error) {
	return w.svc.ListAuthPayments(ctx, req)
}

func (w *payBotServiceWrapper) GetDepositAddress(ctx context.Context, req *paybot.GetDepositAddressRequest) (*paybot.DepositAddressResult, error) {
	return w.svc.GetDepositAddress(ctx, req)
}

func (w *payBotServiceWrapper) QueryDepositRecords(ctx context.Context, req *paybot.QueryDepositsRequest) (*paybot.DepositListResult, error) {
	return w.svc.QueryDepositRecords(ctx, req)
}

func (w *payBotServiceWrapper) GetDepositDetail(ctx context.Context, req *paybot.GetDepositDetailRequest) (*paybot.DepositDetailResult, error) {
	return w.svc.GetDepositDetail(ctx, req)
}

func (w *payBotServiceWrapper) ListDepositAddresses(ctx context.Context, req *paybot.ListDepositAddressesRequest) (*paybot.DepositAddressListResult, error) {
	return w.svc.ListDepositAddresses(ctx, req)
}

func (w *payBotServiceWrapper) QueryTransactions(ctx context.Context, req *paybot.QueryTransactionsRequest) (*paybot.TransactionListResult, error) {
	return w.svc.QueryTransactions(ctx, req)
}

func (w *payBotServiceWrapper) GetTransactionDetail(ctx context.Context, req *paybot.GetTransactionDetailRequest) (*paybot.TransactionDetailResult, error) {
	return w.svc.GetTransactionDetail(ctx, req)
}

func (w *payBotServiceWrapper) QueryWithdrawals(ctx context.Context, req *paybot.QueryWithdrawalsRequest) (*paybot.WithdrawalListResult, error) {
	return w.svc.QueryWithdrawals(ctx, req)
}

func (w *payBotServiceWrapper) GetWithdrawalDetail(ctx context.Context, req *paybot.GetWithdrawalDetailRequest) (*paybot.WithdrawalDetailResult, error) {
	return w.svc.GetWithdrawalDetail(ctx, req)
}

func (w *payBotServiceWrapper) ProcessDepositCallback(ctx context.Context, req *paybot.DepositCallbackRequest) error {
	return w.svc.ProcessDepositCallback(ctx, req)
}

func (w *payBotServiceWrapper) ProcessWithdrawalCallback(ctx context.Context, req *paybot.WithdrawalCallbackRequest) error {
	return w.svc.ProcessWithdrawalCallback(ctx, req)
}

func (w *payBotServiceWrapper) ProcessOkpayAuthCallback(ctx context.Context, userID int64, merchantOrderNo string) (*paybot.OkpayCallbackResult, error) {
	return w.svc.ProcessOkpayAuthCallback(ctx, userID, merchantOrderNo)
}

func (w *payBotServiceWrapper) HealthCheck(ctx context.Context) error {
	return w.svc.HealthCheck(ctx)
}

func (w *payBotServiceWrapper) IsSyncEnabled() bool {
	return w.svc.IsSyncEnabled()
}

func (w *payBotServiceWrapper) GenerateTelegramLink(ctx context.Context, orderNo string) string {
	return w.svc.GenerateTelegramLink(ctx, orderNo)
}

// priceClientAdapter 价格客户端适配器，将service.IPriceClient适配为paybot.IPriceClient
type priceClientAdapter struct {
	serviceClient service.IPriceClient
}

// GetFiatPrice 实现paybot.IPriceClient接口
func (p *priceClientAdapter) GetFiatPrice(ctx context.Context, asset, currency string) (*paybot.FiatPriceData, error) {
	// 调用service包的价格客户端
	serviceFiatPrice, err := p.serviceClient.GetFiatPrice(ctx, asset, currency)
	if err != nil {
		return nil, err
	}

	// 转换为paybot包的FiatPriceData结构
	return &paybot.FiatPriceData{
		Asset:          serviceFiatPrice.Asset,
		Currency:       serviceFiatPrice.Currency,
		CurrencySymbol: serviceFiatPrice.CurrencySymbol,
		BuyPrice:       serviceFiatPrice.BuyPrice,
		SellPrice:      serviceFiatPrice.SellPrice,
		MidPrice:       serviceFiatPrice.MidPrice,
		Spread:         serviceFiatPrice.Spread,
		SpreadPercent:  serviceFiatPrice.SpreadPercent,
		Provider:       serviceFiatPrice.Provider,
		Timestamp:      serviceFiatPrice.Timestamp,
		LastUpdated:    serviceFiatPrice.LastUpdated,
	}, nil
}
