package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Tenant Module Error Codes (4000-4999)
var (
	// 业务错误码
	CodeTenantNotFound         = gcode.New(4001, "租户不存在", nil)
	CodeTenantUsernameExists   = gcode.New(4002, "租户用户名已存在", nil)
	CodeTenantEmailExists      = gcode.New(4003, "租户邮箱已存在", nil)
	CodeTenantInviteCodeExists = gcode.New(4004, "租户邀请码已存在", nil)
	CodeCannotDeleteSelfTenant = gcode.New(4005, "不能删除自己", nil)
	CodeCannotDisableSelfTenant = gcode.New(4006, "不能禁用自己", nil)
	CodeTenantDatabaseExists   = gcode.New(4007, "租户数据库已存在", nil)
	CodeInvalidTenantLevel     = gcode.New(4008, "无效的租户层级", nil)

	// 系统错误码 (如果需要特定于租户模块的系统错误)
	// CodeTenantInternalError = gcode.New(5401, "租户模块内部错误", nil)
)