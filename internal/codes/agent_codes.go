package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Agent <PERSON><PERSON><PERSON> Error Codes (3000-3999)
var (
	// 业务错误码
	CodeAgentNotFound           = gcode.New(3001, "代理不存在", nil)
	CodeAgentUsernameExists     = gcode.New(3002, "代理用户名已存在", nil)
	CodeAgentEmailExists        = gcode.New(3003, "代理邮箱已存在", nil)
	CodePhoneExists             = gcode.New(3004, "手机号已存在", nil)
	CodeAgentInviteCodeExists   = gcode.New(3005, "代理邀请码已存在", nil)
	CodeCannotAddUnderMaxLevel  = gcode.New(3006, "无法在最大层级下添加代理", nil)
	CodeParentAgentNotFound     = gcode.New(3007, "上级代理不存在", nil)
	CodeInvalidAgentLevel       = gcode.New(3008, "无效的代理层级", nil)
	CodeCannotDeleteSelf        = gcode.New(3009, "不能删除自己", nil) // 虽然文档没写，但通常需要
	CodeCannotDisableSelf       = gcode.New(3010, "不能禁用自己", nil) // 虽然文档没写，但通常需要
	CodeIPWhitelistExists       = gcode.New(3011, "IP白名单已存在", nil)
	CodeIPWhitelistNotFound     = gcode.New(3012, "IP白名单记录不存在", nil)
	CodeCannotDeleteDescendants = gcode.New(3013, "删除代理失败，请先删除其下级代理", nil) // 替代直接删除所有下级，根据实际需求调整
	CodeAgentHasActiveChildren  = gcode.New(3014, "代理尚有活动的下级代理，无法删除", nil)
	CodeAgentDatabaseExists     = gcode.New(3015, "代理数据库已存在", nil)

	// 系统错误码 (如果需要特定于代理模块的系统错误)
	// CodeAgentInternalError = gcode.New(5301, "代理模块内部错误", nil)
)
