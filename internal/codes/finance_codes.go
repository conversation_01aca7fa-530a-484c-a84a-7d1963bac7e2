package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Finance / Wallet / Token related business codes (3000-3999)
// Please ensure this range doesn't conflict with other modules.
var (
	// Wallet Errors (3000-3099)
	CodeWalletAlreadyExists       = gcode.New(3001, "钱包已存在", nil)
	CodeWalletNotFound            = gcode.New(3002, "钱包不存在", nil)
	CodeInsufficientBalance       = gcode.New(3003, "可用余额不足", nil)
	CodeInsufficientFrozenBalance = gcode.New(3004, "冻结余额不足", nil)
	CodeInvalidAmountFormat       = gcode.New(3005, "金额格式或精度无效", nil)
	CodeWalletOperationFailed     = gcode.New(3006, "钱包操作失败", nil)   // Generic wallet operation error
	CodeWalletUpdateConflict      = gcode.New(3007, "钱包余额更新冲突", nil) // For optimistic locking or race conditions if implemented

	// Token Errors (3100-3199) - Use codes from token_codes.go if needed (e.g., codes.CodeTokenNotFound)
	// CodeTokenNotFound is already defined in token_codes.go
	CodeTokenNotActive       = gcode.New(3102, "代币未激活", nil)
	CodeTokenOperationDenied = gcode.New(3103, "代币操作不允许", nil) // e.g., deposit/withdrawal disabled

	// Transaction/Ledger Errors (3200-3299) - For the TODO logging part
	CodeLedgerRecordFailed = gcode.New(3201, "记录财务日志失败", nil)

	// Fiat Currency Errors (3300-3399)
	CodeFiatNotFound   = gcode.New(3301, "法币信息不存在", nil)
	CodeFiatCodeExists = gcode.New(3302, "法币代码已存在", nil)
)
