package codes

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

// 角色管理模块错误码 (3300-3399)
var (
	// CodeRoleNotFound 角色不存在
	CodeRoleNotFound = gcode.New(3301, "角色不存在", nil)
	// CodeRoleKeyExists 角色权限标识已存在
	CodeRoleKeyExists = gcode.New(3302, "角色权限标识已存在", nil)
	// CodeRoleNameExists 角色名称已存在
	CodeRoleNameExists = gcode.New(3303, "角色名称已存在", nil) // 可选，如果需要校验名称唯一性
	// CodeRoleHasUsers 角色下存在关联用户，无法删除
	CodeRoleHasUsers = gcode.New(3304, "角色下存在关联用户，无法删除", nil) // 可选，如果删除前需要检查用户关联
	// CodeRoleCannotDeleteAdmin 不能删除超级管理员角色
	CodeRoleCannotDeleteAdmin = gcode.New(3305, "不能删除超级管理员角色", nil) // 可选，如果需要保护特定角色
	// CodeRoleInvalidDataScope 无效的数据范围值
	CodeRoleInvalidDataScope = gcode.New(3306, "无效的数据范围值", nil)
)
