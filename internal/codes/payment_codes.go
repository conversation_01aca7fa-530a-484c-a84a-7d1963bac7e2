package codes

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

// Payment request error codes
var (
	// CodePaymentRequestNotFound 收款请求不存在
	CodePaymentRequestNotFound = gcode.New(4001, "收款请求不存在", nil)
	// CodePaymentRequestStatusUpdateNotAllowed 收款请求状态更新不允许
	CodePaymentRequestStatusUpdateNotAllowed = gcode.New(4002, "收款请求状态更新不允许", nil)
	// CodePaymentRequestAlreadyPaid 收款请求已支付
	CodePaymentRequestAlreadyPaid = gcode.New(4003, "收款请求已支付", nil)
	// CodePaymentRequestExpired 收款请求已过期
	CodePaymentRequestExpired = gcode.New(4004, "收款请求已过期", nil)
	// CodePaymentRequestCancelled 收款请求已取消
	CodePaymentRequestCancelled = gcode.New(4005, "收款请求已取消", nil)
)
