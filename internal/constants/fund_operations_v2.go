package constants

import "fmt"

// FundOperationType represents the type of fund operation
type FundOperationType string

// Fund operation type enums - 使用枚举提供类型安全
const (
	// Red packet operations
	FundOpRedPacketCreate FundOperationType = "red_packet_create"
	FundOpRedPacketClaim  FundOperationType = "red_packet_claim"
	FundOpRedPacketCancel FundOperationType = "red_packet_cancel"
	FundOpRedPacketExpire FundOperationType = "red_packet_expire"

	// Transfer operations
	FundOpTransferOut     FundOperationType = "transfer_out"
	FundOpTransferIn      FundOperationType = "transfer_in"
	FundOpTransferExpire  FundOperationType = "transfer_expire"
	FundOpTransferCollect FundOperationType = "transfer_collect"

	// Withdrawal operations
	FundOpWithdrawReq    FundOperationType = "withdraw_req"
	FundOpWithdrawRefund FundOperationType = "withdraw_refund"

	// Swap/Exchange operations
	FundOpSwapOut FundOperationType = "swap_out"
	FundOpSwapIn  FundOperationType = "swap_in"

	// Payment operations
	FundOpPaymentOut FundOperationType = "payment_out"
	FundOpPaymentIn  FundOperationType = "payment_in"

	// Admin operations
	FundOpAdminAdd    FundOperationType = "admin_add"
	FundOpAdminDeduct FundOperationType = "admin_deduct"

	// System operations
	FundOpSystemAdjust FundOperationType = "system_adjust"
	FundOpCommission   FundOperationType = "commission"
	FundOpReferral     FundOperationType = "referral"
)

// FundOperationTemplate represents a standardized fund operation description template
type FundOperationTemplate struct {
	Operation   FundOperationType
	TemplateCN  string // 中文模板
	TemplateEN  string // 英文模板
	ParamFormat string // 参数格式说明
}

// 统一的资金操作模板 - 格式: 操作-金额-币种
var FundOperationTemplates = map[FundOperationType]FundOperationTemplate{
	// 红包操作 - 统一格式: 操作: 金额 币种
	FundOpRedPacketCreate: {
		Operation:   FundOpRedPacketCreate,
		TemplateCN:  "红包创建: %s %s",
		TemplateEN:  "Red packet create: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpRedPacketClaim: {
		Operation:   FundOpRedPacketClaim,
		TemplateCN:  "红包领取: %s %s",
		TemplateEN:  "Red packet claim: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpRedPacketCancel: {
		Operation:   FundOpRedPacketCancel,
		TemplateCN:  "红包取消: %s %s",
		TemplateEN:  "Red packet cancel: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpRedPacketExpire: {
		Operation:   FundOpRedPacketExpire,
		TemplateCN:  "红包过期: %s %s",
		TemplateEN:  "Red packet expire: %s %s",
		ParamFormat: "amount, symbol",
	},

	// 转账操作 - 统一格式: 操作: 金额 币种
	FundOpTransferOut: {
		Operation:   FundOpTransferOut,
		TemplateCN:  "转账扣除: %s %s",
		TemplateEN:  "Transfer debit: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpTransferIn: {
		Operation:   FundOpTransferIn,
		TemplateCN:  "转账收入: %s %s",
		TemplateEN:  "Transfer credit: %s %s",
		ParamFormat: "amount, symbol",
	},

	// 提现操作 - 统一格式: 操作: 金额 币种
	FundOpWithdrawReq: {
		Operation:   FundOpWithdrawReq,
		TemplateCN:  "提现扣除: %s %s",
		TemplateEN:  "Withdraw debit: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpWithdrawRefund: {
		Operation:   FundOpWithdrawRefund,
		TemplateCN:  "提现退款: %s %s",
		TemplateEN:  "Withdraw refund: %s %s",
		ParamFormat: "amount, symbol",
	},

	// 兑换操作 - 统一格式: 操作: 金额 币种
	FundOpSwapOut: {
		Operation:   FundOpSwapOut,
		TemplateCN:  "兑换扣除: %s %s",
		TemplateEN:  "Swap debit: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpSwapIn: {
		Operation:   FundOpSwapIn,
		TemplateCN:  "兑换获得: %s %s",
		TemplateEN:  "Swap credit: %s %s",
		ParamFormat: "amount, symbol",
	},

	// 支付操作 - 统一格式: 操作: 金额 币种
	FundOpPaymentOut: {
		Operation:   FundOpPaymentOut,
		TemplateCN:  "支付扣除: %s %s",
		TemplateEN:  "Payment debit: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpPaymentIn: {
		Operation:   FundOpPaymentIn,
		TemplateCN:  "支付收入: %s %s",
		TemplateEN:  "Payment credit: %s %s",
		ParamFormat: "amount, symbol",
	},

	// 管理员操作 - 统一格式: 操作: 金额 币种
	FundOpAdminAdd: {
		Operation:   FundOpAdminAdd,
		TemplateCN:  "系统增加: %s %s",
		TemplateEN:  "Admin add: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpAdminDeduct: {
		Operation:   FundOpAdminDeduct,
		TemplateCN:  "系统扣除: %s %s",
		TemplateEN:  "Admin deduct: %s %s",
		ParamFormat: "amount, symbol",
	},

	// 系统操作 - 统一格式: 操作: 金额 币种
	FundOpCommission: {
		Operation:   FundOpCommission,
		TemplateCN:  "佣金收入: %s %s",
		TemplateEN:  "Commission: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpReferral: {
		Operation:   FundOpReferral,
		TemplateCN:  "推荐奖励: %s %s",
		TemplateEN:  "Referral bonus: %s %s",
		ParamFormat: "amount, symbol",
	},
	FundOpSystemAdjust: {
		Operation:   FundOpSystemAdjust,
		TemplateCN:  "系统调整: %s %s",
		TemplateEN:  "System adjust: %s %s",
		ParamFormat: "amount, symbol",
	},
}

// GetBusinessIDPrefix returns the business ID prefix for a fund operation type
func (op FundOperationType) GetBusinessIDPrefix() string {
	switch op {
	case FundOpRedPacketCreate:
		return "rp_create"
	case FundOpRedPacketClaim:
		return "rp_claim"
	case FundOpRedPacketCancel:
		return "rp_cancel"
	case FundOpRedPacketExpire:
		return "rp_expire"
	case FundOpTransferOut:
		return "transfer_out"
	case FundOpTransferIn:
		return "transfer_in"
	case FundOpTransferExpire:
		return "transfer_expire"
	case FundOpTransferCollect:
		return "transfer_collect"
	case FundOpWithdrawReq:
		return "withdraw_req"
	case FundOpWithdrawRefund:
		return "withdraw_refund"
	case FundOpSwapOut:
		return "swap_out"
	case FundOpSwapIn:
		return "swap_in"
	case FundOpPaymentOut:
		return "payment_out"
	case FundOpPaymentIn:
		return "payment_in"
	case FundOpAdminAdd:
		return "admin_add"
	case FundOpAdminDeduct:
		return "admin_deduct"
	case FundOpSystemAdjust:
		return "system_adjust"
	case FundOpCommission:
		return "commission"
	case FundOpReferral:
		return "referral"
	default:
		return string(op)
	}
}

// FormatDescription formats the description using the standardized template
func (op FundOperationType) FormatDescription(language string, amount string, symbol string) string {
	template, exists := FundOperationTemplates[op]
	if !exists {
		return fmt.Sprintf("Unknown operation: %s %s", amount, symbol)
	}

	if language == "en" || language == "EN" {
		return fmt.Sprintf(template.TemplateEN, amount, symbol)
	}
	return fmt.Sprintf(template.TemplateCN, amount, symbol)
}

// FormatDescriptionWithTarget formats description with target information (for transfers, payments, etc.)
func (op FundOperationType) FormatDescriptionWithTarget(language string, target string, amount string, symbol string) string {
	var templateCN, templateEN string

	switch op {
	case FundOpTransferOut:
		templateCN = "转账给 %s: %s %s"
		templateEN = "Transfer to %s: %s %s"
	case FundOpTransferIn:
		templateCN = "收到 %s 转账: %s %s"
		templateEN = "Received from %s: %s %s"
	case FundOpPaymentOut:
		templateCN = "支付给 %s: %s %s"
		templateEN = "Pay to %s: %s %s"
	case FundOpPaymentIn:
		templateCN = "收到 %s 付款: %s %s"
		templateEN = "Received from %s: %s %s"
	case FundOpWithdrawReq:
		templateCN = "提现到 %s: %s %s"
		templateEN = "Withdraw to %s: %s %s"
	default:
		// 回退到基础格式
		return op.FormatDescription(language, amount, symbol)
	}

	if language == "en" || language == "EN" {
		return fmt.Sprintf(templateEN, target, amount, symbol)
	}
	return fmt.Sprintf(templateCN, target, amount, symbol)
}

// IsValid checks if the fund operation type is valid
func (op FundOperationType) IsValid() bool {
	_, exists := FundOperationTemplates[op]
	return exists
}

// String returns the string representation of the fund operation type
func (op FundOperationType) String() string {
	return string(op)
}
