package constants

// 提现状态常量定义
const (
	// WithdrawStatusPending 待审核
	WithdrawStatusPending = 1
	
	// WithdrawStatusProcessing 处理中
	WithdrawStatusProcessing = 2
	
	// WithdrawStatusRejected 已拒绝
	WithdrawStatusRejected = 3
	
	// WithdrawStatusCompleted 已完成
	WithdrawStatusCompleted = 4
	
	// WithdrawStatusFailed 失败
	WithdrawStatusFailed = 5
	
	// WithdrawStatusCancelled 已撤销
	WithdrawStatusCancelled = 6
)

// WithdrawStatusText 提现状态文本映射
var WithdrawStatusText = map[int]string{
	WithdrawStatusPending:    "待审核",
	WithdrawStatusProcessing: "处理中",
	WithdrawStatusRejected:   "已拒绝",
	WithdrawStatusCompleted:  "已完成",
	WithdrawStatusFailed:     "失败",
	WithdrawStatusCancelled:  "已撤销",
}

// GetWithdrawStatusText 获取提现状态文本
func GetWithdrawStatusText(status int) string {
	if text, ok := WithdrawStatusText[status]; ok {
		return text
	}
	return "未知状态"
}