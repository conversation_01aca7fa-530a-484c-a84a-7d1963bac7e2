package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// PaymentRequestAdminInfo 收款请求管理后台信息模型
type PaymentRequestAdminInfo struct {
	// 基础字段
	RequestId            int64           `json:"requestId" orm:"request_id" description:"收款请求ID"`
	RequesterUserId      int64           `json:"requesterUserId" orm:"requester_user_id" description:"收款发起者用户ID"`
	RequesterUsername    string          `json:"requesterUsername" orm:"requester_username" description:"收款发起者用户名"`
	RequesterAccount     string          `json:"requesterAccount" orm:"requester_account" description:"收款发起者账号"`
	PayerUserId          int64           `json:"payerUserId" orm:"payer_user_id" description:"付款人用户ID"`
	PayerUsername        string          `json:"payerUsername" orm:"payer_username" description:"付款人用户名"`
	PayerAccount         string          `json:"payerAccount" orm:"payer_account" description:"付款人账号"`
	TokenId              int             `json:"tokenId" orm:"token_id" description:"代币ID"`
	TokenSymbol          string          `json:"tokenSymbol" orm:"token_symbol" description:"代币符号"`
	TokenName            string          `json:"tokenName" orm:"token_name" description:"代币名称"`
	Amount               decimal.Decimal `json:"amount" orm:"amount" description:"收款金额"`
	Memo                 string          `json:"memo" orm:"memo" description:"收款说明/备注"`
	Status               int             `json:"status" orm:"status" description:"状态"`
	CreatedAt            *gtime.Time     `json:"createdAt" orm:"created_at" description:"创建时间"`
	ExpiresAt            *gtime.Time     `json:"expiresAt" orm:"expires_at" description:"过期时间"`
	
	// 发起者三级代理信息
	FirstAgentName  string `json:"firstAgentName" orm:"requester_first_agent_name" description:"发起者一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"requester_second_agent_name" description:"发起者二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"requester_third_agent_name" description:"发起者三级代理名称"`
	
	// 发起者telegram信息
	TelegramId       string `json:"telegramId" orm:"requester_telegram_id" description:"发起者Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"requester_telegram_username" description:"发起者Telegram用户名"`
	FirstName        string `json:"firstName" orm:"requester_first_name" description:"发起者真实姓名"`
	
	// 付款人三级代理信息
	PayerFirstAgentName  string `json:"payerFirstAgentName" orm:"payer_first_agent_name" description:"付款人一级代理名称"`
	PayerSecondAgentName string `json:"payerSecondAgentName" orm:"payer_second_agent_name" description:"付款人二级代理名称"`
	PayerThirdAgentName  string `json:"payerThirdAgentName" orm:"payer_third_agent_name" description:"付款人三级代理名称"`
	
	// 付款人telegram信息
	PayerTelegramId       string `json:"payerTelegramId" orm:"payer_telegram_id" description:"付款人Telegram ID"`
	PayerTelegramUsername string `json:"payerTelegramUsername" orm:"payer_telegram_username" description:"付款人Telegram用户名"`
	PayerFirstName        string `json:"payerFirstName" orm:"payer_first_name" description:"付款人真实姓名"`
}