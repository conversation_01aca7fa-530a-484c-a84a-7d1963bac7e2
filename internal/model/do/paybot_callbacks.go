// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PaybotCallbacks is the golang structure of table paybot_callbacks for DAO operations like Where/Data.
type PaybotCallbacks struct {
	g.Meta          `orm:"table:paybot_callbacks, do:true"`
	Id              interface{} // 主键ID
	TenantId        interface{} // 租户 id
	EventType       interface{} // 事件类型（deposit_confirmed/withdraw_completed等）
	OrderNo         interface{} // 关联的订单号
	RelatedTable    interface{} // 关联的表名
	RelatedId       interface{} // 关联表的记录ID
	MerchantId      interface{} // 商户ID
	Payload         *gjson.Json // 回调数据（JSON格式）
	Signature       interface{} // 回调签名
	CallbackTime    *gtime.Time // 回调接收时间
	ProcessedStatus interface{} // 处理状态
	ProcessAttempts interface{} // 处理尝试次数
	ErrorMessage    interface{} // 处理错误信息
	NextRetryAt     *gtime.Time // 下次重试时间
	CreatedAt       *gtime.Time // 记录创建时间
	UpdatedAt       *gtime.Time // 记录更新时间
}
