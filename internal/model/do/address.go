// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Address is the golang structure of table address for DAO operations like Where/Data.
type Address struct {
	g.Meta     `orm:"table:address, do:true"`
	Id         interface{} //
	Chan       interface{} // 链
	Address    interface{} // 地址
	Image      interface{} // 二维码
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
	BindStatus interface{} // 绑定状态 0: 未绑定, 1: 已绑定
}
