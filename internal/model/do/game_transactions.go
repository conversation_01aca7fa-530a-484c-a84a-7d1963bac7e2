// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameTransactions is the golang structure of table game_transactions for DAO operations like Where/Data.
type GameTransactions struct {
	g.Meta              `orm:"table:game_transactions, do:true"`
	Id                  interface{} //
	UserId              interface{} // 用户ID
	Amount              interface{} // 投注金额
	Symbol              interface{} // 币种
	GameType            interface{} // 游戏类型
	GameResult          interface{} // 游戏结果（win/lose/draw）
	CommissionProcessed interface{} // 佣金是否已处理
	CreatedAt           *gtime.Time //
	UpdatedAt           *gtime.Time //
}
