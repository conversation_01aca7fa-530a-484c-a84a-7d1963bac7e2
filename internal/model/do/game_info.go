// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameInfo is the golang structure of table game_info for DAO operations like Where/Data.
type GameInfo struct {
	g.Meta              `orm:"table:game_info, do:true"`
	Id                  interface{} // 游戏信息ID
	ProviderCode        interface{} // 游戏提供商代码
	GameCode            interface{} // 游戏代码
	GameName            interface{} // 游戏名称
	GameNameEn          interface{} // 游戏英文名称
	Category            interface{} // 游戏分类
	Subcategory         interface{} // 游戏子分类
	Status              interface{} // 游戏状态
	MinBet              interface{} // 最小投注金额
	MaxBet              interface{} // 最大投注金额
	MaxWin              interface{} // 最大赢取金额
	RtpPercentage       interface{} // RTP百分比
	Volatility          interface{} // 波动性
	SupportedCurrencies *gjson.Json // 支持的货币（JSON数组）
	SupportedLanguages  *gjson.Json // 支持的语言（JSON数组）
	GameThumbnail       interface{} // 游戏缩略图URL
	GameIcon            interface{} // 游戏图标URL
	GameDescription     interface{} // 游戏描述
	Features            *gjson.Json // 游戏特色功能（JSON数组）
	Tags                *gjson.Json // 游戏标签（JSON数组）
	IsMobileSupported   interface{} // 是否支持移动端
	IsDemoAvailable     interface{} // 是否提供试玩模式
	SortOrder           interface{} // 排序顺序
	IsFeatured          interface{} // 是否为推荐游戏
	IsNew               interface{} // 是否为新游戏
	LaunchCount         interface{} // 启动次数
	LastLaunchedAt      *gtime.Time // 最后启动时间
	Metadata            *gjson.Json // 额外元数据
	CreatedAt           *gtime.Time // 创建时间
	UpdatedAt           *gtime.Time // 更新时间
	DeletedAt           *gtime.Time // 删除时间
}
