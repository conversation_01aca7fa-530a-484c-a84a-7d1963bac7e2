// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameBetDetailsApiCalls is the golang structure of table game_bet_details_api_calls for DAO operations like Where/Data.
type GameBetDetailsApiCalls struct {
	g.Meta           `orm:"table:game_bet_details_api_calls, do:true"`
	Id               interface{} //
	ApiMethod        interface{} // API方法 (lbdm=Live, bdm=RNG)
	TenantId         interface{} // 租户 id
	Username         interface{} // 查询的玩家用户名
	StartDate        *gtime.Time // 查询开始日期
	EndDate          *gtime.Time // 查询结束日期
	PageNumber       interface{} // 请求的页码
	TotalPages       interface{} // 总页数
	CurrentPage      interface{} // 当前页码
	TotalCount       interface{} // 总记录数
	ApiStatus        interface{} // API响应状态码 (0=成功)
	ApiErrorDesc     interface{} // API错误描述
	ResponseTimeMs   interface{} // API响应时间(毫秒)
	RecordsProcessed interface{} // 成功处理的记录数
	ProcessingStatus interface{} // 处理状态
	ProcessingError  interface{} // 处理错误信息
	CreatedAt        *gtime.Time // 调用时间
	CompletedAt      *gtime.Time // 处理完成时间
}
