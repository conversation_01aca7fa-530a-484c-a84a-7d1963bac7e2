// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RedPackets is the golang structure of table red_packets for DAO operations like Where/Data.
type RedPackets struct {
	g.Meta              `orm:"table:red_packets, do:true"`
	RedPacketId         interface{} // 红包 ID (主键)
	TenantId            interface{} // 租户 id
	Uuid                interface{} // 红包唯一id
	RedPacketImagesId   interface{} // 图片id 外键id 指向red_packet_images.red_packet_images_id
	CreatorUserId       interface{} // 创建者用户 ID (外键, 指向 users.user_id)
	CreatorUsername     interface{} // 创建者用户 ID (外键, 指向 users.user_id)
	CoverFileId         interface{} // 创建者用户 ID (外键, 指向 users.user_id)
	ThumbUrl            interface{} // 创建者用户 ID (外键, 指向 users.user_id)
	TokenId             interface{} // 红包代币 ID (外键, 指向 tokens.token_id)
	TotalAmount         interface{} // 红包总金额
	Quantity            interface{} // 红包总个数
	RemainingAmount     interface{} // 剩余金额
	RemainingQuantity   interface{} // 剩余个数
	Type                interface{} // 红包类型: random-随机金额, fixed-固定金额
	RedPacketType       interface{} // 红包类型: private-私聊红包, group-群组红包
	DrawStatus          interface{} // 抽奖状态: pending-等待开奖, drawn-已开奖, cancelled-已取消, NULL-非抽奖红包
	DrawTime            *gtime.Time // 开奖时间: 幸运红包开奖的时间
	ChatId              interface{} // Telegram 聊天 ID: 群组红包的群组 ChatID
	GroupMessageId      interface{} // Telegram 群组消息 ID: 用于更新群组红包消息
	Memo                interface{} // 红包留言
	Status              interface{} // 红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消
	CreatedAt           *gtime.Time // 创建时间
	ExpiresAt           *gtime.Time // 过期时间
	DeletedAt           *gtime.Time // 软删除的时间戳
	SenderUserId        interface{} // 发送方用户 ID (外键, 指向 users.user_id)
	Symbol              interface{} // 代币符号 (例如: USDT, BTC, ETH)
	TransactionId       interface{} // 关联的扣款交易流水 ID (外键, 指向 transactions.id)
	MessageId           interface{} // 内联消息 ID，用于后续编辑
	IsPay               interface{} // 是否支付 0 未支付 1 已支付
	BettingVolume       interface{} // 流水金额
	BettingVolumeDays   interface{} // 流水天数
	GroupTitle          interface{} // 群组名
	GroupId             interface{} // 指定群组
	GroupInvitationLink interface{} // 群组邀请链接
	SpecifyGroup        interface{} // 是否指定群组
	SpecifyBetting      interface{} // 是否需要流水
	IsPremium           interface{} // 是否需要会员
	IsVerificationCode  interface{} // 是否需要验证码
}
