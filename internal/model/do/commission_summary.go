// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CommissionSummary is the golang structure of table commission_summary for DAO operations like Where/Data.
type CommissionSummary struct {
	g.Meta             `orm:"table:commission_summary, do:true"`
	Id                 interface{} //
	UserId             interface{} // 用户ID
	TenantId           interface{} // 租户 id
	Date               *gtime.Time // 日期
	DirectCommission   interface{} // 直接邀请佣金
	IndirectCommission interface{} // 间接邀请佣金
	TotalCommission    interface{} // 总佣金
	DirectCount        interface{} // 直接邀请佣金笔数
	IndirectCount      interface{} // 间接邀请佣金笔数
	Symbol             interface{} // 币种
	CreatedAt          *gtime.Time //
	UpdatedAt          *gtime.Time //
}
