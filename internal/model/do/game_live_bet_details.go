// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameLiveBetDetails is the golang structure of table game_live_bet_details for DAO operations like Where/Data.
type GameLiveBetDetails struct {
	g.Meta            `orm:"table:game_live_bet_details, do:true"`
	Id                interface{} //
	Username          interface{} // 游戏账号的登录名
	UserId            interface{} // 用户ID
	TenantId          interface{} // 租户 id
	BetAmount         interface{} // 投注金额
	ValidBetAmount    interface{} // 有效投注金额
	WinAmount         interface{} // 赢金额
	NetPnl            interface{} // 净输赢 (正数为赢，负数为输)
	Currency          interface{} // 币别 (例如: CNY, USD)
	GameCode          interface{} // 游戏代码
	GameName          interface{} // 游戏名称
	ProductType       interface{} // 产品类别 (191=Live游戏)
	GameCategory      interface{} // 游戏类别
	BetOrderNo        interface{} // 投注订单编号
	SessionId         interface{} // 会话标识
	BetTime           *gtime.Time // 投注时间
	TransactionTime   *gtime.Time // 交易时间
	AdditionalDetails *gjson.Json // 产品追加投注详细信息
	ApiStatus         interface{} // API响应状态码
	ApiErrorDesc      interface{} // API错误描述
	CreatedAt         *gtime.Time // 记录创建时间
	UpdatedAt         *gtime.Time // 最后更新时间
}
