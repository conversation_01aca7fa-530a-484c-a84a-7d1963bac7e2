// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PaybotAuthOrders is the golang structure of table paybot_auth_orders for DAO operations like Where/Data.
type PaybotAuthOrders struct {
	g.Meta             `orm:"table:paybot_auth_orders, do:true"`
	Id                 interface{} // 主键ID
	PaybotOrderNo      interface{} // PayBot系统订单号
	TenantId           interface{} // 租户 id
	MerchantOrderNo    interface{} // 商户订单号
	UserAccount        interface{} // 用户账户标识符（关联users.account）
	UserId             interface{} // 用户ID（关联users.id）
	OrderType          interface{} // 订单类型：add-加款，deduct-扣款
	TokenSymbol        interface{} // 代币符号（如USDT）
	Amount             interface{} // 交易金额
	AuthReason         interface{} // 授权原因
	Status             interface{} // 订单状态
	CallbackBot        interface{} // 回调机器人
	CallbackStatus     interface{} // 回调状态
	ExpireAt           *gtime.Time // 过期时间
	CompletedAt        *gtime.Time // 完成时间
	ErrorMessage       interface{} // 错误信息
	MessageId          interface{} // 内联消息 ID，用于后续编辑
	CreatedAt          *gtime.Time // 创建时间
	UpdatedAt          *gtime.Time // 更新时间
	NotificationSent   interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt *gtime.Time // 通知发送时间
}
