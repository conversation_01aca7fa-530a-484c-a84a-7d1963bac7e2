// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AdminRoleCasbin is the golang structure of table admin_role_casbin for DAO operations like Where/Data.
type AdminRoleCasbin struct {
	g.Meta `orm:"table:admin_role_casbin, do:true"`
	Id     interface{} //
	PType  interface{} //
	V0     interface{} //
	V1     interface{} //
	V2     interface{} //
	V3     interface{} //
	V4     interface{} //
	V5     interface{} //
}
