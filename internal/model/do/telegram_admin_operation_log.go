// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramAdminOperationLog is the golang structure of table telegram_admin_operation_log for DAO operations like Where/Data.
type TelegramAdminOperationLog struct {
	g.Meta                        `orm:"table:telegram_admin_operation_log, do:true"`
	Id                            interface{} // 日志ID
	TenantId                      interface{} // 租户ID
	AdminTelegramId               interface{} // 管理员Telegram ID
	AdminName                     interface{} // 管理员名称
	AdminUsername                 interface{} // 管理员用户名
	OperationType                 interface{} // 操作类型(balance/flow/withdrawal/user/system)
	OperationAction               interface{} // 具体操作动作(balance_increase/balance_decrease/flow_increase/flow_decrease/withdraw_approve/withdraw_reject/withdraw_ban/user_ban/user_unban)
	TargetUserId                  interface{} // 目标用户ID
	TargetTelegramId              interface{} // 目标用户Telegram ID
	TargetUsername                interface{} // 目标用户名
	OperationData                 *gjson.Json // 操作数据(JSON格式)
	BeforeValue                   interface{} // 操作前的值
	AfterValue                    interface{} // 操作后的值
	Amount                        interface{} // 涉及金额
	Currency                      interface{} // 币种
	Description                   interface{} // 操作描述/备注
	IpAddress                     interface{} // 操作IP地址
	UserAgent                     interface{} // 用户代理
	Status                        interface{} // 操作状态(1-成功 0-失败)
	ErrorMessage                  interface{} // 错误信息
	Duration                      interface{} // 操作耗时(毫秒)
	CreatedAt                     *gtime.Time // 创建时间
	UpdatedAt                     *gtime.Time // 更新时间
	IsCustomerServiceNotification interface{} // 是否对客服群进行通知
}
