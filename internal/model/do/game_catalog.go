// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameCatalog is the golang structure of table game_catalog for DAO operations like Where/Data.
type GameCatalog struct {
	g.Meta        `orm:"table:game_catalog, do:true"`
	Id            interface{} //
	TcgGameCode   interface{} // TC Gaming游戏代码
	GameName      interface{} // 游戏显示名称
	ProductCode   interface{} // 产品代码
	ProductType   interface{} // 产品类型
	Platform      interface{} // 支持的平台
	GameType      interface{} // 游戏类型分类
	DisplayStatus interface{} // 显示状态 (1=激活, 0=非激活)
	TrialSupport  interface{} // 是否支持试玩模式
	Sort          interface{} // 排序
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
}
