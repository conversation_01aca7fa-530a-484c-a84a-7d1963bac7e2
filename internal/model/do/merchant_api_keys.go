// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantApiKeys is the golang structure of table merchant_api_keys for DAO operations like Where/Data.
type MerchantApiKeys struct {
	g.Meta      `orm:"table:merchant_api_keys, do:true"`
	ApiKeyId    interface{} // API 密钥内部 ID (主键)
	MerchantId  interface{} // 所属商户 ID (外键, 指向 merchants.merchant_id)
	ApiKey      interface{} // API Key (公开标识符, 用于请求时识别商户)
	Secret      interface{} // Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)
	SecretHash  interface{} // Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)
	Label       interface{} // 密钥标签/名称 (方便商户管理, 例如: "生产环境", "测试服务器")
	Status      interface{} // 密钥状态: active-可用, revoked-已撤销, expired-已过期
	Scopes      interface{} // 授权范围 (例如: payment.create, balance.query, withdrawal.create, 使用逗号分隔或 JSON 格式)
	IpWhitelist interface{} // 允许调用此密钥的 IP 地址列表 (逗号分隔或 CIDR, NULL 表示不限制)
	ExpiresAt   *gtime.Time // 密钥过期时间 (NULL 表示永不过期)
	LastUsedAt  *gtime.Time // 最后使用时间 (用于审计和判断活跃度)
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 最后更新时间
	DeletedAt   *gtime.Time // 软删除时间
	RateLimit   interface{} // API调用频率限制(每分钟)
	DailyLimit  interface{} // 每日调用限制
}
