// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramGroupMembers is the golang structure of table telegram_group_members for DAO operations like Where/Data.
type TelegramGroupMembers struct {
	g.Meta    `orm:"table:telegram_group_members, do:true"`
	Id        interface{} // 主键ID
	ChatId    interface{} // Telegram 群组 ChatID(负数)
	TenantId  interface{} // 租户 ID
	UserId    interface{} // Telegram 用户 ID
	Username  interface{} // 用户名
	FirstName interface{} // FirstName
	LastName  interface{} // LastName
	IsBot     interface{} // 是否机器人
	JoinedAt  *gtime.Time // 加入时间
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
