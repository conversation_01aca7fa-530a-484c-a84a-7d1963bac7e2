// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Tenants is the golang structure of table tenants for DAO operations like Where/Data.
type Tenants struct {
	g.Meta                    `orm:"table:tenants, do:true"`
	TenantId                  interface{} // 租户唯一ID
	Username                  interface{} // 租户登录用户名
	PasswordHash              interface{} // 加密后的登录密码
	TenantName                interface{} // 姓名或昵称
	Email                     interface{} // 电子邮箱
	BusinessName              interface{} // 公司/业务注册名称 (可选)
	TelegramAccount           interface{} // telegram 账户
	TelegramBotName           interface{} // 机器人名称
	TelegramBotToken          interface{} // 机器人 token
	Level                     interface{} // 代理级别
	Status                    interface{} // 账户状态 (0-禁用, 1-启用)
	InvitationCode            interface{} // 专属邀请码 (用于下级注册)
	GoogleAuthenticatorSecret interface{} // Google Authenticator 的秘钥 (用于2FA)
	DatabaseCreatedAt         *gtime.Time // 数据库创建时间
	CreatedAt                 *gtime.Time // 创建时间
	UpdatedAt                 *gtime.Time // 最后更新时间
	DeletedAt                 *gtime.Time // 软删除时间
	Group                     interface{} // 官方群组
	Customer                  interface{} // 客服
	BettingBonusRate          interface{} // 下注反水比例
	TelegramGroupsId          interface{} // telegram_groups
}
