// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// VPaybotCallbacksStats is the golang structure of table v_paybot_callbacks_stats for DAO operations like Where/Data.
type VPaybotCallbacksStats struct {
	g.Meta          `orm:"table:v_paybot_callbacks_stats, do:true"`
	EventType       interface{} // 事件类型（deposit_confirmed/withdraw_completed等）
	ProcessedStatus interface{} // 处理状态
	CallbackCount   interface{} //
	AvgAttempts     interface{} //
	FirstCallbackAt *gtime.Time // 记录创建时间
	LastCallbackAt  *gtime.Time // 记录创建时间
}
