// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramGroups is the golang structure of table telegram_groups for DAO operations like Where/Data.
type TelegramGroups struct {
	g.Meta                      `orm:"table:telegram_groups, do:true"`
	Id                          interface{} // 主键ID
	ChatId                      interface{} // Telegram群组ID(负数)
	TenantId                    interface{} // 租户ID
	Type                        interface{} // 聊天类型:    group, supergroup, channel
	Title                       interface{} // 群组名称
	Username                    interface{} // 群组用户名(如果是公开群组)
	Description                 interface{} // 群组描述
	InviteLink                  interface{} // 群组邀请链接
	PhotoFileId                 interface{} // 群组头像文件ID
	PhotoUrl                    interface{} // 群组头像URL
	MemberCount                 interface{} // 群组成员数量
	AllMembersAreAdministrators interface{} // 是否所有成员都是管理员
	HasProtectedContent         interface{} // 是否启用内容保护
	HasVisibleHistory           interface{} // 新成员是否可见历史消息
	CanSetStickerSet            interface{} // 是否可以设置贴纸集
	StickerSetName              interface{} // 群组贴纸集名称
	LinkedChatId                interface{} // 链接的频道/群组ID
	Location                    *gjson.Json // 群组位置信息(JSON格式)
	SlowModeDelay               interface{} // 慢速模式延迟(秒)
	MessageAutoDeleteTime       interface{} // 消息自动删除时间(秒)
	Permissions                 *gjson.Json // 群组权限设置(JSON格式)
	BotAddedByUserId            interface{} // 添加机器人的用户ID
	BotAddedByUsername          interface{} // 添加机器人的用户名
	BotAddedAt                  *gtime.Time // 机器人加入时间
	BotIsAdmin                  interface{} // 机器人是否是管理员
	BotCanManageChat            interface{} // 机器人是否可以管理群组
	BotCanDeleteMessages        interface{} // 机器人是否可以删除消息
	BotCanManageVideoChats      interface{} // 机器人是否可以管理视频聊天
	BotCanRestrictMembers       interface{} // 机器人是否可以限制成员
	BotCanPromoteMembers        interface{} // 机器人是否可以提升成员
	BotCanChangeInfo            interface{} // 机器人是否可以更改群组信息
	BotCanInviteUsers           interface{} // 机器人是否可以邀请用户
	BotCanPinMessages           interface{} // 机器人是否可以置顶消息
	BotCanManageTopics          interface{} // 机器人是否可以管理话题
	BotStatus                   interface{} // 机器人状态:    active, left, kicked
	BotLeftAt                   *gtime.Time // 机器人离开时间
	FirstMessageId              interface{} // 第一条消息ID
	FirstMessageDate            *gtime.Time // 第一条消息时间
	LastMessageId               interface{} // 最后一条消息ID
	LastMessageDate             *gtime.Time // 最后一条消息时间
	TotalMessages               interface{} // 总消息数
	ActiveUsersCount            interface{} // 活跃用户数
	IsForum                     interface{} // 是否是论坛群组
	ActiveTopics                *gjson.Json // 活跃话题列表(JSON格式)
	ExtraData                   *gjson.Json // 额外数据(JSON格式)
	CreatedAt                   *gtime.Time // 记录创建时间
	UpdatedAt                   *gtime.Time // 记录更新时间
}
