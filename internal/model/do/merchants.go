// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Merchants is the golang structure of table merchants for DAO operations like Where/Data.
type Merchants struct {
	g.Meta             `orm:"table:merchants, do:true"`
	MerchantId         interface{} // 商户内部ID (主键)
	MerchantName       interface{} // 商户名称 (对外展示/内部识别)
	BusinessName       interface{} // 公司/业务注册名称 (可选)
	Email              interface{} // 商户邮箱 (用于登录和通知)
	EmailVerifiedAt    *gtime.Time // 邮箱验证时间
	PaymentPassword    interface{} // 支付密码 (经过哈希处理)
	PaymentPasswordSet interface{} // 是否设置支付密码
	AreaCode           interface{} // 电话区号
	Phone              interface{} // 联系电话
	ContactEmail       interface{} // 备用联系邮箱
	WebsiteUrl         interface{} // 商户网站URL
	Google2FaSecret    interface{} // Google 2FA密钥
	Google2FaEnabled   interface{} // Google 2FA是否启用
	RecoveryCodes      interface{} // 恢复代码
	WithdrawPermission interface{} // 提现权限
	RechargePermission interface{} // 充值权限
	ApiPermission      interface{} // API调用权限
	Status             interface{} // 商户状态 (-1:待审核, 0:禁用, 1:启用)
	IsStop             interface{} // 账户暂停状态 (0:活跃, 1:已暂停)
	Reason             interface{} // 暂停或状态变更原因
	CallbackUrl        interface{} // 回调URL
	WebhookSecret      interface{} // 回调密钥
	Language           interface{} // 语言偏好
	Timezone           interface{} // 时区设置
	Avatar             interface{} // 头像URL
	CurrentToken       interface{} // 当前认证令牌
	LastLoginTime      *gtime.Time // 最后登录时间
	LastLoginIp        interface{} // 最后登录IP
	LoginAttempts      interface{} // 登录尝试次数
	LockedUntil        *gtime.Time // 账户锁定到期时间
	Notes              interface{} // 内部备注/审核意见
	ApprovedAt         *gtime.Time // 审核通过时间
	ApprovedBy         interface{} // 审核人ID
	CreatedAt          *gtime.Time // 创建时间
	UpdatedAt          *gtime.Time // 更新时间
	DeletedAt          *gtime.Time // 软删除时间
}
