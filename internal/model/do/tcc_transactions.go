// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TccTransactions is the golang structure of table tcc_transactions for DAO operations like Where/Data.
type TccTransactions struct {
	g.Meta     `orm:"table:tcc_transactions, do:true"`
	Id         interface{} //
	Gid        interface{} // 全局事务ID
	BranchId   interface{} // 分支ID
	BusinessId interface{} // 业务ID(幂等性)
	Operation  interface{} // 操作类型: credit/debit/transfer
	Status     interface{} // 0:待处理, 1:尝试中, 2:已确认, 3:已取消
	TryData    interface{} // Try阶段序列化数据
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
}
