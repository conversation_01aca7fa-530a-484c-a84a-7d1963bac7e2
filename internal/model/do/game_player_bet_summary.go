// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GamePlayerBetSummary is the golang structure of table game_player_bet_summary for DAO operations like Where/Data.
type GamePlayerBetSummary struct {
	g.Meta             `orm:"table:game_player_bet_summary, do:true"`
	Id                 interface{} //
	TenantId           interface{} // 租户 id
	Username           interface{} // 玩家用户名
	SummaryDate        *gtime.Time // 汇总日期
	GameCategory       interface{} // 游戏类别 (LIVE, RNG, FISH)
	LiveBetCount       interface{} // Live游戏投注次数
	LiveTotalBetAmount interface{} // Live游戏总投注金额
	LiveTotalValidBet  interface{} // Live游戏总有效投注
	LiveTotalWinAmount interface{} // Live游戏总赢金额
	LiveTotalNetPnl    interface{} // Live游戏总净输赢
	RngBetCount        interface{} // RNG游戏投注次数
	RngTotalBetAmount  interface{} // RNG游戏总投注金额
	RngTotalValidBet   interface{} // RNG游戏总有效投注
	RngTotalWinAmount  interface{} // RNG游戏总赢金额
	RngTotalNetPnl     interface{} // RNG游戏总净输赢
	TotalBetCount      interface{} // 总投注次数
	TotalBetAmount     interface{} // 总投注金额
	TotalValidBet      interface{} // 总有效投注
	TotalWinAmount     interface{} // 总赢金额
	TotalNetPnl        interface{} // 总净输赢
	CreatedAt          *gtime.Time // 记录创建时间
	UpdatedAt          *gtime.Time // 最后更新时间
}
