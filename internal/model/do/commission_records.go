// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CommissionRecords is the golang structure of table commission_records for DAO operations like Where/Data.
type CommissionRecords struct {
	g.Meta            `orm:"table:commission_records, do:true"`
	Id                interface{} //
	BeneficiaryId     interface{} // 受益人ID（收到佣金的用户）
	SourceUserId      interface{} // 来源用户ID（产生投注的用户）
	TenantId          interface{} // 租户 id
	GameTransactionId interface{} // 游戏流水ID
	BetAmount         interface{} // 投注金额
	CommissionRate    interface{} // 佣金比例（0.003表示0.3%）
	CommissionAmount  interface{} // 佣金金额
	CommissionType    interface{} // 佣金类型（direct:直接邀请，indirect:间接邀请）
	Level             interface{} // 推荐层级（1:直接，2+:间接）
	Symbol            interface{} // 币种
	Status            interface{} // 状态（0:待发放，1:已发放，2:发放失败）
	TransactionId     interface{} // 关联的资金交易ID
	Notified          interface{} // 是否已发送通知
	CreatedAt         *gtime.Time //
	UpdatedAt         *gtime.Time //
	DistributedAt     *gtime.Time // 发放时间
}
