// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameTransactionRecords is the golang structure of table game_transaction_records for DAO operations like Where/Data.
type GameTransactionRecords struct {
	g.Meta                `orm:"table:game_transaction_records, do:true"`
	Id                    interface{} // 交易记录ID
	TenantId              interface{} // 租户 id
	TransactionId         interface{} // 交易唯一标识符
	UserId                interface{} // 用户ID
	SnapshotGameName      interface{} // 冗余字段：游戏名称快照
	SnapshotGameType      interface{} // 冗余字段：游戏类型快照
	ProviderCode          interface{} // 游戏提供商代码
	GameCode              interface{} // 提供商返回的游戏代码 (e.g., tcg_game_code)
	SessionId             interface{} // 会话ID
	Type                  interface{} // 交易类型
	Status                interface{} // 交易状态
	Currency              interface{} // 货币类型
	Amount                interface{} // 交易金额
	WinAmount             interface{} // 赢取金额
	NetAmount             interface{} // 净金额（输赢差值）
	ProviderTransactionId interface{} // 提供商交易ID
	ReferenceId           interface{} // 关联交易ID
	RoundId               interface{} // 游戏轮次ID
	BetId                 interface{} // 投注ID
	CommissionStatus      interface{} // 佣金处理状态
	BettingBonusStatus    interface{} // 投注反水状态
	CreatedAt             *gtime.Time // 创建时间
	UpdatedAt             *gtime.Time // 更新时间
}
