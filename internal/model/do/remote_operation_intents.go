// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RemoteOperationIntents is the golang structure of table remote_operation_intents for DAO operations like Where/Data.
type RemoteOperationIntents struct {
	g.Meta        `orm:"table:remote_operation_intents, do:true"`
	Id            interface{} // 主键ID
	TransactionId interface{} // 关联的交易记录ID
	UserId        interface{} // 用户ID
	TokenSymbol   interface{} // 代币符号
	OperationType interface{} // 操作类型: credit, debit
	Amount        interface{} // 操作金额
	NewBalance    interface{} // 操作后的新余额
	BusinessId    interface{} // 业务ID
	Metadata      interface{} // 元数据JSON
	Status        interface{} // 状态: pending, processing, completed, failed
	RetryCount    interface{} // 重试次数
	LastError     interface{} // 最后一次错误信息
	CreatedAt     *gtime.Time // 创建时间
	UpdatedAt     *gtime.Time // 更新时间
	ProcessedAt   *gtime.Time // 处理完成时间
}
