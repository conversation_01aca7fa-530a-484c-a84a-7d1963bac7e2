// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DepositRewards is the golang structure of table deposit_rewards for DAO operations like Where/Data.
type DepositRewards struct {
	g.Meta           `orm:"table:deposit_rewards, do:true"`
	Id               interface{} //
	UserId           interface{} //
	TenantId         interface{} // 租户 id
	RechargeId       interface{} //
	DepositAmount    interface{} //
	RewardPercentage interface{} //
	RewardAmount     interface{} //
	FlowMultiplier   interface{} //
	RequiredTurnover interface{} //
	CreatedAt        *gtime.Time //
}
