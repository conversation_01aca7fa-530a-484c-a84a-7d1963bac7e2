// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// VPaybotAuthOrdersStats is the golang structure of table v_paybot_auth_orders_stats for DAO operations like Where/Data.
type VPaybotAuthOrdersStats struct {
	g.Meta       `orm:"table:v_paybot_auth_orders_stats, do:true"`
	UserAccount  interface{} // 用户账户标识符（关联users.account）
	OrderType    interface{} // 订单类型：add-加款，deduct-扣款
	TokenSymbol  interface{} // 代币符号（如USDT）
	Status       interface{} // 订单状态
	OrderCount   interface{} //
	TotalAmount  interface{} //
	AvgAmount    interface{} //
	FirstOrderAt *gtime.Time // 创建时间
	LastOrderAt  *gtime.Time // 创建时间
}
