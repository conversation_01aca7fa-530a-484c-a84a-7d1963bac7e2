// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// LuckyRedpacketParticipants is the golang structure of table lucky_redpacket_participants for DAO operations like Where/Data.
type LuckyRedpacketParticipants struct {
	g.Meta         `orm:"table:lucky_redpacket_participants, do:true"`
	Id             interface{} // 主键ID
	RedPacketId    interface{} // 红包 ID (外键, 指向 red_packets.red_packet_id)
	TenantId       interface{} // 租户 ID
	UserId         interface{} // Telegram 用户 ID
	Username       interface{} // 用户名
	ParticipatedAt *gtime.Time // 参与时间
	IsWinner       interface{} // 是否中奖: 0-未中奖, 1-中奖
	WonAmount      interface{} // 中奖金额
	WonAt          *gtime.Time // 中奖时间
	ClaimId        interface{} // 关联的红包领取记录 ID (外键, 指向 red_packet_claims.claim_id)
	CreatedAt      *gtime.Time // 创建时间
	UpdatedAt      *gtime.Time // 更新时间
}
