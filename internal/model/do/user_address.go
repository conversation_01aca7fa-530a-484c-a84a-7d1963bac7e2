// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserAddress is the golang structure of table user_address for DAO operations like Where/Data.
type UserAddress struct {
	g.Meta          `orm:"table:user_address, do:true"`
	UserAddressId   interface{} //
	TokenId         interface{} // 币种ID
	TenantId        interface{} // 租户 id
	UserId          interface{} // 用户id
	Lable           interface{} // 备注
	Name            interface{} // 币种
	Chan            interface{} // 链
	Address         interface{} // 地址
	QrCodeUrl       interface{} // 二维码url
	CreatedAt       *gtime.Time //
	UpdatedAt       *gtime.Time //
	Type            interface{} //
	PaybotAddressId interface{} // PayBot系统中的地址ID
	IsReused        interface{} // 是否复用地址：0-新生成，1-复用
	QrCodeData      interface{} // 二维码数据（Base64格式）
	Status          interface{} // 地址状态
	DepositCount    interface{} // 充值次数统计
	LastDepositAt   *gtime.Time // 最后一次充值时间
}
