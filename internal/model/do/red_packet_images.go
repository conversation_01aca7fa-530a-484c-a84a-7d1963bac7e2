// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RedPacketImages is the golang structure of table red_packet_images for DAO operations like Where/Data.
type RedPacketImages struct {
	g.Meta                `orm:"table:red_packet_images, do:true"`
	RedPacketImagesId     interface{} // 红包 ID (主键)
	TenantId              interface{} // 租户 id
	Status                interface{} // 图片状态 pending_review, fail, success
	ProcessingStatus      interface{} // 处理状态: pending-待处理, processing-处理中, completed-已完成,    failed-处理失败
	ProcessingAttempts    interface{} // 处理尝试次数
	ProcessingStartedAt   *gtime.Time // 开始处理时间
	ProcessingCompletedAt *gtime.Time // 处理完成时间
	ProcessingError       interface{} // 处理错误信息
	OriginalFileSize      interface{} // 原始文件大小(字节)
	ProcessedFileSize     interface{} // 处理后文件大小(字节)
	ImageWidth            interface{} // 图片宽度
	ImageHeight           interface{} // 图片高度
	ImageFormat           interface{} // 图片格式(jpeg, png等)
	CreatedAt             *gtime.Time // 创建时间
	DeletedAt             *gtime.Time // 软删除的时间戳
	UpdatedAt             *gtime.Time // 最后更新时间
	RefuseReasonZh        interface{} // 拒绝原因 (中文)
	RefuseReasonEn        interface{} // 拒绝原因 (英文)
	UserId                interface{} // 用户ID (Foreign key to users table recommended)
	ImagesUrl             interface{} //
	FileId                interface{} //
	NotificationSent      interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    *gtime.Time // 通知发送时间
}
