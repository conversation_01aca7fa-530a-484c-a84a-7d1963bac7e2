// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramGroupEvents is the golang structure of table telegram_group_events for DAO operations like Where/Data.
type TelegramGroupEvents struct {
	g.Meta    `orm:"table:telegram_group_events, do:true"`
	Id        interface{} // 主键ID
	GroupId   interface{} // 关联telegram_groups表ID
	ChatId    interface{} // Telegram群组ID
	TenantId  interface{} // 租户ID
	EventType interface{} // 事件类型: bot_added,    bot_removed, bot_promoted, member_joined, member_left, etc
	UserId    interface{} // 相关用户ID
	Username  interface{} // 相关用户名
	EventData *gjson.Json // 事件详细数据(JSON格式)
	CreatedAt *gtime.Time // 事件时间
}
