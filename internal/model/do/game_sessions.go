// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameSessions is the golang structure of table game_sessions for DAO operations like Where/Data.
type GameSessions struct {
	g.Meta         `orm:"table:game_sessions, do:true"`
	Id             interface{} // 会话ID
	SessionId      interface{} // 会话唯一标识符
	UserId         interface{} // 用户ID
	ProviderCode   interface{} // 游戏提供商代码
	GameCode       interface{} // 游戏代码
	Currency       interface{} // 货币类型
	InitialBalance interface{} // 初始余额
	CurrentBalance interface{} // 当前余额
	Status         interface{} // 会话状态
	IpAddress      interface{} // 用户IP地址
	UserAgent      interface{} // 用户代理
	LoginUrl       interface{} // 游戏登录URL
	SessionToken   interface{} // 会话令牌
	ExpiresAt      *gtime.Time // 过期时间
	LastActivityAt *gtime.Time // 最后活动时间
	Metadata       *gjson.Json // 会话元数据
	CreatedAt      *gtime.Time // 创建时间
	UpdatedAt      *gtime.Time // 更新时间
	DeletedAt      *gtime.Time // 删除时间
}
