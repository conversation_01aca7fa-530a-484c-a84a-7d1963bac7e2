// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameProviders is the golang structure of table game_providers for DAO operations like Where/Data.
type GameProviders struct {
	g.Meta           `orm:"table:game_providers, do:true"`
	Id               interface{} // 游戏提供商唯一ID
	Code             interface{} // 提供商代码（如TCG、PP、PG）
	Name             interface{} // 提供商名称
	ApiUrl           interface{} // API接口地址
	Status           interface{} // 提供商状态
	MerchantCode     interface{} // 商户代码
	SecretKey        interface{} // 签名密钥
	EncryptKey       interface{} // 加密密钥
	EncryptionMethod interface{} // 加密方式
	TimeoutSeconds   interface{} // API超时时间（秒）
	RetryCount       interface{} // 重试次数
	RateLimit        interface{} // 速率限制（每分钟请求数）
	IpWhitelist      interface{} // IP白名单（JSON数组）
	WebhookUrl       interface{} // 回调地址
	WebhookSecret    interface{} // 回调验签密钥
	ConfigData       *gjson.Json // 额外配置数据
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 更新时间
	DeletedAt        *gtime.Time // 删除时间
}
