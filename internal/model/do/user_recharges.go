// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserRecharges is the golang structure of table user_recharges for DAO operations like Where/Data.
type UserRecharges struct {
	g.Meta                        `orm:"table:user_recharges, do:true"`
	UserRechargesId               interface{} // 主键ID
	UserId                        interface{} // 用户ID (Foreign key to users table recommended)
	TokenId                       interface{} // 币种ID
	TokenSymbol                   interface{} // 币种符号，如：USDT、ETH
	Name                          interface{} // 币种ID
	Chan                          interface{} //
	RechangeType                  interface{} // 充值类型auth_pay address_pay
	TokenContractAddress          interface{} // 代币合约地址 (for non-native assets)
	FromAddress                   interface{} // 来源地址 (发送方地址)
	ToAddress                     interface{} // 目标地址 (平台分配的充值地址)
	TxHash                        interface{} // 链上交易哈希/ID (Should be unique)
	Error                         interface{} // 失败原因
	Amount                        interface{} // 充值数量
	ConversionTokenSymbol         interface{} // 折合代币符号, 例如 USDT
	ConversionRate                interface{} // 折合汇率 (充值代币与折合代币之间的汇率)
	ConvertedAmount               interface{} // 折合后的数量 (由 amount * conversion_rate 计算得出)
	State                         interface{} // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	FailureReason                 interface{} // 失败原因
	Confirmations                 interface{} // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	CreatedAt                     *gtime.Time // 记录创建时间 (e.g., 链上发现时间)
	CompletedAt                   *gtime.Time // 完成时间 (状态变为Completed的时间)
	NotificationSent              interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt            *gtime.Time // 通知发送时间
	UpdatedAt                     *gtime.Time // 最后更新时间
	TenantId                      interface{} //
	TokenDecimal                  interface{} // 币种精度（小数位）
	UsdExchangeRate               interface{} // 入账时该币对美元的汇率
	UsdAmount                     interface{} // 折算成美元的数量
	IsCustomerServiceNotification interface{} // 是否对客服群进行通知
}
