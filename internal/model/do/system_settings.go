// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SystemSettings is the golang structure of table system_settings for DAO operations like Where/Data.
type SystemSettings struct {
	g.Meta       `orm:"table:system_settings, do:true"`
	SettingKey   interface{} // 设置键 (主键)
	SettingValue interface{} // 设置值
	Description  interface{} // 设置描述
	CreatedAt    *gtime.Time // 创建时间
	UpdatedAt    *gtime.Time // 最后更新时间
	DeletedAt    *gtime.Time // 软删除时间
}
