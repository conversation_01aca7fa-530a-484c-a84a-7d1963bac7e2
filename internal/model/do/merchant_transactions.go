// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantTransactions is the golang structure of table merchant_transactions for DAO operations like Where/Data.
type MerchantTransactions struct {
	g.Meta               `orm:"table:merchant_transactions, do:true"`
	TransactionId        interface{} // 交易记录 ID (主键)
	MerchantId           interface{} // 关联用户 ID
	Username             interface{} // telegra username
	TokenId              interface{} // 关联代币 ID
	Type                 interface{} // 交易类型: deposit, withdrawal, transfer, red_packet, payment, commission, system_adjust, etc.
	WalletType           interface{} // 钱包类型:  冻结frozen ，余额 available
	Direction            interface{} // 资金方向: in-增加, out-减少
	Amount               interface{} // 交易金额 (绝对值)
	BalanceBefore        interface{} // 交易前余额快照 (对应钱包)
	BalanceAfter         interface{} // 交易后余额快照 (对应钱包)
	RelatedTransactionId interface{} // 关联交易 ID (例如: 转账的对方记录)
	RelatedEntityId      interface{} // 关联实体 ID (例如: 红包 ID, 提现订单 ID)
	RelatedEntityType    interface{} // 关联实体类型 (例如: red_packet, withdrawal_order)
	Status               interface{} // 交易状态: 1-成功, 0-失败
	Memo                 interface{} // 交易备注/消息 (例如: 管理员调账原因)
	CreatedAt            *gtime.Time // 创建时间
	UpdatedAt            *gtime.Time // 最后更新时间
	DeletedAt            *gtime.Time // 软删除时间
	Symbol               interface{} // 代币符号 (例如: USDT, BTC, ETH)
	BusinessId           interface{} // 业务唯一标识符，用于幂等性检查
	RequestAmount        interface{} // 用户请求的原始金额 (用户输入的金额)
	RequestReference     interface{} // 用户请求的参考信息 (如转账备注、提现地址等)
	RequestMetadata      *gjson.Json // 用户请求的元数据 (JSON格式存储扩展信息)
	RequestSource        interface{} // 请求来源 (telegram, web, api, admin等)
	RequestIp            interface{} // 用户请求的IP地址
	RequestUserAgent     interface{} // 用户请求的User-Agent
	RequestTimestamp     *gtime.Time // 用户发起请求的时间戳
	ProcessedAt          *gtime.Time // 交易处理完成时间
	FeeAmount            interface{} // 手续费金额
	FeeType              interface{} // 手续费类型 (fixed, percentage)
	ExchangeRate         interface{} // 汇率 (如果涉及币种转换)
	TargetUserId         interface{} // 目标用户ID (转账、红包等操作的接收方)
	TargetUsername       interface{} // 目标用户名 (转账、红包等操作的接收方用户名)
	DtmGid               interface{} // DTM全局事务ID
	DtmBranchId          interface{} // DTM分支ID
}
