// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameTransferRecords is the golang structure of table game_transfer_records for DAO operations like Where/Data.
type GameTransferRecords struct {
	g.Meta              `orm:"table:game_transfer_records, do:true"`
	Id                  interface{} //
	ReferenceNo         interface{} // 唯一参考号，用于幂等性控制
	UserId              interface{} // 用户ID
	GameCode            interface{} // 游戏代码（如evolution, pp, pg）
	ProductCode         interface{} // 游戏产品代码（如191, 39, 98）
	GameName            interface{} // 游戏名称快照
	TransferType        interface{} // 转账类型（transfer_in:转入游戏, transfer_out:转出游戏）
	Amount              interface{} // 转账金额
	Currency            interface{} // 币种
	LocalStatus         interface{} // 本地资金操作状态（pending:待处理, processing:处理中, success:成功, failed:失败, rollback:已回滚）
	LocalTransactionId  interface{} // 本地钱包扣款交易ID
	LocalErrorMsg       interface{} // 本地操作错误信息
	RemoteStatus        interface{} // 远程API调用状态（pending:待调用, processing:调用中, success:成功, failed:失败）
	RemoteTransactionId interface{} // 远程平台返回的交易ID
	RemoteErrorMsg      interface{} // 远程调用错误信息
	RemoteResponse      interface{} // 远程API完整响应（JSON格式）
	WalletBalanceBefore interface{} // 转账前钱包余额
	WalletBalanceAfter  interface{} // 转账后钱包余额
	GameBalanceBefore   interface{} // 转账前游戏余额
	GameBalanceAfter    interface{} // 转账后游戏余额
	CreatedAt           *gtime.Time //
	UpdatedAt           *gtime.Time //
	CompletedAt         *gtime.Time // 完成时间
}
