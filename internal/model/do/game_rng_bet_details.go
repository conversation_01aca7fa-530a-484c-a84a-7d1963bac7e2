// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameRngBetDetails is the golang structure of table game_rng_bet_details for DAO operations like Where/Data.
type GameRngBetDetails struct {
	g.Meta            `orm:"table:game_rng_bet_details, do:true"`
	Id                interface{} //
	TenantId          interface{} // 租户 id
	Username          interface{} // 游戏账号的登录名
	UserId            interface{} // 用户ID
	BetAmount         interface{} // 投注金额
	ValidBetAmount    interface{} // 有效投注金额
	WinAmount         interface{} // 赢金额
	NetPnl            interface{} // 净输赢 (正数为赢，负数为输)
	Currency          interface{} // 币别 (例如: CNY, USD)
	GameCode          interface{} // 游戏代码
	GameName          interface{} // 游戏名称 (RNG/FISH特有字段)
	ProductType       interface{} // 产品类别 (16=RNG, 其他=FISH)
	GameCategory      interface{} // 游戏类别 (RNG, FISH)
	BetOrderNo        interface{} // 投注订单编号
	SessionId         interface{} // 会话标识
	BetStatus         interface{} // 投注状态 (来自三方的status, e.g., C, V, R)
	BetType           interface{} // 投注类型 (来自三方的type, e.g., R for Regular)
	BetTime           *gtime.Time // 投注时间
	TransactionTime   *gtime.Time // 交易时间
	SettlementTime    *gtime.Time // 结算时间 (来自三方的endDate)
	AdditionalDetails *gjson.Json // 产品追加投注详细信息 (gamehall, gamePlat, status, createTime, endRoundTime, balance)
	ApiStatus         interface{} // API响应状态码
	ApiErrorDesc      interface{} // API错误描述
	CreatedAt         *gtime.Time // 记录创建时间
	UpdatedAt         *gtime.Time // 最后更新时间
}
