package model

// StorageConfig holds the unified configuration for object storage.
type StorageConfig struct {
	Provider string      `mapstructure:"provider" json:"provider"` // Storage provider: "minio" or "s3"
	Minio    MinioConfig `mapstructure:"minio"    json:"minio"`    // Minio specific configuration
	S3       S3Config    `mapstructure:"s3"       json:"s3"`       // S3 specific configuration
}

// MinioConfig holds the configuration for Minio object storage.
type MinioConfig struct {
	Endpoint        string `mapstructure:"endpoint"        json:"endpoint"`        // Minio server address and port
	AccessKeyID     string `mapstructure:"accessKeyID"     json:"accessKeyID"`     // Access Key ID
	SecretAccessKey string `mapstructure:"secretAccessKey" json:"secretAccessKey"` // Secret Access Key
	BucketName      string `mapstructure:"bucketName"      json:"bucketName"`      // Bucket name
	UseSSL          bool   `mapstructure:"useSSL"          json:"useSSL"`          // Whether to use HTTPS (true/false)
	PublicURLPrefix string `mapstructure:"publicURLPrefix" json:"publicURLPrefix"` // Prefix for constructing file access URLs
}

// S3Config holds the configuration for AWS S3 object storage.
type S3Config struct {
	AccessKeyID          string `mapstructure:"accessKeyID"          json:"accessKeyID"`          // AWS Access Key ID
	SecretAccessKey      string `mapstructure:"secretAccessKey"      json:"secretAccessKey"`      // AWS Secret Access Key
	Region               string `mapstructure:"region"               json:"region"`               // AWS Region
	BucketName           string `mapstructure:"bucketName"           json:"bucketName"`           // AWS Bucket Name
	UsePathStyleEndpoint bool   `mapstructure:"usePathStyleEndpoint" json:"usePathStyleEndpoint"` // Whether to use path style endpoint
	PublicURLPrefix      string `mapstructure:"publicURLPrefix"      json:"publicURLPrefix"`      // (Optional) Prefix for constructing file access URLs
}
