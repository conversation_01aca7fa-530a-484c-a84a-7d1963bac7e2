// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// LuckyRedpacketParticipants is the golang structure for table lucky_redpacket_participants.
type LuckyRedpacketParticipants struct {
	Id             uint64          `json:"id"             orm:"id"              description:"主键ID"`                                             // 主键ID
	RedPacketId    int64           `json:"redPacketId"    orm:"red_packet_id"   description:"红包 ID (外键, 指向 red_packets.red_packet_id)"`         // 红包 ID (外键, 指向 red_packets.red_packet_id)
	TenantId       uint            `json:"tenantId"       orm:"tenant_id"       description:"租户 ID"`                                            // 租户 ID
	UserId         int64           `json:"userId"         orm:"user_id"         description:"Telegram 用户 ID"`                                   // Telegram 用户 ID
	Username       string          `json:"username"       orm:"username"        description:"用户名"`                                              // 用户名
	ParticipatedAt *gtime.Time     `json:"participatedAt" orm:"participated_at" description:"参与时间"`                                             // 参与时间
	IsWinner       int             `json:"isWinner"       orm:"is_winner"       description:"是否中奖: 0-未中奖, 1-中奖"`                                // 是否中奖: 0-未中奖, 1-中奖
	WonAmount      decimal.Decimal `json:"wonAmount"      orm:"won_amount"      description:"中奖金额"`                                             // 中奖金额
	WonAt          *gtime.Time     `json:"wonAt"          orm:"won_at"          description:"中奖时间"`                                             // 中奖时间
	ClaimId        int64           `json:"claimId"        orm:"claim_id"        description:"关联的红包领取记录 ID (外键, 指向 red_packet_claims.claim_id)"` // 关联的红包领取记录 ID (外键, 指向 red_packet_claims.claim_id)
	CreatedAt      *gtime.Time     `json:"createdAt"      orm:"created_at"      description:"创建时间"`                                             // 创建时间
	UpdatedAt      *gtime.Time     `json:"updatedAt"      orm:"updated_at"      description:"更新时间"`                                             // 更新时间
}
