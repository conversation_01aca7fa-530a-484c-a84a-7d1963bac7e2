// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// VPaybotCallbacksStats is the golang structure for table v_paybot_callbacks_stats.
type VPaybotCallbacksStats struct {
	EventType       string          `json:"eventType"       orm:"event_type"        description:"事件类型（deposit_confirmed/withdraw_completed等）"` // 事件类型（deposit_confirmed/withdraw_completed等）
	ProcessedStatus string          `json:"processedStatus" orm:"processed_status"  description:"处理状态"`                                        // 处理状态
	CallbackCount   int64           `json:"callbackCount"   orm:"callback_count"    description:""`                                            //
	AvgAttempts     decimal.Decimal `json:"avgAttempts"     orm:"avg_attempts"      description:""`                                            //
	FirstCallbackAt *gtime.Time     `json:"firstCallbackAt" orm:"first_callback_at" description:"记录创建时间"`                                      // 记录创建时间
	LastCallbackAt  *gtime.Time     `json:"lastCallbackAt"  orm:"last_callback_at"  description:"记录创建时间"`                                      // 记录创建时间
}
