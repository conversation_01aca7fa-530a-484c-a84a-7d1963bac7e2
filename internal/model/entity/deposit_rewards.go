// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// DepositRewards is the golang structure for table deposit_rewards.
type DepositRewards struct {
	Id               int64           `json:"id"               orm:"id"                description:""`      //
	UserId           int64           `json:"userId"           orm:"user_id"           description:""`      //
	TenantId         int             `json:"tenantId"         orm:"tenant_id"         description:"租户 id"` // 租户 id
	RechargeId       int64           `json:"rechargeId"       orm:"recharge_id"       description:""`      //
	DepositAmount    decimal.Decimal `json:"depositAmount"    orm:"deposit_amount"    description:""`      //
	RewardPercentage decimal.Decimal `json:"rewardPercentage" orm:"reward_percentage" description:""`      //
	RewardAmount     decimal.Decimal `json:"rewardAmount"     orm:"reward_amount"     description:""`      //
	FlowMultiplier   decimal.Decimal `json:"flowMultiplier"   orm:"flow_multiplier"   description:""`      //
	RequiredTurnover decimal.Decimal `json:"requiredTurnover" orm:"required_turnover" description:""`      //
	CreatedAt        *gtime.Time     `json:"createdAt"        orm:"created_at"        description:""`      //
}
