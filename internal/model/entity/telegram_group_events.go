// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramGroupEvents is the golang structure for table telegram_group_events.
type TelegramGroupEvents struct {
	Id        uint64      `json:"id"        orm:"id"         description:"主键ID"`                                                                           // 主键ID
	GroupId   uint64      `json:"groupId"   orm:"group_id"   description:"关联telegram_groups表ID"`                                                           // 关联telegram_groups表ID
	ChatId    int64       `json:"chatId"    orm:"chat_id"    description:"Telegram群组ID"`                                                                   // Telegram群组ID
	TenantId  uint        `json:"tenantId"  orm:"tenant_id"  description:"租户ID"`                                                                           // 租户ID
	EventType string      `json:"eventType" orm:"event_type" description:"事件类型: bot_added,    bot_removed, bot_promoted, member_joined, member_left, etc"` // 事件类型: bot_added,    bot_removed, bot_promoted, member_joined, member_left, etc
	UserId    int64       `json:"userId"    orm:"user_id"    description:"相关用户ID"`                                                                         // 相关用户ID
	Username  string      `json:"username"  orm:"username"   description:"相关用户名"`                                                                          // 相关用户名
	EventData *gjson.Json `json:"eventData" orm:"event_data" description:"事件详细数据(JSON格式)"`                                                                 // 事件详细数据(JSON格式)
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"事件时间"`                                                                           // 事件时间
}
