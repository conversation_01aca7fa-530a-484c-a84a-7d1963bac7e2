// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// GameBetDetailsApiCalls is the golang structure for table game_bet_details_api_calls.
type GameBetDetailsApiCalls struct {
	Id               uint64      `json:"id"               orm:"id"                description:""`                           //
	ApiMethod        string      `json:"apiMethod"        orm:"api_method"        description:"API方法 (lbdm=Live, bdm=RNG)"` // API方法 (lbdm=Live, bdm=RNG)
	TenantId         int         `json:"tenantId"         orm:"tenant_id"         description:"租户 id"`                      // 租户 id
	Username         string      `json:"username"         orm:"username"          description:"查询的玩家用户名"`                   // 查询的玩家用户名
	StartDate        *gtime.Time `json:"startDate"        orm:"start_date"        description:"查询开始日期"`                     // 查询开始日期
	EndDate          *gtime.Time `json:"endDate"          orm:"end_date"          description:"查询结束日期"`                     // 查询结束日期
	PageNumber       int         `json:"pageNumber"       orm:"page_number"       description:"请求的页码"`                      // 请求的页码
	TotalPages       int         `json:"totalPages"       orm:"total_pages"       description:"总页数"`                        // 总页数
	CurrentPage      int         `json:"currentPage"      orm:"current_page"      description:"当前页码"`                       // 当前页码
	TotalCount       int         `json:"totalCount"       orm:"total_count"       description:"总记录数"`                       // 总记录数
	ApiStatus        int         `json:"apiStatus"        orm:"api_status"        description:"API响应状态码 (0=成功)"`            // API响应状态码 (0=成功)
	ApiErrorDesc     string      `json:"apiErrorDesc"     orm:"api_error_desc"    description:"API错误描述"`                    // API错误描述
	ResponseTimeMs   int         `json:"responseTimeMs"   orm:"response_time_ms"  description:"API响应时间(毫秒)"`                // API响应时间(毫秒)
	RecordsProcessed int         `json:"recordsProcessed" orm:"records_processed" description:"成功处理的记录数"`                   // 成功处理的记录数
	ProcessingStatus string      `json:"processingStatus" orm:"processing_status" description:"处理状态"`                       // 处理状态
	ProcessingError  string      `json:"processingError"  orm:"processing_error"  description:"处理错误信息"`                     // 处理错误信息
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"        description:"调用时间"`                       // 调用时间
	CompletedAt      *gtime.Time `json:"completedAt"      orm:"completed_at"      description:"处理完成时间"`                     // 处理完成时间
}
