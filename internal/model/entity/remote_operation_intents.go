// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// RemoteOperationIntents is the golang structure for table remote_operation_intents.
type RemoteOperationIntents struct {
	Id            uint64          `json:"id"            orm:"id"             description:"主键ID"`                                       // 主键ID
	TransactionId uint64          `json:"transactionId" orm:"transaction_id" description:"关联的交易记录ID"`                                  // 关联的交易记录ID
	UserId        uint64          `json:"userId"        orm:"user_id"        description:"用户ID"`                                       // 用户ID
	TokenSymbol   string          `json:"tokenSymbol"   orm:"token_symbol"   description:"代币符号"`                                       // 代币符号
	OperationType string          `json:"operationType" orm:"operation_type" description:"操作类型: credit, debit"`                        // 操作类型: credit, debit
	Amount        decimal.Decimal `json:"amount"        orm:"amount"         description:"操作金额"`                                       // 操作金额
	NewBalance    decimal.Decimal `json:"newBalance"    orm:"new_balance"    description:"操作后的新余额"`                                    // 操作后的新余额
	BusinessId    string          `json:"businessId"    orm:"business_id"    description:"业务ID"`                                       // 业务ID
	Metadata      string          `json:"metadata"      orm:"metadata"       description:"元数据JSON"`                                    // 元数据JSON
	Status        string          `json:"status"        orm:"status"         description:"状态: pending, processing, completed, failed"` // 状态: pending, processing, completed, failed
	RetryCount    int             `json:"retryCount"    orm:"retry_count"    description:"重试次数"`                                       // 重试次数
	LastError     string          `json:"lastError"     orm:"last_error"     description:"最后一次错误信息"`                                   // 最后一次错误信息
	CreatedAt     *gtime.Time     `json:"createdAt"     orm:"created_at"     description:"创建时间"`                                       // 创建时间
	UpdatedAt     *gtime.Time     `json:"updatedAt"     orm:"updated_at"     description:"更新时间"`                                       // 更新时间
	ProcessedAt   *gtime.Time     `json:"processedAt"   orm:"processed_at"   description:"处理完成时间"`                                     // 处理完成时间
}
