// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GameTransferRecords is the golang structure for table game_transfer_records.
type GameTransferRecords struct {
	Id                  uint64          `json:"id"                  orm:"id"                    description:""`                                                                           //
	ReferenceNo         string          `json:"referenceNo"         orm:"reference_no"          description:"唯一参考号，用于幂等性控制"`                                                              // 唯一参考号，用于幂等性控制
	UserId              uint64          `json:"userId"              orm:"user_id"               description:"用户ID"`                                                                       // 用户ID
	GameCode            string          `json:"gameCode"            orm:"game_code"             description:"游戏代码（如evolution, pp, pg）"`                                                   // 游戏代码（如evolution, pp, pg）
	ProductCode         int             `json:"productCode"         orm:"product_code"          description:"游戏产品代码（如191, 39, 98）"`                                                       // 游戏产品代码（如191, 39, 98）
	GameName            string          `json:"gameName"            orm:"game_name"             description:"游戏名称快照"`                                                                     // 游戏名称快照
	TransferType        string          `json:"transferType"        orm:"transfer_type"         description:"转账类型（transfer_in:转入游戏, transfer_out:转出游戏）"`                                  // 转账类型（transfer_in:转入游戏, transfer_out:转出游戏）
	Amount              decimal.Decimal `json:"amount"              orm:"amount"                description:"转账金额"`                                                                       // 转账金额
	Currency            string          `json:"currency"            orm:"currency"              description:"币种"`                                                                         // 币种
	LocalStatus         string          `json:"localStatus"         orm:"local_status"          description:"本地资金操作状态（pending:待处理, processing:处理中, success:成功, failed:失败, rollback:已回滚）"` // 本地资金操作状态（pending:待处理, processing:处理中, success:成功, failed:失败, rollback:已回滚）
	LocalTransactionId  uint64          `json:"localTransactionId"  orm:"local_transaction_id"  description:"本地钱包扣款交易ID"`                                                                 // 本地钱包扣款交易ID
	LocalErrorMsg       string          `json:"localErrorMsg"       orm:"local_error_msg"       description:"本地操作错误信息"`                                                                   // 本地操作错误信息
	RemoteStatus        string          `json:"remoteStatus"        orm:"remote_status"         description:"远程API调用状态（pending:待调用, processing:调用中, success:成功, failed:失败）"`              // 远程API调用状态（pending:待调用, processing:调用中, success:成功, failed:失败）
	RemoteTransactionId string          `json:"remoteTransactionId" orm:"remote_transaction_id" description:"远程平台返回的交易ID"`                                                                // 远程平台返回的交易ID
	RemoteErrorMsg      string          `json:"remoteErrorMsg"      orm:"remote_error_msg"      description:"远程调用错误信息"`                                                                   // 远程调用错误信息
	RemoteResponse      string          `json:"remoteResponse"      orm:"remote_response"       description:"远程API完整响应（JSON格式）"`                                                          // 远程API完整响应（JSON格式）
	WalletBalanceBefore decimal.Decimal `json:"walletBalanceBefore" orm:"wallet_balance_before" description:"转账前钱包余额"`                                                                    // 转账前钱包余额
	WalletBalanceAfter  decimal.Decimal `json:"walletBalanceAfter"  orm:"wallet_balance_after"  description:"转账后钱包余额"`                                                                    // 转账后钱包余额
	GameBalanceBefore   decimal.Decimal `json:"gameBalanceBefore"   orm:"game_balance_before"   description:"转账前游戏余额"`                                                                    // 转账前游戏余额
	GameBalanceAfter    decimal.Decimal `json:"gameBalanceAfter"    orm:"game_balance_after"    description:"转账后游戏余额"`                                                                    // 转账后游戏余额
	CreatedAt           *gtime.Time     `json:"createdAt"           orm:"created_at"            description:""`                                                                           //
	UpdatedAt           *gtime.Time     `json:"updatedAt"           orm:"updated_at"            description:""`                                                                           //
	CompletedAt         *gtime.Time     `json:"completedAt"         orm:"completed_at"          description:"完成时间"`                                                                       // 完成时间
}
