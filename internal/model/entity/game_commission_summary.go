// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GameCommissionSummary is the golang structure for table game_commission_summary.
type GameCommissionSummary struct {
	Id                     uint64          `json:"id"                     orm:"id"                       description:""`         //
	UserId                 uint64          `json:"userId"                 orm:"user_id"                  description:"用户ID"`     // 用户ID
	TenantId               int             `json:"tenantId"               orm:"tenant_id"                description:"租户 id"`    // 租户 id
	Date                   *gtime.Time     `json:"date"                   orm:"date"                     description:"日期"`       // 日期
	DirectCommission       decimal.Decimal `json:"directCommission"       orm:"direct_commission"        description:"直接邀请佣金"`   // 直接邀请佣金
	IndirectCommission     decimal.Decimal `json:"indirectCommission"     orm:"indirect_commission"      description:"间接邀请佣金"`   // 间接邀请佣金
	BettingBonusCommission decimal.Decimal `json:"bettingBonusCommission" orm:"betting_bonus_commission" description:"反水佣金"`     // 反水佣金
	TotalCommission        decimal.Decimal `json:"totalCommission"        orm:"total_commission"         description:"总佣金"`      // 总佣金
	DirectCount            int             `json:"directCount"            orm:"direct_count"             description:"直接邀请佣金笔数"` // 直接邀请佣金笔数
	IndirectCount          int             `json:"indirectCount"          orm:"indirect_count"           description:"间接邀请佣金笔数"` // 间接邀请佣金笔数
	BonusCommissionCount   int             `json:"bonusCommissionCount"   orm:"bonus_commission_count"   description:"反水笔数"`     // 反水笔数
	Symbol                 string          `json:"symbol"                 orm:"symbol"                   description:"币种"`       // 币种
	CreatedAt              *gtime.Time     `json:"createdAt"              orm:"created_at"               description:""`         //
	UpdatedAt              *gtime.Time     `json:"updatedAt"              orm:"updated_at"               description:""`         //
}
