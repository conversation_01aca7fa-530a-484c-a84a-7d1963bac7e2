// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// Tenants is the golang structure for table tenants.
type Tenants struct {
	TenantId                  uint            `json:"tenantId"                  orm:"tenant_id"                   description:"租户唯一ID"`                           // 租户唯一ID
	Username                  string          `json:"username"                  orm:"username"                    description:"租户登录用户名"`                          // 租户登录用户名
	PasswordHash              string          `json:"passwordHash"              orm:"password_hash"               description:"加密后的登录密码"`                         // 加密后的登录密码
	TenantName                string          `json:"tenantName"                orm:"tenant_name"                 description:"姓名或昵称"`                            // 姓名或昵称
	Email                     string          `json:"email"                     orm:"email"                       description:"电子邮箱"`                             // 电子邮箱
	BusinessName              string          `json:"businessName"              orm:"business_name"               description:"公司/业务注册名称 (可选)"`                   // 公司/业务注册名称 (可选)
	TelegramAccount           int64           `json:"telegramAccount"           orm:"telegram_account"            description:"telegram 账户"`                      // telegram 账户
	TelegramBotName           string          `json:"telegramBotName"           orm:"telegram_bot_name"           description:"机器人名称"`                            // 机器人名称
	TelegramBotToken          string          `json:"telegramBotToken"          orm:"telegram_bot_token"          description:"机器人 token"`                        // 机器人 token
	Level                     uint            `json:"level"                     orm:"level"                       description:"代理级别"`                             // 代理级别
	Status                    int             `json:"status"                    orm:"status"                      description:"账户状态 (0-禁用, 1-启用)"`                // 账户状态 (0-禁用, 1-启用)
	InvitationCode            string          `json:"invitationCode"            orm:"invitation_code"             description:"专属邀请码 (用于下级注册)"`                   // 专属邀请码 (用于下级注册)
	GoogleAuthenticatorSecret string          `json:"googleAuthenticatorSecret" orm:"google_authenticator_secret" description:"Google Authenticator 的秘钥 (用于2FA)"` // Google Authenticator 的秘钥 (用于2FA)
	DatabaseCreatedAt         *gtime.Time     `json:"databaseCreatedAt"         orm:"database_created_at"         description:"数据库创建时间"`                          // 数据库创建时间
	CreatedAt                 *gtime.Time     `json:"createdAt"                 orm:"created_at"                  description:"创建时间"`                             // 创建时间
	UpdatedAt                 *gtime.Time     `json:"updatedAt"                 orm:"updated_at"                  description:"最后更新时间"`                           // 最后更新时间
	DeletedAt                 *gtime.Time     `json:"deletedAt"                 orm:"deleted_at"                  description:"软删除时间"`                            // 软删除时间
	Group                     string          `json:"group"                     orm:"group"                       description:"官方群组"`                             // 官方群组
	Customer                  string          `json:"customer"                  orm:"customer"                    description:"客服"`                               // 客服
	BettingBonusRate          decimal.Decimal `json:"bettingBonusRate"          orm:"betting_bonus_rate"          description:"下注反水比例"`                           // 下注反水比例
	TelegramGroupsId          string          `json:"telegramGroupsId"          orm:"telegram_groups_id"          description:"telegram_groups"`                  // telegram_groups
}
