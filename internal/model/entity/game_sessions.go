// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GameSessions is the golang structure for table game_sessions.
type GameSessions struct {
	Id             uint64          `json:"id"             orm:"id"               description:"会话ID"`    // 会话ID
	SessionId      string          `json:"sessionId"      orm:"session_id"       description:"会话唯一标识符"` // 会话唯一标识符
	UserId         uint64          `json:"userId"         orm:"user_id"          description:"用户ID"`    // 用户ID
	ProviderCode   string          `json:"providerCode"   orm:"provider_code"    description:"游戏提供商代码"` // 游戏提供商代码
	GameCode       string          `json:"gameCode"       orm:"game_code"        description:"游戏代码"`    // 游戏代码
	Currency       string          `json:"currency"       orm:"currency"         description:"货币类型"`    // 货币类型
	InitialBalance decimal.Decimal `json:"initialBalance" orm:"initial_balance"  description:"初始余额"`    // 初始余额
	CurrentBalance decimal.Decimal `json:"currentBalance" orm:"current_balance"  description:"当前余额"`    // 当前余额
	Status         string          `json:"status"         orm:"status"           description:"会话状态"`    // 会话状态
	IpAddress      string          `json:"ipAddress"      orm:"ip_address"       description:"用户IP地址"`  // 用户IP地址
	UserAgent      string          `json:"userAgent"      orm:"user_agent"       description:"用户代理"`    // 用户代理
	LoginUrl       string          `json:"loginUrl"       orm:"login_url"        description:"游戏登录URL"` // 游戏登录URL
	SessionToken   string          `json:"sessionToken"   orm:"session_token"    description:"会话令牌"`    // 会话令牌
	ExpiresAt      *gtime.Time     `json:"expiresAt"      orm:"expires_at"       description:"过期时间"`    // 过期时间
	LastActivityAt *gtime.Time     `json:"lastActivityAt" orm:"last_activity_at" description:"最后活动时间"`  // 最后活动时间
	Metadata       *gjson.Json     `json:"metadata"       orm:"metadata"         description:"会话元数据"`   // 会话元数据
	CreatedAt      *gtime.Time     `json:"createdAt"      orm:"created_at"       description:"创建时间"`    // 创建时间
	UpdatedAt      *gtime.Time     `json:"updatedAt"      orm:"updated_at"       description:"更新时间"`    // 更新时间
	DeletedAt      *gtime.Time     `json:"deletedAt"      orm:"deleted_at"       description:"删除时间"`    // 删除时间
}
