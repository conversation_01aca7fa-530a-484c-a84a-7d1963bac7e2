// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Transfers is the golang structure for table transfers.
type Transfers struct {
	TransferId            uint64      `json:"transferId"            orm:"transfer_id"             description:"转账记录 ID (主键)"`                                              // 转账记录 ID (主键)
	MessageId             int64       `json:"messageId"             orm:"message_id"              description:"转账记录 ID (主键)"`                                              // 转账记录 ID (主键)
	ChatId                int64       `json:"chatId"                orm:"chat_id"                 description:"转账记录 ID (主键)"`                                              // 转账记录 ID (主键)
	SenderUserId          uint64      `json:"senderUserId"          orm:"sender_user_id"          description:"发送方用户 ID (外键, 指向 users.user_id)"`                           // 发送方用户 ID (外键, 指向 users.user_id)
	ReceiverUserId        uint64      `json:"receiverUserId"        orm:"receiver_user_id"        description:"接收方用户 ID (外键, 指向 users.user_id)"`                           // 接收方用户 ID (外键, 指向 users.user_id)
	TokenId               uint        `json:"tokenId"               orm:"token_id"                description:"代币 ID (外键, 指向 tokens.token_id)"`                            // 代币 ID (外键, 指向 tokens.token_id)
	Amount                uint64      `json:"amount"                orm:"amount"                  description:"转账金额 (最小单位)"`                                               // 转账金额 (最小单位)
	SenderTransactionId   uint64      `json:"senderTransactionId"   orm:"sender_transaction_id"   description:"关联的发送方资金扣除交易 ID (外键, 指向 transactions.transaction_id)"`      // 关联的发送方资金扣除交易 ID (外键, 指向 transactions.transaction_id)
	ReceiverTransactionId uint64      `json:"receiverTransactionId" orm:"receiver_transaction_id" description:"关联的接收方资金增加交易 ID (外键, 指向 transactions.transaction_id)"`      // 关联的接收方资金增加交易 ID (外键, 指向 transactions.transaction_id)
	Memo                  string      `json:"memo"                  orm:"memo"                    description:"转账备注"`                                                      // 转账备注
	Status                string      `json:"status"                orm:"status"                  description:"状态 (pending_pass, pending_collection, completed, expired)"` // 状态 (pending_pass, pending_collection, completed, expired)
	HoldId                string      `json:"holdId"                orm:"hold_id"                 description:"钱包服务返回的冻结 ID"`                                              // 钱包服务返回的冻结 ID
	CreatedAt             *gtime.Time `json:"createdAt"             orm:"created_at"              description:"转账发起时间 (记录创建时间)"`                                           // 转账发起时间 (记录创建时间)
	ExpiresAt             *gtime.Time `json:"expiresAt"             orm:"expires_at"              description:"过期时间 (created_at + 24 小时)"`                                 // 过期时间 (created_at + 24 小时)
	UpdatedAt             *gtime.Time `json:"updatedAt"             orm:"updated_at"              description:"最后更新时间"`                                                    // 最后更新时间
	DeletedAt             *gtime.Time `json:"deletedAt"             orm:"deleted_at"              description:"软删除时间"`                                                     // 软删除时间
	NeedPass              int         `json:"needPass"              orm:"need_pass"               description:"是否需要支付密码"`                                                  // 是否需要支付密码
	Key                   string      `json:"key"                   orm:"key"                     description:""`                                                          //
	Symbol                string      `json:"symbol"                orm:"symbol"                  description:""`                                                          //
	Message               string      `json:"message"               orm:"message"                 description:""`                                                          //
	InlineMessageId       string      `json:"inlineMessageId"       orm:"inline_message_id"       description:"内联消息 ID，用于后续编辑"`                                            // 内联消息 ID，用于后续编辑
	SenderUsername        string      `json:"senderUsername"        orm:"sender_username"         description:"发送方用户名"`                                                    // 发送方用户名
	ReceiverUsername      string      `json:"receiverUsername"      orm:"receiver_username"       description:"接收方用户名"`                                                    // 接收方用户名
	NotificationSent      uint        `json:"notificationSent"      orm:"notification_sent"       description:"是否已发送通知: 0-未发送, 1-已发送"`                                     // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    *gtime.Time `json:"notificationSentAt"    orm:"notification_sent_at"    description:"通知发送时间"`                                                    // 通知发送时间
}
