// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminConfigCategories is the golang structure for table admin_config_categories.
type AdminConfigCategories struct {
	Id          int         `json:"id"          orm:"id"           description:"分类唯一标识符 (例如 UUID)"` // 分类唯一标识符 (例如 UUID)
	Name        string      `json:"name"        orm:"name"         description:"分类显示名称"`            // 分类显示名称
	CategoryKey string      `json:"categoryKey" orm:"category_key" description:"分类键 (唯一, 创建后不可修改)"` // 分类键 (唯一, 创建后不可修改)
	SortOrder   int         `json:"sortOrder"   orm:"sort_order"   description:"排序顺序"`              // 排序顺序
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`              // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:"更新时间"`              // 更新时间
}
