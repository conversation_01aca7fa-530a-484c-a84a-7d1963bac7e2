// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramAdminOperationLog is the golang structure for table telegram_admin_operation_log.
type TelegramAdminOperationLog struct {
	Id                            uint64      `json:"id"                            orm:"id"                               description:"日志ID"`                                                                                                                                    // 日志ID
	TenantId                      uint        `json:"tenantId"                      orm:"tenant_id"                        description:"租户ID"`                                                                                                                                    // 租户ID
	AdminTelegramId               int64       `json:"adminTelegramId"               orm:"admin_telegram_id"                description:"管理员Telegram ID"`                                                                                                                          // 管理员Telegram ID
	AdminName                     string      `json:"adminName"                     orm:"admin_name"                       description:"管理员名称"`                                                                                                                                   // 管理员名称
	AdminUsername                 string      `json:"adminUsername"                 orm:"admin_username"                   description:"管理员用户名"`                                                                                                                                  // 管理员用户名
	OperationType                 string      `json:"operationType"                 orm:"operation_type"                   description:"操作类型(balance/flow/withdrawal/user/system)"`                                                                                               // 操作类型(balance/flow/withdrawal/user/system)
	OperationAction               string      `json:"operationAction"               orm:"operation_action"                 description:"具体操作动作(balance_increase/balance_decrease/flow_increase/flow_decrease/withdraw_approve/withdraw_reject/withdraw_ban/user_ban/user_unban)"` // 具体操作动作(balance_increase/balance_decrease/flow_increase/flow_decrease/withdraw_approve/withdraw_reject/withdraw_ban/user_ban/user_unban)
	TargetUserId                  uint64      `json:"targetUserId"                  orm:"target_user_id"                   description:"目标用户ID"`                                                                                                                                  // 目标用户ID
	TargetTelegramId              int64       `json:"targetTelegramId"              orm:"target_telegram_id"               description:"目标用户Telegram ID"`                                                                                                                         // 目标用户Telegram ID
	TargetUsername                string      `json:"targetUsername"                orm:"target_username"                  description:"目标用户名"`                                                                                                                                   // 目标用户名
	OperationData                 *gjson.Json `json:"operationData"                 orm:"operation_data"                   description:"操作数据(JSON格式)"`                                                                                                                            // 操作数据(JSON格式)
	BeforeValue                   string      `json:"beforeValue"                   orm:"before_value"                     description:"操作前的值"`                                                                                                                                   // 操作前的值
	AfterValue                    string      `json:"afterValue"                    orm:"after_value"                      description:"操作后的值"`                                                                                                                                   // 操作后的值
	Amount                        string      `json:"amount"                        orm:"amount"                           description:"涉及金额"`                                                                                                                                    // 涉及金额
	Currency                      string      `json:"currency"                      orm:"currency"                         description:"币种"`                                                                                                                                      // 币种
	Description                   string      `json:"description"                   orm:"description"                      description:"操作描述/备注"`                                                                                                                                 // 操作描述/备注
	IpAddress                     string      `json:"ipAddress"                     orm:"ip_address"                       description:"操作IP地址"`                                                                                                                                  // 操作IP地址
	UserAgent                     string      `json:"userAgent"                     orm:"user_agent"                       description:"用户代理"`                                                                                                                                    // 用户代理
	Status                        int         `json:"status"                        orm:"status"                           description:"操作状态(1-成功 0-失败)"`                                                                                                                         // 操作状态(1-成功 0-失败)
	ErrorMessage                  string      `json:"errorMessage"                  orm:"error_message"                    description:"错误信息"`                                                                                                                                    // 错误信息
	Duration                      int         `json:"duration"                      orm:"duration"                         description:"操作耗时(毫秒)"`                                                                                                                                // 操作耗时(毫秒)
	CreatedAt                     *gtime.Time `json:"createdAt"                     orm:"created_at"                       description:"创建时间"`                                                                                                                                    // 创建时间
	UpdatedAt                     *gtime.Time `json:"updatedAt"                     orm:"updated_at"                       description:"更新时间"`                                                                                                                                    // 更新时间
	IsCustomerServiceNotification int         `json:"isCustomerServiceNotification" orm:"is_customer_service_notification" description:"是否对客服群进行通知"`                                                                                                                              // 是否对客服群进行通知
}
