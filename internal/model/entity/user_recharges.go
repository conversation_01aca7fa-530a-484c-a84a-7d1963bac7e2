// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// UserRecharges is the golang structure for table user_recharges.
type UserRecharges struct {
	UserRechargesId               uint            `json:"userRechargesId"               orm:"user_recharges_id"                description:"主键ID"`                                          // 主键ID
	UserId                        uint64          `json:"userId"                        orm:"user_id"                          description:"用户ID (Foreign key to users table recommended)"` // 用户ID (Foreign key to users table recommended)
	TokenId                       uint            `json:"tokenId"                       orm:"token_id"                         description:"币种ID"`                                          // 币种ID
	TokenSymbol                   string          `json:"tokenSymbol"                   orm:"token_symbol"                     description:"币种符号，如：USDT、ETH"`                               // 币种符号，如：USDT、ETH
	Name                          string          `json:"name"                          orm:"name"                             description:"币种ID"`                                          // 币种ID
	Chan                          string          `json:"chan"                          orm:"chan"                             description:""`                                              //
	RechangeType                  string          `json:"rechangeType"                  orm:"rechange_type"                    description:"充值类型auth_pay address_pay"`                      // 充值类型auth_pay address_pay
	TokenContractAddress          string          `json:"tokenContractAddress"          orm:"token_contract_address"           description:"代币合约地址 (for non-native assets)"`                // 代币合约地址 (for non-native assets)
	FromAddress                   string          `json:"fromAddress"                   orm:"from_address"                     description:"来源地址 (发送方地址)"`                                  // 来源地址 (发送方地址)
	ToAddress                     string          `json:"toAddress"                     orm:"to_address"                       description:"目标地址 (平台分配的充值地址)"`                              // 目标地址 (平台分配的充值地址)
	TxHash                        string          `json:"txHash"                        orm:"tx_hash"                          description:"链上交易哈希/ID (Should be unique)"`                  // 链上交易哈希/ID (Should be unique)
	Error                         string          `json:"error"                         orm:"error"                            description:"失败原因"`                                          // 失败原因
	Amount                        decimal.Decimal `json:"amount"                        orm:"amount"                           description:"充值数量"`                                          // 充值数量
	ConversionTokenSymbol         string          `json:"conversionTokenSymbol"         orm:"conversion_token_symbol"          description:"折合代币符号, 例如 USDT"`                               // 折合代币符号, 例如 USDT
	ConversionRate                decimal.Decimal `json:"conversionRate"                orm:"conversion_rate"                  description:"折合汇率 (充值代币与折合代币之间的汇率)"`                         // 折合汇率 (充值代币与折合代币之间的汇率)
	ConvertedAmount               decimal.Decimal `json:"convertedAmount"               orm:"converted_amount"                 description:"折合后的数量 (由 amount * conversion_rate 计算得出)"`      // 折合后的数量 (由 amount * conversion_rate 计算得出)
	State                         uint            `json:"state"                         orm:"state"                            description:"状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)"`  // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	FailureReason                 string          `json:"failureReason"                 orm:"failure_reason"                   description:"失败原因"`                                          // 失败原因
	Confirmations                 uint            `json:"confirmations"                 orm:"confirmations"                    description:"状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)"`  // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	CreatedAt                     *gtime.Time     `json:"createdAt"                     orm:"created_at"                       description:"记录创建时间 (e.g., 链上发现时间)"`                         // 记录创建时间 (e.g., 链上发现时间)
	CompletedAt                   *gtime.Time     `json:"completedAt"                   orm:"completed_at"                     description:"完成时间 (状态变为Completed的时间)"`                       // 完成时间 (状态变为Completed的时间)
	NotificationSent              uint            `json:"notificationSent"              orm:"notification_sent"                description:"是否已发送通知: 0-未发送, 1-已发送"`                         // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt            *gtime.Time     `json:"notificationSentAt"            orm:"notification_sent_at"             description:"通知发送时间"`                                        // 通知发送时间
	UpdatedAt                     *gtime.Time     `json:"updatedAt"                     orm:"updated_at"                       description:"最后更新时间"`                                        // 最后更新时间
	TenantId                      int             `json:"tenantId"                      orm:"tenant_id"                        description:""`                                              //
	TokenDecimal                  uint            `json:"tokenDecimal"                  orm:"token_decimal"                    description:"币种精度（小数位）"`                                     // 币种精度（小数位）
	UsdExchangeRate               decimal.Decimal `json:"usdExchangeRate"               orm:"usd_exchange_rate"                description:"入账时该币对美元的汇率"`                                   // 入账时该币对美元的汇率
	UsdAmount                     decimal.Decimal `json:"usdAmount"                     orm:"usd_amount"                       description:"折算成美元的数量"`                                      // 折算成美元的数量
	IsCustomerServiceNotification int             `json:"isCustomerServiceNotification" orm:"is_customer_service_notification" description:"是否对客服群进行通知"`                                    // 是否对客服群进行通知
}
