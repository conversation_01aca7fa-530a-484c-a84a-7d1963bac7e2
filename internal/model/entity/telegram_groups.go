// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramGroups is the golang structure for table telegram_groups.
type TelegramGroups struct {
	Id                          uint64      `json:"id"                          orm:"id"                             description:"主键ID"`                                // 主键ID
	ChatId                      int64       `json:"chatId"                      orm:"chat_id"                        description:"Telegram群组ID(负数)"`                    // Telegram群组ID(负数)
	TenantId                    uint        `json:"tenantId"                    orm:"tenant_id"                      description:"租户ID"`                                // 租户ID
	Type                        string      `json:"type"                        orm:"type"                           description:"聊天类型:    group, supergroup, channel"` // 聊天类型:    group, supergroup, channel
	Title                       string      `json:"title"                       orm:"title"                          description:"群组名称"`                                // 群组名称
	Username                    string      `json:"username"                    orm:"username"                       description:"群组用户名(如果是公开群组)"`                      // 群组用户名(如果是公开群组)
	Description                 string      `json:"description"                 orm:"description"                    description:"群组描述"`                                // 群组描述
	InviteLink                  string      `json:"inviteLink"                  orm:"invite_link"                    description:"群组邀请链接"`                              // 群组邀请链接
	PhotoFileId                 string      `json:"photoFileId"                 orm:"photo_file_id"                  description:"群组头像文件ID"`                            // 群组头像文件ID
	PhotoUrl                    string      `json:"photoUrl"                    orm:"photo_url"                      description:"群组头像URL"`                             // 群组头像URL
	MemberCount                 int         `json:"memberCount"                 orm:"member_count"                   description:"群组成员数量"`                              // 群组成员数量
	AllMembersAreAdministrators int         `json:"allMembersAreAdministrators" orm:"all_members_are_administrators" description:"是否所有成员都是管理员"`                         // 是否所有成员都是管理员
	HasProtectedContent         int         `json:"hasProtectedContent"         orm:"has_protected_content"          description:"是否启用内容保护"`                            // 是否启用内容保护
	HasVisibleHistory           int         `json:"hasVisibleHistory"           orm:"has_visible_history"            description:"新成员是否可见历史消息"`                         // 新成员是否可见历史消息
	CanSetStickerSet            int         `json:"canSetStickerSet"            orm:"can_set_sticker_set"            description:"是否可以设置贴纸集"`                           // 是否可以设置贴纸集
	StickerSetName              string      `json:"stickerSetName"              orm:"sticker_set_name"               description:"群组贴纸集名称"`                             // 群组贴纸集名称
	LinkedChatId                int64       `json:"linkedChatId"                orm:"linked_chat_id"                 description:"链接的频道/群组ID"`                          // 链接的频道/群组ID
	Location                    *gjson.Json `json:"location"                    orm:"location"                       description:"群组位置信息(JSON格式)"`                      // 群组位置信息(JSON格式)
	SlowModeDelay               int         `json:"slowModeDelay"               orm:"slow_mode_delay"                description:"慢速模式延迟(秒)"`                           // 慢速模式延迟(秒)
	MessageAutoDeleteTime       int         `json:"messageAutoDeleteTime"       orm:"message_auto_delete_time"       description:"消息自动删除时间(秒)"`                         // 消息自动删除时间(秒)
	Permissions                 *gjson.Json `json:"permissions"                 orm:"permissions"                    description:"群组权限设置(JSON格式)"`                      // 群组权限设置(JSON格式)
	BotAddedByUserId            int64       `json:"botAddedByUserId"            orm:"bot_added_by_user_id"           description:"添加机器人的用户ID"`                          // 添加机器人的用户ID
	BotAddedByUsername          string      `json:"botAddedByUsername"          orm:"bot_added_by_username"          description:"添加机器人的用户名"`                           // 添加机器人的用户名
	BotAddedAt                  *gtime.Time `json:"botAddedAt"                  orm:"bot_added_at"                   description:"机器人加入时间"`                             // 机器人加入时间
	BotIsAdmin                  int         `json:"botIsAdmin"                  orm:"bot_is_admin"                   description:"机器人是否是管理员"`                           // 机器人是否是管理员
	BotCanManageChat            int         `json:"botCanManageChat"            orm:"bot_can_manage_chat"            description:"机器人是否可以管理群组"`                         // 机器人是否可以管理群组
	BotCanDeleteMessages        int         `json:"botCanDeleteMessages"        orm:"bot_can_delete_messages"        description:"机器人是否可以删除消息"`                         // 机器人是否可以删除消息
	BotCanManageVideoChats      int         `json:"botCanManageVideoChats"      orm:"bot_can_manage_video_chats"     description:"机器人是否可以管理视频聊天"`                       // 机器人是否可以管理视频聊天
	BotCanRestrictMembers       int         `json:"botCanRestrictMembers"       orm:"bot_can_restrict_members"       description:"机器人是否可以限制成员"`                         // 机器人是否可以限制成员
	BotCanPromoteMembers        int         `json:"botCanPromoteMembers"        orm:"bot_can_promote_members"        description:"机器人是否可以提升成员"`                         // 机器人是否可以提升成员
	BotCanChangeInfo            int         `json:"botCanChangeInfo"            orm:"bot_can_change_info"            description:"机器人是否可以更改群组信息"`                       // 机器人是否可以更改群组信息
	BotCanInviteUsers           int         `json:"botCanInviteUsers"           orm:"bot_can_invite_users"           description:"机器人是否可以邀请用户"`                         // 机器人是否可以邀请用户
	BotCanPinMessages           int         `json:"botCanPinMessages"           orm:"bot_can_pin_messages"           description:"机器人是否可以置顶消息"`                         // 机器人是否可以置顶消息
	BotCanManageTopics          int         `json:"botCanManageTopics"          orm:"bot_can_manage_topics"          description:"机器人是否可以管理话题"`                         // 机器人是否可以管理话题
	BotStatus                   string      `json:"botStatus"                   orm:"bot_status"                     description:"机器人状态:    active, left, kicked"`      // 机器人状态:    active, left, kicked
	BotLeftAt                   *gtime.Time `json:"botLeftAt"                   orm:"bot_left_at"                    description:"机器人离开时间"`                             // 机器人离开时间
	FirstMessageId              int64       `json:"firstMessageId"              orm:"first_message_id"               description:"第一条消息ID"`                             // 第一条消息ID
	FirstMessageDate            *gtime.Time `json:"firstMessageDate"            orm:"first_message_date"             description:"第一条消息时间"`                             // 第一条消息时间
	LastMessageId               int64       `json:"lastMessageId"               orm:"last_message_id"                description:"最后一条消息ID"`                            // 最后一条消息ID
	LastMessageDate             *gtime.Time `json:"lastMessageDate"             orm:"last_message_date"              description:"最后一条消息时间"`                            // 最后一条消息时间
	TotalMessages               int64       `json:"totalMessages"               orm:"total_messages"                 description:"总消息数"`                                // 总消息数
	ActiveUsersCount            int         `json:"activeUsersCount"            orm:"active_users_count"             description:"活跃用户数"`                               // 活跃用户数
	IsForum                     int         `json:"isForum"                     orm:"is_forum"                       description:"是否是论坛群组"`                             // 是否是论坛群组
	ActiveTopics                *gjson.Json `json:"activeTopics"                orm:"active_topics"                  description:"活跃话题列表(JSON格式)"`                      // 活跃话题列表(JSON格式)
	ExtraData                   *gjson.Json `json:"extraData"                   orm:"extra_data"                     description:"额外数据(JSON格式)"`                        // 额外数据(JSON格式)
	CreatedAt                   *gtime.Time `json:"createdAt"                   orm:"created_at"                     description:"记录创建时间"`                              // 记录创建时间
	UpdatedAt                   *gtime.Time `json:"updatedAt"                   orm:"updated_at"                     description:"记录更新时间"`                              // 记录更新时间
}
