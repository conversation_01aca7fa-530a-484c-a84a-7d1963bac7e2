// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// CommissionRecords is the golang structure for table commission_records.
type CommissionRecords struct {
	Id                uint64          `json:"id"                orm:"id"                  description:""`                                //
	BeneficiaryId     uint64          `json:"beneficiaryId"     orm:"beneficiary_id"      description:"受益人ID（收到佣金的用户）"`                  // 受益人ID（收到佣金的用户）
	SourceUserId      uint64          `json:"sourceUserId"      orm:"source_user_id"      description:"来源用户ID（产生投注的用户）"`                 // 来源用户ID（产生投注的用户）
	TenantId          int             `json:"tenantId"          orm:"tenant_id"           description:"租户 id"`                           // 租户 id
	GameTransactionId uint64          `json:"gameTransactionId" orm:"game_transaction_id" description:"游戏流水ID"`                          // 游戏流水ID
	BetAmount         decimal.Decimal `json:"betAmount"         orm:"bet_amount"          description:"投注金额"`                            // 投注金额
	CommissionRate    decimal.Decimal `json:"commissionRate"    orm:"commission_rate"     description:"佣金比例（0.003表示0.3%）"`               // 佣金比例（0.003表示0.3%）
	CommissionAmount  decimal.Decimal `json:"commissionAmount"  orm:"commission_amount"   description:"佣金金额"`                            // 佣金金额
	CommissionType    string          `json:"commissionType"    orm:"commission_type"     description:"佣金类型（direct:直接邀请，indirect:间接邀请）"` // 佣金类型（direct:直接邀请，indirect:间接邀请）
	Level             int             `json:"level"             orm:"level"               description:"推荐层级（1:直接，2+:间接）"`                // 推荐层级（1:直接，2+:间接）
	Symbol            string          `json:"symbol"            orm:"symbol"              description:"币种"`                              // 币种
	Status            int             `json:"status"            orm:"status"              description:"状态（0:待发放，1:已发放，2:发放失败）"`          // 状态（0:待发放，1:已发放，2:发放失败）
	TransactionId     uint64          `json:"transactionId"     orm:"transaction_id"      description:"关联的资金交易ID"`                       // 关联的资金交易ID
	Notified          int             `json:"notified"          orm:"notified"            description:"是否已发送通知"`                         // 是否已发送通知
	CreatedAt         *gtime.Time     `json:"createdAt"         orm:"created_at"          description:""`                                //
	UpdatedAt         *gtime.Time     `json:"updatedAt"         orm:"updated_at"          description:""`                                //
	DistributedAt     *gtime.Time     `json:"distributedAt"     orm:"distributed_at"      description:"发放时间"`                            // 发放时间
}
