// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// PaybotCallbacks is the golang structure for table paybot_callbacks.
type PaybotCallbacks struct {
	Id              uint64      `json:"id"              orm:"id"               description:"主键ID"`                                        // 主键ID
	TenantId        int         `json:"tenantId"        orm:"tenant_id"        description:"租户 id"`                                       // 租户 id
	EventType       string      `json:"eventType"       orm:"event_type"       description:"事件类型（deposit_confirmed/withdraw_completed等）"` // 事件类型（deposit_confirmed/withdraw_completed等）
	OrderNo         string      `json:"orderNo"         orm:"order_no"         description:"关联的订单号"`                                      // 关联的订单号
	RelatedTable    string      `json:"relatedTable"    orm:"related_table"    description:"关联的表名"`                                       // 关联的表名
	RelatedId       uint64      `json:"relatedId"       orm:"related_id"       description:"关联表的记录ID"`                                    // 关联表的记录ID
	MerchantId      uint64      `json:"merchantId"      orm:"merchant_id"      description:"商户ID"`                                        // 商户ID
	Payload         *gjson.Json `json:"payload"         orm:"payload"          description:"回调数据（JSON格式）"`                                // 回调数据（JSON格式）
	Signature       string      `json:"signature"       orm:"signature"        description:"回调签名"`                                        // 回调签名
	CallbackTime    *gtime.Time `json:"callbackTime"    orm:"callback_time"    description:"回调接收时间"`                                      // 回调接收时间
	ProcessedStatus string      `json:"processedStatus" orm:"processed_status" description:"处理状态"`                                        // 处理状态
	ProcessAttempts int         `json:"processAttempts" orm:"process_attempts" description:"处理尝试次数"`                                      // 处理尝试次数
	ErrorMessage    string      `json:"errorMessage"    orm:"error_message"    description:"处理错误信息"`                                      // 处理错误信息
	NextRetryAt     *gtime.Time `json:"nextRetryAt"     orm:"next_retry_at"    description:"下次重试时间"`                                      // 下次重试时间
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:"记录创建时间"`                                      // 记录创建时间
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:"记录更新时间"`                                      // 记录更新时间
}
