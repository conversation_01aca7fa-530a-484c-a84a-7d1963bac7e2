// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserAddress is the golang structure for table user_address.
type UserAddress struct {
	UserAddressId   uint        `json:"userAddressId"   orm:"user_address_id"   description:""`                  //
	TokenId         uint        `json:"tokenId"         orm:"token_id"          description:"币种ID"`              // 币种ID
	TenantId        int         `json:"tenantId"        orm:"tenant_id"         description:"租户 id"`             // 租户 id
	UserId          int64       `json:"userId"          orm:"user_id"           description:"用户id"`              // 用户id
	Lable           string      `json:"lable"           orm:"lable"             description:"备注"`                // 备注
	Name            string      `json:"name"            orm:"name"              description:"币种"`                // 币种
	Chan            string      `json:"chan"            orm:"chan"              description:"链"`                 // 链
	Address         string      `json:"address"         orm:"address"           description:"地址"`                // 地址
	QrCodeUrl       string      `json:"qrCodeUrl"       orm:"qr_code_url"       description:"二维码url"`            // 二维码url
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"        description:""`                  //
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"        description:""`                  //
	Type            string      `json:"type"            orm:"type"              description:""`                  //
	PaybotAddressId string      `json:"paybotAddressId" orm:"paybot_address_id" description:"PayBot系统中的地址ID"`    // PayBot系统中的地址ID
	IsReused        int         `json:"isReused"        orm:"is_reused"         description:"是否复用地址：0-新生成，1-复用"` // 是否复用地址：0-新生成，1-复用
	QrCodeData      string      `json:"qrCodeData"      orm:"qr_code_data"      description:"二维码数据（Base64格式）"`   // 二维码数据（Base64格式）
	Status          string      `json:"status"          orm:"status"            description:"地址状态"`              // 地址状态
	DepositCount    int         `json:"depositCount"    orm:"deposit_count"     description:"充值次数统计"`            // 充值次数统计
	LastDepositAt   *gtime.Time `json:"lastDepositAt"   orm:"last_deposit_at"   description:"最后一次充值时间"`          // 最后一次充值时间
}
