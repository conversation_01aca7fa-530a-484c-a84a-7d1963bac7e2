// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// GameCatalog is the golang structure for table game_catalog.
type GameCatalog struct {
	Id            uint64      `json:"id"            orm:"id"             description:""`                   //
	TcgGameCode   string      `json:"tcgGameCode"   orm:"tcg_game_code"  description:"TC Gaming游戏代码"`      // TC Gaming游戏代码
	GameName      string      `json:"gameName"      orm:"game_name"      description:"游戏显示名称"`             // 游戏显示名称
	ProductCode   string      `json:"productCode"   orm:"product_code"   description:"产品代码"`               // 产品代码
	ProductType   string      `json:"productType"   orm:"product_type"   description:"产品类型"`               // 产品类型
	Platform      string      `json:"platform"      orm:"platform"       description:"支持的平台"`              // 支持的平台
	GameType      string      `json:"gameType"      orm:"game_type"      description:"游戏类型分类"`             // 游戏类型分类
	DisplayStatus int         `json:"displayStatus" orm:"display_status" description:"显示状态 (1=激活, 0=非激活)"` // 显示状态 (1=激活, 0=非激活)
	TrialSupport  int         `json:"trialSupport"  orm:"trial_support"  description:"是否支持试玩模式"`           // 是否支持试玩模式
	Sort          int         `json:"sort"          orm:"sort"           description:"排序"`                 // 排序
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""`                   //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:""`                   //
}
