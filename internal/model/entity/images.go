// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Images is the golang structure for table images.
type Images struct {
	ImagesId       int64       `json:"imagesId"       orm:"images_id"        description:"红包 ID (主键)"`                                    // 红包 ID (主键)
	Status         string      `json:"status"         orm:"status"           description:"图片状态 pending_review, fail, success"`            // 图片状态 pending_review, fail, success
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"       description:"创建时间"`                                          // 创建时间
	DeletedAt      *gtime.Time `json:"deletedAt"      orm:"deleted_at"       description:"软删除的时间戳"`                                       // 软删除的时间戳
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"       description:"最后更新时间"`                                        // 最后更新时间
	RefuseReasonZh string      `json:"refuseReasonZh" orm:"refuse_reason_zh" description:"拒绝原因 (中文)"`                                     // 拒绝原因 (中文)
	RefuseReasonEn string      `json:"refuseReasonEn" orm:"refuse_reason_en" description:"拒绝原因 (英文)"`                                     // 拒绝原因 (英文)
	UserId         uint64      `json:"userId"         orm:"user_id"          description:"用户ID (Foreign key to users table recommended)"` // 用户ID (Foreign key to users table recommended)
	ImagesUrl      string      `json:"imagesUrl"      orm:"images_url"       description:""`                                              //
	FileId         string      `json:"fileId"         orm:"file_id"          description:""`                                              //
}
