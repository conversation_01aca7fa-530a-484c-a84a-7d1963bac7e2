// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// VPaybotAuthOrdersStats is the golang structure for table v_paybot_auth_orders_stats.
type VPaybotAuthOrdersStats struct {
	UserAccount  string          `json:"userAccount"  orm:"user_account"   description:"用户账户标识符（关联users.account）"` // 用户账户标识符（关联users.account）
	OrderType    string          `json:"orderType"    orm:"order_type"     description:"订单类型：add-加款，deduct-扣款"`    // 订单类型：add-加款，deduct-扣款
	TokenSymbol  string          `json:"tokenSymbol"  orm:"token_symbol"   description:"代币符号（如USDT）"`              // 代币符号（如USDT）
	Status       string          `json:"status"       orm:"status"         description:"订单状态"`                     // 订单状态
	OrderCount   int64           `json:"orderCount"   orm:"order_count"    description:""`                         //
	TotalAmount  decimal.Decimal `json:"totalAmount"  orm:"total_amount"   description:""`                         //
	AvgAmount    decimal.Decimal `json:"avgAmount"    orm:"avg_amount"     description:""`                         //
	FirstOrderAt *gtime.Time     `json:"firstOrderAt" orm:"first_order_at" description:"创建时间"`                     // 创建时间
	LastOrderAt  *gtime.Time     `json:"lastOrderAt"  orm:"last_order_at"  description:"创建时间"`                     // 创建时间
}
