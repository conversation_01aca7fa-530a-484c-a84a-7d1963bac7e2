// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GameRngBetDetails is the golang structure for table game_rng_bet_details.
type GameRngBetDetails struct {
	Id                uint64          `json:"id"                orm:"id"                 description:""`                                                                           //
	TenantId          int             `json:"tenantId"          orm:"tenant_id"          description:"租户 id"`                                                                      // 租户 id
	Username          string          `json:"username"          orm:"username"           description:"游戏账号的登录名"`                                                                   // 游戏账号的登录名
	UserId            uint64          `json:"userId"            orm:"user_id"            description:"用户ID"`                                                                       // 用户ID
	BetAmount         decimal.Decimal `json:"betAmount"         orm:"bet_amount"         description:"投注金额"`                                                                       // 投注金额
	ValidBetAmount    decimal.Decimal `json:"validBetAmount"    orm:"valid_bet_amount"   description:"有效投注金额"`                                                                     // 有效投注金额
	WinAmount         decimal.Decimal `json:"winAmount"         orm:"win_amount"         description:"赢金额"`                                                                        // 赢金额
	NetPnl            decimal.Decimal `json:"netPnl"            orm:"net_pnl"            description:"净输赢 (正数为赢，负数为输)"`                                                            // 净输赢 (正数为赢，负数为输)
	Currency          string          `json:"currency"          orm:"currency"           description:"币别 (例如: CNY, USD)"`                                                          // 币别 (例如: CNY, USD)
	GameCode          string          `json:"gameCode"          orm:"game_code"          description:"游戏代码"`                                                                       // 游戏代码
	GameName          string          `json:"gameName"          orm:"game_name"          description:"游戏名称 (RNG/FISH特有字段)"`                                                        // 游戏名称 (RNG/FISH特有字段)
	ProductType       int             `json:"productType"       orm:"product_type"       description:"产品类别 (16=RNG, 其他=FISH)"`                                                     // 产品类别 (16=RNG, 其他=FISH)
	GameCategory      string          `json:"gameCategory"      orm:"game_category"      description:"游戏类别 (RNG, FISH)"`                                                           // 游戏类别 (RNG, FISH)
	BetOrderNo        string          `json:"betOrderNo"        orm:"bet_order_no"       description:"投注订单编号"`                                                                     // 投注订单编号
	SessionId         string          `json:"sessionId"         orm:"session_id"         description:"会话标识"`                                                                       // 会话标识
	BetStatus         string          `json:"betStatus"         orm:"bet_status"         description:"投注状态 (来自三方的status, e.g., C, V, R)"`                                          // 投注状态 (来自三方的status, e.g., C, V, R)
	BetType           string          `json:"betType"           orm:"bet_type"           description:"投注类型 (来自三方的type, e.g., R for Regular)"`                                      // 投注类型 (来自三方的type, e.g., R for Regular)
	BetTime           *gtime.Time     `json:"betTime"           orm:"bet_time"           description:"投注时间"`                                                                       // 投注时间
	TransactionTime   *gtime.Time     `json:"transactionTime"   orm:"transaction_time"   description:"交易时间"`                                                                       // 交易时间
	SettlementTime    *gtime.Time     `json:"settlementTime"    orm:"settlement_time"    description:"结算时间 (来自三方的endDate)"`                                                        // 结算时间 (来自三方的endDate)
	AdditionalDetails *gjson.Json     `json:"additionalDetails" orm:"additional_details" description:"产品追加投注详细信息 (gamehall, gamePlat, status, createTime, endRoundTime, balance)"` // 产品追加投注详细信息 (gamehall, gamePlat, status, createTime, endRoundTime, balance)
	ApiStatus         int             `json:"apiStatus"         orm:"api_status"         description:"API响应状态码"`                                                                   // API响应状态码
	ApiErrorDesc      string          `json:"apiErrorDesc"      orm:"api_error_desc"     description:"API错误描述"`                                                                    // API错误描述
	CreatedAt         *gtime.Time     `json:"createdAt"         orm:"created_at"         description:"记录创建时间"`                                                                     // 记录创建时间
	UpdatedAt         *gtime.Time     `json:"updatedAt"         orm:"updated_at"         description:"最后更新时间"`                                                                     // 最后更新时间
}
