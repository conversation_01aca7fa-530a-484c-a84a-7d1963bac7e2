// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// ExchangeProducts is the golang structure for table exchange_products.
type ExchangeProducts struct {
	ProductId              uint            `json:"productId"              orm:"product_id"                description:"兑换产品内部 ID (主键)"`                                                  // 兑换产品内部 ID (主键)
	BaseToken              string          `json:"baseToken"              orm:"base_token"                description:"基础代币 ID (用户卖出/花费的代币, 外键关联 tokens.token_id)"`                      // 基础代币 ID (用户卖出/花费的代币, 外键关联 tokens.token_id)
	QuoteToken             string          `json:"quoteToken"             orm:"quote_token"               description:"计价代币 ID (用户买入/获得的代币, 外键关联 tokens.token_id)"`                      // 计价代币 ID (用户买入/获得的代币, 外键关联 tokens.token_id)
	Symbol                 string          `json:"symbol"                 orm:"symbol"                    description:"产品交易对符号 (例如: BTC/USDT, ETH/BTC)"`                                 // 产品交易对符号 (例如: BTC/USDT, ETH/BTC)
	ProductType            string          `json:"productType"            orm:"product_type"              description:"产品类型: swap-即时兑换, spot_limit-现货限价(若支持), 或其他自定义类型"`                 // 产品类型: swap-即时兑换, spot_limit-现货限价(若支持), 或其他自定义类型
	IsActive               int             `json:"isActive"               orm:"is_active"                 description:"该兑换产品是否激活可用 (总开关)"`                                               // 该兑换产品是否激活可用 (总开关)
	AllowBuy               int             `json:"allowBuy"               orm:"allow_buy"                 description:"是否允许买入基础代币 (即 Quote->Base 的兑换)"`                                  // 是否允许买入基础代币 (即 Quote->Base 的兑换)
	AllowSell              int             `json:"allowSell"              orm:"allow_sell"                description:"是否允许卖出基础代币 (即 Base->Quote 的兑换)"`                                  // 是否允许卖出基础代币 (即 Base->Quote 的兑换)
	MaintenanceMessage     string          `json:"maintenanceMessage"     orm:"maintenance_message"       description:"维护信息 (当产品不可用或部分功能禁用时显示)"`                                         // 维护信息 (当产品不可用或部分功能禁用时显示)
	MinBaseAmountPerTx     decimal.Decimal `json:"minBaseAmountPerTx"     orm:"min_base_amount_per_tx"    description:"单笔最小兑换的基础代币数量"`                                                   // 单笔最小兑换的基础代币数量
	MaxBaseAmountPerTx     decimal.Decimal `json:"maxBaseAmountPerTx"     orm:"max_base_amount_per_tx"    description:"单笔最大兑换的基础代币数量 (NULL 表示无特定限制)"`                                    // 单笔最大兑换的基础代币数量 (NULL 表示无特定限制)
	DailyBaseVolumeLimit   decimal.Decimal `json:"dailyBaseVolumeLimit"   orm:"daily_base_volume_limit"   description:"产品每日总兑换基础代币量上限 (平台风控, NULL 不限制)"`                                 // 产品每日总兑换基础代币量上限 (平台风控, NULL 不限制)
	TotalBaseVolumeLimit   decimal.Decimal `json:"totalBaseVolumeLimit"   orm:"total_base_volume_limit"   description:"产品累计总兑换基础代币量上限 (平台风控, NULL 不限制)"`                                 // 产品累计总兑换基础代币量上限 (平台风控, NULL 不限制)
	PriceSource            string          `json:"priceSource"            orm:"price_source"              description:"价格/汇率来源标识 (例如: internal_oracle, binance_feed, weighted_average)"` // 价格/汇率来源标识 (例如: internal_oracle, binance_feed, weighted_average)
	AllowedSlippagePercent decimal.Decimal `json:"allowedSlippagePercent" orm:"allowed_slippage_percent"  description:"允许的最大价格滑点百分比 (例如 0.0050 表示 0.5%). NULL 表示此产品无特定滑点设置"`             // 允许的最大价格滑点百分比 (例如 0.0050 表示 0.5%). NULL 表示此产品无特定滑点设置
	SpreadRate             decimal.Decimal `json:"spreadRate"             orm:"spread_rate"               description:"平台在报价基础上额外添加的价差率 (例如 0.001 表示 0.1%)"`                             // 平台在报价基础上额外添加的价差率 (例如 0.001 表示 0.1%)
	RateRefreshIntervalSec uint            `json:"rateRefreshIntervalSec" orm:"rate_refresh_interval_sec" description:"建议的价格刷新频率 (秒), 应用层参考 (NULL 表示无建议)"`                               // 建议的价格刷新频率 (秒), 应用层参考 (NULL 表示无建议)
	DisplayOrder           int             `json:"displayOrder"           orm:"display_order"             description:"显示排序 (数字越小越靠前)"`                                                  // 显示排序 (数字越小越靠前)
	Description            string          `json:"description"            orm:"description"               description:"产品或交易对的描述信息"`                                                     // 产品或交易对的描述信息
	CreatedAt              *gtime.Time     `json:"createdAt"              orm:"created_at"                description:"创建时间"`                                                            // 创建时间
	UpdatedAt              *gtime.Time     `json:"updatedAt"              orm:"updated_at"                description:"最后更新时间"`                                                          // 最后更新时间
	DeletedAt              *gtime.Time     `json:"deletedAt"              orm:"deleted_at"                description:"软删除时间"`                                                           // 软删除时间
	Status                 int             `json:"status"                 orm:"status"                    description:"状态"`                                                              // 状态
	FeeStrategy            string          `json:"feeStrategy"            orm:"fee_strategy"              description:"手续费策略: output_token_percentage-从输出代币按百分比扣除"`                      // 手续费策略: output_token_percentage-从输出代币按百分比扣除
	OutputFeeRate          decimal.Decimal `json:"outputFeeRate"          orm:"output_fee_rate"           description:"输出代币手续费率 (例如: 0.002 表示 0.2%)"`                                    // 输出代币手续费率 (例如: 0.002 表示 0.2%)
	MinOutputFeeAmount     decimal.Decimal `json:"minOutputFeeAmount"     orm:"min_output_fee_amount"     description:"最小手续费金额（以输出代币计价）"`                                                // 最小手续费金额（以输出代币计价）
}
