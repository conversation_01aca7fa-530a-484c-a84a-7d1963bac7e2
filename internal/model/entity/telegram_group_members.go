// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TelegramGroupMembers is the golang structure for table telegram_group_members.
type TelegramGroupMembers struct {
	Id        uint64      `json:"id"        orm:"id"         description:"主键ID"`                   // 主键ID
	ChatId    int64       `json:"chatId"    orm:"chat_id"    description:"Telegram 群组 ChatID(负数)"` // Telegram 群组 ChatID(负数)
	TenantId  uint        `json:"tenantId"  orm:"tenant_id"  description:"租户 ID"`                  // 租户 ID
	UserId    int64       `json:"userId"    orm:"user_id"    description:"Telegram 用户 ID"`         // Telegram 用户 ID
	Username  string      `json:"username"  orm:"username"   description:"用户名"`                    // 用户名
	FirstName string      `json:"firstName" orm:"first_name" description:"FirstName"`              // FirstName
	LastName  string      `json:"lastName"  orm:"last_name"  description:"LastName"`               // LastName
	IsBot     int         `json:"isBot"     orm:"is_bot"     description:"是否机器人"`                  // 是否机器人
	JoinedAt  *gtime.Time `json:"joinedAt"  orm:"joined_at"  description:"加入时间"`                   // 加入时间
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`                   // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`                   // 更新时间
}
