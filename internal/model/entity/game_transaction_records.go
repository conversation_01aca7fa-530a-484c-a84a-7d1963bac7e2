// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GameTransactionRecords is the golang structure for table game_transaction_records.
type GameTransactionRecords struct {
	Id                    uint64          `json:"id"                    orm:"id"                      description:"交易记录ID"`                           // 交易记录ID
	TenantId              int             `json:"tenantId"              orm:"tenant_id"               description:"租户 id"`                            // 租户 id
	TransactionId         string          `json:"transactionId"         orm:"transaction_id"          description:"交易唯一标识符"`                          // 交易唯一标识符
	UserId                uint64          `json:"userId"                orm:"user_id"                 description:"用户ID"`                             // 用户ID
	SnapshotGameName      string          `json:"snapshotGameName"      orm:"snapshot_game_name"      description:"冗余字段：游戏名称快照"`                      // 冗余字段：游戏名称快照
	SnapshotGameType      string          `json:"snapshotGameType"      orm:"snapshot_game_type"      description:"冗余字段：游戏类型快照"`                      // 冗余字段：游戏类型快照
	ProviderCode          string          `json:"providerCode"          orm:"provider_code"           description:"游戏提供商代码"`                          // 游戏提供商代码
	GameCode              string          `json:"gameCode"              orm:"game_code"               description:"提供商返回的游戏代码 (e.g., tcg_game_code)"` // 提供商返回的游戏代码 (e.g., tcg_game_code)
	SessionId             string          `json:"sessionId"             orm:"session_id"              description:"会话ID"`                             // 会话ID
	Type                  string          `json:"type"                  orm:"type"                    description:"交易类型"`                             // 交易类型
	Status                string          `json:"status"                orm:"status"                  description:"交易状态"`                             // 交易状态
	Currency              string          `json:"currency"              orm:"currency"                description:"货币类型"`                             // 货币类型
	Amount                decimal.Decimal `json:"amount"                orm:"amount"                  description:"交易金额"`                             // 交易金额
	WinAmount             decimal.Decimal `json:"winAmount"             orm:"win_amount"              description:"赢取金额"`                             // 赢取金额
	NetAmount             decimal.Decimal `json:"netAmount"             orm:"net_amount"              description:"净金额（输赢差值）"`                        // 净金额（输赢差值）
	ProviderTransactionId string          `json:"providerTransactionId" orm:"provider_transaction_id" description:"提供商交易ID"`                          // 提供商交易ID
	ReferenceId           string          `json:"referenceId"           orm:"reference_id"            description:"关联交易ID"`                           // 关联交易ID
	RoundId               string          `json:"roundId"               orm:"round_id"                description:"游戏轮次ID"`                           // 游戏轮次ID
	BetId                 string          `json:"betId"                 orm:"bet_id"                  description:"投注ID"`                             // 投注ID
	CommissionStatus      string          `json:"commissionStatus"      orm:"commission_status"       description:"佣金处理状态"`                           // 佣金处理状态
	BettingBonusStatus    string          `json:"bettingBonusStatus"    orm:"betting_bonus_status"    description:"投注反水状态"`                           // 投注反水状态
	CreatedAt             *gtime.Time     `json:"createdAt"             orm:"created_at"              description:"创建时间"`                             // 创建时间
	UpdatedAt             *gtime.Time     `json:"updatedAt"             orm:"updated_at"              description:"更新时间"`                             // 更新时间
}
