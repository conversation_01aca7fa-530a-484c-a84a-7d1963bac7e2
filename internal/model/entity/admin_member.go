// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminMember is the golang structure for table admin_member.
type AdminMember struct {
	Id                 int64       `json:"id"                 orm:"id"                   description:"管理员ID"`   // 管理员ID
	RealName           string      `json:"realName"           orm:"real_name"            description:"真实姓名"`    // 真实姓名
	Username           string      `json:"username"           orm:"username"             description:"帐号"`      // 帐号
	PasswordHash       string      `json:"passwordHash"       orm:"password_hash"        description:"密码"`      // 密码
	Salt               string      `json:"salt"               orm:"salt"                 description:"密码盐"`     // 密码盐
	PasswordResetToken string      `json:"passwordResetToken" orm:"password_reset_token" description:"密码重置令牌"`  // 密码重置令牌
	Avatar             string      `json:"avatar"             orm:"avatar"               description:"头像"`      // 头像
	Email              string      `json:"email"              orm:"email"                description:"邮箱"`      // 邮箱
	Mobile             string      `json:"mobile"             orm:"mobile"               description:"手机号码"`    // 手机号码
	Pid                int64       `json:"pid"                orm:"pid"                  description:"上级管理员ID"` // 上级管理员ID
	Level              int         `json:"level"              orm:"level"                description:"关系树等级"`   // 关系树等级
	Tree               string      `json:"tree"               orm:"tree"                 description:"关系树"`     // 关系树
	InviteCode         string      `json:"inviteCode"         orm:"invite_code"          description:"邀请码"`     // 邀请码
	Cash               *gjson.Json `json:"cash"               orm:"cash"                 description:"提现配置"`    // 提现配置
	LastActiveAt       *gtime.Time `json:"lastActiveAt"       orm:"last_active_at"       description:"最后活跃时间"`  // 最后活跃时间
	Remark             string      `json:"remark"             orm:"remark"               description:"备注"`      // 备注
	Status             int         `json:"status"             orm:"status"               description:"状态"`      // 状态
	CreatedAt          *gtime.Time `json:"createdAt"          orm:"created_at"           description:"创建时间"`    // 创建时间
	UpdatedAt          *gtime.Time `json:"updatedAt"          orm:"updated_at"           description:"修改时间"`    // 修改时间
	DeletedAt          *gtime.Time `json:"deletedAt"          orm:"deleted_at"           description:"软删除的时间戳"` // 软删除的时间戳
}
