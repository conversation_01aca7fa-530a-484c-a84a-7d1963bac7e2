// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// PaybotAuthOrders is the golang structure for table paybot_auth_orders.
type PaybotAuthOrders struct {
	Id                 uint64          `json:"id"                 orm:"id"                   description:"主键ID"`                     // 主键ID
	PaybotOrderNo      string          `json:"paybotOrderNo"      orm:"paybot_order_no"      description:"PayBot系统订单号"`              // PayBot系统订单号
	TenantId           int             `json:"tenantId"           orm:"tenant_id"            description:"租户 id"`                    // 租户 id
	MerchantOrderNo    string          `json:"merchantOrderNo"    orm:"merchant_order_no"    description:"商户订单号"`                    // 商户订单号
	UserAccount        string          `json:"userAccount"        orm:"user_account"         description:"用户账户标识符（关联users.account）"` // 用户账户标识符（关联users.account）
	UserId             uint64          `json:"userId"             orm:"user_id"              description:"用户ID（关联users.id）"`         // 用户ID（关联users.id）
	OrderType          string          `json:"orderType"          orm:"order_type"           description:"订单类型：add-加款，deduct-扣款"`    // 订单类型：add-加款，deduct-扣款
	TokenSymbol        string          `json:"tokenSymbol"        orm:"token_symbol"         description:"代币符号（如USDT）"`              // 代币符号（如USDT）
	Amount             decimal.Decimal `json:"amount"             orm:"amount"               description:"交易金额"`                     // 交易金额
	AuthReason         string          `json:"authReason"         orm:"auth_reason"          description:"授权原因"`                     // 授权原因
	Status             string          `json:"status"             orm:"status"               description:"订单状态"`                     // 订单状态
	CallbackBot        string          `json:"callbackBot"        orm:"callback_bot"         description:"回调机器人"`                    // 回调机器人
	CallbackStatus     string          `json:"callbackStatus"     orm:"callback_status"      description:"回调状态"`                     // 回调状态
	ExpireAt           *gtime.Time     `json:"expireAt"           orm:"expire_at"            description:"过期时间"`                     // 过期时间
	CompletedAt        *gtime.Time     `json:"completedAt"        orm:"completed_at"         description:"完成时间"`                     // 完成时间
	ErrorMessage       string          `json:"errorMessage"       orm:"error_message"        description:"错误信息"`                     // 错误信息
	MessageId          string          `json:"messageId"          orm:"message_id"           description:"内联消息 ID，用于后续编辑"`           // 内联消息 ID，用于后续编辑
	CreatedAt          *gtime.Time     `json:"createdAt"          orm:"created_at"           description:"创建时间"`                     // 创建时间
	UpdatedAt          *gtime.Time     `json:"updatedAt"          orm:"updated_at"           description:"更新时间"`                     // 更新时间
	NotificationSent   uint            `json:"notificationSent"   orm:"notification_sent"    description:"是否已发送通知: 0-未发送, 1-已发送"`    // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt *gtime.Time     `json:"notificationSentAt" orm:"notification_sent_at" description:"通知发送时间"`                   // 通知发送时间
}
