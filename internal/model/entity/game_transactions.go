// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GameTransactions is the golang structure for table game_transactions.
type GameTransactions struct {
	Id                  uint64          `json:"id"                  orm:"id"                   description:""`                    //
	UserId              uint64          `json:"userId"              orm:"user_id"              description:"用户ID"`                // 用户ID
	Amount              decimal.Decimal `json:"amount"              orm:"amount"               description:"投注金额"`                // 投注金额
	Symbol              string          `json:"symbol"              orm:"symbol"               description:"币种"`                  // 币种
	GameType            string          `json:"gameType"            orm:"game_type"            description:"游戏类型"`                // 游戏类型
	GameResult          string          `json:"gameResult"          orm:"game_result"          description:"游戏结果（win/lose/draw）"` // 游戏结果（win/lose/draw）
	CommissionProcessed int             `json:"commissionProcessed" orm:"commission_processed" description:"佣金是否已处理"`             // 佣金是否已处理
	CreatedAt           *gtime.Time     `json:"createdAt"           orm:"created_at"           description:""`                    //
	UpdatedAt           *gtime.Time     `json:"updatedAt"           orm:"updated_at"           description:""`                    //
}
