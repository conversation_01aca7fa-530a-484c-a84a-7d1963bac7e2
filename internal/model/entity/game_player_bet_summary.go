// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GamePlayerBetSummary is the golang structure for table game_player_bet_summary.
type GamePlayerBetSummary struct {
	Id                 uint64          `json:"id"                 orm:"id"                    description:""`                       //
	TenantId           int             `json:"tenantId"           orm:"tenant_id"             description:"租户 id"`                  // 租户 id
	Username           string          `json:"username"           orm:"username"              description:"玩家用户名"`                  // 玩家用户名
	SummaryDate        *gtime.Time     `json:"summaryDate"        orm:"summary_date"          description:"汇总日期"`                   // 汇总日期
	GameCategory       string          `json:"gameCategory"       orm:"game_category"         description:"游戏类别 (LIVE, RNG, FISH)"` // 游戏类别 (LIVE, RNG, FISH)
	LiveBetCount       int             `json:"liveBetCount"       orm:"live_bet_count"        description:"Live游戏投注次数"`             // Live游戏投注次数
	LiveTotalBetAmount decimal.Decimal `json:"liveTotalBetAmount" orm:"live_total_bet_amount" description:"Live游戏总投注金额"`            // Live游戏总投注金额
	LiveTotalValidBet  decimal.Decimal `json:"liveTotalValidBet"  orm:"live_total_valid_bet"  description:"Live游戏总有效投注"`            // Live游戏总有效投注
	LiveTotalWinAmount decimal.Decimal `json:"liveTotalWinAmount" orm:"live_total_win_amount" description:"Live游戏总赢金额"`             // Live游戏总赢金额
	LiveTotalNetPnl    decimal.Decimal `json:"liveTotalNetPnl"    orm:"live_total_net_pnl"    description:"Live游戏总净输赢"`             // Live游戏总净输赢
	RngBetCount        int             `json:"rngBetCount"        orm:"rng_bet_count"         description:"RNG游戏投注次数"`              // RNG游戏投注次数
	RngTotalBetAmount  decimal.Decimal `json:"rngTotalBetAmount"  orm:"rng_total_bet_amount"  description:"RNG游戏总投注金额"`             // RNG游戏总投注金额
	RngTotalValidBet   decimal.Decimal `json:"rngTotalValidBet"   orm:"rng_total_valid_bet"   description:"RNG游戏总有效投注"`             // RNG游戏总有效投注
	RngTotalWinAmount  decimal.Decimal `json:"rngTotalWinAmount"  orm:"rng_total_win_amount"  description:"RNG游戏总赢金额"`              // RNG游戏总赢金额
	RngTotalNetPnl     decimal.Decimal `json:"rngTotalNetPnl"     orm:"rng_total_net_pnl"     description:"RNG游戏总净输赢"`              // RNG游戏总净输赢
	TotalBetCount      int             `json:"totalBetCount"      orm:"total_bet_count"       description:"总投注次数"`                  // 总投注次数
	TotalBetAmount     decimal.Decimal `json:"totalBetAmount"     orm:"total_bet_amount"      description:"总投注金额"`                  // 总投注金额
	TotalValidBet      decimal.Decimal `json:"totalValidBet"      orm:"total_valid_bet"       description:"总有效投注"`                  // 总有效投注
	TotalWinAmount     decimal.Decimal `json:"totalWinAmount"     orm:"total_win_amount"      description:"总赢金额"`                   // 总赢金额
	TotalNetPnl        decimal.Decimal `json:"totalNetPnl"        orm:"total_net_pnl"         description:"总净输赢"`                   // 总净输赢
	CreatedAt          *gtime.Time     `json:"createdAt"          orm:"created_at"            description:"记录创建时间"`                 // 记录创建时间
	UpdatedAt          *gtime.Time     `json:"updatedAt"          orm:"updated_at"            description:"最后更新时间"`                 // 最后更新时间
}
