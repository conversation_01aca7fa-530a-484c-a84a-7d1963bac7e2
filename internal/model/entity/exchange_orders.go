// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// ExchangeOrders is the golang structure for table exchange_orders.
type ExchangeOrders struct {
	OrderId               uint64          `json:"orderId"               orm:"order_id"                 description:"订单内部 ID (主键)"`                                               // 订单内部 ID (主键)
	OrderSn               string          `json:"orderSn"               orm:"order_sn"                 description:"订单唯一编号 (业务层生成, 便于跟踪和对外展示)"`                                  // 订单唯一编号 (业务层生成, 便于跟踪和对外展示)
	UserId                uint64          `json:"userId"                orm:"user_id"                  description:"执行兑换的用户 ID (外键关联 users.user_id)"`                            // 执行兑换的用户 ID (外键关联 users.user_id)
	TenantId              int             `json:"tenantId"              orm:"tenant_id"                description:"租户 id"`                                                      // 租户 id
	ProductId             uint            `json:"productId"             orm:"product_id"               description:"关联的兑换产品 ID (外键关联 exchange_products.product_id)"`             // 关联的兑换产品 ID (外键关联 exchange_products.product_id)
	BaseToken             string          `json:"baseToken"             orm:"base_token"               description:"基础代币 ID (来自 exchange_products)"`                             // 基础代币 ID (来自 exchange_products)
	QuoteToken            string          `json:"quoteToken"            orm:"quote_token"              description:"计价代币 ID (来自 exchange_products)"`                             // 计价代币 ID (来自 exchange_products)
	Symbol                string          `json:"symbol"                orm:"symbol"                   description:"交易对符号 (来自 exchange_products)"`                               // 交易对符号 (来自 exchange_products)
	TradeType             string          `json:"tradeType"             orm:"trade_type"               description:"交易类型: buy-用户买入基础代币(花费计价代币), sell-用户卖出基础代币(获得计价代币)"`          // 交易类型: buy-用户买入基础代币(花费计价代币), sell-用户卖出基础代币(获得计价代币)
	AmountBase            decimal.Decimal `json:"amountBase"            orm:"amount_base"              description:"涉及的基础代币数量（截断后）"`                                             // 涉及的基础代币数量（截断后）
	AmountQuote           decimal.Decimal `json:"amountQuote"           orm:"amount_quote"             description:"涉及的计价代币数量（截断后）"`                                             // 涉及的计价代币数量（截断后）
	OriginalFromAmount    decimal.Decimal `json:"originalFromAmount"    orm:"original_from_amount"     description:"原始兑换金额（未截断）"`                                                // 原始兑换金额（未截断）
	OriginalToAmount      decimal.Decimal `json:"originalToAmount"      orm:"original_to_amount"       description:"原始收到金额（未截断）"`                                                // 原始收到金额（未截断）
	Price                 decimal.Decimal `json:"price"                 orm:"price"                    description:"本次交易实际成交价格 (计价代币数量 / 基础代币数量)"`                               // 本次交易实际成交价格 (计价代币数量 / 基础代币数量)
	SpreadAmount          decimal.Decimal `json:"spreadAmount"          orm:"spread_amount"            description:"点差金额"`                                                       // 点差金额
	SpreadRate            decimal.Decimal `json:"spreadRate"            orm:"spread_rate"              description:"点差率"`                                                        // 点差率
	FeeAmount             decimal.Decimal `json:"feeAmount"             orm:"fee_amount"               description:"收取的手续费金额"`                                                   // 收取的手续费金额
	FeeTokenId            uint            `json:"feeTokenId"            orm:"fee_token_id"             description:"手续费收取的币种 ID (通常是 base_token_id 或 quote_token_id, 根据产品配置决定)"` // 手续费收取的币种 ID (通常是 base_token_id 或 quote_token_id, 根据产品配置决定)
	TransactionHash       string          `json:"transactionHash"       orm:"transaction_hash"         description:"交易哈希（如果有）"`                                                  // 交易哈希（如果有）
	SenderTransactionId   uint64          `json:"senderTransactionId"   orm:"sender_transaction_id"    description:"Wallet transaction ID for debit operation"`                  // Wallet transaction ID for debit operation
	ReceiverTransactionId uint64          `json:"receiverTransactionId" orm:"receiver_transaction_id"  description:"Wallet transaction ID for credit operation"`                 // Wallet transaction ID for credit operation
	QuoteId               string          `json:"quoteId"               orm:"quote_id"                 description:"关联的报价ID"`                                                    // 关联的报价ID
	Status                string          `json:"status"                orm:"status"                   description:"订单状态"`                                                       // 订单状态
	ErrorMessage          string          `json:"errorMessage"          orm:"error_message"            description:"订单失败或取消时的错误信息"`                                              // 订单失败或取消时的错误信息
	ClientOrderId         string          `json:"clientOrderId"         orm:"client_order_id"          description:"客户端提供的订单 ID (用于幂等性检查或客户端关联)"`                                // 客户端提供的订单 ID (用于幂等性检查或客户端关联)
	CreatedAt             *gtime.Time     `json:"createdAt"             orm:"created_at"               description:"订单创建时间"`                                                     // 订单创建时间
	ExecutedAt            *gtime.Time     `json:"executedAt"            orm:"executed_at"              description:"实际执行时间"`                                                     // 实际执行时间
	CompletedAt           *gtime.Time     `json:"completedAt"           orm:"completed_at"             description:"完成时间"`                                                       // 完成时间
	UpdatedAt             *gtime.Time     `json:"updatedAt"             orm:"updated_at"               description:"订单最后更新时间"`                                                   // 订单最后更新时间
	DeletedAt             *gtime.Time     `json:"deletedAt"             orm:"deleted_at"               description:"软删除时间"`                                                      // 软删除时间
	OutputAmountBeforeFee decimal.Decimal `json:"outputAmountBeforeFee" orm:"output_amount_before_fee" description:"扣费前输出金额"`                                                    // 扣费前输出金额
	OutputAmountAfterFee  decimal.Decimal `json:"outputAmountAfterFee"  orm:"output_amount_after_fee"  description:"扣费后输出金额"`                                                    // 扣费后输出金额
	FeeCalculationMethod  string          `json:"feeCalculationMethod"  orm:"fee_calculation_method"   description:"手续费计算方法"`                                                    // 手续费计算方法
}
