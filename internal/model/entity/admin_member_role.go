// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// AdminMemberRole is the golang structure for table admin_member_role.
type AdminMemberRole struct {
	MemberId     int64 `json:"memberId"     orm:"member_id"     description:"管理员ID"` // 管理员ID
	PermissionId int64 `json:"permissionId" orm:"permission_id" description:"角色ID"`  // 角色ID
}
