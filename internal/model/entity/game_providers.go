// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// GameProviders is the golang structure for table game_providers.
type GameProviders struct {
	Id               uint64      `json:"id"               orm:"id"                description:"游戏提供商唯一ID"`         // 游戏提供商唯一ID
	Code             string      `json:"code"             orm:"code"              description:"提供商代码（如TCG、PP、PG）"` // 提供商代码（如TCG、PP、PG）
	Name             string      `json:"name"             orm:"name"              description:"提供商名称"`             // 提供商名称
	ApiUrl           string      `json:"apiUrl"           orm:"api_url"           description:"API接口地址"`           // API接口地址
	Status           string      `json:"status"           orm:"status"            description:"提供商状态"`             // 提供商状态
	MerchantCode     string      `json:"merchantCode"     orm:"merchant_code"     description:"商户代码"`              // 商户代码
	SecretKey        string      `json:"secretKey"        orm:"secret_key"        description:"签名密钥"`              // 签名密钥
	EncryptKey       string      `json:"encryptKey"       orm:"encrypt_key"       description:"加密密钥"`              // 加密密钥
	EncryptionMethod string      `json:"encryptionMethod" orm:"encryption_method" description:"加密方式"`              // 加密方式
	TimeoutSeconds   uint        `json:"timeoutSeconds"   orm:"timeout_seconds"   description:"API超时时间（秒）"`        // API超时时间（秒）
	RetryCount       uint        `json:"retryCount"       orm:"retry_count"       description:"重试次数"`              // 重试次数
	RateLimit        uint        `json:"rateLimit"        orm:"rate_limit"        description:"速率限制（每分钟请求数）"`      // 速率限制（每分钟请求数）
	IpWhitelist      string      `json:"ipWhitelist"      orm:"ip_whitelist"      description:"IP白名单（JSON数组）"`     // IP白名单（JSON数组）
	WebhookUrl       string      `json:"webhookUrl"       orm:"webhook_url"       description:"回调地址"`              // 回调地址
	WebhookSecret    string      `json:"webhookSecret"    orm:"webhook_secret"    description:"回调验签密钥"`            // 回调验签密钥
	ConfigData       *gjson.Json `json:"configData"       orm:"config_data"       description:"额外配置数据"`            // 额外配置数据
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"        description:"创建时间"`              // 创建时间
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"        description:"更新时间"`              // 更新时间
	DeletedAt        *gtime.Time `json:"deletedAt"        orm:"deleted_at"        description:"删除时间"`              // 删除时间
}
