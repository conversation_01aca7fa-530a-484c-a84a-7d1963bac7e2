// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GameInfo is the golang structure for table game_info.
type GameInfo struct {
	Id                  uint64          `json:"id"                  orm:"id"                   description:"游戏信息ID"`         // 游戏信息ID
	ProviderCode        string          `json:"providerCode"        orm:"provider_code"        description:"游戏提供商代码"`        // 游戏提供商代码
	GameCode            string          `json:"gameCode"            orm:"game_code"            description:"游戏代码"`           // 游戏代码
	GameName            string          `json:"gameName"            orm:"game_name"            description:"游戏名称"`           // 游戏名称
	GameNameEn          string          `json:"gameNameEn"          orm:"game_name_en"         description:"游戏英文名称"`         // 游戏英文名称
	Category            string          `json:"category"            orm:"category"             description:"游戏分类"`           // 游戏分类
	Subcategory         string          `json:"subcategory"         orm:"subcategory"          description:"游戏子分类"`          // 游戏子分类
	Status              string          `json:"status"              orm:"status"               description:"游戏状态"`           // 游戏状态
	MinBet              decimal.Decimal `json:"minBet"              orm:"min_bet"              description:"最小投注金额"`         // 最小投注金额
	MaxBet              decimal.Decimal `json:"maxBet"              orm:"max_bet"              description:"最大投注金额"`         // 最大投注金额
	MaxWin              decimal.Decimal `json:"maxWin"              orm:"max_win"              description:"最大赢取金额"`         // 最大赢取金额
	RtpPercentage       decimal.Decimal `json:"rtpPercentage"       orm:"rtp_percentage"       description:"RTP百分比"`         // RTP百分比
	Volatility          string          `json:"volatility"          orm:"volatility"           description:"波动性"`            // 波动性
	SupportedCurrencies *gjson.Json     `json:"supportedCurrencies" orm:"supported_currencies" description:"支持的货币（JSON数组）"`  // 支持的货币（JSON数组）
	SupportedLanguages  *gjson.Json     `json:"supportedLanguages"  orm:"supported_languages"  description:"支持的语言（JSON数组）"`  // 支持的语言（JSON数组）
	GameThumbnail       string          `json:"gameThumbnail"       orm:"game_thumbnail"       description:"游戏缩略图URL"`       // 游戏缩略图URL
	GameIcon            string          `json:"gameIcon"            orm:"game_icon"            description:"游戏图标URL"`        // 游戏图标URL
	GameDescription     string          `json:"gameDescription"     orm:"game_description"     description:"游戏描述"`           // 游戏描述
	Features            *gjson.Json     `json:"features"            orm:"features"             description:"游戏特色功能（JSON数组）"` // 游戏特色功能（JSON数组）
	Tags                *gjson.Json     `json:"tags"                orm:"tags"                 description:"游戏标签（JSON数组）"`   // 游戏标签（JSON数组）
	IsMobileSupported   int             `json:"isMobileSupported"   orm:"is_mobile_supported"  description:"是否支持移动端"`        // 是否支持移动端
	IsDemoAvailable     int             `json:"isDemoAvailable"     orm:"is_demo_available"    description:"是否提供试玩模式"`       // 是否提供试玩模式
	SortOrder           int             `json:"sortOrder"           orm:"sort_order"           description:"排序顺序"`           // 排序顺序
	IsFeatured          int             `json:"isFeatured"          orm:"is_featured"          description:"是否为推荐游戏"`        // 是否为推荐游戏
	IsNew               int             `json:"isNew"               orm:"is_new"               description:"是否为新游戏"`         // 是否为新游戏
	LaunchCount         uint64          `json:"launchCount"         orm:"launch_count"         description:"启动次数"`           // 启动次数
	LastLaunchedAt      *gtime.Time     `json:"lastLaunchedAt"      orm:"last_launched_at"     description:"最后启动时间"`         // 最后启动时间
	Metadata            *gjson.Json     `json:"metadata"            orm:"metadata"             description:"额外元数据"`          // 额外元数据
	CreatedAt           *gtime.Time     `json:"createdAt"           orm:"created_at"           description:"创建时间"`           // 创建时间
	UpdatedAt           *gtime.Time     `json:"updatedAt"           orm:"updated_at"           description:"更新时间"`           // 更新时间
	DeletedAt           *gtime.Time     `json:"deletedAt"           orm:"deleted_at"           description:"删除时间"`           // 删除时间
}
