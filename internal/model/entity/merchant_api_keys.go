// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantApiKeys is the golang structure for table merchant_api_keys.
type MerchantApiKeys struct {
	ApiKeyId    uint        `json:"apiKeyId"    orm:"api_key_id"   description:"API 密钥内部 ID (主键)"`                                                             // API 密钥内部 ID (主键)
	MerchantId  uint        `json:"merchantId"  orm:"merchant_id"  description:"所属商户 ID (外键, 指向 merchants.merchant_id)"`                                       // 所属商户 ID (外键, 指向 merchants.merchant_id)
	ApiKey      string      `json:"apiKey"      orm:"api_key"      description:"API Key (公开标识符, 用于请求时识别商户)"`                                                   // API Key (公开标识符, 用于请求时识别商户)
	Secret      string      `json:"secret"      orm:"secret"       description:"Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)"`                                      // Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)
	SecretHash  string      `json:"secretHash"  orm:"secret_hash"  description:"Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)"`                                      // Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)
	Label       string      `json:"label"       orm:"label"        description:"密钥标签/名称 (方便商户管理, 例如: \"生产环境\", \"测试服务器\")"`                                    // 密钥标签/名称 (方便商户管理, 例如: "生产环境", "测试服务器")
	Status      string      `json:"status"      orm:"status"       description:"密钥状态: active-可用, revoked-已撤销, expired-已过期"`                                    // 密钥状态: active-可用, revoked-已撤销, expired-已过期
	Scopes      string      `json:"scopes"      orm:"scopes"       description:"授权范围 (例如: payment.create, balance.query, withdrawal.create, 使用逗号分隔或 JSON 格式)"` // 授权范围 (例如: payment.create, balance.query, withdrawal.create, 使用逗号分隔或 JSON 格式)
	IpWhitelist string      `json:"ipWhitelist" orm:"ip_whitelist" description:"允许调用此密钥的 IP 地址列表 (逗号分隔或 CIDR, NULL 表示不限制)"`                                    // 允许调用此密钥的 IP 地址列表 (逗号分隔或 CIDR, NULL 表示不限制)
	ExpiresAt   *gtime.Time `json:"expiresAt"   orm:"expires_at"   description:"密钥过期时间 (NULL 表示永不过期)"`                                                         // 密钥过期时间 (NULL 表示永不过期)
	LastUsedAt  *gtime.Time `json:"lastUsedAt"  orm:"last_used_at" description:"最后使用时间 (用于审计和判断活跃度)"`                                                          // 最后使用时间 (用于审计和判断活跃度)
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`                                                                         // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:"最后更新时间"`                                                                       // 最后更新时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"   description:"软删除时间"`                                                                        // 软删除时间
	RateLimit   int         `json:"rateLimit"   orm:"rate_limit"   description:"API调用频率限制(每分钟)"`                                                               // API调用频率限制(每分钟)
	DailyLimit  int         `json:"dailyLimit"  orm:"daily_limit"  description:"每日调用限制"`                                                                       // 每日调用限制
}
