package service

import (
	"context"
	"encoding/json"
	"strconv"

	"admin-api/internal/dao"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// ITelegramAdminLog defines the interface for telegram admin operation log service
type ITelegramAdminLog interface {
	// 记录管理员操作日志
	LogOperation(ctx context.Context, req *LogOperationRequest) error

	// 记录余额调整操作
	LogBalanceAdjustment(ctx context.Context, req *BalanceAdjustmentLogRequest) error

	// 记录流水调整操作
	LogFlowAdjustment(ctx context.Context, req *FlowAdjustmentLogRequest) error

	// 记录提现审核操作
	LogWithdrawalOperation(ctx context.Context, req *WithdrawalOperationLogRequest) error

	// 记录提现操作日志 (便捷方法)
	RecordWithdrawalOperationLog(ctx context.Context, withdrawal *entity.UserWithdraws, action, reason string, status int, errorMessage string)

	// 记录余额调整日志 (便捷方法)
	RecordBalanceAdjustmentLog(ctx context.Context, user *entity.Users, amount string, description string, isIncrease bool, isBonus bool)
}

// 操作类型常量
const (
	OperationTypeBalance    = "balance"    // 余额操作
	OperationTypeBonus      = "bonus"      // 彩金操作
	OperationTypeFlow       = "flow"       // 流水操作
	OperationTypeWithdrawal = "withdrawal" // 提现操作
	OperationTypeUser       = "user"       // 用户操作
	OperationTypeSystem     = "system"     // 系统操作
)

// 操作动作常量
const (
	ActionBalanceIncrease = "balance_increase" // 余额增加
	ActionBalanceDecrease = "balance_decrease" // 余额减少
	ActionBonusAdd        = "bonus_add"        // 彩金增加
	ActionBonusDeduct     = "bonus_deduct"     // 彩金扣除
	ActionFlowIncrease    = "flow_increase"    // 流水增加
	ActionFlowDecrease    = "flow_decrease"    // 流水减少
	ActionWithdrawApprove = "withdraw_approve" // 提现通过
	ActionWithdrawReject  = "withdraw_reject"  // 提现拒绝
	ActionWithdrawBan     = "withdraw_ban"     // 提现拒绝并封号
	ActionUserBan         = "user_ban"         // 用户封号
	ActionUserUnban       = "user_unban"       // 用户解封
)

// LogOperationRequest 通用操作日志请求
type LogOperationRequest struct {
	TenantId         uint                   `json:"tenantId"`         // 租户ID (必填)
	AdminTelegramId  int64                  `json:"adminTelegramId"`  // 管理员Telegram ID
	AdminName        string                 `json:"adminName"`        // 管理员名称
	AdminUsername    string                 `json:"adminUsername"`    // 管理员用户名
	OperationType    string                 `json:"operationType"`    // 操作类型
	OperationAction  string                 `json:"operationAction"`  // 具体操作动作
	TargetUserId     uint64                 `json:"targetUserId"`     // 目标用户ID
	TargetTelegramId int64                  `json:"targetTelegramId"` // 目标用户Telegram ID
	TargetUsername   string                 `json:"targetUsername"`   // 目标用户名
	OperationData    map[string]interface{} `json:"operationData"`    // 操作数据
	BeforeValue      string                 `json:"beforeValue"`      // 操作前的值
	AfterValue       string                 `json:"afterValue"`       // 操作后的值
	Amount           string                 `json:"amount"`           // 涉及金额
	Currency         string                 `json:"currency"`         // 币种
	Description      string                 `json:"description"`      // 操作描述
	IpAddress        string                 `json:"ipAddress"`        // IP地址
	UserAgent        string                 `json:"userAgent"`        // 用户代理
	Status           int                    `json:"status"`           // 操作状态(1-成功 0-失败)
	ErrorMessage     string                 `json:"errorMessage"`     // 错误信息
	Duration         int                    `json:"duration"`         // 操作耗时(毫秒)
}

// BalanceAdjustmentLogRequest 余额调整日志请求
type BalanceAdjustmentLogRequest struct {
	TenantId         uint   `json:"tenantId"`         // 租户ID (必填)
	AdminTelegramId  int64  `json:"adminTelegramId"`  // 管理员Telegram ID
	AdminName        string `json:"adminName"`        // 管理员名称
	AdminUsername    string `json:"adminUsername"`    // 管理员用户名
	TargetUserId     uint64 `json:"targetUserId"`     // 目标用户ID
	TargetTelegramId int64  `json:"targetTelegramId"` // 目标用户Telegram ID
	TargetUsername   string `json:"targetUsername"`   // 目标用户名
	BeforeBalance    string `json:"beforeBalance"`    // 调整前余额
	AfterBalance     string `json:"afterBalance"`     // 调整后余额
	Amount           string `json:"amount"`           // 调整金额
	Currency         string `json:"currency"`         // 币种
	Description      string `json:"description"`      // 操作描述
	IsIncrease       bool   `json:"isIncrease"`       // 是否为增加
	IpAddress        string `json:"ipAddress"`        // IP地址
	UserAgent        string `json:"userAgent"`        // 用户代理
	Status           int    `json:"status"`           // 操作状态
	ErrorMessage     string `json:"errorMessage"`     // 错误信息
	Duration         int    `json:"duration"`         // 操作耗时
}

// FlowAdjustmentLogRequest 流水调整日志请求
type FlowAdjustmentLogRequest struct {
	TenantId         uint   `json:"tenantId"`         // 租户ID (必填)
	AdminTelegramId  int64  `json:"adminTelegramId"`  // 管理员Telegram ID
	AdminName        string `json:"adminName"`        // 管理员名称
	AdminUsername    string `json:"adminUsername"`    // 管理员用户名
	TargetUserId     uint64 `json:"targetUserId"`     // 目标用户ID
	TargetTelegramId int64  `json:"targetTelegramId"` // 目标用户Telegram ID
	TargetUsername   string `json:"targetUsername"`   // 目标用户名
	BeforeFlow       string `json:"beforeFlow"`       // 调整前流水
	AfterFlow        string `json:"afterFlow"`        // 调整后流水
	Amount           string `json:"amount"`           // 调整金额
	Description      string `json:"description"`      // 操作描述
	IsIncrease       bool   `json:"isIncrease"`       // 是否为增加
	IpAddress        string `json:"ipAddress"`        // IP地址
	UserAgent        string `json:"userAgent"`        // 用户代理
	Status           int    `json:"status"`           // 操作状态
	ErrorMessage     string `json:"errorMessage"`     // 错误信息
	Duration         int    `json:"duration"`         // 操作耗时
}

// WithdrawalOperationLogRequest 提现操作日志请求
type WithdrawalOperationLogRequest struct {
	TenantId         uint   `json:"tenantId"`         // 租户ID (必填)
	AdminTelegramId  int64  `json:"adminTelegramId"`  // 管理员Telegram ID
	AdminName        string `json:"adminName"`        // 管理员名称
	AdminUsername    string `json:"adminUsername"`    // 管理员用户名
	TargetUserId     uint64 `json:"targetUserId"`     // 目标用户ID
	TargetTelegramId int64  `json:"targetTelegramId"` // 目标用户Telegram ID
	TargetUsername   string `json:"targetUsername"`   // 目标用户名
	WithdrawalId     uint64 `json:"withdrawalId"`     // 提现ID
	Amount           string `json:"amount"`           // 提现金额
	Currency         string `json:"currency"`         // 币种
	Action           string `json:"action"`           // 操作动作(approve/reject/ban)
	Reason           string `json:"reason"`           // 操作原因
	IpAddress        string `json:"ipAddress"`        // IP地址
	UserAgent        string `json:"userAgent"`        // 用户代理
	Status           int    `json:"status"`           // 操作状态
	ErrorMessage     string `json:"errorMessage"`     // 错误信息
	Duration         int    `json:"duration"`         // 操作耗时
}

// GetOperationLogsRequest 查询操作日志请求
type GetOperationLogsRequest struct {
	AdminTelegramId  int64  `json:"adminTelegramId"`  // 管理员Telegram ID (可选)
	OperationType    string `json:"operationType"`    // 操作类型 (可选)
	OperationAction  string `json:"operationAction"`  // 操作动作 (可选)
	TargetUserId     uint64 `json:"targetUserId"`     // 目标用户ID (可选)
	TargetTelegramId int64  `json:"targetTelegramId"` // 目标用户Telegram ID (可选)
	StartTime        string `json:"startTime"`        // 开始时间 (可选)
	EndTime          string `json:"endTime"`          // 结束时间 (可选)
	Page             int    `json:"page"`             // 页码
	PageSize         int    `json:"pageSize"`         // 每页大小
}

// GetOperationLogsResponse 查询操作日志响应
type GetOperationLogsResponse struct {
	Total int                                 `json:"total"` // 总数
	List  []*entity.TelegramAdminOperationLog `json:"list"`  // 日志列表
}

// AdminOperationStats 管理员操作统计
type AdminOperationStats struct {
	TotalOperations     int64             `json:"totalOperations"`     // 总操作数
	SuccessOperations   int64             `json:"successOperations"`   // 成功操作数
	FailedOperations    int64             `json:"failedOperations"`    // 失败操作数
	OperationsByType    map[string]int64  `json:"operationsByType"`    // 按类型统计
	OperationsByAction  map[string]int64  `json:"operationsByAction"`  // 按动作统计
	TotalAmount         map[string]string `json:"totalAmount"`         // 按币种统计总金额
	AverageResponseTime int64             `json:"averageResponseTime"` // 平均响应时间(毫秒)
}

type telegramAdminLogService struct{}

// LogOperation 记录管理员操作日志
func (s *telegramAdminLogService) LogOperation(ctx context.Context, req *LogOperationRequest) error {
	// 转换操作数据为JSON
	var operationDataJson *gjson.Json
	if req.OperationData != nil {
		dataBytes, err := json.Marshal(req.OperationData)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to marshal operation data: %v", err)
		} else {
			operationDataJson = gjson.New(dataBytes)
		}
	}

	// 创建日志记录
	logData := &entity.TelegramAdminOperationLog{
		TenantId:         req.TenantId,
		AdminTelegramId:  req.AdminTelegramId,
		AdminName:        req.AdminName,
		AdminUsername:    req.AdminUsername,
		OperationType:    req.OperationType,
		OperationAction:  req.OperationAction,
		TargetUserId:     req.TargetUserId,
		TargetTelegramId: req.TargetTelegramId,
		TargetUsername:   req.TargetUsername,
		OperationData:    operationDataJson,
		BeforeValue:      req.BeforeValue,
		AfterValue:       req.AfterValue,
		Amount:           req.Amount,
		Currency:         req.Currency,
		Description:      req.Description,
		IpAddress:        req.IpAddress,
		UserAgent:        req.UserAgent,
		Status:           req.Status,
		ErrorMessage:     req.ErrorMessage,
		Duration:         req.Duration,
		CreatedAt:        gtime.Now(),
		UpdatedAt:        gtime.Now(),
	}

	// 插入数据库
	_, err := dao.TelegramAdminOperationLog.Ctx(ctx).Data(logData).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to insert telegram admin operation log: %v", err)
		return gerror.Wrap(err, "记录管理员操作日志失败")
	}

	g.Log().Infof(ctx, "Telegram admin operation logged: admin=%d, type=%s, action=%s, target=%d",
		req.AdminTelegramId, req.OperationType, req.OperationAction, req.TargetUserId)

	return nil
}

// LogBalanceAdjustment 记录余额调整操作
func (s *telegramAdminLogService) LogBalanceAdjustment(ctx context.Context, req *BalanceAdjustmentLogRequest) error {
	action := ActionBalanceIncrease
	if !req.IsIncrease {
		action = ActionBalanceDecrease
	}

	operationData := map[string]interface{}{
		"before_balance":    req.BeforeBalance,
		"after_balance":     req.AfterBalance,
		"adjustment_amount": req.Amount,
		"is_increase":       req.IsIncrease,
	}

	logReq := &LogOperationRequest{
		TenantId:         req.TenantId,
		AdminTelegramId:  req.AdminTelegramId,
		AdminName:        req.AdminName,
		AdminUsername:    req.AdminUsername,
		OperationType:    OperationTypeBalance,
		OperationAction:  action,
		TargetUserId:     req.TargetUserId,
		TargetTelegramId: req.TargetTelegramId,
		TargetUsername:   req.TargetUsername,
		OperationData:    operationData,
		BeforeValue:      req.BeforeBalance,
		AfterValue:       req.AfterBalance,
		Amount:           req.Amount,
		Currency:         req.Currency,
		Description:      req.Description,
		IpAddress:        req.IpAddress,
		UserAgent:        req.UserAgent,
		Status:           req.Status,
		ErrorMessage:     req.ErrorMessage,
		Duration:         req.Duration,
	}

	return s.LogOperation(ctx, logReq)
}

// LogFlowAdjustment 记录流水调整操作
func (s *telegramAdminLogService) LogFlowAdjustment(ctx context.Context, req *FlowAdjustmentLogRequest) error {
	action := ActionFlowIncrease
	if !req.IsIncrease {
		action = ActionFlowDecrease
	}

	operationData := map[string]interface{}{
		"before_flow":       req.BeforeFlow,
		"after_flow":        req.AfterFlow,
		"adjustment_amount": req.Amount,
		"is_increase":       req.IsIncrease,
	}

	logReq := &LogOperationRequest{
		TenantId:         req.TenantId,
		AdminTelegramId:  req.AdminTelegramId,
		AdminName:        req.AdminName,
		AdminUsername:    req.AdminUsername,
		OperationType:    OperationTypeFlow,
		OperationAction:  action,
		TargetUserId:     req.TargetUserId,
		TargetTelegramId: req.TargetTelegramId,
		TargetUsername:   req.TargetUsername,
		OperationData:    operationData,
		BeforeValue:      req.BeforeFlow,
		AfterValue:       req.AfterFlow,
		Amount:           req.Amount,
		Currency:         "CNY", // 流水通常以CNY计算
		Description:      req.Description,
		IpAddress:        req.IpAddress,
		UserAgent:        req.UserAgent,
		Status:           req.Status,
		ErrorMessage:     req.ErrorMessage,
		Duration:         req.Duration,
	}

	return s.LogOperation(ctx, logReq)
}

// LogWithdrawalOperation 记录提现操作
func (s *telegramAdminLogService) LogWithdrawalOperation(ctx context.Context, req *WithdrawalOperationLogRequest) error {
	var action string
	switch req.Action {
	case "approve":
		action = ActionWithdrawApprove
	case "reject":
		action = ActionWithdrawReject
	case "ban":
		action = ActionWithdrawBan
	default:
		action = req.Action
	}

	operationData := map[string]interface{}{
		"withdrawal_id": req.WithdrawalId,
		"action":        req.Action,
		"reason":        req.Reason,
	}

	logReq := &LogOperationRequest{
		TenantId:         req.TenantId,
		AdminTelegramId:  req.AdminTelegramId,
		AdminName:        req.AdminName,
		AdminUsername:    req.AdminUsername,
		OperationType:    OperationTypeWithdrawal,
		OperationAction:  action,
		TargetUserId:     req.TargetUserId,
		TargetTelegramId: req.TargetTelegramId,
		TargetUsername:   req.TargetUsername,
		OperationData:    operationData,
		BeforeValue:      "",
		AfterValue:       "",
		Amount:           req.Amount,
		Currency:         req.Currency,
		Description:      req.Reason,
		IpAddress:        req.IpAddress,
		UserAgent:        req.UserAgent,
		Status:           req.Status,
		ErrorMessage:     req.ErrorMessage,
		Duration:         req.Duration,
	}

	return s.LogOperation(ctx, logReq)
}

// RecordWithdrawalOperationLog 记录提现操作日志
func (s *telegramAdminLogService) RecordWithdrawalOperationLog(ctx context.Context, withdrawal *entity.UserWithdraws, action, reason string, status int, errorMessage string) {
	// Create a new context to avoid context cancellation issues
	logCtx := context.Background()

	// Get admin information from current user context
	adminTelegramId := int64(0)
	adminName := "System"
	adminUsername := ""

	// Get admin info from request context (since Update().GetTgUser doesn't exist)
	if r := g.RequestFromCtx(ctx); r != nil {
		username := r.GetCtxVar("username").String()
		if username != "" {
			adminName = username
			// For now, we'll use the username as the admin name
			// In a real implementation, you might want to get more details from the user service
		}
	}

	// Get target user information
	var targetUser *entity.Users
	if err := dao.Users.Ctx(logCtx).Where("id = ?", withdrawal.UserId).Scan(&targetUser); err == nil && targetUser != nil {
		targetTelegramId, _ := strconv.ParseInt(targetUser.Account, 10, 64)
		targetUsername := ""

		// Try to get target user's username from backup accounts
		if backupAccount, err := dao.UserBackupAccounts.Ctx(logCtx).
			Where("user_id = ? AND is_master = ?", targetUser.Id, 1).
			One(); err == nil && !backupAccount.IsEmpty() {
			if username := backupAccount["username"].String(); username != "" {
				targetUsername = "@" + username
			}
		}

		// Get currency symbol
		currency := "CNY" // default
		if token, err := dao.Tokens.Ctx(logCtx).Where("token_id = ?", withdrawal.TokenId).One(); err == nil && !token.IsEmpty() {
			currency = token["symbol"].String()
		}

		// Record the log
		logReq := &WithdrawalOperationLogRequest{
			TenantId:         uint(targetUser.TenantId),
			AdminTelegramId:  adminTelegramId,
			AdminName:        adminName,
			AdminUsername:    adminUsername,
			TargetUserId:     targetUser.Id,
			TargetTelegramId: targetTelegramId,
			TargetUsername:   targetUsername,
			WithdrawalId:     uint64(withdrawal.UserWithdrawsId),
			Amount:           withdrawal.Amount.String(),
			Currency:         currency,
			Action:           action,
			Reason:           reason,
			IpAddress:        "", // TODO: Get from context if available
			UserAgent:        "", // TODO: Get from context if available
			Status:           status,
			ErrorMessage:     errorMessage,
			Duration:         0, // TODO: Calculate if needed
		}

		if err := s.LogWithdrawalOperation(logCtx, logReq); err != nil {
			g.Log().Errorf(logCtx, "Failed to record withdrawal operation log: %v", err)
		}
	}
}

// RecordBalanceAdjustmentLog 记录余额调整日志
func (s *telegramAdminLogService) RecordBalanceAdjustmentLog(ctx context.Context, user *entity.Users, amount string, description string, isIncrease bool, isBonus bool) {
	// Create a new context to avoid context cancellation issues
	logCtx := context.Background()

	// Get admin information from current user context
	adminTelegramId := int64(0)
	adminName := "System"
	adminUsername := ""

	// Get admin info from request context
	if r := g.RequestFromCtx(ctx); r != nil {
		username := r.GetCtxVar("username").String()
		if username != "" {
			adminName = username
		}
	}

	// Get target user information
	targetTelegramId, _ := strconv.ParseInt(user.Account, 10, 64)
	targetUsername := ""

	// Try to get target user's username from backup accounts
	if backupAccount, err := dao.UserBackupAccounts.Ctx(logCtx).
		Where("user_id = ? AND is_master = ?", user.Id, 1).
		One(); err == nil && !backupAccount.IsEmpty() {
		if username := backupAccount["username"].String(); username != "" {
			targetUsername = "@" + username
		}
	}

	// Get current balance (this is approximate since it's after the operation)
	beforeBalance := "0"
	afterBalance := "0"
	// Note: wallet.Manager() might not be available here, so we'll use approximate values
	// In a real implementation, you should get the actual balance from the wallet service

	// Determine operation type and action based on bonus flag
	var operationType, operationAction string
	if isBonus {
		operationType = "bonus"
		if isIncrease {
			operationAction = "bonus_add"
		} else {
			operationAction = "bonus_deduct"
		}
	} else {
		operationType = "balance"
		if isIncrease {
			operationAction = "balance_increase"
		} else {
			operationAction = "balance_decrease"
		}
	}

	// Create operation data with bonus information
	operationData := map[string]interface{}{
		"before_balance":    beforeBalance,
		"after_balance":     afterBalance,
		"adjustment_amount": amount,
		"is_increase":       isIncrease,
		"is_bonus":          isBonus,
	}

	// Add bonus-specific data
	if isBonus {
		// Calculate bonus flow requirement (amount * 6)
		if amountDecimal, err := decimal.NewFromString(amount); err == nil {
			bonusFlow := amountDecimal.Mul(decimal.NewFromInt(6))
			operationData["bonus_flow_added"] = bonusFlow.String()
		}
	}

	// Record the log using general LogOperation method
	logReq := &LogOperationRequest{
		TenantId:         uint(user.TenantId),
		AdminTelegramId:  adminTelegramId,
		AdminName:        adminName,
		AdminUsername:    adminUsername,
		OperationType:    operationType,
		OperationAction:  operationAction,
		TargetUserId:     user.Id,
		TargetTelegramId: targetTelegramId,
		TargetUsername:   targetUsername,
		OperationData:    operationData,
		BeforeValue:      beforeBalance,
		AfterValue:       afterBalance,
		Amount:           amount,
		Currency:         "CNY",
		Description:      description,
		IpAddress:        "", // TODO: Get from context if available
		UserAgent:        "", // TODO: Get from context if available
		Status:           1,  // Success
		ErrorMessage:     "",
		Duration:         0, // TODO: Calculate if needed
	}

	if err := s.LogOperation(logCtx, logReq); err != nil {
		g.Log().Errorf(logCtx, "Failed to record balance adjustment log: %v", err)
	}
}

// TelegramAdminLog returns the telegram admin log service instance
func TelegramAdminLog() ITelegramAdminLog {
	return &telegramAdminLogService{}
}
