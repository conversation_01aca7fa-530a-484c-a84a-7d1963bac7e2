package service

import (
	"admin-api/internal/casdoor"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type IAgentCasdoor interface {
	// SyncAddAgent 同步添加代理到 Casdoor
	SyncAddAgent(ctx context.Context, agent *entity.Agents, password string) error
	// SyncUpdateAgent 同步更新代理信息到 Casdoor
	SyncUpdateAgent(ctx context.Context, agent *entity.Agents) error
	// SyncUpdatePassword 同步更新密码到 Casdoor
	SyncUpdatePassword(ctx context.Context, username, newPassword string) error
	// SyncUpdateIPWhitelist 同步更新IP白名单到 Casdoor
	SyncUpdateIPWhitelist(ctx context.Context, username string, ipWhitelist string) error
	// SyncDeleteAgent 同步删除（禁用）代理
	SyncDeleteAgent(ctx context.Context, username string) error
	// DeleteMFA 调用 Casdoor 的 delete-mfa API 端点，删除代理的 MFA 设置
	DeleteMFA(ctx context.Context, username string) error
	// BatchSyncAgents 批量同步代理（用于数据迁移）
	BatchSyncAgents(ctx context.Context, agents []*entity.Agents) (successCount int, failCount int, err error)
	// CheckConsistency 检查数据一致性
	CheckConsistency(ctx context.Context, localAgents []*entity.Agents) (inconsistencies []string, err error)
	// HealthCheck 健康检查
	HealthCheck(ctx context.Context) error
}

type agentCasdoorImpl struct {
	syncService *casdoor.AgentSyncService
}

var localAgentCasdoor IAgentCasdoor

// AgentCasdoor 获取代理 Casdoor 服务实例
func AgentCasdoor() IAgentCasdoor {
	if localAgentCasdoor == nil {
		panic("implement not found for interface IAgentCasdoor, forgot register?")
	}
	return localAgentCasdoor
}

// RegisterAgentCasdoor 注册代理 Casdoor 服务实例
func RegisterAgentCasdoor(i IAgentCasdoor) {
	localAgentCasdoor = i
}

// 实现接口方法

func (s *agentCasdoorImpl) SyncAddAgent(ctx context.Context, agent *entity.Agents, password string) error {
	return s.syncService.SyncAddAgent(ctx, agent, password)
}

func (s *agentCasdoorImpl) SyncUpdateAgent(ctx context.Context, agent *entity.Agents) error {
	return s.syncService.SyncUpdateAgent(ctx, agent)
}

func (s *agentCasdoorImpl) SyncUpdatePassword(ctx context.Context, username, newPassword string) error {
	return s.syncService.SyncUpdatePassword(ctx, username, newPassword)
}

func (s *agentCasdoorImpl) SyncUpdateIPWhitelist(ctx context.Context, username string, ipWhitelist string) error {
	return s.syncService.SyncUpdateIPWhitelist(ctx, username, ipWhitelist)
}

func (s *agentCasdoorImpl) SyncDeleteAgent(ctx context.Context, username string) error {
	return s.syncService.SyncDeleteAgent(ctx, username)
}

func (s *agentCasdoorImpl) DeleteMFA(ctx context.Context, username string) error {
	return s.syncService.DeleteMFA(ctx, username)
}

func (s *agentCasdoorImpl) BatchSyncAgents(ctx context.Context, agents []*entity.Agents) (int, int, error) {
	return s.syncService.BatchSyncAgents(ctx, agents)
}

func (s *agentCasdoorImpl) CheckConsistency(ctx context.Context, localAgents []*entity.Agents) ([]string, error) {
	return s.syncService.CheckConsistency(ctx, localAgents)
}

func (s *agentCasdoorImpl) HealthCheck(ctx context.Context) error {
	return s.syncService.HealthCheck(ctx)
}

// InitAgentCasdoorService 初始化代理 Casdoor 服务
func InitAgentCasdoorService(ctx context.Context) error {
	// 检查配置是否存在
	config := g.Cfg().MustGet(ctx, "agent_casdoor_server")
	if config.IsEmpty() {
		g.Log().Warning(ctx, "代理 Casdoor 配置不存在，跳过初始化")
		return nil
	}

	syncService, err := casdoor.NewAgentSyncService(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "初始化代理 Casdoor 同步服务失败: %v", err)
		return err
	}

	RegisterAgentCasdoor(&agentCasdoorImpl{
		syncService: syncService,
	})

	g.Log().Info(ctx, "代理 Casdoor 服务初始化成功")
	return nil
}