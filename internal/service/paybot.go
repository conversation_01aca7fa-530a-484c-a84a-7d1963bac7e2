package service

import (
	"admin-api/internal/service/paybot"
	"context"
)

// IPayBot defines the interface for PayBot service operations.
type IPayBot interface {
	// 授权支付相关
	CreateAuthPayment(ctx context.Context, req *paybot.CreateAuthPaymentRequest) (*paybot.AuthPaymentResult, error)
	QueryAuthPayment(ctx context.Context, req *paybot.QueryAuthPaymentRequest) (*paybot.AuthPaymentResult, error)
	ListAuthPayments(ctx context.Context, req *paybot.ListAuthPaymentsRequest) (*paybot.AuthPaymentListResult, error)
	GenerateTelegramLink(ctx context.Context, orderNo string) string

	// 充值相关
	GetDepositAddress(ctx context.Context, req *paybot.GetDepositAddressRequest) (*paybot.DepositAddressResult, error)
	QueryDepositRecords(ctx context.Context, req *paybot.QueryDepositsRequest) (*paybot.DepositListResult, error)
	GetDepositDetail(ctx context.Context, req *paybot.GetDepositDetailRequest) (*paybot.DepositDetailResult, error)
	ListDepositAddresses(ctx context.Context, req *paybot.ListDepositAddressesRequest) (*paybot.DepositAddressListResult, error)

	// 交易查询
	QueryTransactions(ctx context.Context, req *paybot.QueryTransactionsRequest) (*paybot.TransactionListResult, error)
	GetTransactionDetail(ctx context.Context, req *paybot.GetTransactionDetailRequest) (*paybot.TransactionDetailResult, error)

	// 提现相关
	QueryWithdrawals(ctx context.Context, req *paybot.QueryWithdrawalsRequest) (*paybot.WithdrawalListResult, error)
	GetWithdrawalDetail(ctx context.Context, req *paybot.GetWithdrawalDetailRequest) (*paybot.WithdrawalDetailResult, error)

	// 回调处理
	ProcessDepositCallback(ctx context.Context, req *paybot.DepositCallbackRequest) error
	ProcessWithdrawalCallback(ctx context.Context, req *paybot.WithdrawalCallbackRequest) error
	ProcessOkpayAuthCallback(ctx context.Context, userID int64, merchantOrderNo string) (*paybot.OkpayCallbackResult, error)

	// 健康检查
	HealthCheck(ctx context.Context) error

	// 配置检查
	IsSyncEnabled() bool
}

var localPayBot IPayBot

// RegisterPayBot registers the PayBot service implementation.
func RegisterPayBot(i IPayBot) {
	localPayBot = i
}

// PayBot returns the registered PayBot service instance.
func PayBot() IPayBot {
	if localPayBot == nil {
		panic("implement not found for interface IPayBot, forgot register?")
	}
	return localPayBot
}
