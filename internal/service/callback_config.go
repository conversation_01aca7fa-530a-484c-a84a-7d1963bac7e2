package service

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// CallbackConfig 回调配置结构
type CallbackConfig struct {
	Enabled          bool          `json:"enabled"`
	Timeout          time.Duration `json:"timeout"`
	MaxRetries       int           `json:"max_retries"`
	RetryIntervals   []time.Duration `json:"retry_intervals"`
	MaxConcurrent    int           `json:"max_concurrent"`
	LogRetentionDays int           `json:"log_retention_days"`
	ForceHTTPS       bool          `json:"force_https"`
	VerifySSL        bool          `json:"verify_ssl"`
	UserAgent        string        `json:"user_agent"`
}

// GetCallbackConfig 获取回调配置
func GetCallbackConfig() *CallbackConfig {
	// 默认配置
	config := &CallbackConfig{
		Enabled:          true,
		Timeout:          30 * time.Second,
		MaxRetries:       3,
		RetryIntervals:   []time.Duration{1 * time.Minute, 5 * time.Minute, 15 * time.Minute},
		MaxConcurrent:    10,
		LogRetentionDays: 30,
		ForceHTTPS:       false,
		VerifySSL:        true,
		UserAgent:        "X-Pay-Merchant-Callback/1.0",
	}

	// 从配置文件读取
	if g.Cfg().Available(context.Background()) {
		// 是否启用
		if enabled := g.Cfg().MustGet(nil, "callback.enabled"); !enabled.IsNil() {
			config.Enabled = enabled.Bool()
		}

		// 超时时间
		if timeout := g.Cfg().MustGet(nil, "callback.timeout"); !timeout.IsNil() {
			if duration, err := time.ParseDuration(timeout.String()); err == nil {
				config.Timeout = duration
			}
		}

		// 最大重试次数
		if maxRetries := g.Cfg().MustGet(nil, "callback.max_retries"); !maxRetries.IsNil() {
			config.MaxRetries = maxRetries.Int()
		}

		// 重试间隔
		if retryIntervals := g.Cfg().MustGet(nil, "callback.retry_intervals"); !retryIntervals.IsNil() {
			intervals := retryIntervals.Strings()
			config.RetryIntervals = make([]time.Duration, 0, len(intervals))
			for _, interval := range intervals {
				if duration, err := time.ParseDuration(interval); err == nil {
					config.RetryIntervals = append(config.RetryIntervals, duration)
				}
			}
		}

		// 最大并发数
		if maxConcurrent := g.Cfg().MustGet(nil, "callback.max_concurrent"); !maxConcurrent.IsNil() {
			config.MaxConcurrent = maxConcurrent.Int()
		}

		// 日志保留天数
		if logRetentionDays := g.Cfg().MustGet(nil, "callback.log_retention_days"); !logRetentionDays.IsNil() {
			config.LogRetentionDays = logRetentionDays.Int()
		}

		// 强制HTTPS
		if forceHTTPS := g.Cfg().MustGet(nil, "callback.force_https"); !forceHTTPS.IsNil() {
			config.ForceHTTPS = forceHTTPS.Bool()
		}

		// 验证SSL
		if verifySSL := g.Cfg().MustGet(nil, "callback.verify_ssl"); !verifySSL.IsNil() {
			config.VerifySSL = verifySSL.Bool()
		}

		// User-Agent
		if userAgent := g.Cfg().MustGet(nil, "callback.user_agent"); !userAgent.IsNil() {
			config.UserAgent = userAgent.String()
		}
	}

	return config
}

// IsCallbackEnabled 检查回调功能是否启用
func IsCallbackEnabled() bool {
	return GetCallbackConfig().Enabled
}

// GetCallbackTimeout 获取回调超时时间
func GetCallbackTimeout() time.Duration {
	return GetCallbackConfig().Timeout
}

// GetMaxRetries 获取最大重试次数
func GetMaxRetries() int {
	return GetCallbackConfig().MaxRetries
}

// GetRetryIntervals 获取重试间隔列表
func GetRetryIntervals() []time.Duration {
	return GetCallbackConfig().RetryIntervals
}

// ShouldForceHTTPS 是否强制使用HTTPS
func ShouldForceHTTPS() bool {
	return GetCallbackConfig().ForceHTTPS
}

// ShouldVerifySSL 是否验证SSL证书
func ShouldVerifySSL() bool {
	return GetCallbackConfig().VerifySSL
}

// GetUserAgent 获取User-Agent
func GetUserAgent() string {
	return GetCallbackConfig().UserAgent
}