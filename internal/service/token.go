package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"

	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
)

// IToken defines the interface for token service operations.
type IToken interface {
	// IsDepositAllowed checks if depositing a specific token on a given network is allowed.
	// It considers the token's allow_deposit, is_active, and status fields.
	IsDepositAllowed(ctx context.Context, symbol string, network string) (bool, error)
	// GetActiveWithdrawalSymbols returns a list of symbols that are active for withdrawal.
	GetActiveWithdrawalSymbols(ctx context.Context) ([]string, error)
	// GetTokenDetailsForWithdrawal returns token details for a specific symbol and chain.
	GetTokenDetailsForWithdrawal(ctx context.Context, symbol string, chain string) (*entity.Tokens, error)
	// GetTokensBySymbol returns all tokens with the given symbol that are active.
	GetTokensBySymbol(ctx context.Context, symbol string) ([]*entity.Tokens, error)
	// GetTokenInfo returns token information with withdrawal fee calculations.
	// It calculates the fee based on the token's fee type (fixed or percent).
	// Returns token info, withdrawal fee, min/max withdrawal amount.
	GetTokenInfo(ctx context.Context, symbol string, chain string) (*entity.Tokens, string, error)
	// GetActiveTransferSymbols returns a list of unique symbols that are active and allowed for transfer.
	GetActiveTransferSymbols(ctx context.Context) ([]string, error)
	// GetTokenBySymbol returns the first active token matching the given symbol.
	GetTokenBySymbol(ctx context.Context, symbol string) (*entity.Tokens, error)
	GetTokenByID(ctx context.Context, id uint) (*entity.Tokens, error)
	// GetActiveTransferTokens returns a list of tokens that are active and allowed for transfer.
	GetActiveTransferTokens(ctx context.Context) ([]*entity.Tokens, error)
	// GetActiveRedPacketTokens returns a list of tokens that are active and allowed for red packet.
	GetActiveRedPacketTokens(ctx context.Context) ([]*entity.Tokens, error)
	// GetActiveReceiveTokens returns a list of tokens that are active and allowed for receiving payments.
	GetActiveReceiveTokens(ctx context.Context) ([]*entity.Tokens, error)
	// GetToken retrieves a specific token by symbol and network.
	GetToken(ctx context.Context, symbol, network string) (*entity.Tokens, error)
	// ConvertBalanceToRaw 将用户余额转换为原始值（乘以10^decimals）
	ConvertBalanceToRaw(ctx context.Context, balance decimal.Decimal, symbol string) (decimal.Decimal, error)
	// ConvertRawToBalance 将原始值转换回用户余额（除以10^decimals）
	ConvertRawToBalance(ctx context.Context, rawBalance decimal.Decimal, symbol string) (decimal.Decimal, error)
	// FormatUserBalance 格式化用户余额并移除多余小数点和零
	FormatUserBalance(ctx context.Context, balance decimal.Decimal, symbol string) decimal.Decimal
	// FormatUserBalanceWithSymbol 格式化用户余额并添加代币符号
	FormatUserBalanceWithSymbol(ctx context.Context, balance decimal.Decimal, symbol string) string
}

type sToken struct{}

// NewToken creates and returns a new instance of the token service.
func NewToken() *sToken {
	return &sToken{}
}

func init() {
	RegisterToken(NewToken())
}

var localToken IToken

// RegisterToken registers the token service implementation.
func RegisterToken(i IToken) {
	localToken = i
}

// Token returns the registered token service instance.
func Token() IToken {
	if localToken == nil {
		panic("implement not found for interface IToken, forgot register?")
	}
	return localToken
}

// IsDepositAllowed checks if depositing a specific token on a given network is allowed.
func (s *sToken) IsDepositAllowed(ctx context.Context, symbol string, network string) (bool, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Network, network).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token %s on network %s: %v", symbol, network, err)
		// Consider returning a user-friendly error or just false
		return false, gerror.Wrapf(err, "查询代币信息失败 (token: %s, network: %s)", symbol, network)
	}

	if token == nil {
		g.Log().Warningf(ctx, "Token %s on network %s not found in database.", symbol, network)
		// Token not configured in the system
		return false, gerror.Newf("不支持的代币或网络 (token: %s, network: %s)", symbol, network)
	}

	// Check the conditions: must be active, status must be 1 (上架), and allow_deposit must be true (1)
	if token.IsActive == 1 && token.Status == 1 && token.AllowDeposit == 1 {
		g.Log().Debugf(ctx, "Deposit check for %s on %s: Allowed (IsActive: %d, Status: %d, AllowDeposit: %d)", symbol, network, token.IsActive, token.Status, token.AllowDeposit)
		return true, nil
	}

	g.Log().Warningf(ctx, "Deposit check for %s on %s: Denied (IsActive: %d, Status: %d, AllowDeposit: %d)", symbol, network, token.IsActive, token.Status, token.AllowDeposit)
	// Provide a generic reason or potentially use token.MaintenanceMessage if available and relevant
	// For now, return a generic "deposit disabled" error
	return false, gerror.Newf("该币种/网络暂停充值 (token: %s, network: %s)", symbol, network)
}

// GetActiveWithdrawalSymbols returns a list of symbols that are active for withdrawal.

func (s *sToken) GetActiveWithdrawalSymbols(ctx context.Context) ([]string, error) {
	// It's often better practice to initialize the slice to avoid returning nil on success with no results
	symbols := make([]string, 0)

	// 1. Use All() to fetch the result set.
	result, err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol). // Select only the symbol column
		Distinct().                          // Ensure symbols are unique
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().AllowWithdraw, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		All() // Fetch all matching rows

	// Check the error from the database query itself
	if err != nil {
		g.Log().Errorf(ctx, "Failed to query active withdrawal symbols: %+v", err)
		return nil, gerror.Wrap(err, "查询可提现币种失败")
	}

	// 2. Extract the array of values from the result's first column.
	// result.Array() returns a gdb.Value (which is often aliased or acts like *g.Var)
	// containing the slice data.
	if result != nil && !result.IsEmpty() { // Check if the result is not nil and not empty
		value := result.Array() // Gets the values of the first column as a gdb.Value / *g.Var

		// 3. Convert the gdb.Value/*g.Var to a []string slice.
		for _, v := range value {
			symbols = append(symbols, v.String())
		}

		// Note: Depending on the exact GF version, error handling on conversion might be needed,
		// but .Strings() often just returns an empty slice if conversion isn't possible.
		// If you needed integers, you'd use value.Ints(), etc.
	}

	// Log the actual symbols found for better debugging
	g.Log().Debugf(ctx, "Found %d active withdrawal symbols: %v", len(symbols), symbols)
	return symbols, nil
}

// GetActiveTransferSymbols returns a list of unique symbols that are active and allowed for transfer.
func (s *sToken) GetActiveTransferSymbols(ctx context.Context) ([]string, error) {
	symbols := make([]string, 0)

	result, err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol). // Select only the symbol column
		Distinct().                          // Ensure symbols are unique
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().AllowTransfer, 1). // Filter by allow_transfer
		Where(dao.Tokens.Columns().IsActive, 1).
		All() // Fetch all matching rows

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query active transfer symbols: %+v", err)
		return nil, gerror.Wrap(err, "查询可转账币种失败")
	}

	if result != nil && !result.IsEmpty() {
		value := result.Array()
		for _, v := range value {
			symbols = append(symbols, v.String())
		}
	}

	g.Log().Debugf(ctx, "Found %d active transfer symbols: %v", len(symbols), symbols)
	return symbols, nil
}

// GetTokenDetailsForWithdrawal returns token details for a specific symbol and chain.
func (s *sToken) GetTokenDetailsForWithdrawal(ctx context.Context, symbol string, chain string) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Network, chain).
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token details for withdrawal (symbol: %s, chain: %s): %v", symbol, chain, err)
		return nil, gerror.Wrapf(err, "查询代币详情失败 (symbol: %s, chain: %s)", symbol, chain)
	}

	if token == nil {
		g.Log().Warningf(ctx, "Token %s on chain %s not found or not active for withdrawal", symbol, chain)
		return nil, gerror.Newf("不支持的代币或网络 (symbol: %s, chain: %s)", symbol, chain)
	}

	// 检查是否允许提现
	if token.AllowWithdraw != 1 {
		g.Log().Warningf(ctx, "Token %s on chain %s is not allowed for withdrawal (AllowWithdraw: %d)", symbol, chain, token.AllowWithdraw)
		return nil, gerror.Newf("该币种/网络暂停提现 (symbol: %s, chain: %s)", symbol, chain)
	}

	g.Log().Debugf(ctx, "Found token details for withdrawal (symbol: %s, chain: %s)", symbol, chain)
	return token, nil
}

// GetTokensBySymbol returns all tokens with the given symbol that are active.
func (s *sToken) GetTokensBySymbol(ctx context.Context, symbol string) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Scan(&tokens)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query tokens by symbol %s: %v", symbol, err)
		return nil, gerror.Wrapf(err, "查询代币信息失败 (symbol: %s)", symbol)
	}

	if len(tokens) == 0 {
		g.Log().Warningf(ctx, "No active tokens found for symbol %s", symbol)
		return nil, gerror.Newf("未找到可用的代币 (symbol: %s)", symbol)
	}

	g.Log().Debugf(ctx, "Found %d tokens for symbol %s", len(tokens), symbol)
	return tokens, nil
}

// GetTokenInfo returns token information with withdrawal fee calculations.
// It calculates the fee based on the token's fee type (fixed or percent).
// Returns the token entity, calculated withdrawal fee, and any error.
func (s *sToken) GetTokenInfo(ctx context.Context, symbol string, chain string) (*entity.Tokens, string, error) {
	// 获取代币信息
	token, err := s.GetTokenDetailsForWithdrawal(ctx, symbol, chain)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get token details for withdrawal (symbol: %s, chain: %s): %v", symbol, chain, err)
		return nil, "", gerror.Wrapf(err, "获取代币信息失败 (symbol: %s, chain: %s)", symbol, chain)
	}

	// 计算手续费，根据不同的手续费类型
	var fee string
	if token.WithdrawalFeeType == "fixed" {
		// 固定金额手续费
		fee = token.WithdrawalFeeAmount
	} else if token.WithdrawalFeeType == "percent" {
		// 百分比手续费，这里不计算具体数值，因为需要实际提现金额才能算
		// 返回百分比值，由调用方根据提现金额计算
		fee = token.WithdrawalFeeAmount
		g.Log().Debugf(ctx, "Token %s on %s has percent withdrawal fee: %s%%", symbol, chain, fee)
	} else {
		// 未知手续费类型，返回原始值并记录警告
		fee = token.WithdrawalFeeAmount
		g.Log().Warningf(ctx, "Unknown withdrawal fee type for %s on %s: %s, using raw value: %s",
			symbol, chain, token.WithdrawalFeeType, token.WithdrawalFeeAmount)
	}

	g.Log().Debugf(ctx, "Retrieved token info for %s on %s with fee: %s (%s)",
		symbol, chain, fee, token.WithdrawalFeeType)
	return token, fee, nil
}

// GetTokenBySymbol returns the first active token matching the given symbol.
// It prioritizes active and status=1 tokens.
func (s *sToken) GetTokenBySymbol(ctx context.Context, symbol string) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Status, 1).   // Ensure token is listed
		Where(dao.Tokens.Columns().IsActive, 1). // Ensure token is active
		Scan(&token)                             // Get the first match

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token by symbol %s: %v", symbol, err)
		return nil, gerror.Wrapf(err, "查询代币信息失败 (symbol: %s)", symbol)
	}

	if token == nil {
		g.Log().Warningf(ctx, "No active token found for symbol %s", symbol)
		// Consider if a different error is needed if *any* token exists but isn't active/listed
		return nil, gerror.Newf("未找到可用的代币 (symbol: %s)", symbol) // Reusing existing error message
	}

	g.Log().Debugf(ctx, "Found active token for symbol %s: ID %d, LogoURL: %s", symbol, token.TokenId, token.LogoUrl)
	return token, nil
}

// GetTokenByID retrieves token details by its primary key ID.
func (s *sToken) GetTokenByID(ctx context.Context, id uint) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().TokenId, id).
		// Optionally add checks for Status=1 and IsActive=1 if needed for transfers
		// Where(dao.Tokens.Columns().Status, 1).
		// Where(dao.Tokens.Columns().IsActive, 1).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token by ID %d: %v", id, err)
		return nil, gerror.Wrapf(err, "查询代币信息失败 (ID: %d)", id)
	}

	if token == nil {
		g.Log().Warningf(ctx, "No token found for ID %d", id)
		return nil, gerror.Newf("未找到代币 (ID: %d)", id)
	}

	g.Log().Debugf(ctx, "Found token for ID %d: Symbol %s, Decimals %d", id, token.Symbol, token.Decimals)
	return token, nil
}

// GetActiveTransferTokens returns a list of tokens that are active and allowed for transfer.
func (s *sToken) GetActiveTransferTokens(ctx context.Context) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol, "MAX(`order`) as `order`"). // Use MAX aggregate function for order
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Where(dao.Tokens.Columns().AllowTransfer, 1).
		Group(dao.Tokens.Columns().Symbol).
		OrderDesc("order"). // Order by the aggregated order column
		Scan(&tokens)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to get active transfer tokens: %v", err)
		return nil, gerror.Wrap(err, "查询可用转账代币失败")
	}

	return tokens, nil
}

// GetActiveRedPacketTokens returns a list of tokens that are active and allowed for red packet.
func (s *sToken) GetActiveRedPacketTokens(ctx context.Context) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol, "MAX(`order`) as `order`"). // Use MAX aggregate function for order
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Where(dao.Tokens.Columns().AllowRedPacket, 1).
		Group(dao.Tokens.Columns().Symbol).
		OrderDesc("order"). // Order by the aggregated order column
		Scan(&tokens)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get active red packet tokens: %v", err)
		// Wrap the error with a user-friendly message
		return nil, gerror.Wrap(err, "查询可用红包代币失败")
	}

	// Log the number of tokens found for debugging purposes
	g.Log().Debugf(ctx, "Found %d active red packet tokens", len(tokens))
	return tokens, nil
}

// GetActiveReceiveTokens returns a list of tokens that are active and allowed for receiving payments.
func (s *sToken) GetActiveReceiveTokens(ctx context.Context) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol, "MAX(`order`) as `order`"). // Use MAX aggregate function for order
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Where(dao.Tokens.Columns().AllowReceive, 1). // Filter by allow_receive
		Group(dao.Tokens.Columns().Symbol).
		OrderDesc("order"). // Order by the aggregated order column
		Scan(&tokens)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to get active receive tokens: %v", err)
		return nil, gerror.Wrap(err, "查询可用收款代币失败")
	}

	g.Log().Debugf(ctx, "Found %d active receive tokens", len(tokens))
	return tokens, nil
}

// GetToken retrieves a specific token by symbol and network.
// It returns the token entity or an error if not found or on database error.
func (s *sToken) GetToken(ctx context.Context, symbol, network string) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Network, network).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token by symbol '%s' and network '%s': %v", symbol, network, err)
		// Wrap the error for better context, but don't expose internal details directly unless needed.
		return nil, gerror.Wrapf(err, "查询代币信息时出错 (symbol: %s, network: %s)", symbol, network)
	}

	if token == nil {
		g.Log().Warningf(ctx, "Token not found for symbol '%s' and network '%s'", symbol, network)
		// Use CodeNotFound for semantic clarity when the token specifically doesn't exist.
		return nil, gerror.NewCodef(gcode.CodeNotFound, "未找到指定的代币/网络组合 (symbol: %s, network: %s)", symbol, network)
	}

	// Optionally, you might want to check IsActive or Status here depending on the use case.
	// For deposit info, we probably want the token details even if it's currently inactive for deposit,
	// as the calling function (getDepositInfoText) might handle the display logic based on AllowDeposit.
	// If this function should *only* return active/usable tokens, add those checks here.
	// Example:
	// if token.IsActive != 1 || token.Status != 1 {
	//     g.Log().Warningf(ctx, "Token found but is inactive/delisted (symbol: %s, network: %s, IsActive: %d, Status: %d)", symbol, network, token.IsActive, token.Status)
	//     return nil, gerror.NewCodef(gcode.CodeNotFound, "指定的代币当前不可用 (symbol: %s, network: %s)", symbol, network)
	// }

	g.Log().Debugf(ctx, "Successfully retrieved token for symbol '%s' and network '%s': ID %d", symbol, network, token.TokenId)
	return token, nil
}

// ConvertBalanceToRaw 将用户余额转换为原始值（乘以10^decimals）
func (s *sToken) ConvertBalanceToRaw(ctx context.Context, balance decimal.Decimal, symbol string) (decimal.Decimal, error) {
	// 获取代币信息以获取小数位数
	tokenInfo, err := s.GetTokenBySymbol(ctx, symbol) // Changed Token() to s
	if err != nil {
		g.Log().Errorf(ctx, "获取代币信息失败 (Symbol: %s): %v", symbol, err)
		return decimal.Zero, gerror.Wrapf(err, "获取代币信息失败 (Symbol: %s)", symbol)
	}
	if tokenInfo == nil {
		g.Log().Errorf(ctx, "未找到代币信息 (Symbol: %s)", symbol)
		return decimal.Zero, gerror.Newf("未找到代币信息 (Symbol: %s)", symbol)
	}

	// 计算 10^decimals
	multiplier := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenInfo.Decimals)))

	// 乘以 10^decimals
	rawBalance := balance.Mul(multiplier)

	g.Log().Infof(ctx, "余额转换: %s %s => %s 原始值 (乘以10^%d)",
		balance.String(), symbol, rawBalance.String(), tokenInfo.Decimals)

	return rawBalance, nil
}

// ConvertRawToBalance 将原始值转换回用户余额（除以10^decimals）
func (s *sToken) ConvertRawToBalance(ctx context.Context, rawBalance decimal.Decimal, symbol string) (decimal.Decimal, error) {
	// 获取代币信息以获取小数位数
	tokenInfo, err := s.GetTokenBySymbol(ctx, symbol) // Changed Token() to s
	if err != nil {
		g.Log().Debug(ctx, "获取代币信息失败 (Symbol: %s): %v", symbol, err)
		return decimal.Zero, gerror.Wrapf(err, "获取代币信息失败 (Symbol: %s)", symbol)
	}
	if tokenInfo == nil {
		g.Log().Debug(ctx, "未找到代币信息 (Symbol: %s)", symbol)
		return decimal.Zero, gerror.Newf("未找到代币信息 (Symbol: %s)", symbol)
	}

	// 计算 10^decimals
	divisor := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenInfo.Decimals)))

	// 除以 10^decimals
	balance := rawBalance.Div(divisor)

	g.Log().Infof(ctx, "原始值转换: %s 原始值 => %s %s (除以10^%d)",
		rawBalance.String(), balance.String(), symbol, tokenInfo.Decimals)

	return balance, nil
}

// FormatUserBalance 格式化用户余额并移除多余小数点和零 12.3000 → 12.3，但保留整数部分的0
func (s *sToken) FormatUserBalance(ctx context.Context, balance decimal.Decimal, symbol string) decimal.Decimal {
	balanceStr := balance.String()

	// 检查是否包含小数点
	if strings.Contains(balanceStr, ".") {
		// 只处理小数部分的末尾0和多余的小数点
		parts := strings.Split(balanceStr, ".")
		integerPart := parts[0]
		decimalPart := parts[1]

		// 去除小数部分末尾的0
		decimalPart = strings.TrimRight(decimalPart, "0")

		// 如果小数部分为空，则不需要小数点
		if decimalPart == "" {
			balanceStr = integerPart
		} else {
			balanceStr = integerPart + "." + decimalPart
		}
	}

	g.Log().Debugf(ctx, "格式化余额: %s => %s %s ", balance.String(), balanceStr, symbol)
	// 转换回decimal.Decimal类型
	formattedDecimal, err := decimal.NewFromString(balanceStr)
	if err != nil {
		g.Log().Errorf(ctx, "转换格式化余额失败: %v", err)
		return balance // 如果转换失败，返回原始余额
	}
	return formattedDecimal
}

// FormatUserBalanceWithSymbol 格式化用户余额并添加代币符号 例如：12.3000 + BTC → 12.3 BTC
func (s *sToken) FormatUserBalanceWithSymbol(ctx context.Context, balance decimal.Decimal, symbol string) string {
	return fmt.Sprintf("%s %s", s.FormatUserBalance(ctx, balance, symbol), symbol) // Kept s.FormatUserBalance
}
