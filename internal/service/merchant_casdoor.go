package service

import (
	"admin-api/internal/casdoor"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type IMerchantCasdoor interface {
	// SyncAddMerchant 同步添加商户到 Casdoor，返回生成的 TOTP Secret 和恢复码
	SyncAddMerchant(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string, password string) (totpSecret string, recoveryCodes []string, err error)
	// SyncAddMerchantWithMFA 同步添加商户到 Casdoor，使用预生成的 TOTP Secret 和恢复码
	SyncAddMerchantWithMFA(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string, password string, totpSecret string, recoveryCodes []string) error
	// SyncUpdateMerchant 同步更新商户信息到 Casdoor
	SyncUpdateMerchant(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string) error
	// SyncUpdatePassword 同步更新密码到 Casdoor
	SyncUpdatePassword(ctx context.Context, userAccount string, newPassword string) error
	// SyncDeleteMerchant 同步删除（禁用）商户
	SyncDeleteMerchant(ctx context.Context, userAccount string) error
	// DeleteMFA 调用 Casdoor 的 delete-mfa API 端点，删除商户的 MFA 设置
	DeleteMFA(ctx context.Context, userAccount string) error
	// BatchSyncMerchants 批量同步商户（用于数据迁移）
	BatchSyncMerchants(ctx context.Context, merchants []*entity.Merchants) (successCount int, failCount int, err error)
	// HealthCheck 健康检查
	HealthCheck(ctx context.Context) error
}

type merchantCasdoorImpl struct {
	syncService *casdoor.MerchantSyncService
}

var localMerchantCasdoor IMerchantCasdoor

// MerchantCasdoor 获取商户 Casdoor 服务实例
func MerchantCasdoor() IMerchantCasdoor {
	if localMerchantCasdoor == nil {
		panic("implement not found for interface IMerchantCasdoor, forgot register?")
	}
	return localMerchantCasdoor
}

// RegisterMerchantCasdoor 注册商户 Casdoor 服务实例
func RegisterMerchantCasdoor(i IMerchantCasdoor) {
	localMerchantCasdoor = i
}

// 实现接口方法

func (s *merchantCasdoorImpl) SyncAddMerchant(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string, password string) (string, []string, error) {
	return s.syncService.SyncAddMerchant(ctx, merchant, userAccount, userEmail, password)
}

func (s *merchantCasdoorImpl) SyncAddMerchantWithMFA(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string, password string, totpSecret string, recoveryCodes []string) error {
	return s.syncService.SyncAddMerchantWithMFA(ctx, merchant, userAccount, userEmail, password, totpSecret, recoveryCodes)
}

func (s *merchantCasdoorImpl) SyncUpdateMerchant(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string) error {
	return s.syncService.SyncUpdateMerchant(ctx, merchant, userAccount, userEmail)
}

func (s *merchantCasdoorImpl) SyncUpdatePassword(ctx context.Context, userAccount string, newPassword string) error {
	return s.syncService.SyncUpdatePassword(ctx, userAccount, newPassword)
}

func (s *merchantCasdoorImpl) SyncDeleteMerchant(ctx context.Context, userAccount string) error {
	return s.syncService.SyncDeleteMerchant(ctx, userAccount)
}

func (s *merchantCasdoorImpl) DeleteMFA(ctx context.Context, userAccount string) error {
	return s.syncService.DeleteMFA(ctx, userAccount)
}

func (s *merchantCasdoorImpl) BatchSyncMerchants(ctx context.Context, merchants []*entity.Merchants) (int, int, error) {
	return s.syncService.BatchSyncMerchants(ctx, merchants)
}

func (s *merchantCasdoorImpl) HealthCheck(ctx context.Context) error {
	return s.syncService.HealthCheck(ctx)
}

// InitMerchantCasdoorService 初始化商户 Casdoor 服务
func InitMerchantCasdoorService(ctx context.Context) error {
	// 检查配置是否存在
	config := g.Cfg().MustGet(ctx, "merchant_casdoor_server")
	if config.IsEmpty() {
		g.Log().Warning(ctx, "商户 Casdoor 配置不存在，跳过初始化")
		return nil
	}

	syncService, err := casdoor.NewMerchantSyncService(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "初始化商户 Casdoor 同步服务失败: %v", err)
		return err
	}

	RegisterMerchantCasdoor(&merchantCasdoorImpl{
		syncService: syncService,
	})

	g.Log().Info(ctx, "商户 Casdoor 服务初始化成功")
	return nil
}
