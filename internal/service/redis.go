package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/a19ba14d/tg-bot-common/consts"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gredis" // 导入 gredis
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv" // 添加 gconv 导入
	// "github.com/gogf/gf/v2/os/gcache" // 移除 gcache 导入
)

// redisService 封装 Redis 相关操作
type redisService struct {
	// 缓存客户端实例
	clientCache map[string]*gredis.Redis
	// 缓存互斥锁
	cacheMutex sync.RWMutex
}

var insRedis = redisService{
	clientCache: make(map[string]*gredis.Redis),
}

// Redis 返回 Redis 服务单例
func Redis() *redisService {
	return &insRedis
}

// Client 获取默认 Redis 客户端实例
func (s *redisService) Client(name ...string) *gredis.Redis {
	// 如果配置文件中定义了多个 redis 实例，可以通过 name 指定
	redisName := ""
	if len(name) > 0 {
		redisName = name[0]
	}

	// 尝试从缓存获取客户端实例
	s.cacheMutex.RLock()
	client, exists := s.clientCache[redisName]
	s.cacheMutex.RUnlock()

	if exists && client != nil {
		return client
	}

	// 缓存未命中，创建新实例
	client = g.Redis(name...)

	// 更新缓存
	s.cacheMutex.Lock()
	s.clientCache[redisName] = client
	s.cacheMutex.Unlock()

	return client
}

// Set 设置缓存
func (s *redisService) Set(ctx context.Context, key string, value interface{}, duration time.Duration, name ...string) error {
	// 当duration为0时表示永不过期，使用Set命令；否则使用SetEX命令
	if duration == 0 {
		// 使用Set命令不设置过期时间
		_, err := s.Client(name...).Set(ctx, key, value)
		return err
	}
	// 使用SetEX命令设置带过期时间的缓存
	return s.Client(name...).SetEX(ctx, key, value, int64(duration.Seconds()))
}

// Get 获取缓存
func (s *redisService) Get(ctx context.Context, key string, name ...string) (*gvar.Var, error) {
	return s.Client(name...).Get(ctx, key)
}

// GetVar 获取缓存并转换为 *gvar.Var
// Deprecated: Use Get instead.
func (s *redisService) GetVar(ctx context.Context, key string, name ...string) (*gvar.Var, error) {
	return s.Get(ctx, key, name...)
}

// GetString 获取缓存并转换为 string
func (s *redisService) GetString(ctx context.Context, key string, name ...string) (string, error) {
	v, err := s.Get(ctx, key, name...)
	if err != nil {
		return "", err
	}
	if v == nil {
		return "", nil // 或者根据业务返回特定错误 gcache.ErrCacheNotExist
	}
	return v.String(), nil
}

// Remove 删除缓存
func (s *redisService) Remove(ctx context.Context, key string, name ...string) error {
	_, err := s.Client(name...).Del(ctx, key)
	return err
}

// Exists 检查 Key 是否存在 (使用 g.Redis() 示例)
func (s *redisService) Exists(ctx context.Context, key string, name ...string) (bool, error) {
	n, err := s.Client(name...).Exists(ctx, key)
	return n > 0, err
}

// Incr 自增 (使用 g.Redis() 示例)
func (s *redisService) Incr(ctx context.Context, key string, name ...string) (int64, error) {
	return s.Client(name...).Incr(ctx, key)
}

// Decr 自减 (使用 g.Redis() 示例)
func (s *redisService) Decr(ctx context.Context, key string, name ...string) (int64, error) {
	return s.Client(name...).Decr(ctx, key)
}

// LPush 列表左侧推入 (使用 g.Redis() 示例)
func (s *redisService) LPush(ctx context.Context, key string, values ...interface{}) (int64, error) {
	return s.Client().LPush(ctx, key, values...) // 假设默认实例
}

// RPop 列表右侧弹出 (使用 g.Redis() 示例)
func (s *redisService) RPop(ctx context.Context, key string) (*gvar.Var, error) {
	return s.Client().RPop(ctx, key) // 假设默认实例
}

// HSet 哈希表设置字段 (使用 g.Redis() 示例)
func (s *redisService) HSet(ctx context.Context, key string, field string, value interface{}) (int64, error) {
	return s.Client().HSet(ctx, key, g.Map{field: value}) // 假设默认实例
}

// HGet 哈希表获取字段 (使用 g.Redis() 示例)
func (s *redisService) HGet(ctx context.Context, key string, field string) (*gvar.Var, error) {
	return s.Client().HGet(ctx, key, field) // 假设默认实例
}

// HGetAll 哈希表获取所有字段 (使用 g.Redis() 示例)
func (s *redisService) HGetAll(ctx context.Context, key string) (*gvar.Var, error) {
	return s.Client().HGetAll(ctx, key) // 假设默认实例
}

// ClearCache 清除客户端缓存
func (s *redisService) ClearCache() {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	s.clientCache = make(map[string]*gredis.Redis)
}

// --- Payment Password Attempts Management ---

// getPaymentAttemptsKey returns the Redis key for payment password attempts
func getPaymentAttemptsKey(userID uint64) string {
	return fmt.Sprintf("payment_attempts:%d", userID)
}

// getPaymentLockKey returns the Redis key for payment password lock
func getPaymentLockKey(userID uint64) string {
	return fmt.Sprintf("payment_lock:%d", userID)
}

// IncrementPaymentPasswordAttempts increments the payment password attempt counter for a user
func (s *redisService) IncrementPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error) {
	key := getPaymentAttemptsKey(userID)
	g.Log().Debugf(ctx, "Incrementing payment password attempts for user %d, key=%s", userID, key)

	// 使用 INCR 命令增加计数
	count, err := s.Incr(ctx, key)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to increment payment password attempts for user %d: %v", userID, err)
		return 0, err
	}

	// 设置过期时间（24小时）
	if count == 1 {
		// 只有在第一次设置时添加过期时间
		_, expireErr := s.Client().Expire(ctx, key, 24*60*60) // 24小时
		if expireErr != nil {
			g.Log().Warningf(ctx, "Failed to set expiration for payment attempts key %s: %v", key, expireErr)
			// 继续处理，不因为设置过期时间失败而中断
		}
	}

	return count, nil
}

// GetPaymentPasswordAttempts gets the current payment password attempt count for a user
func (s *redisService) GetPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error) {
	key := getPaymentAttemptsKey(userID)
	g.Log().Debugf(ctx, "Getting payment password attempts for user %d, key=%s", userID, key)

	// 获取计数
	v, err := s.Get(ctx, key)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get payment password attempts for user %d: %v", userID, err)
		return 0, err
	}

	if v == nil || v.IsNil() {
		// 键不存在，返回0
		return 0, nil
	}

	return v.Int64(), nil
}

// ResetPaymentPasswordAttempts resets the payment password attempt counter for a user
func (s *redisService) ResetPaymentPasswordAttempts(ctx context.Context, userID uint64) error {
	// 删除尝试次数键
	attemptsKey := getPaymentAttemptsKey(userID)
	// 删除锁定键
	lockKey := getPaymentLockKey(userID)

	g.Log().Debugf(ctx, "Resetting payment password attempts for user %d, keys=%s,%s", userID, attemptsKey, lockKey)

	// 删除两个键
	_, err1 := s.Client().Del(ctx, attemptsKey)
	_, err2 := s.Client().Del(ctx, lockKey)

	// 如果有任何一个删除失败，返回错误
	if err1 != nil {
		g.Log().Errorf(ctx, "Failed to delete payment attempts key %s: %v", attemptsKey, err1)
		return err1
	}
	if err2 != nil {
		g.Log().Errorf(ctx, "Failed to delete payment lock key %s: %v", lockKey, err2)
		return err2
	}

	return nil
}

// LockPaymentPassword locks the payment password for a user for the specified duration
func (s *redisService) LockPaymentPassword(ctx context.Context, userID uint64, duration time.Duration) error {
	key := getPaymentLockKey(userID)
	g.Log().Debugf(ctx, "Locking payment password for user %d, key=%s, duration=%v", userID, key, duration)

	// 设置锁定键，值为当前时间戳
	err := s.Set(ctx, key, time.Now().Unix(), duration)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to set payment lock for user %d: %v", userID, err)
		return err
	}

	return nil
}

// IsPaymentPasswordLocked checks if the payment password is locked for a user
func (s *redisService) IsPaymentPasswordLocked(ctx context.Context, userID uint64) (bool, error) {
	key := getPaymentLockKey(userID)
	g.Log().Debugf(ctx, "Checking if payment password is locked for user %d, key=%s", userID, key)

	// 检查锁定键是否存在
	exists, err := s.Exists(ctx, key)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to check if payment lock exists for user %d: %v", userID, err)
		return false, err
	}

	return exists, nil
}

// --- First Start Command Tracking ---

// getFirstStartKey returns the Redis key for tracking first start command
func getFirstStartKey(telegramID int64) string {
	return fmt.Sprintf("first_start:%d", telegramID)
}

// IsFirstStartCommand checks if this is the user's first start command
func (s *redisService) IsFirstStartCommand(ctx context.Context, telegramID int64) (bool, error) {
	key := getFirstStartKey(telegramID)
	g.Log().Debugf(ctx, "Checking if first start command for user %d, key=%s", telegramID, key)

	exists, err := s.Exists(ctx, key)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to check first start command for user %d: %v", telegramID, err)
		return false, err
	}

	// Return true if key does NOT exist (first time)
	return !exists, nil
}

// MarkFirstStartCommand marks that the user has already sent the start command
func (s *redisService) MarkFirstStartCommand(ctx context.Context, telegramID int64) error {
	key := getFirstStartKey(telegramID)
	g.Log().Debugf(ctx, "Marking first start command for user %d, key=%s", telegramID, key)

	// Set key with no expiration (duration = 0)
	err := s.Set(ctx, key, time.Now().Unix(), 0)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to mark first start command for user %d: %v", telegramID, err)
		return err
	}

	return nil
}

// --- User Language Cache ---

// SetUserLanguage caches the user's language setting.
// It uses Telegram ID as part of the key.
// Duration 0 means no expiration.
func (s *redisService) SetUserLanguage(ctx context.Context, telegramID int64, language string, name ...string) error {
	key := consts.UserLanguageKeyPrefix + gconv.String(telegramID)

	// 标准化语言代码，确保格式一致性
	stdLang := language
	// 处理简写的语言代码
	// if language == "zh" {
	// 	stdLang = string(consts.LangZhCN) // 使用标准的 "zh-CN" 格式
	// } else if language == "en-US" || language == "en-GB" {
	// 	stdLang = string(consts.LangEn) // 使用标准的 "en" 格式
	// }

	g.Log().Debugf(ctx, "Setting user language cache: key=%s, language=%s (standardized from %s)", key, stdLang, language)
	// 使用 Set 方法，duration 设置为 0 表示永不过期
	return s.Set(ctx, key, stdLang, 0, name...)
}

// GetUserLanguage retrieves the user's language setting from the cache.
// Returns the language string and an error if any occurs (except key not found).
// If the key is not found, it returns an empty string and nil error.
func (s *redisService) GetUserLanguage(ctx context.Context, telegramID int64, name ...string) (string, error) {
	key := consts.UserLanguageKeyPrefix + gconv.String(telegramID)
	g.Log().Debugf(ctx, "Getting user language cache: key=%s", key)
	lang, err := s.GetString(ctx, key, name...)
	if err != nil {
		// GetString 在 key 不存在时会返回 "", nil，所以这里主要处理其他 Redis 错误
		g.Log().Errorf(ctx, "Error getting user language from Redis for key %s: %v", key, err)
		return "", err
	}

	// 标准化语言代码，确保格式一致性
	stdLang := lang
	// 处理简写的语言代码
	// if lang == "zh" {
	// 	stdLang = string(consts.LangZhCN) // 使用标准的 "zh-CN" 格式
	// } else if lang == "en-US" || lang == "en-GB" {
	// 	stdLang = string(consts.LangEn) // 使用标准的 "en" 格式
	// }

	if stdLang != lang {
		g.Log().Debugf(ctx, "Standardized language from %s to %s for key %s", lang, stdLang, key)
	}

	// 如果 key 不存在，lang 会是空字符串
	return stdLang, nil
}

// --- Distributed Lock Implementation ---

// AcquireLock attempts to acquire a distributed lock using Redis SETNX
// key: the lock key
// value: unique identifier for the lock holder (e.g., UUID)
// ttl: time-to-live for the lock
// Returns true if lock acquired, false otherwise
func (s *redisService) AcquireLock(ctx context.Context, key string, value string, ttl time.Duration) (bool, error) {
	g.Log().Debugf(ctx, "Attempting to acquire lock: key=%s, value=%s, ttl=%v", key, value, ttl)

	// Use SET with NX (only set if not exists) and EX (expiration)
	result, err := s.Client().Do(ctx, "SET", key, value, "NX", "EX", int64(ttl.Seconds()))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to acquire lock for key %s: %v", key, err)
		return false, err
	}

	// Check if the lock was acquired (result should be "OK")
	if result != nil && gconv.String(result) == "OK" {
		g.Log().Debugf(ctx, "Successfully acquired lock: key=%s", key)
		return true, nil
	}

	g.Log().Debugf(ctx, "Failed to acquire lock (already exists): key=%s", key)
	return false, nil
}

// ReleaseLock releases a distributed lock
// Only releases if the current holder matches the provided value
func (s *redisService) ReleaseLock(ctx context.Context, key string, value string) error {
	g.Log().Debugf(ctx, "Attempting to release lock: key=%s, value=%s", key, value)

	// Lua script to ensure atomic release (only delete if value matches)
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`

	result, err := s.Client().Do(ctx, "EVAL", script, 1, key, value)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to release lock for key %s: %v", key, err)
		return err
	}

	if gconv.Int64(result) == 1 {
		g.Log().Debugf(ctx, "Successfully released lock: key=%s", key)
	} else {
		g.Log().Warningf(ctx, "Lock not released (value mismatch or not exists): key=%s", key)
	}

	return nil
}

// ExtendLock extends the TTL of an existing lock
// Only extends if the current holder matches the provided value
func (s *redisService) ExtendLock(ctx context.Context, key string, value string, ttl time.Duration) (bool, error) {
	g.Log().Debugf(ctx, "Attempting to extend lock: key=%s, value=%s, ttl=%v", key, value, ttl)

	// Lua script to ensure atomic extension (only extend if value matches)
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("expire", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	result, err := s.Client().Do(ctx, "EVAL", script, 1, key, value, int64(ttl.Seconds()))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to extend lock for key %s: %v", key, err)
		return false, err
	}

	if gconv.Int64(result) == 1 {
		g.Log().Debugf(ctx, "Successfully extended lock: key=%s", key)
		return true, nil
	}

	g.Log().Warningf(ctx, "Lock not extended (value mismatch or not exists): key=%s", key)
	return false, nil
}

// GetFundTransferLockKey returns the Redis key for fund transfer lock
func GetFundTransferLockKey(mainUserID int64) string {
	return fmt.Sprintf("fund_transfer_lock:%d", mainUserID)
}
