package service

import (
	"admin-api/internal/library/minio"
	"admin-api/internal/library/s3"
	"admin-api/internal/library/storage" // Import the unified interface
	"admin-api/internal/model"
	"context"
	"io"
	"sync"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// IStorage defines the interface for storage operations, exposing the library interface.
// This allows mocking or further extension at the service layer if needed.
type IStorage interface {
	UploadObject(ctx context.Context, objectKey string, reader io.Reader, size int64, contentType string) (string, error)
	GetObjectURL(ctx context.Context, objectKey string) (string, error)
}

// sStorage implements the IStorage service interface.
// It holds an instance of a storage provider (Minio or S3) that conforms to storage.IStorage.
type sStorage struct {
	client  storage.IStorage // Holds the actual client (Minio or S3)
	once    sync.Once
	initErr error // Store initialization error
}

var insStorage = sStorage{}

// Storage returns the singleton instance of the storage service.
// It initializes the underlying client (Minio or S3) based on configuration on first call.
func Storage() IStorage {
	insStorage.once.Do(func() {
		ctx := context.Background() // Use background context for initialization
		var cfg model.StorageConfig
		err := g.Cfg().MustGet(ctx, "storage").Scan(&cfg)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to read storage configuration: %v", err)
			insStorage.initErr = gerror.Wrap(err, "Failed to read storage configuration")
			return
		}

		g.Log().Infof(ctx, "Initializing storage provider: %s", cfg.Provider)

		switch cfg.Provider {
		case "minio":
			insStorage.client, insStorage.initErr = minio.NewClient(ctx, cfg.Minio)
			if insStorage.initErr != nil {
				g.Log().Errorf(ctx, "Failed to initialize Minio client: %v", insStorage.initErr)
			}
		case "s3":
			insStorage.client, insStorage.initErr = s3.NewClient(ctx, cfg.S3)
			if insStorage.initErr != nil {
				g.Log().Errorf(ctx, "Failed to initialize S3 client: %v", insStorage.initErr)
			}
		default:
			insStorage.initErr = gerror.Newf("Unsupported storage provider specified: %s", cfg.Provider)
			g.Log().Errorf(ctx, "Unsupported storage provider specified: %s", cfg.Provider)
		}
	})

	// Check for initialization error after Do() has run
	if insStorage.initErr != nil {
		// Log the error again on subsequent calls? Or just return a wrapper that always errors?
		// Returning an instance that holds the error seems reasonable.
		// The methods below will check s.client and return error if nil.
		g.Log().Errorf(context.Background(), "Storage service initialization failed: %v", insStorage.initErr)
	} else if insStorage.client == nil {
		// This case should ideally not happen if initErr is nil, but as a safeguard:
		insStorage.initErr = gerror.New("Storage client is nil after initialization without error")
		g.Log().Errorf(context.Background(), "Storage client is nil after initialization without error")
	}

	return &insStorage // Return the singleton instance
}

// UploadObject uploads a file using the configured storage provider.
func (s *sStorage) UploadObject(ctx context.Context, objectKey string, reader io.Reader, size int64, contentType string) (string, error) {
	if s.initErr != nil {
		return "", gerror.Wrap(s.initErr, "Storage service not initialized correctly")
	}
	if s.client == nil {
		// This check might be redundant if initErr is always set when client is nil, but safe to keep.
		return "", gerror.New("Storage client is not available")
	}
	return s.client.UploadObject(ctx, objectKey, reader, size, contentType)
}

// GetObjectURL gets the public URL of a file using the configured storage provider.
func (s *sStorage) GetObjectURL(ctx context.Context, objectKey string) (string, error) {
	if s.initErr != nil {
		return "", gerror.Wrap(s.initErr, "Storage service not initialized correctly")
	}
	if s.client == nil {
		return "", gerror.New("Storage client is not available")
	}
	return s.client.GetObjectURL(ctx, objectKey)
}
