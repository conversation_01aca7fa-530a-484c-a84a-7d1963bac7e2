package service

import (
	"admin-api/internal/constants"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CallbackManager 回调管理器
type CallbackManager struct {
	sender         *CallbackSender
	retryIntervals []time.Duration
}

// NewCallbackManager 创建新的回调管理器
func NewCallbackManager() *CallbackManager {
	config := GetCallbackConfig()
	return &CallbackManager{
		sender:         NewCallbackSender(),
		retryIntervals: config.RetryIntervals,
	}
}

// WithdrawSuccessData 提现成功回调数据
type WithdrawSuccessData struct {
	EventType    string `json:"event_type"`
	OrderNo      string `json:"order_no"`
	MerchantId   uint64 `json:"merchant_id"`
	Amount       string `json:"amount"`
	Currency     string `json:"currency"`
	ActualAmount string `json:"actual_amount"`
	HandlingFee  string `json:"handling_fee"`
	TxHash       string `json:"tx_hash"`
	CompletedAt  string `json:"completed_at"`
	Timestamp    int64  `json:"timestamp"`
}

// DepositSuccessData 充值成功回调数据
type DepositSuccessData struct {
	EventType     string `json:"event_type"`
	OrderNo       string `json:"order_no"`
	MerchantId    uint64 `json:"merchant_id"`
	Amount        string `json:"amount"`
	Currency      string `json:"currency"`
	FromAddress   string `json:"from_address"`
	ToAddress     string `json:"to_address"`
	TxHash        string `json:"tx_hash"`
	Confirmations int    `json:"confirmations"`
	CompletedAt   string `json:"completed_at"`
	Timestamp     int64  `json:"timestamp"`
}

// WithdrawCreatedData 提现创建回调数据
type WithdrawCreatedData struct {
	EventType  string `json:"event_type"`
	OrderNo    string `json:"order_no"`
	MerchantId uint64 `json:"merchant_id"`
	Amount     string `json:"amount"`
	Currency   string `json:"currency"`
	CreatedAt  string `json:"created_at"`
	Timestamp  int64  `json:"timestamp"`
}

// WithdrawCancelledData 提现撤销回调数据
type WithdrawCancelledData struct {
	EventType    string `json:"event_type"`
	OrderNo      string `json:"order_no"`
	MerchantId   uint64 `json:"merchant_id"`
	CancelReason string `json:"cancel_reason"`
	CancelledAt  string `json:"cancelled_at"`
	Timestamp    int64  `json:"timestamp"`
}

// WithdrawApprovedData 提现审批通过回调数据
type WithdrawApprovedData struct {
	EventType    string `json:"event_type"`
	OrderNo      string `json:"order_no"`
	MerchantId   uint64 `json:"merchant_id"`
	ApprovedAt   string `json:"approved_at"`
	ApproverNote string `json:"approver_note"`
	Timestamp    int64  `json:"timestamp"`
}

// WithdrawRejectedData 提现拒绝回调数据
type WithdrawRejectedData struct {
	EventType    string `json:"event_type"`
	OrderNo      string `json:"order_no"`
	MerchantId   uint64 `json:"merchant_id"`
	RejectReason string `json:"reject_reason"`
	RejectedAt   string `json:"rejected_at"`
	Timestamp    int64  `json:"timestamp"`
}

// WithdrawCompletedData 提现完成回调数据
type WithdrawCompletedData struct {
	EventType    string `json:"event_type"`
	OrderNo      string `json:"order_no"`
	MerchantId   uint64 `json:"merchant_id"`
	Amount       string `json:"amount"`
	Currency     string `json:"currency"`
	ActualAmount string `json:"actual_amount"`
	HandlingFee  string `json:"handling_fee"`
	TxHash       string `json:"tx_hash"`
	CompletedAt  string `json:"completed_at"`
	Timestamp    int64  `json:"timestamp"`
}

// CreateCallbackRecord 创建回调记录（不发送）
func (m *CallbackManager) CreateCallbackRecord(ctx context.Context, eventType string, merchantId, relatedId uint64, orderNo string, data interface{}) error {
	// 检查事件类型是否需要回调
	if !constants.IsCallbackEventEnabled(eventType) {
		g.Log().Debugf(ctx, "事件类型 %s 未启用回调，跳过", eventType)
		return nil
	}

	// 获取商户信息
	merchant, err := m.getMerchantInfo(ctx, merchantId)
	if err != nil {
		return err
	}

	// 检查是否有回调URL
	if merchant.CallbackUrl == "" {
		g.Log().Infof(ctx, "商户 %d 未配置回调URL，跳过回调", merchantId)
		return nil
	}

	// 序列化数据
	payloadBytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化回调数据失败: %v", err)
	}

	// 创建回调记录
	callbackRecord := &entity.MerchantCallbacks{
		MerchantId:   merchantId,
		CallbackType: eventType, // 使用具体的事件类型
		RelatedId:    relatedId,
		CallbackUrl:  merchant.CallbackUrl,
		Payload:      string(payloadBytes),
		Status:       "pending",
		RetryCount:   0,
		CreatedAt:    gtime.Now(),
		UpdatedAt:    gtime.Now(),
	}

	// 插入数据库
	_, err = dao.MerchantCallbacks.Ctx(ctx).Data(callbackRecord).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "创建回调记录失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "创建回调记录成功: 事件=%s, 商户=%d, 订单=%s", eventType, merchantId, orderNo)
	return nil
}

// CreateWithdrawSuccessCallback 创建提现成功回调
func (m *CallbackManager) CreateWithdrawSuccessCallback(ctx context.Context, withdrawId uint64) error {
	// 检查回调功能是否启用
	if !IsCallbackEnabled() {
		g.Log().Debug(ctx, "回调功能未启用，跳过提现成功回调: withdrawId=%d", withdrawId)
		return nil
	}
	// 获取提现记录
	withdraw, err := dao.MerchantWithdraws.Ctx(ctx).Where("withdraws_id = ?", withdrawId).One()
	if err != nil {
		g.Log().Errorf(ctx, "获取提现记录失败: %v", err)
		return err
	}
	if withdraw.IsEmpty() {
		return fmt.Errorf("提现记录不存在: %d", withdrawId)
	}

	var withdrawEntity *entity.MerchantWithdraws
	if err := withdraw.Struct(&withdrawEntity); err != nil {
		return fmt.Errorf("转换提现实体失败: %v", err)
	}

	// 获取商户信息
	merchant, err := m.getMerchantInfo(ctx, withdrawEntity.MerchantId)
	if err != nil {
		return err
	}

	// 检查是否有回调URL
	if merchant.CallbackUrl == "" {
		g.Log().Infof(ctx, "商户 %d 未配置回调URL，跳过回调", merchant.MerchantId)
		return nil
	}

	// 准备回调数据
	callbackData := &WithdrawCompletedData{
		EventType:    constants.CallbackEventWithdrawCompleted,
		OrderNo:      withdrawEntity.OrderNo,
		MerchantId:   withdrawEntity.MerchantId,
		Amount:       withdrawEntity.Amount.String(),
		Currency:     withdrawEntity.Name,
		ActualAmount: withdrawEntity.ActualAmount.String(),
		HandlingFee:  withdrawEntity.HandlingFee.String(),
		TxHash:       withdrawEntity.TxHash,
		CompletedAt:  withdrawEntity.CompletedAt.String(),
		Timestamp:    time.Now().Unix(),
	}

	// 使用 WebhookSecret 进行签名
	signingSecret := merchant.WebhookSecret
	if signingSecret == "" {
		g.Log().Warningf(ctx, "商户 %d 未设置 WebhookSecret，跳过回调", merchant.MerchantId)
		return fmt.Errorf("webhook secret not configured")
	}

	return m.createCallback(ctx, constants.CallbackEventWithdrawCompleted, withdrawEntity.MerchantId, withdrawId,
		withdrawEntity.OrderNo, merchant.CallbackUrl, callbackData, signingSecret)
}

// CreateDepositSuccessCallback 创建充值成功回调
func (m *CallbackManager) CreateDepositSuccessCallback(ctx context.Context, depositId uint64) error {
	// 检查回调功能是否启用
	if !IsCallbackEnabled() {
		g.Log().Debug(ctx, "回调功能未启用，跳过充值成功回调: depositId=%d", depositId)
		return nil
	}
	// 获取充值记录
	deposit, err := dao.MerchantDeposits.Ctx(ctx).Where("recharges_id = ?", depositId).One()
	if err != nil {
		g.Log().Errorf(ctx, "获取充值记录失败: %v", err)
		return err
	}
	if deposit.IsEmpty() {
		return fmt.Errorf("充值记录不存在: %d", depositId)
	}

	var depositEntity *entity.MerchantDeposits
	if err := deposit.Struct(&depositEntity); err != nil {
		return fmt.Errorf("转换充值实体失败: %v", err)
	}

	// 获取商户信息
	merchant, err := m.getMerchantInfo(ctx, depositEntity.MerchantId)
	if err != nil {
		return err
	}

	// 检查是否有回调URL
	if merchant.CallbackUrl == "" {
		g.Log().Infof(ctx, "商户 %d 未配置回调URL，跳过回调", merchant.MerchantId)
		return nil
	}

	// 准备回调数据
	callbackData := &DepositSuccessData{
		EventType:     constants.CallbackEventDepositConfirmed,
		OrderNo:       fmt.Sprintf("DP%d", depositEntity.RechargesId), // 如果没有order_no字段，使用ID生成
		MerchantId:    depositEntity.MerchantId,
		Amount:        depositEntity.Amount.String(),
		Currency:      depositEntity.Name,
		FromAddress:   depositEntity.FromAddress,
		ToAddress:     depositEntity.ToAddress,
		TxHash:        depositEntity.TxHash,
		Confirmations: int(depositEntity.Confirmations),
		CompletedAt:   depositEntity.CompletedAt.String(),
		Timestamp:     time.Now().Unix(),
	}

	// 使用 WebhookSecret 进行签名
	signingSecret := merchant.WebhookSecret
	if signingSecret == "" {
		g.Log().Warningf(ctx, "商户 %d 未设置 WebhookSecret，跳过回调", merchant.MerchantId)
		return fmt.Errorf("webhook secret not configured")
	}

	return m.createCallback(ctx, constants.CallbackEventDepositConfirmed, depositEntity.MerchantId, depositId,
		fmt.Sprintf("DP%d", depositEntity.RechargesId), merchant.CallbackUrl, callbackData, signingSecret)
}

// createCallback 创建回调记录并异步发送
func (m *CallbackManager) createCallback(ctx context.Context, eventType string, merchantId, relatedId uint64,
	orderNo, callbackURL string, data interface{}, secretSalt string) error {

	// 序列化回调数据
	payloadBytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化回调数据失败: %v", err)
	}
	payload := string(payloadBytes)

	// 创建回调记录
	callbackRecord := &entity.MerchantCallbacks{
		MerchantId:   merchantId,
		CallbackType: eventType,
		RelatedId:    relatedId,
		CallbackUrl:  callbackURL,
		Payload:      payload,
		Status:       "pending",
		RetryCount:   0,
		CreatedAt:    gtime.Now(),
		UpdatedAt:    gtime.Now(),
	}

	// 插入数据库
	_, err = dao.MerchantCallbacks.Ctx(ctx).Data(callbackRecord).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "创建回调记录失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "创建回调记录成功: 事件=%s, 商户=%d, 订单=%s", eventType, merchantId, orderNo)

	// 异步发送回调
	go func() {
		bgCtx := context.Background()
		m.sendCallbackAsync(bgCtx, callbackRecord, secretSalt)
	}()

	return nil
}

// sendCallbackAsync 异步发送回调
func (m *CallbackManager) sendCallbackAsync(ctx context.Context, callback *entity.MerchantCallbacks, secretSalt string) {
	// 获取商户的API Key
	merchant, err := m.getMerchantInfo(ctx, callback.MerchantId)
	if err != nil {
		g.Log().Errorf(ctx, "获取商户信息失败: %v", err)
		return
	}

	// 准备回调请求
	var callbackData interface{}
	if err := json.Unmarshal([]byte(callback.Payload), &callbackData); err != nil {
		g.Log().Errorf(ctx, "解析回调数据失败: %v", err)
		return
	}

	// 使用 WebhookSecret 进行签名
	signingSecret := merchant.WebhookSecret
	if signingSecret == "" {
		g.Log().Errorf(ctx, "商户 %d 未设置 WebhookSecret", merchant.MerchantId)
		return
	}

	req := &CallbackRequest{
		URL:        callback.CallbackUrl,
		Method:     "POST",
		Body:       callbackData,
		APIKey:     "",                    // 不再发送 API Key
		SecretSalt: signingSecret,         // 使用 webhook secret 进行签名
		EventType:  callback.CallbackType, // 添加事件类型
	}

	// 更新状态为处理中
	callback.Status = "processing"
	callback.LastAttemptAt = gtime.Now()
	callback.UpdatedAt = gtime.Now()
	dao.MerchantCallbacks.Ctx(ctx).Where("id = ?", callback.Id).Data(callback).Update()

	// 发送回调
	response := m.sender.SendCallback(ctx, req)

	// 更新回调记录
	m.updateCallbackResult(ctx, callback, response)
}

// updateCallbackResult 更新回调结果
func (m *CallbackManager) updateCallbackResult(ctx context.Context, callback *entity.MerchantCallbacks, response *CallbackResponse) {
	callback.ResponseCode = response.StatusCode
	callback.ResponseBody = response.Body
	callback.UpdatedAt = gtime.Now()

	if response.Success {
		callback.Status = "success"
		g.Log().Infof(ctx, "回调成功: ID=%d, 商户=%d, 类型=%s", callback.Id, callback.MerchantId, callback.CallbackType)
	} else {
		callback.Status = "failed"
		callback.RetryCount++

		// 安排重试
		if callback.RetryCount <= len(m.retryIntervals) {
			nextRetryTime := time.Now().Add(m.retryIntervals[callback.RetryCount-1])
			callback.Status = "pending" // 重新设置为pending等待重试
			g.Log().Warningf(ctx, "回调失败，安排重试: ID=%d, 重试次数=%d, 下次重试时间=%v",
				callback.Id, callback.RetryCount, nextRetryTime)
		} else {
			callback.Status = "max_retries_exceeded"
			g.Log().Errorf(ctx, "回调达到最大重试次数: ID=%d, 商户=%d", callback.Id, callback.MerchantId)
		}
	}

	// 更新数据库
	_, err := dao.MerchantCallbacks.Ctx(ctx).Where("id = ?", callback.Id).Data(callback).Update()
	if err != nil {
		g.Log().Errorf(ctx, "更新回调记录失败: %v", err)
	}
}

// ProcessPendingCallbacks 处理待发送的回调（定时任务调用）
func (m *CallbackManager) ProcessPendingCallbacks(ctx context.Context) error {
	// 查询待发送的回调（包括新创建的和需要重试的）
	callbacks, err := dao.MerchantCallbacks.Ctx(ctx).
		Where("status = ?", "pending").
		Where("retry_count < ?", len(m.retryIntervals)).
		Where("(last_attempt_at IS NULL OR last_attempt_at <= ?)",
			time.Now().Add(-1*time.Minute)).
		OrderAsc("created_at").
		Limit(100). // 每次处理100个
		All()

	if err != nil {
		g.Log().Errorf(ctx, "查询重试回调失败: %v", err)
		return err
	}

	if len(callbacks) == 0 {
		return nil
	}

	g.Log().Infof(ctx, "处理待发送回调: %d 个", len(callbacks))

	for _, record := range callbacks {
		var callback *entity.MerchantCallbacks
		if err := record.Struct(&callback); err != nil {
			g.Log().Errorf(ctx, "转换回调实体失败: %v", err)
			continue
		}

		// 检查是否到了重试时间
		if callback.RetryCount > 0 && callback.LastAttemptAt != nil {
			retryInterval := m.retryIntervals[callback.RetryCount-1]
			nextRetryTime := callback.LastAttemptAt.Add(retryInterval)
			if time.Now().Before(nextRetryTime.Time) {
				continue // 还没到重试时间
			}
		}

		// 获取商户信息
		merchant, err := m.getMerchantInfo(ctx, callback.MerchantId)
		if err != nil {
			g.Log().Errorf(ctx, "获取商户信息失败: %v", err)
			continue
		}

		// 使用 WebhookSecret 进行签名
		signingSecret := merchant.WebhookSecret
		if signingSecret == "" {
			g.Log().Warningf(ctx, "商户 %d 未设置 WebhookSecret，跳过回调", merchant.MerchantId)
			continue
		}

		// 异步重试
		go func(cb *entity.MerchantCallbacks, secret string) {
			m.sendCallbackAsync(context.Background(), cb, secret)
		}(callback, signingSecret)
	}

	return nil
}

// getMerchantInfo 获取商户信息
func (m *CallbackManager) getMerchantInfo(ctx context.Context, merchantId uint64) (*entity.Merchants, error) {
	merchant, err := dao.Merchants.Ctx(ctx).Where("merchant_id = ?", merchantId).One()
	if err != nil {
		return nil, fmt.Errorf("查询商户失败: %v", err)
	}
	if merchant.IsEmpty() {
		return nil, fmt.Errorf("商户不存在: %d", merchantId)
	}

	var merchantEntity *entity.Merchants
	if err := merchant.Struct(&merchantEntity); err != nil {
		return nil, fmt.Errorf("转换商户实体失败: %v", err)
	}

	return merchantEntity, nil
}

// GetCallbackStats 获取回调统计信息
func (m *CallbackManager) GetCallbackStats(ctx context.Context, merchantId uint64) (map[string]interface{}, error) {
	// 总回调数
	total, err := dao.MerchantCallbacks.Ctx(ctx).Where("merchant_id = ?", merchantId).Count()
	if err != nil {
		return nil, err
	}

	// 成功回调数
	success, err := dao.MerchantCallbacks.Ctx(ctx).
		Where("merchant_id = ? AND status = ?", merchantId, "success").Count()
	if err != nil {
		return nil, err
	}

	// 失败回调数
	failed, err := dao.MerchantCallbacks.Ctx(ctx).
		Where("merchant_id = ? AND status IN (?)", merchantId, []string{"failed", "max_retries_exceeded"}).Count()
	if err != nil {
		return nil, err
	}

	// 待处理回调数
	pending, err := dao.MerchantCallbacks.Ctx(ctx).
		Where("merchant_id = ? AND status = ?", merchantId, "pending").Count()
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total":   total,
		"success": success,
		"failed":  failed,
		"pending": pending,
		"success_rate": func() float64 {
			if total > 0 {
				return float64(success) / float64(total) * 100
			}
			return 0
		}(),
	}, nil
}

// ProcessRetryCallbacks 处理重试回调（为向后兼容保留，调用ProcessPendingCallbacks）
func (m *CallbackManager) ProcessRetryCallbacks(ctx context.Context) error {
	return m.ProcessPendingCallbacks(ctx)
}
