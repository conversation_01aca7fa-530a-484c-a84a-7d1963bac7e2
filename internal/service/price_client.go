package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"

	"admin-api/internal/price-monitor/model"
)

// IPriceClient 价格服务客户端接口
type IPriceClient interface {
	// GetRealTimePrice 获取实时加密货币价格 (自动检查数据新鲜度)
	GetRealTimePrice(ctx context.Context, symbol string) (*PriceData, error)

	// GetMultiplePrices 批量获取加密货币价格 (自动检查数据新鲜度)
	GetMultiplePrices(ctx context.Context, symbols []string) (map[string]*PriceData, error)

	// GetFiatPrice 获取法币价格 (支持买价和卖价)
	GetFiatPrice(ctx context.Context, asset, currency string) (*FiatPriceData, error)

	// GetMultipleFiatPrices 批量获取法币价格
	GetMultipleFiatPrices(ctx context.Context, pairs []FiatPair) (map[string]*FiatPriceData, error)

	// GetPriceHistory 获取加密货币价格历史
	GetPriceHistory(ctx context.Context, symbol string, from, to time.Time) ([]*PriceData, error)

	// GetFiatPriceHistory 获取法币价格历史
	GetFiatPriceHistory(ctx context.Context, asset, currency string, from, to time.Time) ([]*FiatPriceData, error)

	// SubscribePriceUpdates 订阅价格更新
	SubscribePriceUpdates(ctx context.Context, symbols []string) (<-chan *PriceUpdateEvent, error)
}

// PriceData 加密货币价格数据（简化版，供外部使用）
type PriceData struct {
	Symbol      string          `json:"symbol"`
	Price       decimal.Decimal `json:"price"`
	Volume24h   decimal.Decimal `json:"volume_24h"`
	Change24h   decimal.Decimal `json:"change_24h"`
	High24h     decimal.Decimal `json:"high_24h"`
	Low24h      decimal.Decimal `json:"low_24h"`
	Provider    string          `json:"provider"`
	Timestamp   time.Time       `json:"timestamp"`
	LastUpdated int64           `json:"last_updated"`
}

// FiatPriceData 法币价格数据（供外部使用）
type FiatPriceData struct {
	Asset          string          `json:"asset"`           // 资产符号 (USDT)
	Currency       string          `json:"currency"`        // 法币符号 (CNY)
	CurrencySymbol string          `json:"currency_symbol"` // 法币符号 (￥)
	BuyPrice       decimal.Decimal `json:"buy_price"`       // 买入价格
	SellPrice      decimal.Decimal `json:"sell_price"`      // 卖出价格
	MidPrice       decimal.Decimal `json:"mid_price"`       // 中间价格 (买价+卖价)/2
	Spread         decimal.Decimal `json:"spread"`          // 价差 (卖价-买价)
	SpreadPercent  decimal.Decimal `json:"spread_percent"`  // 价差百分比
	Provider       string          `json:"provider"`        // 价格提供商
	Timestamp      time.Time       `json:"timestamp"`       // 更新时间戳
	LastUpdated    int64           `json:"last_updated"`    // Unix时间戳
}

// FiatPair 法币交易对
type FiatPair struct {
	Asset    string `json:"asset"`    // 资产符号
	Currency string `json:"currency"` // 法币符号
}

// PriceUpdateEvent 价格更新事件
type PriceUpdateEvent struct {
	Type      string     `json:"type"`
	Symbol    string     `json:"symbol"`
	PriceData *PriceData `json:"priceData"`
}

// ClientConfig 客户端配置
type ClientConfig struct {
	RedisConfigName     string        `json:"redis_config_name"`     // 使用项目现有的Redis配置名称
	DefaultStaleTimeout time.Duration `json:"default_stale_timeout"` // 自动检查数据新鲜度的默认超时时间
	RetryAttempts       int           `json:"retry_attempts"`
	RetryDelay          time.Duration `json:"retry_delay"`
}

// PriceClient 价格客户端实现
type PriceClient struct {
	redis  *gredis.Redis
	config *ClientConfig
}

// 价格客户端单例
var (
	priceClientInstance IPriceClient
)

// NewPriceClient 创建价格客户端
func NewPriceClient(config *ClientConfig) IPriceClient {
	if config == nil {
		config = &ClientConfig{
			RedisConfigName:     "default",
			DefaultStaleTimeout: 30 * time.Second,
			RetryAttempts:       3,
			RetryDelay:          100 * time.Millisecond,
		}
	}

	return &PriceClient{
		redis:  Redis().Client(config.RedisConfigName),
		config: config,
	}
}

// PriceClientInstance 获取价格客户端单例
func PriceClientInstance() IPriceClient {
	if priceClientInstance == nil {
		priceClientInstance = NewPriceClient(nil)
	}
	return priceClientInstance
}

// GetRealTimePrice 获取实时价格
func (c *PriceClient) GetRealTimePrice(ctx context.Context, symbol string) (*PriceData, error) {
	var priceData *PriceData
	var err error

	// 重试机制
	for i := 0; i < c.config.RetryAttempts; i++ {
		priceData, err = c.getRealTimePriceOnce(ctx, symbol)
		if err == nil {
			// 检查数据新鲜度
			if time.Since(priceData.Timestamp) <= c.config.DefaultStaleTimeout {
				return priceData, nil
			}
			err = fmt.Errorf("price data is stale: last update %v ago", time.Since(priceData.Timestamp))
		}

		if i < c.config.RetryAttempts-1 {
			time.Sleep(c.config.RetryDelay)
		}
	}

	return nil, err
}

// getRealTimePriceOnce 获取一次实时价格
func (c *PriceClient) getRealTimePriceOnce(ctx context.Context, symbol string) (*PriceData, error) {
	key := "price:live:" + symbol

	result, err := c.redis.HGetAll(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get price data: %w", err)
	}

	// 将gvar结果转换为map
	resultMap := result.MapStrStr()

	if len(resultMap) == 0 {
		return nil, model.NewPriceError(
			model.ErrorTypeStaleData,
			"NO_DATA",
			"No price data available",
		).WithSymbol(symbol)
	}

	// 解析数据
	priceData := &PriceData{
		Symbol:   symbol,
		Provider: resultMap["provider"],
	}

	// 解析价格字段
	if priceStr, ok := resultMap["price"]; ok {
		priceData.Price, _ = decimal.NewFromString(priceStr)
	}
	if volumeStr, ok := resultMap["volume_24h"]; ok {
		priceData.Volume24h, _ = decimal.NewFromString(volumeStr)
	}
	if changeStr, ok := resultMap["change_24h"]; ok {
		priceData.Change24h, _ = decimal.NewFromString(changeStr)
	}
	if highStr, ok := resultMap["high_24h"]; ok {
		priceData.High24h, _ = decimal.NewFromString(highStr)
	}
	if lowStr, ok := resultMap["low_24h"]; ok {
		priceData.Low24h, _ = decimal.NewFromString(lowStr)
	}

	// 解析时间戳
	if tsStr, ok := resultMap["timestamp"]; ok {
		if ts, err := strconv.ParseInt(tsStr, 10, 64); err == nil {
			priceData.Timestamp = time.Unix(0, ts*int64(time.Millisecond))
		}
	}
	if luStr, ok := resultMap["last_updated"]; ok {
		priceData.LastUpdated, _ = strconv.ParseInt(luStr, 10, 64)
	}

	return priceData, nil
}

// GetMultiplePrices 批量获取价格
func (c *PriceClient) GetMultiplePrices(ctx context.Context, symbols []string) (map[string]*PriceData, error) {
	results := make(map[string]*PriceData)

	// 由于gredis不支持Pipeline，我们逐个获取
	// 可以考虑使用goroutine并发获取以提高性能
	for _, symbol := range symbols {
		priceData, err := c.getRealTimePriceOnce(ctx, symbol)
		if err != nil {
			g.Log().Warningf(ctx, "Failed to get price for %s: %v", symbol, err)
			continue
		}

		// 检查数据新鲜度
		if time.Since(priceData.Timestamp) <= c.config.DefaultStaleTimeout {
			results[symbol] = priceData
		}
	}

	return results, nil
}

// GetPriceHistory 获取价格历史
func (c *PriceClient) GetPriceHistory(ctx context.Context, symbol string, from, to time.Time) ([]*PriceData, error) {
	key := "price:history:" + symbol

	// 使用ZRange获取指定时间范围的数据
	// gredis v2使用ZRange而不是ZRangeByScore
	results, err := c.redis.ZRange(ctx, key, 0, -1)
	if err != nil {
		return nil, fmt.Errorf("failed to get price history: %w", err)
	}

	var history []*PriceData
	for _, result := range results.Strings() {
		var data model.PriceData
		if err := json.Unmarshal([]byte(result), &data); err != nil {
			g.Log().Warningf(ctx, "Failed to unmarshal price data: %v", err)
			continue
		}

		// 过滤时间范围
		if data.Timestamp.Before(from) || data.Timestamp.After(to) {
			continue
		}

		history = append(history, &PriceData{
			Symbol:      data.Symbol,
			Price:       data.Price,
			Volume24h:   data.Volume24h,
			Change24h:   data.Change24h,
			High24h:     data.High24h,
			Low24h:      data.Low24h,
			Provider:    data.Provider,
			Timestamp:   data.Timestamp,
			LastUpdated: data.LastUpdated,
		})
	}

	return history, nil
}

// GetFiatPrice 获取法币价格
func (c *PriceClient) GetFiatPrice(ctx context.Context, asset, currency string) (*FiatPriceData, error) {
	var fiatData *FiatPriceData
	var err error

	// 重试机制
	for i := 0; i < c.config.RetryAttempts; i++ {
		fiatData, err = c.getFiatPriceOnce(ctx, asset, currency)
		if err == nil {
			// 检查数据新鲜度 (法币价格更新间隔较长，允许更长的过期时间)
			staleTimeout := c.config.DefaultStaleTimeout * 10 // 法币数据允许10倍过期时间
			if time.Since(fiatData.Timestamp) <= staleTimeout {
				return fiatData, nil
			}
			err = fmt.Errorf("fiat price data is stale: last update %v ago", time.Since(fiatData.Timestamp))
		}

		if i < c.config.RetryAttempts-1 {
			time.Sleep(c.config.RetryDelay)
		}
	}

	return nil, err
}

// getFiatPriceOnce 获取一次法币价格
func (c *PriceClient) getFiatPriceOnce(ctx context.Context, asset, currency string) (*FiatPriceData, error) {
	key := fmt.Sprintf("fiat:price:%s_%s", asset, currency)

	result, err := c.redis.HGetAll(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get fiat price data: %w", err)
	}

	// 将gvar结果转换为map
	resultMap := result.MapStrStr()

	if len(resultMap) == 0 {
		return nil, fmt.Errorf("no fiat price data available for %s/%s", asset, currency)
	}

	// 解析数据
	fiatData := &FiatPriceData{
		Asset:          resultMap["asset"],
		Currency:       resultMap["currency"],
		CurrencySymbol: resultMap["currency_symbol"],
		Provider:       resultMap["provider"],
	}

	// 解析价格字段
	if buyPriceStr, ok := resultMap["buy_price"]; ok {
		fiatData.BuyPrice, _ = decimal.NewFromString(buyPriceStr)
	}
	if sellPriceStr, ok := resultMap["sell_price"]; ok {
		fiatData.SellPrice, _ = decimal.NewFromString(sellPriceStr)
	}

	// 计算中间价格和价差
	if !fiatData.BuyPrice.IsZero() && !fiatData.SellPrice.IsZero() {
		fiatData.MidPrice = fiatData.BuyPrice.Add(fiatData.SellPrice).Div(decimal.NewFromInt(2))
		fiatData.Spread = fiatData.SellPrice.Sub(fiatData.BuyPrice)
		if !fiatData.MidPrice.IsZero() {
			fiatData.SpreadPercent = fiatData.Spread.Div(fiatData.MidPrice).Mul(decimal.NewFromInt(100))
		}
	}

	// 解析时间戳
	if tsStr, ok := resultMap["timestamp"]; ok {
		if ts, err := strconv.ParseInt(tsStr, 10, 64); err == nil {
			fiatData.Timestamp = time.Unix(0, ts*int64(time.Millisecond))
		}
	}
	if luStr, ok := resultMap["last_updated"]; ok {
		fiatData.LastUpdated, _ = strconv.ParseInt(luStr, 10, 64)
	}

	return fiatData, nil
}

// GetMultipleFiatPrices 批量获取法币价格
func (c *PriceClient) GetMultipleFiatPrices(ctx context.Context, pairs []FiatPair) (map[string]*FiatPriceData, error) {
	results := make(map[string]*FiatPriceData)

	for _, pair := range pairs {
		fiatData, err := c.getFiatPriceOnce(ctx, pair.Asset, pair.Currency)
		if err != nil {
			g.Log().Warningf(ctx, "Failed to get fiat price for %s/%s: %v", pair.Asset, pair.Currency, err)
			continue
		}

		// 检查数据新鲜度
		staleTimeout := c.config.DefaultStaleTimeout * 10 // 法币数据允许10倍过期时间
		if time.Since(fiatData.Timestamp) <= staleTimeout {
			key := fmt.Sprintf("%s_%s", pair.Asset, pair.Currency)
			results[key] = fiatData
		}
	}

	return results, nil
}

// GetFiatPriceHistory 获取法币价格历史
func (c *PriceClient) GetFiatPriceHistory(ctx context.Context, asset, currency string, from, to time.Time) ([]*FiatPriceData, error) {
	key := fmt.Sprintf("fiat:history:%s_%s", asset, currency)

	// 使用ZRange获取指定时间范围的数据
	results, err := c.redis.ZRange(ctx, key, 0, -1)
	if err != nil {
		return nil, fmt.Errorf("failed to get fiat price history: %w", err)
	}

	var history []*FiatPriceData
	for _, result := range results.Strings() {
		var data model.FiatPriceData
		if err := json.Unmarshal([]byte(result), &data); err != nil {
			g.Log().Warningf(ctx, "Failed to unmarshal fiat price data: %v", err)
			continue
		}

		// 过滤时间范围
		if data.Timestamp.Before(from) || data.Timestamp.After(to) {
			continue
		}

		fiatData := &FiatPriceData{
			Asset:          data.Asset,
			Currency:       data.Currency,
			CurrencySymbol: data.CurrencySymbol,
			BuyPrice:       data.BuyPrice,
			SellPrice:      data.SellPrice,
			Provider:       data.Provider,
			Timestamp:      data.Timestamp,
			LastUpdated:    data.LastUpdated,
		}

		// 计算中间价格和价差
		if !fiatData.BuyPrice.IsZero() && !fiatData.SellPrice.IsZero() {
			fiatData.MidPrice = fiatData.BuyPrice.Add(fiatData.SellPrice).Div(decimal.NewFromInt(2))
			fiatData.Spread = fiatData.SellPrice.Sub(fiatData.BuyPrice)
			if !fiatData.MidPrice.IsZero() {
				fiatData.SpreadPercent = fiatData.Spread.Div(fiatData.MidPrice).Mul(decimal.NewFromInt(100))
			}
		}

		history = append(history, fiatData)
	}

	return history, nil
}

// SubscribePriceUpdates 订阅价格更新（使用Redis发布订阅）
func (c *PriceClient) SubscribePriceUpdates(ctx context.Context, symbols []string) (<-chan *PriceUpdateEvent, error) {
	// 这个功能需要价格监控服务支持Redis发布订阅
	// 暂时返回未实现错误
	return nil, fmt.Errorf("price subscription not implemented yet")
}
