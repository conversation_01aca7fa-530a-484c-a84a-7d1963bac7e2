package user_address

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/model/entity"
	"context"
)

// IUserAddressRepository defines the repository interface for user address operations.
type IUserAddressRepository interface {
	// List retrieves a paginated list of user addresses based on the provided criteria.
	List(ctx context.Context, req *v1.ListUserAddressesReq) (list []*v1.UserAddressListItem, total int, err error)

	// GetByID retrieves a single user address by its ID.
	GetByID(ctx context.Context, id uint) (*entity.UserAddress, error)
}
