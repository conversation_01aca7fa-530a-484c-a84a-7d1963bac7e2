package operation_log

import (
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// IOperationLogRepository defines the repository interface for operation log data operations.
type IOperationLogRepository interface {
	// List retrieves a paginated list of operation logs based on the provided conditions.
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.OperationLog, total int, err error)

	// GetByID retrieves a single operation log entry by its ID.
	GetByID(ctx context.Context, id int64) (*entity.OperationLog, error)
}
