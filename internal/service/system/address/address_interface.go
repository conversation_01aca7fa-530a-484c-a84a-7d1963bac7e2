package address

import (
	v1 "admin-api/api/system/v1"
	"context"
	"io"
)

// Constants for import task status
const (
	TaskStatusPending    = "pending"
	TaskStatusValidating = "validating"
	TaskStatusImporting  = "importing"
	TaskStatusCompleted  = "completed"
	TaskStatusFailed     = "failed"
)

// AddressRecord represents a single address record for import.
type AddressRecord struct {
	Chain   string `json:"chain"`
	Address string `json:"address"`
}

// ImportTask represents the status of an address import task.
type ImportTask struct {
	TaskId        string  `json:"taskId"`
	Status        string  `json:"status"`
	Progress      float64 `json:"progress"`
	ProcessedRows int     `json:"processedRows"`
	TotalRows     int     `json:"totalRows"`
	ErrorMessage  string  `json:"errorMessage"`
}

// IAddressRepository defines the repository interface for address operations.
type IAddressRepository interface {
	// GetStatistics retrieves statistics about bound and unbound addresses grouped by chain.
	GetStatistics(ctx context.Context) ([]*v1.AddressStatisticsItem, error)

	// ValidateAddresses checks if any addresses in the CSV file already exist in the database.
	ValidateAddresses(ctx context.Context, reader io.Reader) (bool, []string, error)

	// ImportAddresses imports addresses from a CSV file into the database.
	ImportAddresses(ctx context.Context, taskId string, records []AddressRecord) error

	// CreateImportTask creates a new import task and returns its ID.
	CreateImportTask(ctx context.Context) (string, error)

	// UpdateImportTaskStatus updates the status of an import task.
	UpdateImportTaskStatus(ctx context.Context, taskId string, status string, progress float64, processedRows int, totalRows int, errorMessage string) error

	// GetImportTaskStatus retrieves the status of an import task.
	GetImportTaskStatus(ctx context.Context, taskId string) (*ImportTask, error)
}
