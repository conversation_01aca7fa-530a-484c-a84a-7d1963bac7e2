package user_withdraw

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserWithdrawListItemDTO defines the data transfer object for a user withdraw list item.
type UserWithdrawListItemDTO struct {
	*entity.UserWithdraws
	Account  string // 用户账号
	Nickname string // 用户昵称
	Symbol   string // 币种符号
}

// UserWithdrawDetailDTO defines the data transfer object for a user withdraw detail.
type UserWithdrawDetailDTO struct {
	*entity.UserWithdraws
	Account  string // 用户账号
	Nickname string // 用户昵称
	Symbol   string // 币种符号
}

// IUserWithdrawRepository defines the repository interface for user withdraw data operations.
type IUserWithdrawRepository interface {
	// List retrieves a paginated list of user withdraws based on the provided conditions.
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*UserWithdrawListItemDTO, total int, err error)

	// ListWithAgentInfo retrieves a paginated list of user withdraws with agent and telegram info.
	ListWithAgentInfo(ctx context.Context, req *v1.ListUserWithdrawsReq) (list []*v1.UserWithdrawsListItem, total int, err error)

	// GetByID retrieves a single user withdraw entry by its ID.
	GetByID(ctx context.Context, id uint) (*entity.UserWithdraws, error)

	// GetDetailByID retrieves a user withdraw detail by its ID.
	GetDetailByID(ctx context.Context, id uint) (*UserWithdrawDetailDTO, error)

	// GetDetailWithAgentInfo retrieves a user withdraw detail by its ID with agent and telegram info.
	GetDetailWithAgentInfo(ctx context.Context, id uint) (*v1.UserWithdrawDetailItem, error)

	// UpdateState updates the state of a user withdraw.
	UpdateState(ctx context.Context, tx gdb.TX, id uint, state uint) error

	// UpdateStateWithReason updates the state of a user withdraw with a reason.
	UpdateStateWithReason(ctx context.Context, tx gdb.TX, id uint, state uint, refuseReasonZh, refuseReasonEn, adminRemark string) error

	// UpdateStateWithTxInfo updates the state of a user withdraw with transaction information.
	UpdateStateWithTxInfo(ctx context.Context, tx gdb.TX, id uint, state uint, txHash, errorMessage, adminRemark string) error
}
