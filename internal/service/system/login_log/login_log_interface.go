package login_log

import (
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// LoginLogWithUserInfo represents login log with user information
type LoginLogWithUserInfo struct {
	*entity.LoginLog
	// User information
	UserAccount string `json:"userAccount" dc:"用户账号"`
	// Agent information
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`
	// Telegram information
	TelegramId       string `json:"telegramId" dc:"Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名"`
	FirstName        string `json:"firstName" dc:"真实姓名"`
}

// ILoginLogRepository defines the interface for login log data operations.
type ILoginLogRepository interface {
	// Create inserts a new login log entry into the database.
	Create(ctx context.Context, log *entity.LoginLog) error

	// List retrieves a paginated list of login logs based on the provided conditions.
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*LoginLogWithUserInfo, total int, err error)

	// GetByID retrieves a single login log entry by its ID.
	GetByID(ctx context.Context, id int64) (*entity.LoginLog, error)
}
