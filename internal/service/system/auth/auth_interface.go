package auth

import (
	v1 "admin-api/api/system/v1" // Import v1 for request structure
	// "admin-api/internal/library/captcha" // Assuming captcha utility path
	"admin-api/internal/model/entity"
	"context"
)

// IAuthRepository defines the repository interface for authentication-related user data access.
type IAuthRepository interface {
	// FindAdminMemberByUsername retrieves an admin member by their username.
	// It should return the entity and an error if any occurs.
	// Returns nil, nil if the user is not found.
	FindAdminMemberByUsername(ctx context.Context, username string) (*entity.AdminMember, error)

	// GetAdminInfo retrieves detailed information about an admin member including roles, departments, posts and menus.
	GetAdminInfo(ctx context.Context, memberId int64) (*v1.GetAdminInfoRes, error)

	// UpdateAdminInfo updates the personal information of an admin member.
	// Returns an error if the operation fails.
	UpdateAdminInfo(ctx context.Context, memberId int64, req *v1.UpdateAdminInfoReq) error
}

// IAuthService defines the service interface for authentication operations.
type IAuthService interface {
	// VerifyPassword compares a plain password with a stored hash.
	VerifyPassword(ctx context.Context, plainPassword, hashedPassword string) bool

	// HashPassword generates a secure hash for a given password.
	HashPassword(ctx context.Context, password string) (string, error)

	// GenerateToken creates a new authentication token for the given user ID and username.
	GenerateToken(ctx context.Context, userId uint, username string) (token string, expireAt int64, err error)

	// GenerateCaptcha creates a new captcha challenge.
	// GenerateCaptcha(ctx context.Context) (*captcha.SlideData, error)

	// VerifyCaptcha validates the user's response to a captcha challenge.
	// VerifyCaptcha(ctx context.Context, token, code string) bool
}

// ILoginLogRepository defines the repository interface for login log operations.
type ILoginLogRepository interface {
	// CreateLoginLog records a new login attempt.
	CreateLoginLog(ctx context.Context, log *entity.LoginLog) error

	// List retrieves a paginated list of login logs based on the provided criteria.
	List(ctx context.Context, req *v1.GetLoginLogListReq) ([]*entity.LoginLog, int, error)

	// FindByID retrieves a single login log entry by its ID.
	FindByID(ctx context.Context, id int64) (*entity.LoginLog, error)
}
