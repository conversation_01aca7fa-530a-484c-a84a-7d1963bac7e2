package agent

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/model/entity"
	"context"
)

// IAgentRepository defines the interface for agent data persistence.
type IAgentRepository interface {
	// Create creates a new agent.
	Create(ctx context.Context, data *entity.Agents) (agentId int64, err error) // Return created ID
	// GetByID retrieves agent details by ID.
	GetByID(ctx context.Context, agentId int64) (*entity.Agents, error)
	// GetByIDs retrieves multiple agents by their IDs.
	GetByIDs(ctx context.Context, agentIds []int64) ([]*entity.Agents, error)
	// GetByUsername retrieves agent details by username.
	GetByUsername(ctx context.Context, username string) (*entity.Agents, error)
	// GetByEmail retrieves agent details by email, optionally excluding an ID.
	GetByEmail(ctx context.Context, email string, excludeAgentId ...int64) (*entity.Agents, error)
	// GetByPhoneNumber retrieves agent details by phone number, optionally excluding an ID.
	// GetByPhoneNumber(ctx context.Context, phoneNumber string, excludeAgentId ...int64) (*entity.Agents, error)
	// GetByInvitationCode retrieves agent details by invitation code.
	GetByInvitationCode(ctx context.Context, code string) (*entity.Agents, error)
	// List retrieves a list of agents based on criteria.
	List(ctx context.Context, req *v1.GetAgentListReq) (list []*entity.Agents, total int, err error)
	// ListAll retrieves all agents matching criteria, without pagination.
	ListAll(ctx context.Context, req *v1.GetAgentListReq) ([]*entity.Agents, error)
	// Update updates basic agent information.
	Update(ctx context.Context, data *entity.Agents) error
	// UpdateRelationship updates the relationship path for a specific agent.
	UpdateRelationship(ctx context.Context, agentId int64, relationship string) error
	// DeleteSoft performs soft deletion for agents by IDs.
	DeleteSoft(ctx context.Context, agentIds []int64) error
	// UpdateStatus updates the status for agents by IDs.
	UpdateStatus(ctx context.Context, agentIds []int64, status int) error
	// UpdatePassword updates the password for a specific agent.
	UpdatePassword(ctx context.Context, agentId int64, hashedPassword string) error
	// Reset2FASecret resets the 2FA secret for a specific agent.
	Reset2FASecret(ctx context.Context, agentId int64) error
	// FindDescendantIDsByRelationship finds all descendant agent IDs based on a relationship prefix.
	FindDescendantIDsByRelationship(ctx context.Context, relationshipPrefix string) ([]int64, error)
}

// IAgentIPWhitelistRepository defines the interface for agent IP whitelist persistence.
type IAgentIPWhitelistRepository interface {
	// Add adds an IP address to the agent's whitelist.
	Add(ctx context.Context, data *entity.IpAccessList) error
	// ListByAgentID retrieves the IP whitelist for a specific agent.
	ListByAgentID(ctx context.Context, agentId int64, pageReq common.PageRequest) (list []*entity.IpAccessList, total int, err error)
	// DeleteByID deletes an IP whitelist entry by its ID.
	DeleteByID(ctx context.Context, ipWhitelistId int64) error
	// GetByAgentAndIP retrieves a whitelist entry by agent ID and IP address.
	GetByAgentAndIP(ctx context.Context, agentId int64, ipAddress string) (*entity.IpAccessList, error)
	// GetByID retrieves a whitelist entry by its ID.
	GetByID(ctx context.Context, ipWhitelistId int64) (*entity.IpAccessList, error)
}

// IAgentAuthService defines the interface for agent authentication and security operations.
type IAgentAuthService interface {
	// HashPassword hashes the provided password.
	HashPassword(ctx context.Context, password string) (string, error)
	// Reset2FA handles the logic for resetting an agent's 2FA.
	Reset2FA(ctx context.Context, agentId int64) error
}
