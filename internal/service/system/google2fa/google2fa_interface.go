package google2fa

import (
	"context"
)

// IGoogle2FAService defines the interface for Google Authenticator operations.
type IGoogle2FAService interface {
	// GenerateSecret generates a new secret key for Google Authenticator.
	GenerateSecret(ctx context.Context) (secret string, err error)

	// VerifyCode verifies the code provided by the user against the stored secret.
	// Note: This might not be needed for the reset functionality but is common for 2FA.
	// VerifyCode(ctx context.Context, secret, code string) (bool, error)
}
