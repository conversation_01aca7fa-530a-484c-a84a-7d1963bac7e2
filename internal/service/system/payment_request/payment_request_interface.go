package payment_request

import (
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// PaymentRequestListItemDTO 定义了收款请求列表项的数据传输对象
type PaymentRequestListItemDTO struct {
	RequestId         int64  `json:"requestId" dc:"收款请求ID"`
	RequesterUserId   int64  `json:"requesterUserId" dc:"收款发起者用户ID"`
	RequesterUsername string `json:"requesterUsername" dc:"收款发起者用户名"`
	RequesterAccount  string `json:"requesterAccount" dc:"收款发起者账号"`
	PayerUserId       int64  `json:"payerUserId" dc:"付款人用户ID"`
	PayerUsername     string `json:"payerUsername" dc:"付款人用户名"`
	PayerAccount      string `json:"payerAccount" dc:"付款人账号"`
	TokenId           int    `json:"tokenId" dc:"代币ID"`
	TokenSymbol       string `json:"tokenSymbol" dc:"代币符号"`
	TokenName         string `json:"tokenName" dc:"代币名称"`
	Amount            string `json:"amount" dc:"收款金额"`
	Memo              string `json:"memo" dc:"收款说明/备注"`
	Status            int    `json:"status" dc:"状态 (1:待支付, 2:已支付, 3:已过期, 4:已取消)"`
	StatusText        string `json:"statusText" dc:"状态文本"` // 需要在 logic 层填充
	CreatedAt         string `json:"createdAt" dc:"创建时间"`
	ExpiresAt         string `json:"expiresAt" dc:"过期时间"`

	// 发起者代理信息
	RequesterFirstAgentName  string `json:"requesterFirstAgentName" dc:"发起者一级代理名称"`
	RequesterSecondAgentName string `json:"requesterSecondAgentName" dc:"发起者二级代理名称"`
	RequesterThirdAgentName  string `json:"requesterThirdAgentName" dc:"发起者三级代理名称"`

	// 发起者telegram信息
	RequesterTelegramId       string `json:"requesterTelegramId" dc:"发起者Telegram ID"`
	RequesterTelegramUsername string `json:"requesterTelegramUsername" dc:"发起者Telegram用户名"`
	RequesterFirstName        string `json:"requesterFirstName" dc:"发起者真实姓名"`

	// 付款人代理信息
	PayerFirstAgentName  string `json:"payerFirstAgentName" dc:"付款人一级代理名称"`
	PayerSecondAgentName string `json:"payerSecondAgentName" dc:"付款人二级代理名称"`
	PayerThirdAgentName  string `json:"payerThirdAgentName" dc:"付款人三级代理名称"`

	// 付款人telegram信息
	PayerTelegramId       string `json:"payerTelegramId" dc:"付款人Telegram ID"`
	PayerTelegramUsername string `json:"payerTelegramUsername" dc:"付款人Telegram用户名"`
	PayerFirstName        string `json:"payerFirstName" dc:"付款人真实姓名"`
}

// PaymentRequestDetailDTO 定义了收款请求详情的数据传输对象
type PaymentRequestDetailDTO struct {
	*entity.PaymentRequests
	RequesterUsername string `json:"requesterUsername" dc:"收款发起者用户名"`
	RequesterAccount  string `json:"requesterAccount" dc:"收款发起者账号"`
	PayerUsername     string `json:"payerUsername" dc:"付款人用户名"`
	PayerAccount      string `json:"payerAccount" dc:"付款人账号"`
	TokenSymbol       string `json:"tokenSymbol" dc:"代币符号"`
	TokenName         string `json:"tokenName" dc:"代币名称"`
	StatusText        string `json:"statusText" dc:"状态文本"` // 需要在 logic 层填充
	TransactionId     int64  `json:"transactionId" dc:"关联交易ID"`
	TransactionStatus int    `json:"transactionStatus" dc:"交易状态"`
	TransactionTime   string `json:"transactionTime" dc:"交易时间"`

	// 发起者代理信息
	RequesterFirstAgentName  string `json:"requesterFirstAgentName" dc:"发起者一级代理名称"`
	RequesterSecondAgentName string `json:"requesterSecondAgentName" dc:"发起者二级代理名称"`
	RequesterThirdAgentName  string `json:"requesterThirdAgentName" dc:"发起者三级代理名称"`

	// 发起者Telegram信息
	RequesterTelegramId       string `json:"requesterTelegramId" dc:"发起者Telegram ID"`
	RequesterTelegramUsername string `json:"requesterTelegramUsername" dc:"发起者Telegram用户名"`
	RequesterFirstName        string `json:"requesterFirstName" dc:"发起者真实姓名"`

	// 付款人代理信息
	PayerFirstAgentName  string `json:"payerFirstAgentName" dc:"付款人一级代理名称"`
	PayerSecondAgentName string `json:"payerSecondAgentName" dc:"付款人二级代理名称"`
	PayerThirdAgentName  string `json:"payerThirdAgentName" dc:"付款人三级代理名称"`

	// 付款人Telegram信息
	PayerTelegramId       string `json:"payerTelegramId" dc:"付款人Telegram ID"`
	PayerTelegramUsername string `json:"payerTelegramUsername" dc:"付款人Telegram用户名"`
	PayerFirstName        string `json:"payerFirstName" dc:"付款人真实姓名"`
}

// IPaymentRequestRepository 定义了收款请求数据仓库的接口
type IPaymentRequestRepository interface {
	// List 获取收款请求列表 (包含关联信息)
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*PaymentRequestListItemDTO, total int, err error)

	// GetByID 根据 ID 获取原始收款请求实体
	GetByID(ctx context.Context, requestId int64) (*entity.PaymentRequests, error)

	// GetDetailByID 根据 ID 获取收款请求详情 (包含关联信息)
	GetDetailByID(ctx context.Context, requestId int64) (*PaymentRequestDetailDTO, error)

	// UpdateStatus 更新收款请求状态
	UpdateStatus(ctx context.Context, requestId int64, status int) error

	// SoftDelete 软删除收款请求 (虽然当前 API 未直接暴露，但仓库层应提供)
	// SoftDelete(ctx context.Context, requestId int64) error // 暂时注释掉，如果需要再取消注释

	// FindOneByCondition 根据条件查找单个记录 (如果需要)
	// FindOneByCondition(ctx context.Context, condition g.Map) (*entity.PaymentRequests, error)
}
