package dashboard

import (
	v1 "admin-api/api/system/v1"
	"context"
)

// IDashboardRepository defines the repository interface for dashboard statistics operations.
type IDashboardRepository interface {
	// GetUserStats retrieves user-related statistics.
	GetUserStats(ctx context.Context) (*v1.UserStats, error)

	// GetDepositStats retrieves token deposit statistics.
	GetDepositStats(ctx context.Context) (*v1.TokenAmountStats, error)

	// GetWithdrawalStats retrieves token withdrawal statistics.
	GetWithdrawalStats(ctx context.Context) (*v1.WithdrawalStats, error)

	// GetTransferStats retrieves transfer statistics.
	GetTransferStats(ctx context.Context) (*v1.TransferStats, error)

	// GetPaymentStats retrieves payment statistics.
	GetPaymentStats(ctx context.Context) (*v1.TokenAmountStats, error)

	// GetRedPacketStats retrieves red packet statistics.
	GetRedPacketStats(ctx context.Context) (*v1.RedPacketStats, error)
}
