package api_key

import (
	v1 "admin-api/api/system/v1" // Import v1 for DTO definition
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	// 需要用于expiresAt参数
	// "github.com/gogf/gf/v2/os/gtime" // Removed unused import
)

// IApiKeyRepository 定义了 API 密钥数据仓库的接口
type IApiKeyRepository interface {
	// List 获取 API 密钥列表 (包含商户名称)
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.ApiKeyInfoType, total int, err error)

	// GetByID 获取原始 API 密钥实体
	GetByID(ctx context.Context, apiKeyId uint) (*entity.MerchantApiKeys, error)

	// GetByMerchantID 根据商户ID获取API密钥列表
	GetByMerchantID(ctx context.Context, merchantId uint) ([]*entity.MerchantApiKeys, error)

	// Update 更新 API 密钥信息 (标签, 范围, 白名单, 过期时间)
	Update(ctx context.Context, apiKeyId uint, data g.Map) error

	// UpdateStatus 更新 API 密钥状态
	UpdateStatus(ctx context.Context, apiKeyId uint, status string) error

	// DeleteSoft 软删除 API 密钥
	DeleteSoft(ctx context.Context, apiKeyIds []uint) error

	// Create 创建API密钥并返回ID
	Create(ctx context.Context, data *do.MerchantApiKeys) (uint, error)

	// CreateRaw 插入原始 API 密钥数据 (Service 层应负责生成 Key/Secret)
	CreateRaw(ctx context.Context, data *entity.MerchantApiKeys) error

	// FindByKey 根据 API Key 字符串查找记录 (用于验证)
	FindByKey(ctx context.Context, apiKey string) (*entity.MerchantApiKeys, error)

	// UpdateLastUsed 更新最后使用时间
	UpdateLastUsed(ctx context.Context, apiKeyId uint) error

	// Revoke 撤销API密钥 (实质上是将状态设为 "revoked")
	Revoke(ctx context.Context, apiKeyId uint) error
}
