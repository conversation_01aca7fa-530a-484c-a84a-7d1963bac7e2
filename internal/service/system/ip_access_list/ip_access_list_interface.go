package ip_access_list

import (
	v1 "admin-api/api/system/v1" // Import v1 for request structure
	"admin-api/internal/model/entity"
	"context"
)

// IIpAccessListRepository defines the repository interface for IP access list operations.
type IIpAccessListRepository interface {
	// List retrieves a paginated list of IP access list entries based on the provided criteria.
	List(ctx context.Context, req *v1.GetIpAccessListReq) ([]*entity.IpAccessList, int, error)

	// Create adds a new IP access list entry.
	Create(ctx context.Context, entry *entity.IpAccessList) (int64, error) // Returns the new ID

	// Delete removes an IP access list entry by its ID.
	Delete(ctx context.Context, id int64) error

	// UpdateStatus updates the enabled status of an IP access list entry.
	UpdateStatus(ctx context.Context, id int64, isEnabled int) error

	// FindByID retrieves a single IP access list entry by its ID.
	FindByID(ctx context.Context, id int64) (*entity.IpAccessList, error)

	// FindByIPAndType checks if a specific IP address already exists in a specific list type.
	FindByIPAndType(ctx context.Context, ipAddress string, listType string) (*entity.IpAccessList, error)
}
