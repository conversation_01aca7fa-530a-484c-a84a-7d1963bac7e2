package member

import (
	v1 "admin-api/api/system/v1" // Import v1 for UserInfo type
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// IMemberRepository defines the interface for admin member data operations.
type IMemberRepository interface {
	Create(ctx context.Context, member *do.AdminMember) (id int64, err error)
	UpdateFields(ctx context.Context, tx gdb.TX, id int64, data g.Map) error
	Delete(ctx context.Context, tx gdb.TX, ids []int64) error
	GetByID(ctx context.Context, id int64) (*entity.AdminMember, error)
	GetByUsername(ctx context.Context, username string) (*entity.AdminMember, error)
	ExistsByEmail(ctx context.Context, email string, excludeId ...int64) (bool, error)
	ExistsByMobile(ctx context.Context, mobile string, excludeId ...int64) (bool, error)
	ExistsByInviteCode(ctx context.Context, code string, excludeId ...int64) (bool, error)
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminMember, total int, err error)
	UpdateStatus(ctx context.Context, id int64, status int) error
	UpdatePassword(ctx context.Context, id int64, passwordHash string) error
	GetDirectSubordinatesCount(ctx context.Context, id int64) (int, error)
	FindDescendantsByTree(ctx context.Context, treePrefix string) ([]*entity.AdminMember, error)
	BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error
	// BuildMemberTree is considered a utility function for now.
	// ListForNotice retrieves a paginated list of users formatted for notice selection/status display.
	ListForNotice(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.UserInfo, total int, err error)
	// GetAllActiveUserIDs retrieves IDs of all active users.
	GetAllActiveUserIDs(ctx context.Context) ([]int64, error)
	// GetUserInfoMap retrieves a map of user IDs to UserInfo structs.
	GetUserInfoMap(ctx context.Context, ids []int64) (map[int64]*v1.UserInfo, error)
}
