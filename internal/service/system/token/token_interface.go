package token

import (
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// ITokenRepository 定义了代币数据仓库的接口
type ITokenRepository interface {
	// List 获取代币列表
	List(ctx context.Context, page, pageSize int, condition g.Map, orderBy, orderDirection string) (list []*entity.Tokens, total int, err error)

	// GetByID 根据 ID 获取代币实体
	GetByID(ctx context.Context, tokenId uint) (*entity.Tokens, error)

	// Create 创建代币
	Create(ctx context.Context, data *do.Tokens) (id int64, err error)

	// Update 更新代币信息 (部分更新)
	Update(ctx context.Context, tokenId uint, data g.Map) error

	// DeleteSoft 软删除代币
	DeleteSoft(ctx context.Context, tokenId uint) error

	// CheckExistence 检查指定条件下代币是否存在 (排除指定 ID)
	// field 可以是 "symbol" 或 "contract_address"
	CheckExistence(ctx context.Context, network string, value string, field string, excludeId ...uint) (bool, error)
}
