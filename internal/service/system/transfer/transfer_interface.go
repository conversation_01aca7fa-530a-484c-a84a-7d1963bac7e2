package transfer

import (
	v1 "admin-api/api/system/v1" // Import v1 for DTO definition
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// ITransferRepository 定义了转账记录数据仓库的接口
type ITransferRepository interface {
	// ListAdmin 获取后台转账记录列表
	// 返回包含关联信息的 DTO 列表
	ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.TransferAdminInfoItem, total int, err error)

	// GetAdminDetail 获取后台转账记录详情
	// 返回包含关联信息的 DTO
	GetAdminDetail(ctx context.Context, transferId int64) (detail *v1.TransferAdminInfoItem, err error)

	// ListAdminWithAgentInfo 获取后台转账记录列表（带代理和telegram信息）
	ListAdminWithAgentInfo(ctx context.Context, req *v1.ListAdminTransfersReq) (list []*v1.TransferAdminInfoItem, total int, err error)

	// Create (如果需要，注意事务处理)
	// Create(ctx context.Context, tx gdb.TX, data *do.Transfers) (id int64, err error)
}
