package paybot

import (
	"fmt"
)

// PayBotError PayBot错误
type PayBotError struct {
	Code       string `json:"code"`       // 错误码
	Message    string `json:"message"`    // 错误信息
	Type       string `json:"type"`       // 错误类型: network, api, business, config
	Retryable  bool   `json:"retryable"`  // 是否可重试
	StatusCode int    `json:"statusCode"` // HTTP状态码
}

func (e *PayBotError) Error() string {
	return fmt.Sprintf("PayBot Error [%s]: %s", e.Code, e.Message)
}

// 预定义错误
var (
	ErrNetworkTimeout     = &PayBotError{Code: "NETWORK_TIMEOUT", Type: "network", Retryable: true, Message: "网络超时"}
	ErrAPIKeyInvalid      = &PayBotError{Code: "API_KEY_INVALID", Type: "api", Retryable: false, Message: "API密钥无效"}
	ErrSignatureInvalid   = &PayBotError{Code: "SIGNATURE_INVALID", Type: "api", Retryable: false, Message: "签名验证失败"}
	ErrInsufficientFunds  = &PayBotError{Code: "INSUFFICIENT_FUNDS", Type: "business", Retryable: false, Message: "余额不足"}
	ErrOrderNotFound      = &PayBotError{Code: "ORDER_NOT_FOUND", Type: "business", Retryable: false, Message: "订单不存在"}
	ErrRateLimitExceeded  = &PayBotError{Code: "RATE_LIMIT_EXCEEDED", Type: "api", Retryable: true, Message: "请求频率超限"}
	ErrServiceUnavailable = &PayBotError{Code: "SERVICE_UNAVAILABLE", Type: "network", Retryable: true, Message: "服务不可用"}
	ErrInvalidParameter   = &PayBotError{Code: "INVALID_PARAMETER", Type: "business", Retryable: false, Message: "参数无效"}
	ErrUserNotFound       = &PayBotError{Code: "USER_NOT_FOUND", Type: "business", Retryable: false, Message: "用户不存在"}
	ErrDuplicateOrder     = &PayBotError{Code: "DUPLICATE_ORDER", Type: "business", Retryable: false, Message: "订单号重复"}
	ErrOrderExpired       = &PayBotError{Code: "ORDER_EXPIRED", Type: "business", Retryable: false, Message: "订单已过期"}
	ErrCallbackFailed     = &PayBotError{Code: "CALLBACK_FAILED", Type: "api", Retryable: true, Message: "回调处理失败"}
)

// NewPayBotError 创建自定义PayBot错误
func NewPayBotError(code, message, errType string, retryable bool, statusCode int) *PayBotError {
	return &PayBotError{
		Code:       code,
		Message:    message,
		Type:       errType,
		Retryable:  retryable,
		StatusCode: statusCode,
	}
}

// IsRetryableError 判断是否可重试错误
func IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	pbErr, ok := err.(*PayBotError)
	if ok {
		return pbErr.Retryable
	}

	// 默认不可重试
	return false
}

// GetErrorCode 获取错误码
func GetErrorCode(err error) string {
	if err == nil {
		return ""
	}

	pbErr, ok := err.(*PayBotError)
	if ok {
		return pbErr.Code
	}

	return "UNKNOWN_ERROR"
}