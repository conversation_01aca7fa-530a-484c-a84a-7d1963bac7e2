package paybot

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// ExampleUsage 演示如何使用PayBot服务
// 注意：这个函数应该在service包或其他包中调用，而不是在paybot包内部
func ExampleUsage(ctx context.Context, paybot IPayBot) {

	// 示例1: 创建授权支付
	authPaymentExample(ctx, paybot)

	// 示例2: 获取充值地址
	depositAddressExample(ctx, paybot)

	// 示例3: 查询提现记录
	withdrawalQueryExample(ctx, paybot)
}

func authPaymentExample(ctx context.Context, paybot IPayBot) {
	// 创建授权支付请求
	req := &CreateAuthPaymentRequest{
		UserAccount:     "user123",
		OrderType:       "deduct",
		TokenSymbol:     "USDT",
		Amount:          decimal.NewFromFloat(100.50),
		AuthReason:      "游戏充值",
		MerchantOrderNo: fmt.Sprintf("GAME_%d", time.Now().Unix()),
		ExpireMinutes:   30,
		CallbackUrl:     "https://example.com/callback",
	}

	// 调用服务
	result, err := paybot.CreateAuthPayment(ctx, req)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to create auth payment: %v", err)
		return
	}

	g.Log().Infof(ctx, "Auth payment created successfully: OrderNo=%s, Status=%s", 
		result.OrderNo, result.Status)
}

func depositAddressExample(ctx context.Context, paybot IPayBot) {
	// 获取充值地址请求
	req := &GetDepositAddressRequest{
		UserLabel: "user123",
		Chain:     "TRC20",
		Token:     "USDT",
	}

	// 调用服务
	result, err := paybot.GetDepositAddress(ctx, req)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get deposit address: %v", err)
		return
	}

	g.Log().Infof(ctx, "Deposit address: %s (IsReused: %v)", 
		result.Address, result.IsReused)
}

func withdrawalQueryExample(ctx context.Context, paybot IPayBot) {
	// 查询提现记录请求
	req := &QueryWithdrawalsRequest{
		Page:      1,
		PageSize:  10,
		Status:    "completed",
		UserLabel: "user123",
	}

	// 调用服务
	result, err := paybot.QueryWithdrawals(ctx, req)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to query withdrawals: %v", err)
		return
	}

	g.Log().Infof(ctx, "Found %d withdrawals (total: %d)", 
		len(result.Data), result.Total)
	
	// 遍历提现记录
	for _, withdrawal := range result.Data {
		g.Log().Infof(ctx, "Withdrawal: ID=%d, Amount=%s, Status=%s", 
			withdrawal.ID, withdrawal.Amount.String(), withdrawal.Status)
	}
}

// ExampleCallbackHandling 演示如何处理PayBot回调
// 注意：paybot参数应该从外部传入，通常通过service.PayBot()获取
func ExampleCallbackHandling(ctx context.Context, paybot IPayBot) {

	// 处理充值回调
	depositCallback := &DepositCallbackRequest{
		EventType:     "deposit_confirmed",
		OrderNo:       "DEPOSIT_12345",
		MerchantID:    1,
		Amount:        decimal.NewFromFloat(100),
		Currency:      "USDT",
		FromAddress:   "TRX123...",
		ToAddress:     "TRX456...",
		TxHash:        "0xabc123...",
		Confirmations: 6,
		CompletedAt:   time.Now(),
		Timestamp:     time.Now().Unix(),
		Signature:     "signature_here",
	}

	err := paybot.ProcessDepositCallback(ctx, depositCallback)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to process deposit callback: %v", err)
		return
	}

	g.Log().Info(ctx, "Deposit callback processed successfully")
}

// ExampleErrorHandling 演示错误处理
func ExampleErrorHandling(ctx context.Context, paybot IPayBot) {

	// 创建一个可能失败的请求
	req := &CreateAuthPaymentRequest{
		UserAccount:     "invalid_user",
		OrderType:       "deduct",
		TokenSymbol:     "USDT",
		Amount:          decimal.NewFromFloat(100),
		AuthReason:      "测试",
		MerchantOrderNo: "TEST_ORDER",
		ExpireMinutes:   30,
	}

	_, err := paybot.CreateAuthPayment(ctx, req)
	if err != nil {
		// 检查是否为PayBot错误
		if pbErr, ok := err.(*PayBotError); ok {
			g.Log().Errorf(ctx, "PayBot error: Code=%s, Type=%s, Retryable=%v", 
				pbErr.Code, pbErr.Type, pbErr.Retryable)
			
			// 根据错误类型处理
			switch pbErr.Code {
			case "USER_NOT_FOUND":
				// 处理用户不存在
				g.Log().Error(ctx, "User not found, please check user account")
			case "INSUFFICIENT_FUNDS":
				// 处理余额不足
				g.Log().Error(ctx, "Insufficient funds")
			default:
				// 其他错误
				g.Log().Errorf(ctx, "Other error: %v", err)
			}
		} else {
			// 非PayBot错误
			g.Log().Errorf(ctx, "System error: %v", err)
		}
	}
}