package paybot

import (
	"context"
	"fmt"
	"strings"
	"time"

	config "admin-api/internal/configv2"
	"admin-api/internal/dao"
	"admin-api/internal/utils"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
)

// SPayBot PayBot服务实现（导出类型以供boot包使用）
type SPayBot struct {
	client      *APIClient
	logger      *glog.Logger
	config      *Config
	priceClient IPriceClient
}

// New 创建PayBot服务实例
func New(config *Config, priceClient IPriceClient) *SPayBot {
	logger := g.Log("paybot")
	client := NewAPIClient(config, logger)

	return &SPayBot{
		client:      client,
		logger:      logger,
		config:      config,
		priceClient: priceClient,
	}
}

// InitPayBotService 初始化PayBot服务（应该在boot层调用）
func InitPayBotService(ctx context.Context, priceClient IPriceClient) *SPayBot {
	cfg := &Config{}

	// 从配置中心或环境变量加载配置
	if err := loadConfig(ctx, cfg); err != nil {
		g.Log().Warningf(ctx, "Failed to load PayBot config, using defaults: %v", err)
		cfg = DefaultConfig()
	}

	if cfg.Enabled {
		g.Log().Infof(ctx, "PayBot service initialized successfully with base URL: %s", cfg.API.BaseURL)
		return New(cfg, priceClient)
	} else {
		g.Log().Info(ctx, "PayBot service is disabled in configuration")
		return nil
	}
}

// loadConfig 加载配置
func loadConfig(ctx context.Context, cfg *Config) error {
	// Start with default config
	*cfg = *DefaultConfig()

	// Get config manager
	configMgr := config.GetManager()
	if configMgr == nil {
		g.Log().Warning(ctx, "Config manager not initialized, using defaults")
		return nil
	}

	// Load main enabled flag
	cfg.Enabled = configMgr.GetBool(ctx, "paybot.enabled", true)

	// Load API configuration
	cfg.API.BaseURL = configMgr.GetString(ctx, "paybot.api_base_url", "http://paybot-api:9000")
	cfg.API.APIKey = configMgr.GetString(ctx, "paybot.api_key", "")
	cfg.API.SecretHash = configMgr.GetString(ctx, "paybot.secret_hash", "")

	// Load API timeout (convert seconds to time.Duration)
	timeoutSeconds := configMgr.GetInt(ctx, "paybot.api_timeout", 30)
	cfg.API.Timeout = time.Duration(timeoutSeconds) * time.Second

	// Load sync configuration
	cfg.Sync.Enabled = configMgr.GetBool(ctx, "paybot.sync_enabled", true)
	cfg.Sync.BatchSize = configMgr.GetInt(ctx, "paybot.sync_batch_size", 100)

	// Load retry configuration
	cfg.Retry.MaxRetries = configMgr.GetInt(ctx, "paybot.retry_max_attempts", 3)

	// Load retry backoff (convert seconds to time.Duration)
	backoffSeconds := configMgr.GetInt(ctx, "paybot.retry_backoff_seconds", 1)
	cfg.Retry.Backoff = time.Duration(backoffSeconds) * time.Second

	// Set max backoff based on backoff value
	cfg.Retry.MaxBackoff = cfg.Retry.Backoff * 30

	// Load features configuration
	cfg.Features.AuthPayment = configMgr.GetBool(ctx, "paybot.features_auth_payment", true)

	cfg.Features.Withdrawals = configMgr.GetBool(ctx, "paybot.features_withdrawals", true)

	// Load deposits and callbacks features (using default values)
	cfg.Features.Deposits = configMgr.GetBool(ctx, "paybot.features_deposits", true)
	cfg.Features.Callbacks = configMgr.GetBool(ctx, "paybot.features_callbacks", true)

	// g.Log().Infof(ctx, "PayBot config loaded: enabled=%v, apiBaseURL=%s, syncEnabled=%v, deposits=%v, apiKey=%s, secretHash=%s",
	// 	cfg.Enabled, cfg.API.BaseURL, cfg.Sync.Enabled, cfg.Features.Deposits,
	// 	cfg.API.APIKey, cfg.API.SecretHash)

	return nil
}

// CreateAuthPayment 创建授权支付
func (s *SPayBot) CreateAuthPayment(ctx context.Context, req *CreateAuthPaymentRequest) (*AuthPaymentResult, error) {
	if !s.config.Features.AuthPayment {
		return nil, fmt.Errorf("auth payment feature is disabled")
	}

	// 验证用户 - 如果提供了UserID，直接使用；否则通过account查询
	var user gdb.Record
	var err error

	if req.UserID > 0 {
		// 直接通过UserID查询
		user, err = dao.Users.Ctx(ctx).Where("id", req.UserID).One()
		if err != nil {
			return nil, fmt.Errorf("failed to query user by ID: %w", err)
		}
	} else {
		// 通过account查询（兼容性）
		user, err = dao.Users.Ctx(ctx).Where("account", req.UserAccount).One()
		if err != nil {
			return nil, fmt.Errorf("failed to query user by account: %w", err)
		}
	}

	if user.IsEmpty() {
		return nil, ErrUserNotFound
	}

	// 检查是否有重复订单
	existingOrder, err := dao.PaybotAuthOrders.Ctx(ctx).
		Where("merchant_order_no", req.MerchantOrderNo).
		One()
	if err != nil {
		return nil, fmt.Errorf("failed to check existing order: %w", err)
	}
	if !existingOrder.IsEmpty() {
		return nil, ErrDuplicateOrder
	}

	// 调用PayBot API
	result, err := s.client.CreateAuthPayment(ctx, req)
	if err != nil {
		s.logger.Errorf(ctx, "Failed to create auth payment: %v", err)
		return nil, err
	}

	// 设置回调状态默认值
	callbackStatus := result.CallbackStatus
	if callbackStatus == "" {
		callbackStatus = "pending"
	}

	// 保存订单到数据库
	err = dao.PaybotAuthOrders.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.PaybotAuthOrders.Ctx(ctx).TX(tx).Insert(g.Map{
			"paybot_order_no":   result.OrderNo,
			"merchant_order_no": result.MerchantOrderNo,
			"user_id":           user["id"],
			"user_account":      result.UserAccount,
			"order_type":        result.OrderType,
			"token_symbol":      result.TokenSymbol,
			"amount":            result.Amount.String(),
			"auth_reason":       result.AuthReason,
			"status":            result.Status,
			"callback_status":   callbackStatus,
			"expire_at":         result.ExpireAt,
			"created_at":        result.CreatedAt,
		})
		return err
	})

	if err != nil {
		s.logger.Errorf(ctx, "Failed to save auth order: %v", err)
		return nil, fmt.Errorf("failed to save auth order: %w", err)
	}

	return result, nil
}

// QueryAuthPayment 查询授权支付
func (s *SPayBot) QueryAuthPayment(ctx context.Context, req *QueryAuthPaymentRequest) (*AuthPaymentResult, error) {
	if !s.config.Features.AuthPayment {
		return nil, fmt.Errorf("auth payment feature is disabled")
	}

	// 调用API查询
	result, err := s.client.QueryAuthPayment(ctx, req)
	if err != nil {
		return nil, err
	}

	// 只更新非关键状态字段，不要自动更新status为completed
	// completed状态应该由业务处理逻辑负责，确保所有相关操作都被执行
	updateData := g.Map{
		"callback_status": result.CallbackStatus,
		"updated_at":      time.Now(),
	}

	// 只有非completed状态才自动更新，completed状态交由业务逻辑处理
	if result.Status != "completed" {
		updateData["status"] = result.Status
		if result.CompletedAt != nil {
			updateData["completed_at"] = result.CompletedAt
		}
	}

	if result.ErrorMessage != "" {
		updateData["error_message"] = result.ErrorMessage
	}

	condition := g.Map{}
	if req.OrderNo != "" {
		condition["order_no"] = req.OrderNo
	}
	if req.MerchantOrderNo != "" {
		condition["merchant_order_no"] = req.MerchantOrderNo
	}

	if len(condition) > 0 {
		_, err = dao.PaybotAuthOrders.Ctx(ctx).Where(condition).Update(updateData)
		if err != nil {
			s.logger.Warningf(ctx, "Failed to update auth order status: %v", err)
		}
	}

	return result, nil
}

// ListAuthPayments 列出授权支付
func (s *SPayBot) ListAuthPayments(ctx context.Context, req *ListAuthPaymentsRequest) (*AuthPaymentListResult, error) {
	if !s.config.Features.AuthPayment {
		return nil, fmt.Errorf("auth payment feature is disabled")
	}

	return s.client.ListAuthPayments(ctx, req)
}

// GetDepositAddress 获取充值地址
func (s *SPayBot) GetDepositAddress(ctx context.Context, req *GetDepositAddressRequest) (*DepositAddressResult, error) {
	if !s.config.Features.Deposits {
		return nil, fmt.Errorf("deposits feature is disabled")
	}

	// 验证用户
	user, err := dao.Users.Ctx(ctx).Where("account", req.UserLabel).One()
	if err != nil {
		return nil, fmt.Errorf("failed to query user: %w", err)
	}
	if user.IsEmpty() {
		return nil, ErrUserNotFound
	}

	// 检查是否已有地址
	existingAddress, err := dao.UserAddress.Ctx(ctx).
		Where("user_id", user["id"]).
		Where("chan", req.Chain).
		Where("name", req.Token).
		Where("status", "active").
		One()

	if err != nil {
		return nil, fmt.Errorf("failed to check existing address: %w", err)
	}

	// 如果已有地址且可复用，直接返回
	if !existingAddress.IsEmpty() && existingAddress["is_reused"].Bool() {
		return &DepositAddressResult{
			Address:   existingAddress["address"].String(),
			UserLabel: req.UserLabel,
			Chain:     req.Chain,
			Token:     req.Token,
			IsReused:  true,
			QRCode:    existingAddress["qr_code_data"].String(),
			QRCodeURL: existingAddress["qr_code_url"].String(),
			CreatedAt: PayBotTime{Time: existingAddress["created_at"].Time()},
		}, nil
	}

	// 调用PayBot API获取新地址
	result, err := s.client.GetDepositAddress(ctx, req)
	if err != nil {
		return nil, err
	}

	// 保存地址到数据库
	err = dao.UserAddress.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 将旧地址标记为inactive
		if !existingAddress.IsEmpty() {
			_, err := dao.UserAddress.Ctx(ctx).TX(tx).
				Where("user_address_id", existingAddress["user_address_id"]).
				Update(g.Map{
					"status":     "inactive",
					"updated_at": time.Now(),
				})
			if err != nil {
				return err
			}
		}

		// 插入新地址
		_, err := dao.UserAddress.Ctx(ctx).TX(tx).Insert(g.Map{
			"user_id":           user["id"],
			"chan":              result.Chain,
			"name":              result.Token,
			"address":           result.Address,
			"lable":             fmt.Sprintf("PayBot %s %s", result.Chain, result.Token),
			"type":              "deposit",
			"paybot_address_id": fmt.Sprintf("%s_%s_%s", req.UserLabel, req.Chain, req.Token),
			"is_reused":         result.IsReused,
			"qr_code_data":      result.QRCode,
			"qr_code_url":       result.QRCodeURL,
			"status":            "active",
			"deposit_count":     0,
			"created_at":        result.CreatedAt.Time,
		})
		return err
	})

	if err != nil {
		s.logger.Errorf(ctx, "Failed to save deposit address: %v", err)
		return nil, fmt.Errorf("failed to save deposit address: %w", err)
	}

	return result, nil
}

// QueryDepositRecords 查询充值记录
func (s *SPayBot) QueryDepositRecords(ctx context.Context, req *QueryDepositsRequest) (*DepositListResult, error) {
	if !s.config.Features.Deposits {
		return nil, fmt.Errorf("deposits feature is disabled")
	}

	return s.client.QueryDepositRecords(ctx, req)
}

// GetDepositDetail 获取充值详情
func (s *SPayBot) GetDepositDetail(ctx context.Context, req *GetDepositDetailRequest) (*DepositDetailResult, error) {
	if !s.config.Features.Deposits {
		return nil, fmt.Errorf("deposits feature is disabled")
	}

	return s.client.GetDepositDetail(ctx, req)
}

// ListDepositAddresses 列出充值地址
func (s *SPayBot) ListDepositAddresses(ctx context.Context, req *ListDepositAddressesRequest) (*DepositAddressListResult, error) {
	if !s.config.Features.Deposits {
		return nil, fmt.Errorf("deposits feature is disabled")
	}

	return s.client.ListDepositAddresses(ctx, req)
}

// QueryTransactions 查询交易记录
func (s *SPayBot) QueryTransactions(ctx context.Context, req *QueryTransactionsRequest) (*TransactionListResult, error) {
	return s.client.QueryTransactions(ctx, req)
}

// GetTransactionDetail 获取交易详情
func (s *SPayBot) GetTransactionDetail(ctx context.Context, req *GetTransactionDetailRequest) (*TransactionDetailResult, error) {
	return s.client.GetTransactionDetail(ctx, req)
}

// QueryWithdrawals 查询提现记录
func (s *SPayBot) QueryWithdrawals(ctx context.Context, req *QueryWithdrawalsRequest) (*WithdrawalListResult, error) {
	if !s.config.Features.Withdrawals {
		return nil, fmt.Errorf("withdrawals feature is disabled")
	}

	return s.client.QueryWithdrawals(ctx, req)
}

// GetWithdrawalDetail 获取提现详情
func (s *SPayBot) GetWithdrawalDetail(ctx context.Context, req *GetWithdrawalDetailRequest) (*WithdrawalDetailResult, error) {
	if !s.config.Features.Withdrawals {
		return nil, fmt.Errorf("withdrawals feature is disabled")
	}

	return s.client.GetWithdrawalDetail(ctx, req)
}

// ProcessDepositCallback 处理充值回调
func (s *SPayBot) ProcessDepositCallback(ctx context.Context, req *DepositCallbackRequest) error {
	if !s.config.Features.Callbacks {
		return fmt.Errorf("callbacks feature is disabled")
	}

	// 验证签名
	if !s.verifyCallbackSignature(req.Timestamp, req.Signature, req) {
		return fmt.Errorf("invalid callback signature")
	}

	// 查找用户地址
	userAddress, err := dao.UserAddress.Ctx(ctx).
		Where("address", req.ToAddress).
		Where("status", "active").
		One()

	if err != nil {
		return fmt.Errorf("failed to query user address: %w", err)
	}
	if userAddress.IsEmpty() {
		return fmt.Errorf("user address not found: %s", req.ToAddress)
	}

	// 保存回调记录
	err = dao.PaybotCallbacks.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入回调记录
		_, err := dao.PaybotCallbacks.Ctx(ctx).TX(tx).Insert(g.Map{
			"callback_type":  "deposit",
			"event_type":     req.EventType,
			"order_no":       req.OrderNo,
			"merchant_id":    req.MerchantID,
			"user_id":        userAddress["user_id"],
			"amount":         req.Amount.String(),
			"currency":       req.Currency,
			"tx_hash":        req.TxHash,
			"raw_data":       gconv.String(req),
			"signature":      req.Signature,
			"process_status": "processing",
			"created_at":     time.Now(),
		})
		if err != nil {
			return err
		}

		// 更新用户钱包余额
		// 查找用户的USDT钱包
		wallet, err := dao.Wallets.Ctx(ctx).TX(tx).
			Where("user_id", userAddress["user_id"]).
			Where("symbol", req.Currency). // 假设Currency就是代币符号
			One()
		if err != nil {
			return err
		}

		if wallet.IsEmpty() {
			// 如果钱包不存在，创建新钱包
			_, err = dao.Wallets.Ctx(ctx).TX(tx).Insert(g.Map{
				"user_id":           userAddress["user_id"],
				"symbol":            req.Currency,
				"available_balance": req.Amount.Mul(decimal.NewFromInt(1000000)).IntPart(), // 转换为最小单位
				"frozen_balance":    0,
				"decimal_places":    6, // USDT默认6位小数
				"created_at":        time.Now(),
			})
			if err != nil {
				return err
			}
		} else {
			// 更新现有钱包余额
			currentBalance := wallet["available_balance"].Int64()
			amountInMinUnit := req.Amount.Mul(decimal.NewFromInt(1000000)).IntPart()
			newBalance := currentBalance + amountInMinUnit

			_, err = dao.Wallets.Ctx(ctx).TX(tx).
				Where("wallet_id", wallet["wallet_id"]).
				Update(g.Map{
					"available_balance": newBalance,
					"updated_at":        time.Now(),
				})
			if err != nil {
				return err
			}
		}

		// 更新地址统计
		_, err = dao.UserAddress.Ctx(ctx).TX(tx).
			Where("user_address_id", userAddress["user_address_id"]).
			Update(g.Map{
				"deposit_count":   gdb.Raw("deposit_count + 1"),
				"last_deposit_at": req.CompletedAt,
				"updated_at":      time.Now(),
			})
		if err != nil {
			return err
		}

		// 更新回调状态
		_, err = dao.PaybotCallbacks.Ctx(ctx).TX(tx).
			Where("order_no", req.OrderNo).
			Update(g.Map{
				"process_status": "completed",
				"processed_at":   time.Now(),
			})
		return err
	})

	if err != nil {
		s.logger.Errorf(ctx, "Failed to process deposit callback: %v", err)
		return err
	}

	return nil
}

// ProcessWithdrawalCallback 处理提现回调
func (s *SPayBot) ProcessWithdrawalCallback(ctx context.Context, req *WithdrawalCallbackRequest) error {
	if !s.config.Features.Callbacks {
		return fmt.Errorf("callbacks feature is disabled")
	}

	// 验证签名
	if !s.verifyCallbackSignature(req.Timestamp, req.Signature, req) {
		return fmt.Errorf("invalid callback signature")
	}

	// 查找提现记录
	withdraw, err := dao.UserWithdraws.Ctx(ctx).
		Where("paybot_order_no", req.OrderNo).
		One()

	if err != nil {
		return fmt.Errorf("failed to query withdrawal: %w", err)
	}
	if withdraw.IsEmpty() {
		return fmt.Errorf("withdrawal not found: %s", req.OrderNo)
	}

	// 保存回调记录并更新提现状态
	err = dao.PaybotCallbacks.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入回调记录
		_, err := dao.PaybotCallbacks.Ctx(ctx).TX(tx).Insert(g.Map{
			"callback_type":  "withdrawal",
			"event_type":     req.EventType,
			"order_no":       req.OrderNo,
			"merchant_id":    req.MerchantID,
			"user_id":        withdraw["user_id"],
			"amount":         req.Amount.String(),
			"currency":       req.Currency,
			"tx_hash":        req.TxHash,
			"raw_data":       gconv.String(req),
			"signature":      req.Signature,
			"process_status": "processing",
			"created_at":     time.Now(),
		})
		if err != nil {
			return err
		}

		// 更新提现记录
		updateData := g.Map{
			"state":              4, // 4-已完成(Completed)
			"actual_amount":      req.ActualAmount.String(),
			"handling_fee":       req.HandlingFee.String(),
			"tx_hash":            req.TxHash,
			"paybot_sync_status": "synced",
			"paybot_sync_at":     time.Now(),
			"completed_at":       req.CompletedAt,
			"updated_at":         time.Now(),
		}

		_, err = dao.UserWithdraws.Ctx(ctx).TX(tx).
			Where("user_withdraws_id", withdraw["user_withdraws_id"]).
			Update(updateData)
		if err != nil {
			return err
		}

		// 更新回调状态
		_, err = dao.PaybotCallbacks.Ctx(ctx).TX(tx).
			Where("order_no", req.OrderNo).
			Update(g.Map{
				"process_status": "completed",
				"processed_at":   time.Now(),
			})
		return err
	})

	if err != nil {
		s.logger.Errorf(ctx, "Failed to process withdrawal callback: %v", err)
		return err
	}

	return nil
}

// HealthCheck 健康检查
func (s *SPayBot) HealthCheck(ctx context.Context) error {
	return s.client.HealthCheck(ctx)
}

// verifyCallbackSignature 验证回调签名
func (s *SPayBot) verifyCallbackSignature(timestamp int64, signature string, data interface{}) bool {
	// TODO: 实现签名验证逻辑
	// 这里需要根据PayBot的签名规则实现
	return true
}

// IsSyncEnabled 检查是否启用同步功能
func (s *SPayBot) IsSyncEnabled() bool {
	return s.config.Enabled && s.config.Sync.Enabled
}

// GenerateTelegramLink generates a Telegram deep link for auth payment orders
func (s *SPayBot) GenerateTelegramLink(ctx context.Context, orderNo string) string {
	// Get bot name from configuration
	botName := g.Cfg().MustGet(ctx, "telegram.botName").String()
	if botName == "" {
		g.Log().Warning(ctx, "Telegram bot name not configured")
		return ""
	}

	// Build deep link
	// Format: https://t.me/{botName}?start={orderNo}
	deepLink := fmt.Sprintf("https://t.me/%s?start=%s",
		botName,
		orderNo)

	return deepLink
}

// ProcessOkpayAuthCallback processes Okpay authorization payment callbacks
func (s *SPayBot) ProcessOkpayAuthCallback(ctx context.Context, userID int64, merchantOrderNo string) (*OkpayCallbackResult, error) {
	g.Log().Infof(ctx, "Processing Okpay auth callback for user %d, order: %s", userID, merchantOrderNo)

	// Check if PayBot service is enabled
	if !s.config.Enabled || !s.config.Features.AuthPayment {
		g.Log().Warningf(ctx, "PayBot auth payment feature is disabled for user %d", userID)
		return &OkpayCallbackResult{
			Success: false,
			Message: "服务暂时不可用，请稍后再试",
		}, nil
	}

	// Step 1: Query local order status
	localOrder, err := dao.PaybotAuthOrders.Ctx(ctx).
		Where(dao.PaybotAuthOrders.Columns().MerchantOrderNo, merchantOrderNo).
		One()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to query local order %s: %v", merchantOrderNo, err)
		return &OkpayCallbackResult{
			Success: false,
			Message: "系统繁忙，请稍后再试",
		}, nil
	}

	if localOrder.IsEmpty() {
		g.Log().Warningf(ctx, "Order %s not found", merchantOrderNo)
		return &OkpayCallbackResult{
			Success: false,
			Message: "订单不存在或已失效",
		}, nil
	}

	// Check if order belongs to the current user (userID is telegram ID, need to convert to database user ID)
	// Use backup account table to find the user
	backupAccount, err := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().TelegramId, userID).
		One()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to query backup account by telegram ID %d: %v", userID, err)
		return &OkpayCallbackResult{
			Success: false,
			Message: "系统繁忙，请稍后再试",
		}, nil
	}
	if backupAccount.IsEmpty() {
		g.Log().Warningf(ctx, "Backup account not found for telegram ID %d", userID)
		return &OkpayCallbackResult{
			Success: false,
			Message: "用户信息错误",
		}, nil
	}

	dbUserID := backupAccount["user_id"].Int64()
	if localOrder["user_id"].Int64() != dbUserID {
		g.Log().Warningf(ctx, "Order %s does not belong to user %d (db_user_id: %d)", merchantOrderNo, userID, dbUserID)
		return &OkpayCallbackResult{
			Success: false,
			Message: "订单信息错误",
		}, nil
	}

	localStatus := localOrder["status"].String()
	g.Log().Infof(ctx, "Local order %s status: %s", merchantOrderNo, localStatus)

	// Step 2: If local status is not pending, handle accordingly
	if localStatus != "pending" {
		return s.handleNonPendingStatus(ctx, localOrder)
	}

	// Step 3: Query remote order status
	remoteResult, err := s.QueryAuthPayment(ctx, &QueryAuthPaymentRequest{
		MerchantOrderNo: merchantOrderNo,
	})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to query remote order %s: %v", merchantOrderNo, err)
		return &OkpayCallbackResult{
			Success: false,
			Message: "查询订单状态失败，请稍后再试",
		}, nil
	}

	// Step 4: Handle different remote status
	return s.handleRemoteOrderStatus(ctx, localOrder, remoteResult)
}

// handleNonPendingStatus handles orders that are not in pending status
func (s *SPayBot) handleNonPendingStatus(ctx context.Context, localOrder gdb.Record) (*OkpayCallbackResult, error) {
	status := localOrder["status"].String()
	amount := localOrder["amount"].String()
	currency := localOrder["token_symbol"].String()

	switch status {
	case "completed":
		// Parse amount to clean up decimal precision
		amountDecimal, err := decimal.NewFromString(amount)
		if err != nil {
			amountDecimal = decimal.Zero
		}
		cleanAmount := amountDecimal.String()

		return &OkpayCallbackResult{
			Success:     false, // Changed to false to avoid confusion
			Message:     fmt.Sprintf("🟡 此订单已完成\n💰 金额: %s %s", cleanAmount, currency),
			OrderStatus: status,
			Amount:      cleanAmount,
			Currency:    currency,
		}, nil
	case "failed":
		errorMsg := localOrder["error_message"].String()
		if errorMsg == "" {
			errorMsg = "未知错误"
		}
		return &OkpayCallbackResult{
			Success:     false,
			Message:     fmt.Sprintf("❌ 此订单已失败\n原因: %s", errorMsg),
			OrderStatus: status,
		}, nil
	case "expired":
		return &OkpayCallbackResult{
			Success:     false,
			Message:     "⏰ 此订单已过期，请重新发起充值",
			OrderStatus: status,
		}, nil
	case "cancelled":
		return &OkpayCallbackResult{
			Success:     false,
			Message:     "🚫 此订单已取消，请重新发起充值",
			OrderStatus: status,
		}, nil
	default:
		return &OkpayCallbackResult{
			Success: false,
			Message: "🔍 订单状态异常，请联系客服处理",
		}, nil
	}
}

// handleRemoteOrderStatus handles different remote order statuses
func (s *SPayBot) handleRemoteOrderStatus(ctx context.Context, localOrder gdb.Record, remoteResult *AuthPaymentResult) (*OkpayCallbackResult, error) {
	remoteStatus := remoteResult.Status
	g.Log().Infof(ctx, "Remote order %s status: %s", remoteResult.MerchantOrderNo, remoteStatus)

	switch remoteStatus {
	case "pending":
		return &OkpayCallbackResult{
			Success: false,
			Message: "⏳ 订单处理中，请稍后再试",
		}, nil
	case "completed":
		// Process completed order with complex transaction logic
		return s.processCompletedOrder(ctx, localOrder, remoteResult)
	case "failed":
		// Update local status and return error message
		return s.processFailedOrder(ctx, localOrder, remoteResult)
	case "expired":
		// Update local status
		return s.processExpiredOrder(ctx, localOrder)
	case "cancelled":
		// Update local status
		return s.processCancelledOrder(ctx, localOrder)
	default:
		g.Log().Warningf(ctx, "Unknown remote order status: %s for order %s", remoteStatus, remoteResult.MerchantOrderNo)
		return &OkpayCallbackResult{
			Success: false,
			Message: "订单状态异常，请联系客服处理",
		}, nil
	}
}

// processCompletedOrder processes completed orders with full transaction logic
func (s *SPayBot) processCompletedOrder(ctx context.Context, localOrder gdb.Record, remoteResult *AuthPaymentResult) (*OkpayCallbackResult, error) {
	merchantOrderNo := localOrder["merchant_order_no"].String()
	userID := localOrder["user_id"].Int64()
	amount := remoteResult.Amount
	currency := remoteResult.TokenSymbol

	g.Log().Infof(ctx, "Processing completed order %s for user %d: %s %s",
		merchantOrderNo, userID, amount.String(), currency)

	// Step 1: Use Redis distributed lock to ensure only one process handles this order
	lockKey := fmt.Sprintf("okpay_callback_%s", merchantOrderNo)
	lockTTL := 30 * time.Second

	// Get Redis instance
	redisInstance := g.Redis()
	if redisInstance == nil {
		g.Log().Errorf(ctx, "Redis instance not available for order %s", merchantOrderNo)
		return &OkpayCallbackResult{
			Success: false,
			Message: "系统繁忙，请稍后再试",
		}, nil
	}

	// Execute with distributed lock
	err := utils.WithLock(ctx, redisInstance, lockKey, lockTTL, func() error {
		return s.processOrderInTransaction(ctx, localOrder, remoteResult)
	})

	if err != nil {
		g.Log().Errorf(ctx, "Failed to process completed order %s: %v", merchantOrderNo, err)

		// Check if it's a lock acquisition error
		if fmt.Sprintf("%v", err) == "lock is already held by another process" ||
			strings.Contains(fmt.Sprintf("%v", err), "failed to acquire lock") {
			return &OkpayCallbackResult{
				Success: false,
				Message: "订单正在处理中，请稍后再试",
			}, nil
		}

		return &OkpayCallbackResult{
			Success: false,
			Message: "充值处理失败，请联系客服",
		}, nil
	}

	// Success - return result showing converted fiat amount
	// Get the conversion variables that were calculated in processOrderInTransaction
	var displayAmount decimal.Decimal
	var displayCurrency string
	var conversionNote string

	if currency == "USDT" {
		// Get fiat symbol for display
		fiatSymbol, _ := config.GetString(ctx, "recharges_setting.fiat_symbol", "CNY")
		fiatPrice, err := s.priceClient.GetFiatPrice(ctx, "USDT", fiatSymbol)
		if err == nil {
			exchangeRate := fiatPrice.MidPrice
			displayAmount = amount.Mul(exchangeRate)
			displayCurrency = fiatSymbol
			conversionNote = fmt.Sprintf(" (原始: %s USDT)", amount.String())
		} else {
			// Fallback to original values if conversion fails
			displayAmount = amount
			displayCurrency = currency
			conversionNote = ""
		}
	} else {
		displayAmount = amount
		displayCurrency = currency
		conversionNote = ""
	}

	// Check if user has a reward for this deposit
	var needsRewardNotification bool
	var rewardNotificationMsg string

	// Get user info for the reward check
	user, err := dao.Users.Ctx(ctx).Where("id", userID).One()
	if err == nil && !user.IsEmpty() {
		// Check for deposit reward
		depositReward, err := dao.DepositRewards.Ctx(ctx).
			Where("user_id", userID).
			Where("deposit_amount", displayAmount).
			Order("id DESC").
			One()
		if err == nil && !depositReward.IsEmpty() {
			rewardAmount := depositReward["reward_amount"].String()
			rewardPercentage := depositReward["reward_percentage"].Float64()
			requiredTurnover := depositReward["required_turnover"].String()
			flowMultiplier := depositReward["flow_multiplier"].Float64()

			// Parse decimal values for formatting
			rewardAmountDec, _ := decimal.NewFromString(rewardAmount)
			requiredTurnoverDec, _ := decimal.NewFromString(requiredTurnover)

			// Get current total turnover requirement
			currentTurnover := decimal.NewFromFloat(user["withdraw_betting_volume"].Float64())

			// Only send notification if there's a reward or meaningful turnover requirement
			// Skip notification if both reward and flow multiplier are 0
			if rewardPercentage > 0 || (flowMultiplier > 0 && requiredTurnoverDec.GreaterThan(decimal.Zero)) {
				// Build separate reward notification message
				// Check if this is a 0% reward (turnover requirement only)
				if rewardPercentage == 0 {
					rewardNotificationMsg = fmt.Sprintf(
						"📊 充值流水要求通知\n\n"+
							"🔄 产生流水要求: %s %s (%.1fx)\n"+
							"💧 当前总流水要求: %s %s",
						requiredTurnoverDec.StringFixed(2), displayCurrency, flowMultiplier,
						currentTurnover.StringFixed(2), displayCurrency)
				} else {
					rewardNotificationMsg = fmt.Sprintf(
						"🎁 存款奖励已到账\n\n"+
							"💰 奖励金额: %s %s\n"+
							"📊 奖励比例: %.1f%%\n"+
							"🔄 产生流水要求: %s %s (%.1fx)\n"+
							"💧 当前总流水要求: %s %s",
						rewardAmountDec.StringFixed(2), displayCurrency,
						rewardPercentage,
						requiredTurnoverDec.StringFixed(2), displayCurrency, flowMultiplier,
						currentTurnover.StringFixed(2), displayCurrency)
				}

				needsRewardNotification = true
			}
		}
	}

	return &OkpayCallbackResult{
		Success:     true,
		Message:     fmt.Sprintf("✅ +%s %s 充值到账%s @okpay", displayAmount.StringFixed(2), displayCurrency, conversionNote),
		OrderStatus: "completed",
		Amount:      displayAmount.String(),
		Currency:    displayCurrency,
		NeedsNotify: needsRewardNotification,
		NotifyMsg:   rewardNotificationMsg,
	}, nil
}

// processFailedOrder processes failed orders
func (s *SPayBot) processFailedOrder(ctx context.Context, localOrder gdb.Record, remoteResult *AuthPaymentResult) (*OkpayCallbackResult, error) {
	merchantOrderNo := localOrder["merchant_order_no"].String()

	// Update local order status
	_, err := dao.PaybotAuthOrders.Ctx(ctx).
		Where(dao.PaybotAuthOrders.Columns().MerchantOrderNo, merchantOrderNo).
		Update(g.Map{
			"status":        "failed",
			"error_message": remoteResult.ErrorMessage,
			"updated_at":    time.Now(),
		})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to update failed order %s: %v", merchantOrderNo, err)
	}

	errorMsg := remoteResult.ErrorMessage
	if errorMsg == "" {
		errorMsg = "未知错误"
	}

	return &OkpayCallbackResult{
		Success:     false,
		Message:     fmt.Sprintf("❌ 充值失败\n原因: %s", errorMsg),
		OrderStatus: "failed",
	}, nil
}

// processExpiredOrder processes expired orders
func (s *SPayBot) processExpiredOrder(ctx context.Context, localOrder gdb.Record) (*OkpayCallbackResult, error) {
	merchantOrderNo := localOrder["merchant_order_no"].String()

	// Update local order status
	_, err := dao.PaybotAuthOrders.Ctx(ctx).
		Where(dao.PaybotAuthOrders.Columns().MerchantOrderNo, merchantOrderNo).
		Update(g.Map{
			"status":     "expired",
			"updated_at": time.Now(),
		})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to update expired order %s: %v", merchantOrderNo, err)
	}

	return &OkpayCallbackResult{
		Success:     false,
		Message:     "⏰ 订单已过期，请重新发起充值",
		OrderStatus: "expired",
	}, nil
}

// processCancelledOrder processes cancelled orders
func (s *SPayBot) processCancelledOrder(ctx context.Context, localOrder gdb.Record) (*OkpayCallbackResult, error) {
	merchantOrderNo := localOrder["merchant_order_no"].String()

	// Update local order status
	_, err := dao.PaybotAuthOrders.Ctx(ctx).
		Where(dao.PaybotAuthOrders.Columns().MerchantOrderNo, merchantOrderNo).
		Update(g.Map{
			"status":     "cancelled",
			"updated_at": time.Now(),
		})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to update cancelled order %s: %v", merchantOrderNo, err)
	}

	return &OkpayCallbackResult{
		Success:     false,
		Message:     "🚫 订单已取消，请重新发起充值",
		OrderStatus: "cancelled",
	}, nil
}

// processOrderInTransaction handles the complete transaction logic for completed orders
func (s *SPayBot) processOrderInTransaction(ctx context.Context, localOrder gdb.Record, remoteResult *AuthPaymentResult) error {
	merchantOrderNo := localOrder["merchant_order_no"].String()
	userID := localOrder["user_id"].Int64()
	amount := remoteResult.Amount
	currency := remoteResult.TokenSymbol

	g.Log().Infof(ctx, "Starting transaction processing for order %s, user %d, amount %s %s",
		merchantOrderNo, userID, amount.String(), currency)

	// Currency conversion: Convert USDT to system fiat currency
	var fiatAmount decimal.Decimal
	var fiatCurrency string
	var exchangeRate decimal.Decimal
	var conversionMemo string

	if currency == "USDT" {
		// Get fiat symbol from config
		fiatSymbol, err := config.GetString(ctx, "recharges_setting.fiat_symbol", "CNY")
		if err != nil {
			g.Log().Warningf(ctx, "Failed to get fiat symbol from config: %v, using default CNY", err)
			fiatSymbol = "CNY"
		}

		// Use injected price client
		fiatPrice, err := s.priceClient.GetFiatPrice(ctx, "USDT", fiatSymbol)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get fiat price for USDT/%s: %v", fiatSymbol, err)
			return fmt.Errorf("failed to get exchange rate for currency conversion: %v", err)
		}

		// Use mid-price for conversion
		exchangeRate = fiatPrice.MidPrice
		fiatAmount = amount.Mul(exchangeRate)
		fiatCurrency = fiatSymbol

		// Create detailed conversion memo
		conversionMemo = fmt.Sprintf("Okpay授权支付 %s (原始金额: %s USDT, 汇率: 1 USDT = %s %s)",
			remoteResult.OrderNo, amount.String(), exchangeRate.StringFixed(4), fiatSymbol)

		g.Log().Infof(ctx, "Currency conversion for order %s: %s USDT -> %s %s (rate: %s)",
			merchantOrderNo, amount.String(), fiatAmount.StringFixed(2), fiatCurrency, exchangeRate.StringFixed(4))
	} else {
		// No conversion needed for non-USDT currencies
		fiatAmount = amount
		fiatCurrency = currency
		exchangeRate = decimal.NewFromInt(1)
		conversionMemo = fmt.Sprintf("Okpay授权支付 %s", remoteResult.OrderNo)

		g.Log().Infof(ctx, "No currency conversion needed for order %s: %s %s",
			merchantOrderNo, amount.String(), currency)
	}

	// Double-check order status to prevent duplicate processing
	currentOrder, err := dao.PaybotAuthOrders.Ctx(ctx).
		Where(dao.PaybotAuthOrders.Columns().MerchantOrderNo, merchantOrderNo).
		One()
	if err != nil {
		return fmt.Errorf("failed to re-query order: %v", err)
	}
	if currentOrder.IsEmpty() {
		return fmt.Errorf("order not found during transaction")
	}
	if currentOrder["status"].String() != "pending" {
		g.Log().Warningf(ctx, "Order %s status is %s, already processed by another request",
			merchantOrderNo, currentOrder["status"].String())
		// If the order was completed by another concurrent request, this is still a successful completion
		// Return success to indicate the order was processed successfully
		return nil // Success - order was processed (by another concurrent request)
	}

	// Execute all operations in a database transaction
	return dao.PaybotAuthOrders.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		now := time.Now()

		// Step 1: Update local order status to completed
		g.Log().Infof(ctx, "Step 1: Updating order status to completed for %s", merchantOrderNo)
		_, err := dao.PaybotAuthOrders.Ctx(ctx).TX(tx).
			Where(dao.PaybotAuthOrders.Columns().MerchantOrderNo, merchantOrderNo).
			Where(dao.PaybotAuthOrders.Columns().Status, "pending"). // Ensure it's still pending
			Update(g.Map{
				"status":       "completed",
				"completed_at": remoteResult.CompletedAt,
				"updated_at":   now,
			})
		if err != nil {
			return fmt.Errorf("failed to update order status: %v", err)
		}

		// Step 2: Create recharge record
		g.Log().Infof(ctx, "Step 2: Creating recharge record for %s", merchantOrderNo)
		rechargeData := g.Map{
			"user_id":                userID,
			"token_id":               s.getTokenIDForSymbol(currency),
			"name":                   currency,
			"chan":                   "okpay",
			"rechange_type":          "auth_pay", // 充值类型为授权支付
			"token_contract_address": "",
			"from_address":           "",
			"to_address":             "",
			"tx_hash":                remoteResult.OrderNo, // Use PayBot order number as tx_hash
			"error":                  "",                   // 成功时为空字符串
			"amount":                 amount,
			"state":                  2,  // 2 = Completed
			"failure_reason":         "", // 成功时为空字符串
			"confirmations":          1,
			"created_at":             now,
			"completed_at":           now,
			"notification_sent":      0,
			"notification_sent_at":   nil, // 通知发送时间，暂时为空
			"updated_at":             now,
		}
		rechargeID, err := dao.UserRecharges.Ctx(ctx).TX(tx).InsertAndGetId(rechargeData)
		if err != nil {
			return fmt.Errorf("failed to create recharge record: %v", err)
		}

		// Step 3: Create transaction record (financial flow)
		g.Log().Infof(ctx, "Step 3: Creating transaction record for %s", merchantOrderNo)

		// Get current wallet balance using fiat currency
		currentWallet, err := dao.Wallets.Ctx(ctx).TX(tx).
			Where("user_id", userID).
			Where("symbol", fiatCurrency).
			One()

		var balanceBefore decimal.Decimal
		var walletID int

		if currentWallet.IsEmpty() {
			balanceBefore = decimal.Zero
			walletID = 0 // Will be created
		} else {
			walletID = currentWallet["wallet_id"].Int()
			// Convert from int64 (smallest unit) to decimal
			balanceInMinUnit := currentWallet["available_balance"].Int64()
			decimalPlaces := currentWallet["decimal_places"].Uint()
			divisor := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(decimalPlaces)))
			balanceBefore = decimal.NewFromInt(balanceInMinUnit).Div(divisor)
		}

		balanceAfter := balanceBefore.Add(fiatAmount)

		// Create transaction record using fiat amounts
		transactionData := g.Map{
			"user_id":                userID,
			"username":               0, // 设置为0或获取实际的telegram ID
			"token_id":               s.getTokenIDForSymbol(fiatCurrency),
			"type":                   "deposit",
			"wallet_type":            "available",
			"direction":              "in",
			"amount":                 fiatAmount, // 使用转换后的法币金额
			"balance_before":         balanceBefore,
			"balance_after":          balanceAfter,
			"related_transaction_id": 0, // 充值没有关联交易
			"related_entity_id":      rechargeID,
			"related_entity_type":    "user_recharge",
			"status":                 1,              // Success
			"memo":                   conversionMemo, // 使用包含汇率信息的备注
			"symbol":                 fiatCurrency,   // 使用法币符号
			"business_id":            merchantOrderNo,
			"request_amount":         fiatAmount, // 使用转换后的法币金额
			"request_reference":      fmt.Sprintf("Okpay授权支付 %s", remoteResult.OrderNo),
			"request_metadata":       nil, // JSON元数据，暂时为空
			"request_source":         "okpay",
			"request_ip":             "", // 暂时为空
			"request_user_agent":     "", // 暂时为空
			"request_timestamp":      now,
			"processed_at":           now,
			"fee_amount":             decimal.Zero, // 充值无手续费
			"fee_type":               "",           // 无手续费类型
			"exchange_rate":          exchangeRate, // 记录使用的汇率
			"target_user_id":         0,            // 充值无目标用户
			"target_username":        "",           // 充值无目标用户名
			"dtm_gid":                "",           // DTM事务ID，暂时为空
			"dtm_branch_id":          "",           // DTM分支ID，暂时为空
			"created_at":             now,
			"updated_at":             now,
		}
		_, err = dao.Transactions.Ctx(ctx).TX(tx).Insert(transactionData)
		if err != nil {
			return fmt.Errorf("failed to create transaction record: %v", err)
		}

		// Step 4: Update or create wallet balance using fiat currency
		g.Log().Infof(ctx, "Step 4: Updating wallet balance for user %d, currency %s", userID, fiatCurrency)

		// Convert fiat amount to smallest unit for storage
		decimalPlaces := s.getDecimalPlacesForSymbol(fiatCurrency)
		multiplier := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(decimalPlaces)))
		fiatAmountInMinUnit := fiatAmount.Mul(multiplier).IntPart()

		if walletID == 0 {
			// Create new wallet with fiat currency
			walletData := g.Map{
				"user_id":           userID,
				"token_id":          s.getTokenIDForSymbol(fiatCurrency),
				"available_balance": fiatAmountInMinUnit,
				"frozen_balance":    0,
				"decimal_places":    decimalPlaces,
				"telegram_id":       userID, // Assuming telegram_id = user_id
				"type":              "main",
				"symbol":            fiatCurrency,
				"created_at":        now,
				"updated_at":        now,
			}
			_, err = dao.Wallets.Ctx(ctx).TX(tx).Insert(walletData)
			if err != nil {
				return fmt.Errorf("failed to create wallet: %v", err)
			}
		} else {
			// Update existing wallet with fiat amount
			newBalance := balanceBefore.Add(fiatAmount).Mul(multiplier).IntPart()
			_, err = dao.Wallets.Ctx(ctx).TX(tx).
				Where("wallet_id", walletID).
				Update(g.Map{
					"available_balance": newBalance,
					"updated_at":        now,
				})
			if err != nil {
				return fmt.Errorf("failed to update wallet balance: %v", err)
			}
		}

		// Step 5: Handle reward system if applicable (using fiat amounts)
		g.Log().Infof(ctx, "Step 5: Checking reward eligibility for user %d", userID)
		err = s.processDepositReward(ctx, tx, userID, fiatAmount, fiatCurrency, rechargeID)
		if err != nil {
			g.Log().Warningf(ctx, "Failed to process deposit reward for user %d: %v", userID, err)
			// Don't fail the entire transaction for reward processing errors
		}

		g.Log().Infof(ctx, "Successfully completed transaction processing for order %s", merchantOrderNo)
		return nil
	})
}

// Helper methods (you may need to implement these based on your token management)
func (s *SPayBot) getTokenIDForSymbol(symbol string) uint {
	// This should return the token ID for the given symbol
	// You may need to query your tokens table or have a predefined mapping
	switch symbol {
	case "USDT":
		return 1
	case "CNY":
		return 2
	default:
		return 1 // Default to USDT
	}
}

func (s *SPayBot) getDecimalPlacesForSymbol(symbol string) uint {
	// Return decimal places for the symbol
	switch symbol {
	case "USDT":
		return 6
	case "CNY":
		return 2
	default:
		return 6
	}
}

// processDepositReward handles deposit reward processing
func (s *SPayBot) processDepositReward(ctx context.Context, tx gdb.TX, userID int64, amount decimal.Decimal, currency string, rechargeID interface{}) error {
	// Get user's deposit reward multiplier
	user, err := dao.Users.Ctx(ctx).TX(tx).Where("id", userID).One()
	if err != nil {
		return fmt.Errorf("failed to get user info: %v", err)
	}
	if user.IsEmpty() {
		return fmt.Errorf("user not found: %d", userID)
	}

	rewardMultiplier := user["deposit_reward_multiplier"].Float64()
	// Note: Even if reward is 0%, we still need to process turnover requirements

	// Calculate reward amount
	rewardPercentage := decimal.NewFromFloat(rewardMultiplier)
	rewardAmount := amount.Mul(rewardPercentage).Div(decimal.NewFromInt(100))

	// Get flow multiplier from config
	rewardMapStr, err := config.GetString(ctx, "recharges_setting.run_reward_map", "{\"0\":\"1\",\"3\":\"3\",\"5\":\"5\"}")
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get run_reward_map: %v, using default", err)
	}

	// Parse reward map to get flow multiplier
	rewardMap := make(map[string]string)
	if j := gjson.New(rewardMapStr); j != nil {
		if m := j.Map(); m != nil {
			for k, v := range m {
				rewardMap[k] = gconv.String(v)
			}
		}
	}

	// Get flow multiplier for this reward percentage
	flowMultiplierStr := "1" // default
	rewardKey := fmt.Sprintf("%.0f", rewardMultiplier)
	if val, exists := rewardMap[rewardKey]; exists {
		flowMultiplierStr = val
	}
	flowMultiplier := decimal.NewFromFloat(gconv.Float64(flowMultiplierStr))

	// Log for debugging
	g.Log().Infof(ctx, "Reward calculation: rewardMultiplier=%.2f, rewardKey=%s, flowMultiplierStr=%s, flowMultiplier=%s",
		rewardMultiplier, rewardKey, flowMultiplierStr, flowMultiplier.String())

	// Calculate required turnover: deposit amount * flow multiplier
	requiredTurnover := amount.Mul(flowMultiplier)

	// Convert recharge ID to int64
	rechargeIDInt := int64(0)
	if id, ok := rechargeID.(int64); ok {
		rechargeIDInt = id
	}

	// Create deposit reward record (for audit trail)
	rewardData := g.Map{
		"user_id":           userID,
		"recharge_id":       rechargeIDInt,
		"deposit_amount":    amount,
		"reward_percentage": rewardPercentage,
		"reward_amount":     rewardAmount,
		"flow_multiplier":   flowMultiplier,
		"required_turnover": requiredTurnover,
		"created_at":        gtime.Now(),
	}

	_, err = dao.DepositRewards.Ctx(ctx).TX(tx).Insert(rewardData)
	if err != nil {
		return fmt.Errorf("failed to create deposit reward record: %v", err)
	}

	// Update wallet balance with reward amount (only if reward > 0)
	if rewardAmount.GreaterThan(decimal.Zero) {
		// Convert reward amount to smallest unit for storage
		decimalPlaces := s.getDecimalPlacesForSymbol(currency)
		multiplier := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(decimalPlaces)))
		rewardAmountInMinUnit := rewardAmount.Mul(multiplier).IntPart()

		_, err = dao.Wallets.Ctx(ctx).TX(tx).
			Where("user_id", userID).
			Where("symbol", currency).
			Increment("available_balance", rewardAmountInMinUnit)
		if err != nil {
			return fmt.Errorf("failed to update wallet balance with reward: %v", err)
		}
	}

	// Update user's withdraw betting volume requirement
	currentBettingVolume := decimal.NewFromFloat(user["withdraw_betting_volume"].Float64())
	newBettingVolume := currentBettingVolume.Add(requiredTurnover)

	_, err = dao.Users.Ctx(ctx).TX(tx).
		Where("id", userID).
		Update(g.Map{
			"withdraw_betting_volume": newBettingVolume,
			"updated_at":              gtime.Now(),
		})
	if err != nil {
		return fmt.Errorf("failed to update user withdraw betting volume: %v", err)
	}

	g.Log().Infof(ctx, "Successfully created deposit reward for user %d: reward=%s %s (%.0f%%), turnover added=%s, total turnover=%s",
		userID, rewardAmount.String(), currency, rewardMultiplier, requiredTurnover.String(), newBettingVolume.String())

	return nil
}
