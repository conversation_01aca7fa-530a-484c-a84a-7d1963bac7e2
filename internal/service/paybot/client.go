package paybot

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/os/glog"
)

// APIClient PayBot API客户端
type APIClient struct {
	baseURL     string
	apiKey      string
	secretHash  string
	timeout     time.Duration
	client      *http.Client
	logger      *glog.Logger
	retryConfig *RetryConfig
}

// NewAPIClient 创建新的API客户端
func NewAPIClient(config *Config, logger *glog.Logger) *APIClient {
	return &APIClient{
		baseURL:     config.API.BaseURL,
		apiKey:      config.API.APIKey,
		secretHash:  config.API.SecretHash,
		timeout:     config.API.Timeout,
		client: &http.Client{
			Timeout: config.API.Timeout,
		},
		logger:      logger,
		retryConfig: &config.Retry,
	}
}

// APIResponse 通用API响应
type APIResponse struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// generateNonce 生成随机字符串
func generateNonce() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.StdEncoding.EncodeToString(bytes)
}

// generateSignature 生成HMAC签名
func (c *APIClient) generateSignature(method, uri, timestamp, nonce, body string) string {
	signString := method + uri + timestamp + nonce + body
	c.logger.Debugf(context.Background(), "PayBot Signature Debug - SignString: %s", signString)
	c.logger.Debugf(context.Background(), "PayBot Signature Debug - SecretHash: %s", c.secretHash)
	
	h := hmac.New(sha256.New, []byte(c.secretHash))
	h.Write([]byte(signString))
	signature := hex.EncodeToString(h.Sum(nil))
	
	c.logger.Debugf(context.Background(), "PayBot Signature Debug - Generated: %s", signature)
	return signature
}

// makeRequest 发送API请求
func (c *APIClient) makeRequest(ctx context.Context, method, path string, data interface{}) (*APIResponse, error) {
	var jsonData []byte
	var err error

	if data != nil {
		jsonData, err = json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request data: %w", err)
		}
	}

	// 生成认证参数
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := generateNonce()
	body := string(jsonData)
	signature := c.generateSignature(method, path, timestamp, nonce, body)

	// 创建HTTP请求
	url := c.baseURL + path
	c.logger.Debugf(ctx, "PayBot API Full URL: %s", url)
	
	var req *http.Request
	if len(jsonData) > 0 {
		req, err = http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
	} else {
		req, err = http.NewRequestWithContext(ctx, method, url, nil)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	if len(jsonData) > 0 {
		req.Header.Set("Content-Type", "application/json")
	}
	req.Header.Set("X-API-Key", c.apiKey)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)

	// 记录请求日志
	c.logger.Debugf(ctx, "PayBot API Request: %s %s", method, path)
	if len(jsonData) > 0 {
		c.logger.Debugf(ctx, "PayBot API Request Body: %s", string(jsonData))
	}
	c.logger.Debugf(ctx, "PayBot API Base URL: %s", c.baseURL)
	c.logger.Debugf(ctx, "PayBot API Request Headers: X-API-Key=%s, X-Timestamp=%s, X-Nonce=%s, X-Signature=%s", 
		c.apiKey, timestamp, nonce, signature)

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 记录响应日志
	c.logger.Debugf(ctx, "PayBot API Response: status=%d, body=%s", resp.StatusCode, string(responseBody))
	c.logger.Debugf(ctx, "PayBot API Response Headers: %+v", resp.Header)
	c.logger.Debugf(ctx, "PayBot API Response Content-Length: %d, Actual Body Length: %d", resp.ContentLength, len(responseBody))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, &PayBotError{
			Code:       "HTTP_ERROR",
			Message:    fmt.Sprintf("HTTP error: %d", resp.StatusCode),
			Type:       "network",
			Retryable:  resp.StatusCode >= 500,
			StatusCode: resp.StatusCode,
		}
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(responseBody, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查API错误
	if apiResp.Code != 0 {
		return nil, &PayBotError{
			Code:       fmt.Sprintf("API_%d", apiResp.Code),
			Message:    apiResp.Message,
			Type:       "api",
			Retryable:  false,
			StatusCode: resp.StatusCode,
		}
	}

	return &apiResp, nil
}

// makeRequestWithRetry 带重试的API调用
func (c *APIClient) makeRequestWithRetry(ctx context.Context, method, path string, data interface{}) (*APIResponse, error) {
	var lastErr error

	for i := 0; i <= c.retryConfig.MaxRetries; i++ {
		if i > 0 {
			// 指数退避
			backoff := time.Duration(i) * c.retryConfig.Backoff
			if backoff > c.retryConfig.MaxBackoff {
				backoff = c.retryConfig.MaxBackoff
			}

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoff):
			}

			c.logger.Infof(ctx, "Retrying PayBot API request: method=%s, path=%s, retry=%d, backoff=%v",
				method, path, i, backoff)
		}

		resp, err := c.makeRequest(ctx, method, path, data)
		if err == nil {
			return resp, nil
		}

		lastErr = err

		// 检查是否可重试
		if !c.isRetryableError(err) {
			break
		}
	}

	return nil, lastErr
}

// isRetryableError 判断错误是否可重试
func (c *APIClient) isRetryableError(err error) bool {
	var payBotErr *PayBotError
	if errors.As(err, &payBotErr) {
		return payBotErr.Retryable
	}

	// 网络错误通常可重试
	if isNetworkError(err) {
		return true
	}

	return false
}

// isNetworkError 检查是否为网络错误
func isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	// 检查超时错误
	if errors.Is(err, context.DeadlineExceeded) {
		return true
	}

	// 检查网络操作错误
	var netErr net.Error
	if errors.As(err, &netErr) {
		return true
	}

	// 检查URL错误
	var urlErr *url.Error
	if errors.As(err, &urlErr) {
		return true
	}

	return false
}

// 授权支付相关方法

// CreateAuthPayment 创建授权支付
func (c *APIClient) CreateAuthPayment(ctx context.Context, req *CreateAuthPaymentRequest) (*AuthPaymentResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/auth-payment/create", req)
	if err != nil {
		return nil, err
	}

	var dataResp AuthPaymentDataResponse
	if err := json.Unmarshal(resp.Data, &dataResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal auth payment data response: %w", err)
	}

	if dataResp.Code != 0 {
		return nil, fmt.Errorf("paybot api error: code=%d, message=%s", dataResp.Code, dataResp.Message)
	}

	return &dataResp.Data, nil
}

// QueryAuthPayment 查询授权支付
func (c *APIClient) QueryAuthPayment(ctx context.Context, req *QueryAuthPaymentRequest) (*AuthPaymentResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/auth-payment/query", req)
	if err != nil {
		return nil, err
	}

	var dataResp AuthPaymentDataResponse
	if err := json.Unmarshal(resp.Data, &dataResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal auth payment data response: %w", err)
	}

	if dataResp.Code != 0 {
		return nil, fmt.Errorf("paybot api error: code=%d, message=%s", dataResp.Code, dataResp.Message)
	}

	return &dataResp.Data, nil
}

// ListAuthPayments 列出授权支付
func (c *APIClient) ListAuthPayments(ctx context.Context, req *ListAuthPaymentsRequest) (*AuthPaymentListResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/auth-payment/list", req)
	if err != nil {
		return nil, err
	}

	var result AuthPaymentListResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal auth payment list result: %w", err)
	}

	return &result, nil
}

// 充值相关方法

// GetDepositAddress 获取充值地址
func (c *APIClient) GetDepositAddress(ctx context.Context, req *GetDepositAddressRequest) (*DepositAddressResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/deposits/address", req)
	if err != nil {
		return nil, err
	}

	var nestedResp PayBotNestedResponse
	if err := json.Unmarshal(resp.Data, &nestedResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal nested response: %w", err)
	}

	if nestedResp.Code != 0 {
		return nil, fmt.Errorf("PayBot API error: code=%d, message=%s", nestedResp.Code, nestedResp.Message)
	}

	return &nestedResp.Data, nil
}

// QueryDepositRecords 查询充值记录
func (c *APIClient) QueryDepositRecords(ctx context.Context, req *QueryDepositsRequest) (*DepositListResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/deposits/query", req)
	if err != nil {
		return nil, err
	}

	var result DepositListResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal deposit list result: %w", err)
	}

	return &result, nil
}

// GetDepositDetail 获取充值详情
func (c *APIClient) GetDepositDetail(ctx context.Context, req *GetDepositDetailRequest) (*DepositDetailResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/deposits/detail", req)
	if err != nil {
		return nil, err
	}

	var result DepositDetailResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal deposit detail result: %w", err)
	}

	return &result, nil
}

// ListDepositAddresses 列出充值地址
func (c *APIClient) ListDepositAddresses(ctx context.Context, req *ListDepositAddressesRequest) (*DepositAddressListResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/deposits/addresses", req)
	if err != nil {
		return nil, err
	}

	var result DepositAddressListResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal deposit address list result: %w", err)
	}

	return &result, nil
}

// 交易查询方法

// QueryTransactions 查询交易记录
func (c *APIClient) QueryTransactions(ctx context.Context, req *QueryTransactionsRequest) (*TransactionListResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/transaction/query", req)
	if err != nil {
		return nil, err
	}

	var result TransactionListResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal transaction list result: %w", err)
	}

	return &result, nil
}

// GetTransactionDetail 获取交易详情
func (c *APIClient) GetTransactionDetail(ctx context.Context, req *GetTransactionDetailRequest) (*TransactionDetailResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/transaction/detail", req)
	if err != nil {
		return nil, err
	}

	var result TransactionDetailResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal transaction detail result: %w", err)
	}

	return &result, nil
}

// 提现相关方法

// QueryWithdrawals 查询提现记录
func (c *APIClient) QueryWithdrawals(ctx context.Context, req *QueryWithdrawalsRequest) (*WithdrawalListResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/withdrawal/query", req)
	if err != nil {
		return nil, err
	}

	var result WithdrawalListResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal withdrawal list result: %w", err)
	}

	return &result, nil
}

// GetWithdrawalDetail 获取提现详情
func (c *APIClient) GetWithdrawalDetail(ctx context.Context, req *GetWithdrawalDetailRequest) (*WithdrawalDetailResult, error) {
	resp, err := c.makeRequestWithRetry(ctx, "POST", "/api/v1/withdrawal/detail", req)
	if err != nil {
		return nil, err
	}

	var result WithdrawalDetailResult
	if err := json.Unmarshal(resp.Data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal withdrawal detail result: %w", err)
	}

	return &result, nil
}

// 健康检查

// HealthCheck 健康检查
func (c *APIClient) HealthCheck(ctx context.Context) error {
	resp, err := c.makeRequest(ctx, "GET", "/api/v1/health", nil)
	if err != nil {
		return err
	}

	if resp.Code != 0 {
		return fmt.Errorf("health check failed: %s", resp.Message)
	}

	return nil
}