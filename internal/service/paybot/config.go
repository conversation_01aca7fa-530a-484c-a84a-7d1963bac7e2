package paybot

import (
	"time"
)

// Config PayBot配置
type Config struct {
	Enabled bool `json:"enabled" yaml:"enabled"`

	API APIConfig `json:"api" yaml:"api"`

	Features FeaturesConfig `json:"features" yaml:"features"`

	Sync SyncConfig `json:"sync" yaml:"sync"`

	Retry RetryConfig `json:"retry" yaml:"retry"`
}

// APIConfig API配置
type APIConfig struct {
	BaseURL    string        `json:"base_url" yaml:"base_url"`
	APIKey     string        `json:"api_key" yaml:"api_key"`
	SecretHash string        `json:"secret_hash" yaml:"secret_hash"`
	Timeout    time.Duration `json:"timeout" yaml:"timeout"`
}

// FeaturesConfig 功能配置
type FeaturesConfig struct {
	AuthPayment bool `json:"auth_payment" yaml:"auth_payment"`
	Deposits    bool `json:"deposits" yaml:"deposits"`
	Withdrawals bool `json:"withdrawals" yaml:"withdrawals"`
	Callbacks   bool `json:"callbacks" yaml:"callbacks"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	Enabled   bool `json:"enabled" yaml:"enabled"`
	BatchSize int  `json:"batch_size" yaml:"batch_size"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries int           `json:"max_retries" yaml:"max_retries"`
	Backoff    time.Duration `json:"backoff" yaml:"backoff"`
	MaxBackoff time.Duration `json:"max_backoff" yaml:"max_backoff"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		Enabled: true,
		API: APIConfig{
			BaseURL: "http://paybot-api:9000",
			Timeout: 30 * time.Second,
		},
		Features: FeaturesConfig{
			AuthPayment: true,
			Deposits:    true,
			Withdrawals: true,
			Callbacks:   true,
		},
		Sync: SyncConfig{
			Enabled:   true,
			BatchSize: 100,
		},
		Retry: RetryConfig{
			MaxRetries: 3,
			Backoff:    1 * time.Second,
			MaxBackoff: 30 * time.Second,
		},
	}
}