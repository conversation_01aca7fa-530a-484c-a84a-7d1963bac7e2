package paybot

import (
	"context"
	"time"
	
	"github.com/shopspring/decimal"
)

// IPriceClient 价格客户端接口（避免循环依赖）
type IPriceClient interface {
	GetFiatPrice(ctx context.Context, asset, currency string) (*FiatPriceData, error)
}

// FiatPriceData 法币价格数据（本地定义避免循环依赖）
type FiatPriceData struct {
	Asset          string          `json:"asset"`           // 资产符号 (USDT)
	Currency       string          `json:"currency"`        // 法币符号 (CNY)
	CurrencySymbol string          `json:"currency_symbol"` // 法币符号 (￥)
	BuyPrice       decimal.Decimal `json:"buy_price"`       // 买入价格
	SellPrice      decimal.Decimal `json:"sell_price"`      // 卖出价格
	MidPrice       decimal.Decimal `json:"mid_price"`       // 中间价格 (买价+卖价)/2
	Spread         decimal.Decimal `json:"spread"`          // 价差 (卖价-买价)
	SpreadPercent  decimal.Decimal `json:"spread_percent"`  // 价差百分比
	Provider       string          `json:"provider"`        // 价格提供商
	Timestamp      time.Time       `json:"timestamp"`       // 更新时间戳
	LastUpdated    int64           `json:"last_updated"`    // Unix时间戳
}

// IPayBot PayBot服务接口（避免循环依赖）
type IPayBot interface {
	// 授权支付相关
	CreateAuthPayment(ctx context.Context, req *CreateAuthPaymentRequest) (*AuthPaymentResult, error)
	QueryAuthPayment(ctx context.Context, req *QueryAuthPaymentRequest) (*AuthPaymentResult, error)
	ListAuthPayments(ctx context.Context, req *ListAuthPaymentsRequest) (*AuthPaymentListResult, error)
	GenerateTelegramLink(ctx context.Context, orderNo string) string

	// 充值相关
	GetDepositAddress(ctx context.Context, req *GetDepositAddressRequest) (*DepositAddressResult, error)
	QueryDepositRecords(ctx context.Context, req *QueryDepositsRequest) (*DepositListResult, error)
	GetDepositDetail(ctx context.Context, req *GetDepositDetailRequest) (*DepositDetailResult, error)
	ListDepositAddresses(ctx context.Context, req *ListDepositAddressesRequest) (*DepositAddressListResult, error)

	// 交易查询
	QueryTransactions(ctx context.Context, req *QueryTransactionsRequest) (*TransactionListResult, error)
	GetTransactionDetail(ctx context.Context, req *GetTransactionDetailRequest) (*TransactionDetailResult, error)

	// 提现相关
	QueryWithdrawals(ctx context.Context, req *QueryWithdrawalsRequest) (*WithdrawalListResult, error)
	GetWithdrawalDetail(ctx context.Context, req *GetWithdrawalDetailRequest) (*WithdrawalDetailResult, error)

	// 回调处理
	ProcessDepositCallback(ctx context.Context, req *DepositCallbackRequest) error
	ProcessWithdrawalCallback(ctx context.Context, req *WithdrawalCallbackRequest) error
	ProcessOkpayAuthCallback(ctx context.Context, userID int64, merchantOrderNo string) (*OkpayCallbackResult, error)

	// 健康检查
	HealthCheck(ctx context.Context) error

	// 配置检查
	IsSyncEnabled() bool
}

// 确保SPayBot实现了IPayBot接口
var _ IPayBot = (*SPayBot)(nil)