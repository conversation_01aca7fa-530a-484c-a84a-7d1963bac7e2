package casdoor

import (
	"admin-api/internal/model/entity"
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type AgentSyncService struct {
	client        *casdoorsdk.Client
	defaultAvatar string
}

// NewAgentSyncService 创建代理同步服务
func NewAgentSyncService(ctx context.Context) (*AgentSyncService, error) {
	client, err := GetAgentCasdoorClient(ctx)
	if err != nil {
		return nil, err
	}

	// 获取默认头像配置
	defaultAvatar := g.Cfg().MustGet(ctx, "agent_casdoor_server.default_avatar").String()
	if defaultAvatar == "" {
		defaultAvatar = "https://cdn.casbin.org/img/casbin.svg" // 默认值
	}

	return &AgentSyncService{
		client:        client,
		defaultAvatar: defaultAvatar,
	}, nil
}

// SyncAddAgent 同步添加代理到 Casdoor
func (s *AgentSyncService) SyncAddAgent(ctx context.Context, agent *entity.Agents, password string) error {
	user := &casdoorsdk.User{
		Owner:             s.client.OrganizationName,
		SignupApplication: s.client.ApplicationName,
		Name:              agent.Username,
		Email:             agent.Email,
		Phone:             "", // agent.PhoneNumber 字段已被注释，暂时留空
		DisplayName:       agent.AgentName,
		Password:          password,
		Type:              "normal-user",
		Avatar:            s.defaultAvatar,   // 使用默认头像
		IsForbidden:       agent.Status == 0, // 状态为0时禁用
		CreatedTime:       gtime.Now().Format("Y-m-d H:i:s"),

		// 使用 Properties 存储额外信息
		Properties: map[string]string{
			"agentId":        fmt.Sprintf("%d", agent.AgentId),
			"level":          fmt.Sprintf("%d", agent.Level),
			"parentAgentId":  fmt.Sprintf("%d", agent.ParentAgentId),
			"invitationCode": agent.InvitationCode,
			"ipWhitelist":    "", // IP白名单字段，待实现
		},
	}

	// 分配代理角色
	agentRole := g.Cfg().MustGet(ctx, "agent_casdoor_server.role").String()
	if agentRole != "" {
		// 从配置中解析组织名和角色名
		// 格式: organization_agent/role_agent
		user.Roles = []*casdoorsdk.Role{
			{
				Owner: s.client.OrganizationName,
				Name:  getResourceName(agentRole), // 提取角色名
			},
		}
	}

	_, err := s.client.AddUser(user)
	if err != nil {
		g.Log().Errorf(ctx, "同步添加代理到 Casdoor 失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "成功同步代理 %s 到 Casdoor", agent.Username)
	return nil
}

// SyncUpdateAgent 同步更新代理信息到 Casdoor
func (s *AgentSyncService) SyncUpdateAgent(ctx context.Context, agent *entity.Agents) error {
	// 先获取 Casdoor 中的用户信息
	user, err := s.client.GetUser(agent.Username)
	if err != nil {
		return fmt.Errorf("获取 Casdoor 用户失败: %w", err)
	}

	if user == nil {
		// 用户不存在，可能需要创建
		return fmt.Errorf("Casdoor 中不存在用户: %s", agent.Username)
	}

	// 更新字段
	user.Email = agent.Email
	user.Phone = "" // agent.PhoneNumber 字段已被注释
	user.DisplayName = agent.AgentName
	user.IsForbidden = agent.Status == 0 // 状态为0时禁用

	// 更新 Properties
	if user.Properties == nil {
		user.Properties = make(map[string]string)
	}
	user.Properties["lastUpdated"] = gtime.Now().Format("Y-m-d H:i:s")
	user.Properties["status"] = fmt.Sprintf("%d", agent.Status)
	// user.Properties["ipWhitelist"] = agent.IpWhitelist // IP白名单更新待实现

	// 只更新特定字段
	columns := []string{"email", "phone", "displayName", "isForbidden", "properties"}

	_, err = s.client.UpdateUserForColumns(user, columns)
	if err != nil {
		g.Log().Errorf(ctx, "同步更新代理到 Casdoor 失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "成功更新代理 %s 到 Casdoor", agent.Username)
	return nil
}

// SyncUpdatePassword 同步更新密码到 Casdoor
func (s *AgentSyncService) SyncUpdatePassword(ctx context.Context, username, newPassword string) error {
	// Casdoor 的 SetPassword 方法不需要旧密码
	_, err := s.client.SetPassword(s.client.OrganizationName, username, "", newPassword)
	if err != nil {
		g.Log().Errorf(context.Background(), "同步更新密码到 Casdoor 失败: %v", err)
		return err
	}
	return nil
}

// SyncUpdateIPWhitelist 同步更新代理的IP白名单
func (s *AgentSyncService) SyncUpdateIPWhitelist(ctx context.Context, username string, ipWhitelist string) error {
	user, err := s.client.GetUser(username)
	if err != nil {
		return fmt.Errorf("获取 Casdoor 用户失败: %w", err)
	}

	if user == nil {
		return fmt.Errorf("Casdoor 中不存在用户: %s", username)
	}

	// 更新 Properties 中的 IP 白名单
	if user.Properties == nil {
		user.Properties = make(map[string]string)
	}
	user.Properties["ipWhitelist"] = ipWhitelist
	user.Properties["ipWhitelistUpdated"] = gtime.Now().Format("Y-m-d H:i:s")

	// 只更新 properties 字段
	columns := []string{"properties"}
	_, err = s.client.UpdateUserForColumns(user, columns)
	if err != nil {
		g.Log().Errorf(ctx, "同步更新IP白名单到 Casdoor 失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "成功更新代理 %s 的IP白名单到 Casdoor", username)
	return nil
}

// SyncDeleteAgent 同步删除（禁用）代理
func (s *AgentSyncService) SyncDeleteAgent(ctx context.Context, username string) error {
	user, err := s.client.GetUser(username)
	if err != nil {
		return fmt.Errorf("获取 Casdoor 用户失败: %w", err)
	}

	if user == nil {
		// 用户不存在，直接返回成功
		return nil
	}

	// 禁用用户而不是删除
	user.IsForbidden = true

	// 更新 Properties 记录删除时间
	if user.Properties == nil {
		user.Properties = make(map[string]string)
	}
	user.Properties["deletedAt"] = gtime.Now().Format("Y-m-d H:i:s")

	columns := []string{"isForbidden", "properties"}
	_, err = s.client.UpdateUserForColumns(user, columns)
	if err != nil {
		g.Log().Errorf(ctx, "禁用 Casdoor 用户失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "成功禁用 Casdoor 用户 %s", username)
	return nil
}

// BatchSyncAgents 批量同步代理（用于数据迁移）
func (s *AgentSyncService) BatchSyncAgents(ctx context.Context, agents []*entity.Agents) (int, int, error) {
	successCount := 0
	failCount := 0

	for _, agent := range agents {
		// 检查用户是否已存在
		existingUser, _ := s.client.GetUser(agent.Username)
		if existingUser != nil {
			g.Log().Infof(ctx, "用户 %s 已存在，跳过", agent.Username)
			successCount++
			continue
		}

		// 使用临时密码，需要用户首次登录时修改
		tempPassword := fmt.Sprintf("Temp_%s_2024!", agent.Username)

		// 创建一个新的 agent 副本，确保有完整的信息
		agentCopy := *agent

		err := s.SyncAddAgent(ctx, &agentCopy, tempPassword)
		if err != nil {
			g.Log().Errorf(ctx, "同步代理 %s 失败: %v", agent.Username, err)
			failCount++
			continue
		}
		successCount++
	}

	g.Log().Infof(ctx, "批量同步完成: 成功 %d, 失败 %d", successCount, failCount)
	if failCount > 0 {
		return successCount, failCount, fmt.Errorf("批量同步部分失败")
	}
	return successCount, failCount, nil
}

// CheckConsistency 检查数据一致性
func (s *AgentSyncService) CheckConsistency(ctx context.Context, localAgents []*entity.Agents) ([]string, error) {
	var inconsistencies []string

	// 获取 Casdoor 中的所有用户
	casdoorUsers, err := s.client.GetUsers()
	if err != nil {
		return nil, fmt.Errorf("获取 Casdoor 用户列表失败: %w", err)
	}

	// 创建用户名映射
	casdoorUserMap := make(map[string]*casdoorsdk.User)
	for _, user := range casdoorUsers {
		casdoorUserMap[user.Name] = user
	}

	// 检查本地代理是否在 Casdoor 中存在
	for _, agent := range localAgents {
		casdoorUser, exists := casdoorUserMap[agent.Username]
		if !exists {
			inconsistencies = append(inconsistencies,
				fmt.Sprintf("代理 %s (ID: %d) 在 Casdoor 中不存在", agent.Username, agent.AgentId))
			continue
		}

		// 检查状态是否一致
		// 本地状态为1表示启用，Casdoor 中 IsForbidden=false 表示启用
		expectedForbidden := agent.Status == 0
		if casdoorUser.IsForbidden != expectedForbidden {
			inconsistencies = append(inconsistencies,
				fmt.Sprintf("代理 %s 状态不一致: 本地状态=%d, Casdoor IsForbidden=%v",
					agent.Username, agent.Status, casdoorUser.IsForbidden))
		}

		// 检查邮箱是否一致
		if casdoorUser.Email != agent.Email {
			inconsistencies = append(inconsistencies,
				fmt.Sprintf("代理 %s 邮箱不一致: 本地=%s, Casdoor=%s",
					agent.Username, agent.Email, casdoorUser.Email))
		}
	}

	return inconsistencies, nil
}

// DeleteMFA 调用 Casdoor 的 delete-mfa API 端点
func (s *AgentSyncService) DeleteMFA(ctx context.Context, username string) error {
	// 构建请求参数
	params := map[string]string{
		"owner": s.client.OrganizationName,
		"name":  username,
	}

	// 转换为 JSON
	paramBytes, err := json.Marshal(params)
	if err != nil {
		return fmt.Errorf("序列化参数失败: %w", err)
	}

	// 调用 delete-mfa API
	response, err := s.client.DoPost("delete-mfa", nil, paramBytes, false, false)
	if err != nil {
		// 检查错误信息是否表示用户不存在
		errStr := err.Error()
		errLower := strings.ToLower(errStr)
		if strings.Contains(errLower, "doesn't exist") ||
			strings.Contains(errLower, "does not exist") ||
			strings.Contains(errLower, "not found") ||
			strings.Contains(errLower, "用户不存在") {
			g.Log().Warningf(ctx, "用户 %s 在 Casdoor 中不存在，跳过删除 MFA", username)
			return nil
		}
		g.Log().Errorf(ctx, "调用 delete-mfa API 失败: %v", err)
		return fmt.Errorf("删除 MFA 失败: %w", err)
	}

	if response.Status != "ok" {
		// 如果用户不存在，这不是错误 - 没有什么需要删除的
		msgLower := strings.ToLower(response.Msg)
		if strings.Contains(msgLower, "doesn't exist") ||
			strings.Contains(msgLower, "does not exist") ||
			strings.Contains(msgLower, "not found") ||
			strings.Contains(msgLower, "用户不存在") {
			g.Log().Warningf(ctx, "用户 %s 在 Casdoor 中不存在，跳过删除 MFA", username)
			return nil
		}
		return fmt.Errorf("删除 MFA 失败: %s", response.Msg)
	}

	g.Log().Infof(ctx, "成功通过 delete-mfa API 删除用户 %s 的 MFA", username)
	return nil
}

// HealthCheck 健康检查
func (s *AgentSyncService) HealthCheck(ctx context.Context) error {
	// 测试连接到 Casdoor
	_, err := s.client.GetUsers()
	if err != nil {
		return fmt.Errorf("Casdoor 连接失败: %w", err)
	}
	return nil
}

// getResourceName 从完整的资源ID中提取资源名称
// 例如: "organization_agent/role_agent" -> "role_agent"
func getResourceName(fullName string) string {
	if fullName == "" {
		return ""
	}

	// 查找最后一个 "/" 的位置
	lastSlash := -1
	for i := len(fullName) - 1; i >= 0; i-- {
		if fullName[i] == '/' {
			lastSlash = i
			break
		}
	}

	if lastSlash >= 0 && lastSlash < len(fullName)-1 {
		return fullName[lastSlash+1:]
	}

	return fullName
}
