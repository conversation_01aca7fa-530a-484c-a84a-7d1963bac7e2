package casdoor

import (
	"context"
	"fmt"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
)

// SyncRoutesWithCasdoorSafe 安全地同步路由到Casdoor
// 如果api目录不存在，则跳过同步并返回错误
func SyncRoutesWithCasdoorSafe(ctx context.Context, existingPermissions []*casdoorsdk.Permission) error {
	// 检查 api 目录是否存在
	apiDir := "api"
	if !gfile.Exists(apiDir) {
		// 在生产环境中，api 目录可能不存在，这是正常的
		return fmt.Errorf("api 目录不存在，跳过路由权限同步")
	}

	// 如果 api 目录存在，执行原有的同步逻辑
	g.Log().Info(ctx, "api 目录存在，开始同步路由权限")
	SyncRoutesWithCasdoor(ctx, existingPermissions)
	return nil
}