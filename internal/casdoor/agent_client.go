package casdoor

import (
	"context"
	"sync"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

var (
	agentClient     *casdoorsdk.Client
	agentClientOnce sync.Once
)

// GetAgentCasdoorClient 获取代理专用的 Casdoor 客户端（单例）
func GetAgentCasdoorClient(ctx context.Context) (*casdoorsdk.Client, error) {
	var err error
	agentClientOnce.Do(func() {
		config := &ServerConfig{}
		err = g.Cfg().MustGet(ctx, "agent_casdoor_server").Scan(config)
		if err != nil {
			g.Log().Errorf(ctx, "加载代理 Casdoor 配置失败: %v", err)
			return
		}

		// 获取证书配置
		certificate := g.Cfg().MustGet(ctx, "certificate").String()
		if certificate == "" {
			g.Log().Error(ctx, "证书配置为空")
			return
		}

		// 创建代理专用的 Casdoor 客户端
		agentClient = casdoorsdk.NewClient(
			config.Endpoint,
			config.ClientID,
			config.ClientSecret,
			certificate,
			config.Organization,
			config.Application,
		)

		g.Log().Infof(ctx, "代理 Casdoor 客户端初始化成功 - Organization: %s, Application: %s",
			config.Organization, config.Application)
	})

	if err != nil {
		return nil, err
	}

	if agentClient == nil {
		return nil, gerror.New("代理 Casdoor 客户端初始化失败")
	}

	return agentClient, nil
}