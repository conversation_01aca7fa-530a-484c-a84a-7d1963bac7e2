package casdoor

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"strings"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
)

// SyncRoutesWithCasdoorImproved 使用运行时路由信息同步到Casdoor作为权限
func SyncRoutesWithCasdoorImproved(ctx context.Context, existingPermissions []*casdoorsdk.Permission) {
	// 创建权限映射，便于快速查找
	permissionMap := make(map[string]*casdoorsdk.Permission)
	for _, perm := range existingPermissions {
		permissionMap[perm.Name] = perm
	}

	// 获取所有已注册的路由
	server := g.Server()
	if server == nil {
		g.Log().Warning(ctx, "HTTP服务器未初始化，跳过路由同步")
		return
	}

	// 获取所有路由
	routes := server.GetRoutes()
	if len(routes) == 0 {
		g.Log().Info(ctx, "没有已注册的HTTP路由，无需同步API权限")
		return
	}

	g.Log().Infof(ctx, "获取到HTTP路由 %d 条", len(routes))

	// 遍历所有路由，创建Casdoor权限
	permissionsToAdd := []*casdoorsdk.Permission{}
	existingCount := 0

	for _, route := range routes {
		// 跳过一些内部路由或不需要同步的路由
		if strings.HasPrefix(route.Route, "/api.json") ||
			strings.HasPrefix(route.Route, "/swagger") ||
			strings.HasPrefix(route.Route, "/debug") ||
			strings.HasPrefix(route.Route, "/health") {
			continue
		}

		// 生成权限名称：使用路由和方法的组合
		permName := generatePermissionName(route.Method, route.Route)
		
		// 检查是否已存在
		if _, exists := permissionMap[permName]; exists {
			existingCount++
			continue
		}

		// 创建新权限
		permission := &casdoorsdk.Permission{
			Owner:       GlobalConfig.Server.Organization,
			Name:        permName,
			DisplayName: route.Route,
			Description: route.Method + " " + route.Route,
			Users:       []string{},
			Roles:       []string{},
			Domains:     []string{},
			Actions:     []string{route.Method},
			Resources:   []string{route.Route},
			Effect:      "allow",
			IsEnabled:   true,
		}

		permissionsToAdd = append(permissionsToAdd, permission)
	}

	// 批量添加权限
	if len(permissionsToAdd) > 0 {
		g.Log().Infof(ctx, "发现 %d 条路由需要同步到Casdoor权限", len(permissionsToAdd))
		g.Log().Info(ctx, "开始将路由同步到Casdoor权限....")

		successCount := 0
		for _, perm := range permissionsToAdd {
			_, err := casdoorsdk.AddPermission(perm)
			if err != nil {
				g.Log().Errorf(ctx, "添加权限失败: %s, 错误: %v", perm.Name, err)
				continue
			}
			successCount++
		}

		g.Log().Info(ctx, "Casdoor API权限同步完成")
		g.Log().Infof(ctx, "API权限同步统计: 总扫描 %d, 新创建 %d, 已存在 %d, 跳过 %d",
			len(routes), successCount, existingCount, len(routes)-successCount-existingCount)
	} else {
		g.Log().Infof(ctx, "所有 %d 条路由已存在于Casdoor权限中，无需同步", existingCount)
	}
}

// generatePermissionName 生成权限名称
func generatePermissionName(method, path string) string {
	// 使用MD5生成唯一标识
	hash := md5.New()
	hash.Write([]byte(method + ":" + path))
	hashBytes := hash.Sum(nil)
	hashStr := hex.EncodeToString(hashBytes)[:8]
	
	// 清理路径中的特殊字符
	cleanPath := strings.ReplaceAll(path, "/", "_")
	cleanPath = strings.ReplaceAll(cleanPath, "{", "")
	cleanPath = strings.ReplaceAll(cleanPath, "}", "")
	cleanPath = strings.ReplaceAll(cleanPath, ":", "")
	
	// 生成权限名称
	return "api_" + strings.ToLower(method) + cleanPath + "_" + hashStr
}