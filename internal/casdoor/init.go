package casdoor

import (
	"context"
	"fmt"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
)

func initAuthConfig() {
	casdoorsdk.InitConfig(
		GlobalConfig.Server.Endpoint,
		GlobalConfig.Server.ClientID,
		GlobalConfig.Server.ClientSecret,
		GlobalConfig.Certificate,
		GlobalConfig.Server.Organization,
		GlobalConfig.Server.Application,
	)
}

// InitCasdoor 初始化Casdoor配置和SDK
func InitCasdoor(ctx context.Context) {
	if err := LoadConfig(ctx); err != nil {
		panic(fmt.Errorf("failed to load casdoor config: %w", err))
	}
	initAuthConfig()

	// 获取所有权限
	permissions, err := casdoorsdk.GetPermissions()
	if err != nil {
		g.Log().Errorf(ctx, "获取Casdoor权限列表失败: %v", err)
	} else {
		g.Log().Infof(ctx, "已获取 %d 个Casdoor权限", len(permissions))
	}
	// g.Log().Info(ctx, " Casdoor权限: %+v", permissions)

	g.Log().Info(ctx, "Casdoor初始化完成")

	// for _, perm := range permissions {
	// 	casdoorsdk.DeletePermission(perm)
	// }

	// 同步路由到Casdoor权限
	// 检查是否在生产环境，如果 api 目录不存在则跳过
	if err := SyncRoutesWithCasdoorSafe(ctx, permissions); err != nil {
		g.Log().Warningf(ctx, "路由同步跳过: %v", err)
	}

	// // // // // // 重新获取最新权限列表（包含刚刚添加的路由权限）
	// permissions, _ = casdoorsdk.GetPermissions()
	SyncMenusWithCasdoor(ctx, permissions)
	// g.Log().Info(ctx, "Casdoor菜单同步完成")
}
