package cache

import (
	"context"
	"fmt"
	"strings"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
)

// PermissionService 权限服务
type PermissionService struct {
	cacheManager *CacheManager
}

// NewPermissionService 创建权限服务实例
func NewPermissionService() *PermissionService {
	return &PermissionService{
		cacheManager: GetInstance(),
	}
}

// GetUserPermissionsFromCasdoor 从Casdoor获取用户权限
func (p *PermissionService) GetUserPermissionsFromCasdoor(ctx context.Context, username string) ([]Permission, error) {
	// 获取用户信息
	user, err := casdoorsdk.GetUser(username)
	if err != nil {
		g.Log().Errorf(ctx, "获取Casdoor用户信息失败: username=%s, error=%v", username, err)
		return nil, err
	}

	if user == nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "Casdoor用户不存在: username=%s", username)
		return nil, fmt.Errorf("用户不存在")
	}

	// 转换权限格式
	permissions := make([]Permission, 0, len(user.Permissions))
	for _, casdoorPerm := range user.Permissions {
		if casdoorPerm == nil {
			continue
		}

		perm := Permission{
			Name:         casdoorPerm.Name,
			DisplayName:  casdoorPerm.DisplayName,
			Description:  casdoorPerm.Description,
			ResourceType: casdoorPerm.ResourceType,
			Effect:       casdoorPerm.Effect,
		}

		// 处理资源和动作
		if len(casdoorPerm.Resources) > 0 {
			perm.Resource = casdoorPerm.Resources[0]
		}
		if len(casdoorPerm.Actions) > 0 {
			perm.Action = casdoorPerm.Actions[0]
		}

		permissions = append(permissions, perm)
	}

	g.Log().Infof(ctx, "从Casdoor获取用户权限: username=%s, count=%d", username, len(permissions))
	return permissions, nil
}

// CacheUserPermissionsFromCasdoor 从Casdoor获取权限并缓存
func (p *PermissionService) CacheUserPermissionsFromCasdoor(ctx context.Context, username string) error {
	// 从Casdoor获取权限
	permissions, err := p.GetUserPermissionsFromCasdoor(ctx, username)
	if err != nil {
		return err
	}

	// 缓存完整权限信息
	if err := p.cacheManager.SetUserPermissions(ctx, username, permissions); err != nil {
		return err
	}

	// 解析并缓存API权限
	apiPermissions := p.parseAPIPermissions(permissions)
	if err := p.cacheManager.SetUserAPIPermissions(ctx, username, apiPermissions); err != nil {
		return err
	}

	// 解析并缓存菜单权限
	menuPermissions := p.parseMenuPermissions(permissions)
	if err := p.cacheManager.SetUserMenuPermissions(ctx, username, menuPermissions); err != nil {
		return err
	}

	g.Log().Infof(ctx, "成功缓存用户权限: username=%s, total=%d, api=%d, menu=%d",
		username, len(permissions), len(apiPermissions), len(menuPermissions))
	return nil
}

// parseAPIPermissions 解析API权限
func (p *PermissionService) parseAPIPermissions(permissions []Permission) map[string]bool {
	apiPermissions := make(map[string]bool)

	for _, perm := range permissions {
		if perm.Effect != "Allow" || perm.ResourceType != "Custom" {
			continue
		}

		resource := perm.Resource
		if strings.Contains(resource, "@") {
			// 资源格式：/api/path@method
			apiPermissions[resource] = true
		}
	}

	return apiPermissions
}

// parseMenuPermissions 解析菜单权限
func (p *PermissionService) parseMenuPermissions(permissions []Permission) map[string]bool {
	menuPermissions := make(map[string]bool)

	for _, perm := range permissions {
		if perm.Effect != "Allow" || perm.ResourceType != "Custom" {
			continue
		}

		resource := perm.Resource
		// 菜单权限通常以/admin开头且不包含@符号
		if strings.HasPrefix(resource, "/admin") && !strings.Contains(resource, "@") {
			menuPermissions[resource] = true
		}
	}

	return menuPermissions
}

// GetOrCacheUserPermissions 获取或缓存用户权限
func (p *PermissionService) GetOrCacheUserPermissions(ctx context.Context, username string) ([]Permission, error) {
	// 先尝试从缓存获取
	permissions, err := p.cacheManager.GetUserPermissions(ctx, username)
	if err != nil {
		g.Log().Warningf(ctx, "从缓存获取用户权限失败: username=%s, error=%v", username, err)
	}

	// 如果缓存中没有数据，从Casdoor获取并缓存
	if permissions == nil || len(permissions) == 0 {
		g.Log().Debugf(ctx, "缓存中无权限数据，从Casdoor获取: username=%s", username)

		if err := p.CacheUserPermissionsFromCasdoor(ctx, username); err != nil {
			return nil, err
		}

		// 再次从缓存获取
		permissions, err = p.cacheManager.GetUserPermissions(ctx, username)
		if err != nil {
			return nil, err
		}
	}

	return permissions, nil
}

// HasAPIPermission 检查用户是否有API权限
func (p *PermissionService) HasAPIPermission(ctx context.Context, username string, apiPath string, method string) (bool, error) {
	return p.cacheManager.HasAPIPermission(ctx, username, apiPath, method)
}

// HasMenuPermission 检查用户是否有菜单权限
func (p *PermissionService) HasMenuPermission(ctx context.Context, username string, menuPath string) (bool, error) {
	return p.cacheManager.HasMenuPermission(ctx, username, menuPath)
}

// RefreshUserPermissions 刷新用户权限缓存
func (p *PermissionService) RefreshUserPermissions(ctx context.Context, username string) error {
	// 清除旧缓存
	if err := p.cacheManager.ClearUserCache(ctx, username); err != nil {
		g.Log().Errorf(ctx, "清除用户缓存失败: username=%s, error=%v", username, err)
	}

	// 重新获取并缓存权限
	return p.CacheUserPermissionsFromCasdoor(ctx, username)
}
