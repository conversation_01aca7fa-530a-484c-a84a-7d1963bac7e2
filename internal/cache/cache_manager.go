package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

// CacheManager 缓存管理器（单例模式）
type CacheManager struct {
	// redis 实例通过g.Redis()获取
}

var (
	cacheManager *CacheManager
	once         sync.Once
)

// GetInstance 获取缓存管理器实例
func GetInstance() *CacheManager {
	once.Do(func() {
		cacheManager = &CacheManager{}
	})
	return cacheManager
}

// 缓存键常量
const (
	// 用户信息缓存键前缀
	UserInfoCachePrefix = "admin:user:%s:info"
	// 用户权限缓存键前缀
	UserPermissionsCachePrefix = "admin:user:%s:permissions"
	// 用户角色缓存键前缀
	UserRolesCachePrefix = "admin:user:%s:roles"
	// 用户菜单权限缓存键前缀
	UserMenuPermissionsCachePrefix = "admin:user:%s:menu_permissions"
	// 用户API权限缓存键前缀
	UserAPIPermissionsCachePrefix = "admin:user:%s:api_permissions"

	// 缓存过期时间
	UserInfoCacheTTL       = 600 * time.Minute // 用户信息缓存30分钟
	UserPermissionsTTL     = 600 * time.Minute // 权限缓存1小时
	UserMenuPermissionsTTL = 600 * time.Minute // 菜单权限缓存1小时
	UserAPIPermissionsTTL  = 600 * time.Minute // API权限缓存1小时
)

// // UserInfo 用户信息结构
// type UserInfo struct {
// 	*casdoorsdk.User
// }

// Permission 权限结构
type Permission struct {
	Name         string   `json:"name"`
	DisplayName  string   `json:"display_name"`
	Description  string   `json:"description"`
	ResourceType string   `json:"resource_type"`
	Resources    []string `json:"resources"`
	Resource     string   `json:"resource"`
	Action       string   `json:"action"`
	Effect       string   `json:"effect"`
}

// SetUserInfo 缓存用户信息
func (c *CacheManager) SetUserInfo(ctx context.Context, username string, userInfo *casdoorsdk.User) error {
	cacheKey := fmt.Sprintf(UserInfoCachePrefix, username)

	// 序列化用户信息
	data, err := json.Marshal(userInfo)
	if err != nil {
		g.Log().Errorf(ctx, "序列化用户信息失败: username=%s, error=%v", username, err)
		return err
	}

	// 存储到Redis
	redis := g.Redis()
	_, err = redis.Set(ctx, cacheKey, string(data))
	if err != nil {
		g.Log().Errorf(ctx, "存储用户信息到Redis失败: username=%s, error=%v", username, err)
		return err
	}

	// 设置过期时间
	_, err = redis.Expire(ctx, cacheKey, int64(UserInfoCacheTTL.Seconds()))
	if err != nil {
		g.Log().Errorf(ctx, "设置用户信息缓存过期时间失败: username=%s, error=%v", username, err)
		return err
	}

	g.Log().Debugf(ctx, "成功缓存用户信息: username=%s", username)
	return nil
}

// GetUserInfo 获取缓存的用户信息
func (c *CacheManager) GetUserInfo(ctx context.Context, username string) (*casdoorsdk.User, error) {
	cacheKey := fmt.Sprintf(UserInfoCachePrefix, username)

	// 从Redis获取用户信息
	redis := g.Redis()
	cachedData, err := redis.Get(ctx, cacheKey)
	if err != nil {
		g.Log().Debugf(ctx, "从Redis获取用户信息失败: username=%s, error=%v", username, err)
		return nil, err
	}

	if cachedData == nil {
		g.Log().Debugf(ctx, "Redis中不存在用户信息缓存: username=%s", username)
		return nil, nil
	}

	// 检查缓存数据是否为空
	cachedString := cachedData.String()
	if cachedString == "" {
		g.Log().Warningf(ctx, "Redis中用户信息缓存为空: username=%s", username)
		// 删除损坏的缓存
		_, _ = redis.Del(ctx, cacheKey)
		return nil, nil
	}

	// 反序列化用户信息
	var userInfo *casdoorsdk.User
	if err := json.Unmarshal([]byte(cachedString), &userInfo); err != nil {
		g.Log().Errorf(ctx, "反序列化用户信息失败: username=%s, cached_data_length=%d, error=%v", username, len(cachedString), err)
		// 删除损坏的缓存数据
		_, delErr := redis.Del(ctx, cacheKey)
		if delErr != nil {
			g.Log().Errorf(ctx, "删除损坏的用户缓存失败: username=%s, error=%v", username, delErr)
		}
		return nil, nil // 返回nil而不是错误，让上层重新获取用户信息
	}

	g.Log().Debugf(ctx, "从Redis成功获取用户信息: username=%s", username)
	return userInfo, nil
}

// SetUserPermissions 缓存用户权限
func (c *CacheManager) SetUserPermissions(ctx context.Context, username string, permissions []Permission) error {
	cacheKey := fmt.Sprintf(UserPermissionsCachePrefix, username)

	// 序列化权限信息
	data, err := json.Marshal(permissions)
	if err != nil {
		g.Log().Errorf(ctx, "序列化用户权限失败: username=%s, error=%v", username, err)
		return err
	}

	// 存储到Redis
	redis := g.Redis()
	_, err = redis.Set(ctx, cacheKey, string(data))
	if err != nil {
		g.Log().Errorf(ctx, "存储用户权限到Redis失败: username=%s, error=%v", username, err)
		return err
	}

	// 设置过期时间
	_, err = redis.Expire(ctx, cacheKey, int64(UserPermissionsTTL.Seconds()))
	if err != nil {
		g.Log().Errorf(ctx, "设置用户权限缓存过期时间失败: username=%s, error=%v", username, err)
		return err
	}

	g.Log().Debugf(ctx, "成功缓存用户权限: username=%s, count=%d", username, len(permissions))
	return nil
}

// GetUserPermissions 获取缓存的用户权限
func (c *CacheManager) GetUserPermissions(ctx context.Context, username string) ([]Permission, error) {
	cacheKey := fmt.Sprintf(UserPermissionsCachePrefix, username)

	// 从Redis获取权限信息
	redis := g.Redis()
	cachedData, err := redis.Get(ctx, cacheKey)
	if err != nil {
		g.Log().Debugf(ctx, "从Redis获取用户权限失败: username=%s, error=%v", username, err)
		return nil, err
	}

	if cachedData == nil {
		g.Log().Debugf(ctx, "Redis中不存在用户权限缓存: username=%s", username)
		return nil, nil
	}

	// 检查缓存数据是否为空
	cachedString := cachedData.String()
	if cachedString == "" {
		g.Log().Warningf(ctx, "Redis中用户权限缓存为空: username=%s", username)
		// 删除损坏的缓存
		_, _ = redis.Del(ctx, cacheKey)
		return nil, nil
	}

	// 反序列化权限信息
	var permissions []Permission
	if err := json.Unmarshal([]byte(cachedString), &permissions); err != nil {
		g.Log().Errorf(ctx, "反序列化用户权限失败: username=%s, cached_data_length=%d, error=%v", username, len(cachedString), err)
		// 删除损坏的缓存数据
		_, delErr := redis.Del(ctx, cacheKey)
		if delErr != nil {
			g.Log().Errorf(ctx, "删除损坏的权限缓存失败: username=%s, error=%v", username, delErr)
		}
		return nil, nil // 返回nil而不是错误，让上层重新获取权限信息
	}

	g.Log().Debugf(ctx, "从Redis成功获取用户权限: username=%s, count=%d", username, len(permissions))
	return permissions, nil
}

// SetUserAPIPermissions 缓存用户API权限
func (c *CacheManager) SetUserAPIPermissions(ctx context.Context, username string, apiPermissions map[string]bool) error {
	cacheKey := fmt.Sprintf(UserAPIPermissionsCachePrefix, username)

	// 序列化API权限
	data, err := json.Marshal(apiPermissions)
	if err != nil {
		g.Log().Errorf(ctx, "序列化用户API权限失败: username=%s, error=%v", username, err)
		return err
	}

	// 存储到Redis
	redis := g.Redis()
	_, err = redis.Set(ctx, cacheKey, string(data))
	if err != nil {
		g.Log().Errorf(ctx, "存储用户API权限到Redis失败: username=%s, error=%v", username, err)
		return err
	}

	// 设置过期时间
	_, err = redis.Expire(ctx, cacheKey, int64(UserAPIPermissionsTTL.Seconds()))
	if err != nil {
		g.Log().Errorf(ctx, "设置用户API权限缓存过期时间失败: username=%s, error=%v", username, err)
		return err
	}

	g.Log().Debugf(ctx, "成功缓存用户API权限: username=%s, count=%d", username, len(apiPermissions))
	return nil
}

// GetUserAPIPermissions 获取缓存的用户API权限
func (c *CacheManager) GetUserAPIPermissions(ctx context.Context, username string) (map[string]bool, error) {
	cacheKey := fmt.Sprintf(UserAPIPermissionsCachePrefix, username)

	// 从Redis获取API权限
	redis := g.Redis()
	cachedData, err := redis.Get(ctx, cacheKey)
	if err != nil {
		g.Log().Debugf(ctx, "从Redis获取用户API权限失败: username=%s, error=%v", username, err)
		return nil, err
	}

	if cachedData == nil {
		g.Log().Debugf(ctx, "Redis中不存在用户API权限缓存: username=%s", username)
		return nil, nil
	}

	// 反序列化API权限
	var apiPermissions map[string]bool
	if err := json.Unmarshal([]byte(cachedData.String()), &apiPermissions); err != nil {
		g.Log().Errorf(ctx, "反序列化用户API权限失败: username=%s, error=%v", username, err)
		return nil, err
	}

	g.Log().Debugf(ctx, "从Redis成功获取用户API权限: username=%s, count=%d", username, len(apiPermissions))
	return apiPermissions, nil
}

// SetUserMenuPermissions 缓存用户菜单权限
func (c *CacheManager) SetUserMenuPermissions(ctx context.Context, username string, menuPermissions map[string]bool) error {
	cacheKey := fmt.Sprintf(UserMenuPermissionsCachePrefix, username)

	// 序列化菜单权限
	data, err := json.Marshal(menuPermissions)
	if err != nil {
		g.Log().Errorf(ctx, "序列化用户菜单权限失败: username=%s, error=%v", username, err)
		return err
	}

	// 存储到Redis
	redis := g.Redis()
	_, err = redis.Set(ctx, cacheKey, string(data))
	if err != nil {
		g.Log().Errorf(ctx, "存储用户菜单权限到Redis失败: username=%s, error=%v", username, err)
		return err
	}

	// 设置过期时间
	_, err = redis.Expire(ctx, cacheKey, int64(UserMenuPermissionsTTL.Seconds()))
	if err != nil {
		g.Log().Errorf(ctx, "设置用户菜单权限缓存过期时间失败: username=%s, error=%v", username, err)
		return err
	}

	g.Log().Debugf(ctx, "成功缓存用户菜单权限: username=%s, count=%d", username, len(menuPermissions))
	return nil
}

// GetUserMenuPermissions 获取缓存的用户菜单权限
func (c *CacheManager) GetUserMenuPermissions(ctx context.Context, username string) (map[string]bool, error) {
	cacheKey := fmt.Sprintf(UserMenuPermissionsCachePrefix, username)

	// 从Redis获取菜单权限
	redis := g.Redis()
	cachedData, err := redis.Get(ctx, cacheKey)
	if err != nil {
		g.Log().Debugf(ctx, "从Redis获取用户菜单权限失败: username=%s, error=%v", username, err)
		return nil, err
	}

	if cachedData == nil {
		g.Log().Debugf(ctx, "Redis中不存在用户菜单权限缓存: username=%s", username)
		return nil, nil
	}

	// 反序列化菜单权限
	var menuPermissions map[string]bool
	if err := json.Unmarshal([]byte(cachedData.String()), &menuPermissions); err != nil {
		g.Log().Errorf(ctx, "反序列化用户菜单权限失败: username=%s, error=%v", username, err)
		return nil, err
	}

	g.Log().Debugf(ctx, "从Redis成功获取用户菜单权限: username=%s, count=%d", username, len(menuPermissions))
	return menuPermissions, nil
}

// HasAPIPermission 检查用户是否有指定API权限
func (c *CacheManager) HasAPIPermission(ctx context.Context, username string, apiPath string, method string) (bool, error) {
	apiPermissions, err := c.GetUserAPIPermissions(ctx, username)
	if err != nil {
		return false, err
	}

	if apiPermissions == nil {
		return false, nil
	}

	// 构建权限键：path@method
	permissionKey := fmt.Sprintf("%s@%s", apiPath, method)

	// 检查是否有权限
	hasPermission, exists := apiPermissions[permissionKey]
	if !exists {
		// 如果具体权限不存在，检查是否有通用权限
		generalKey := fmt.Sprintf("%s@*", apiPath)
		hasPermission, exists = apiPermissions[generalKey]
	}

	return hasPermission && exists, nil
}

// HasMenuPermission 检查用户是否有指定菜单权限
func (c *CacheManager) HasMenuPermission(ctx context.Context, username string, menuPath string) (bool, error) {
	menuPermissions, err := c.GetUserMenuPermissions(ctx, username)
	if err != nil {
		return false, err
	}

	if menuPermissions == nil {
		return false, nil
	}

	hasPermission, exists := menuPermissions[menuPath]
	return hasPermission && exists, nil
}

// ClearUserCache 清除用户所有缓存
func (c *CacheManager) ClearUserCache(ctx context.Context, username string) error {
	// 构建所有缓存键
	keys := []string{
		fmt.Sprintf(UserInfoCachePrefix, username),
		fmt.Sprintf(UserPermissionsCachePrefix, username),
		fmt.Sprintf(UserRolesCachePrefix, username),
		fmt.Sprintf(UserMenuPermissionsCachePrefix, username),
		fmt.Sprintf(UserAPIPermissionsCachePrefix, username),
	}

	// 删除所有相关缓存
	redis := g.Redis()
	for _, key := range keys {
		if _, err := redis.Del(ctx, key); err != nil {
			g.Log().Errorf(ctx, "删除缓存失败: key=%s, error=%v", key, err)
			return err
		}
	}

	g.Log().Debugf(ctx, "成功清除用户所有缓存: username=%s", username)
	return nil
}

// ClearUserCacheAsync 异步清除用户缓存
func (c *CacheManager) ClearUserCacheAsync(username string) {
	go func(user string) {
		ctx := gctx.New()
		if err := c.ClearUserCache(ctx, user); err != nil {
			g.Log().Errorf(ctx, "异步清除用户缓存失败: username=%s, error=%v", user, err)
		}
	}(username)
}
