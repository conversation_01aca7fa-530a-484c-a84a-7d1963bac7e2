package middleware

import (
	"strings"

	"admin-api/internal/cache"
	"admin-api/internal/codes"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// 需要跳过权限检查的路径
var skipPermissionPaths = []string{
	"/api/system/login",
	"/api/system/captcha",
	"/api/system/auth/casdoor/signin",
	"/api/auth/login",
	"/api/auth/captcha",
	"/api/system/admin/info",            // 获取管理员信息
	"/api/system/admin/update",          // 更新管理员信息
	"/api/system/user/accessible-menus", // 获取用户可访问菜单
	"static",
	"favicon.ico",
}

// shouldSkipPermissionCheck 判断是否需要跳过权限检查
func shouldSkipPermissionCheck(r *ghttp.Request) bool {
	path := r.URL.Path
	for _, skipPath := range skipPermissionPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// PermissionMiddleware 权限验证中间件
func PermissionMiddleware(r *ghttp.Request) {
	// 如果需要跳过权限检查，直接通过
	if shouldSkipPermissionCheck(r) {
		r.Middleware.Next()
		return
	}

	// 获取用户名
	usernameVar := r.GetCtxVar("username")
	if usernameVar.IsNil() {
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeUnauthorized.Code(),
			"message": "用户未认证",
			"data":    nil,
		})
		r.Exit()
		return
	}

	username := usernameVar.String()
	if username == "" {
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeUnauthorized.Code(),
			"message": "无效的用户名",
			"data":    nil,
		})
		r.Exit()
		return
	}

	// 构建API权限检查路径
	apiPath := r.URL.Path
	method := strings.ToLower(r.Method)

	// 使用权限服务检查API权限
	permissionService := cache.NewPermissionService()
	hasPermission, err := permissionService.HasAPIPermission(r.Context(), username, apiPath, method)
	if err != nil {
		g.Log().Errorf(r.Context(), "检查API权限失败: username=%s, path=%s, method=%s, error=%v",
			username, apiPath, method, err)
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeInternalError.Code(),
			"message": "权限检查失败",
			"data":    nil,
		})
		r.Exit()
		return
	}

	if !hasPermission {
		g.Log().Warningf(r.Context(), "用户无API权限: username=%s, path=%s, method=%s",
			username, apiPath, method)
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeForbidden.Code(),
			"message": "没有访问权限",
			"data":    nil,
		})
		r.Exit()
		return
	}

	// 权限检查通过，继续处理请求
	r.Middleware.Next()
}

// MenuPermissionMiddleware 菜单权限验证中间件（用于前端菜单相关的API）
func MenuPermissionMiddleware(r *ghttp.Request) {
	// 获取用户名
	usernameVar := r.GetCtxVar("username")
	if usernameVar.IsNil() {
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeUnauthorized.Code(),
			"message": "用户未认证",
			"data":    nil,
		})
		r.Exit()
		return
	}

	username := usernameVar.String()

	// 从请求参数或header中获取菜单路径
	menuPath := r.Get("menuPath").String()
	if menuPath == "" {
		menuPath = r.Header.Get("X-Menu-Path")
	}

	if menuPath != "" {
		// 使用权限服务检查菜单权限
		permissionService := cache.NewPermissionService()
		hasPermission, err := permissionService.HasMenuPermission(r.Context(), username, menuPath)
		if err != nil {
			g.Log().Errorf(r.Context(), "检查菜单权限失败: username=%s, menuPath=%s, error=%v",
				username, menuPath, err)
			r.Response.WriteJson(g.Map{
				"code":    codes.CodeInternalError.Code(),
				"message": "权限检查失败",
				"data":    nil,
			})
			r.Exit()
			return
		}

		if !hasPermission {
			g.Log().Warningf(r.Context(), "用户无菜单权限: username=%s, menuPath=%s",
				username, menuPath)
			r.Response.WriteJson(g.Map{
				"code":    codes.CodeForbidden.Code(),
				"message": "没有菜单访问权限",
				"data":    nil,
			})
			r.Exit()
			return
		}
	}

	// 权限检查通过，继续处理请求
	r.Middleware.Next()
}

// GetUserPermissionInfo 获取用户权限信息（用于调试）
func GetUserPermissionInfo(r *ghttp.Request) {
	usernameVar := r.GetCtxVar("username")
	if usernameVar.IsNil() {
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeUnauthorized.Code(),
			"message": "用户未认证",
			"data":    nil,
		})
		return
	}

	username := usernameVar.String()
	cacheManager := cache.GetInstance()

	// 获取用户权限信息
	permissions, _ := cacheManager.GetUserPermissions(r.Context(), username)
	apiPermissions, _ := cacheManager.GetUserAPIPermissions(r.Context(), username)
	menuPermissions, _ := cacheManager.GetUserMenuPermissions(r.Context(), username)

	r.Response.WriteJson(g.Map{
		"code":    0,
		"message": "success",
		"data": g.Map{
			"username":        username,
			"permissions":     permissions,
			"apiPermissions":  apiPermissions,
			"menuPermissions": menuPermissions,
		},
	})
}
