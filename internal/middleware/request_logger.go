package middleware

import (
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// RequestLogger 请求日志中间件，记录所有HTTP请求信息
func RequestLogger(r *ghttp.Request) {
	// 开始时间
	startTime := gtime.Now()

	// 获取请求ID，如果没有则生成一个
	requestId := r.GetHeader("X-Request-Id")
	if requestId == "" {
		requestId = gconv.String(startTime.UnixNano())
	}

	// 保存请求ID到请求上下文，方便后续使用
	r.SetCtxVar("requestId", requestId)

	// 获取请求内容
	clientIP := r.GetClientIp()
	method := r.Method
	url := r.URL.String()
	userAgent := r.GetHeader("User-Agent")

	// 记录请求信息
	loggerCtx := r.GetCtx()
	httpLogger := g.Log("http")

	// 处理请求前的日志
	httpLogger.Infof(loggerCtx, "[REQUEST][%s] %s | %s | %s | %s", requestId, clientIP, method, url, userAgent)

	// 对于非GET请求，记录请求体内容
	if method != "GET" && method != "OPTIONS" {
		// 检查是否为文件上传请求
		contentType := r.Header.Get("Content-Type")
		httpLogger.Debugf(loggerCtx, "[DEBUG][%s] Checking for multipart form. Content-Type: %s", requestId, contentType)
		multipartForm := r.GetMultipartForm()                                                                    // 调用一次并存储结果
		httpLogger.Debugf(loggerCtx, "[DEBUG][%s] Result of GetMultipartForm(): %v", requestId, multipartForm)   // 记录结果
		isMultipartForm := len(contentType) > 0 && multipartForm != nil && len(multipartForm.File) > 0           // 修改后的代码
		httpLogger.Debugf(loggerCtx, "[DEBUG][%s] isMultipartForm evaluated to: %t", requestId, isMultipartForm) // 记录判断结果
		if isMultipartForm {
			// 对于文件上传请求，记录文件信息而非内容
			fileInfo := make(map[string]interface{})
			for name, fileHeaders := range multipartForm.File { // 修改后的代码
				files := make([]string, 0)
				for _, fh := range fileHeaders {
					files = append(files, fh.Filename)
				}
				fileInfo[name] = files
			}
			fileInfoJson, _ := gjson.Encode(fileInfo)
			httpLogger.Debugf(loggerCtx, "[REQUEST-BODY][%s] File upload: %s", requestId, string(fileInfoJson))
		} else {
			requestBody := string(r.GetBody())

			if requestBody != "" {
				// 尝试解析JSON请求体，过滤敏感字段
				if len(requestBody) > 0 && (requestBody[0] == '{' || requestBody[0] == '[') {
					var requestMap map[string]interface{}
					if j, err := gjson.DecodeToJson(requestBody); err == nil {
						requestMap = j.Map()
						// 删除file和files字段
						delete(requestMap, "file")
						delete(requestMap, "files")
						// 将过滤后的请求体转回JSON
						filteredBody, _ := gjson.Encode(requestMap)
						httpLogger.Debugf(loggerCtx, "[REQUEST-BODY][%s] %s", requestId, string(filteredBody))
					} else {
						// 如果JSON解析失败，则记录原始内容
						httpLogger.Debugf(loggerCtx, "[REQUEST-BODY][%s] %s", requestId, requestBody)
					}
				} else {
					// 非JSON内容直接记录
					httpLogger.Debugf(loggerCtx, "[REQUEST-BODY][%s] %s", requestId, requestBody)
				}
			}
		}
	}

	// 启用响应内容缓存
	r.Response.Buffer()

	// 处理请求
	r.Middleware.Next()

	// 结束时间
	endTime := gtime.Now()

	// 计算处理时间（毫秒）
	latency := endTime.Sub(startTime) / time.Millisecond

	// 获取响应状态
	status := r.Response.Status

	// 处理响应日志
	var errorMsg string
	if r.GetError() != nil {
		errorMsg = r.GetError().Error()
	}

	// 根据状态码选择日志级别
	if status >= 500 {
		// 服务器错误用Error级别记录
		httpLogger.Errorf(loggerCtx, "[RESPONSE][%s] %s | %s | %s | %dms | %d | %s",
			requestId, clientIP, method, url, latency, status, errorMsg)
	} else if status >= 400 {
		// 客户端错误用Warning级别记录
		httpLogger.Warningf(loggerCtx, "[RESPONSE][%s] %s | %s | %s | %dms | %d | %s",
			requestId, clientIP, method, url, latency, status, errorMsg)
	} else {
		// 正常请求用Info级别记录
		httpLogger.Infof(loggerCtx, "[RESPONSE][%s] %s | %s | %s | %dms | %d",
			requestId, clientIP, method, url, latency, status)
	}

	// 记录响应内容
	if r.Response.BufferLength() > 0 {
		// 记录响应大小
		httpLogger.Debugf(loggerCtx, "[RESPONSE-SIZE][%s] %d bytes", requestId, r.Response.BufferLength())

		// 获取响应内容并记录（注意：这里应该避免记录过大的响应，如文件下载等）
		// 可以设置一个阈值，只记录小于某个大小的响应
		if r.Response.BufferLength() < 10240 { // 例如小于10KB的响应
			responseContent := r.Response.BufferString()
			httpLogger.Debugf(loggerCtx, "[RESPONSE-BODY][%s] %s", requestId, responseContent)
		} else {
			httpLogger.Debugf(loggerCtx, "[RESPONSE-BODY][%s] Content too large to log", requestId)
		}
	}
}
