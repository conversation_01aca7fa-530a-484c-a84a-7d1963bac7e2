package middleware

import (
	"strings"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"

	"admin-api/internal/codes"
)

// 忽略记录的路径前缀
var ignoreAuthPaths = []string{
	"/api/system/login",
	"/api/system/captcha",
	"/api/system/auth/casdoor/signin",
	"/api/auth/login",                // 登录接口单独使用登录日志
	"/api/auth/captcha",              // 验证码不需要记录操作日志
	"/api/system/operation-log",      // 操作日志接口自身不记录
	"static",                         // 静态资源
	"favicon.ico",                    // 网站图标
	"/api/system/operation-log/list", // 操作日志接口自身不记录
}

// shouldIgnoreAuthRequest 判断是否需要忽略该请求
func shouldIgnoreAuthRequest(r *ghttp.Request) bool {
	// 检查请求方法
	for _, method := range ignoreMethods {
		if r.Method == method {
			return true
		}
	}

	// 检查请求路径
	path := r.URL.Path
	for _, prefix := range ignoreAuthPaths {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	// 特定路由判断逻辑
	if strings.HasPrefix(path, "/api") && r.Method == "GET" {
		// 可以选择性忽略GET请求
		// return true
	}

	return false
}

// AuthMiddleware 用户Token验证中间件（重构版）
func AuthMiddleware(r *ghttp.Request) {
	// 检查是否需要忽略该请求
	if shouldIgnoreAuthRequest(r) {
		r.Middleware.Next()
		return
	}

	// 1. 获取 Authorization 头部
	authorization := r.GetHeader("Authorization")
	if authorization == "" {
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeUnauthorized.Code(),
			"message": "缺少认证",
			"data":    nil,
		})
		r.Exit()
		return
	}

	// 2. 解析 Bearer Token
	token := strings.Split(authorization, "Bearer ")
	if len(token) != 2 {
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeUnauthorized.Code(),
			"message": "无效的认证格式",
			"data":    nil,
		})
		r.Exit()
		return
	}

	// 3. 解析JWT Token
	claims, err := casdoorsdk.ParseJwtToken(token[1])
	if err != nil {
		g.Log().Errorf(r.Context(), "JWT Token解析失败: %v", err)
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeAuthInvalidRequest.Code(),
			"message": "无效的认证",
			"data":    nil,
		})
		r.Exit()
		return
	}

	// 4. 从缓存中获取用户信息来获取userId
	username := claims.Name
	if username == "" {
		r.Response.WriteJson(g.Map{
			"code":    codes.CodeUnauthorized.Code(),
			"message": "Token中缺少用户名",
			"data":    nil,
		})
		r.Exit()
		return
	}

	r.SetCtxVar("username", username)
	r.SetCtxVar("tokenType", "admin")

	// 7. 继续处理请求
	r.Middleware.Next()
}
