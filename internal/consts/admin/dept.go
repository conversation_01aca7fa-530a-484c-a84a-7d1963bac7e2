// internal/consts/admin/dept.go
package dept

import "github.com/gogf/gf/v2/frame/g"

// 定义部门类型常量 (字符串形式，对应数据库 varchar)
const (
	TypeCompany    = "company"    // 公司级别
	TypeCenter     = "center"     // 中心/事业部级别
	TypeDepartment = "department" // 部门级别
	TypeGroup      = "group"      // 小组级别
	TypeOther      = "other"      // 其他自定义级别
)

// DeptType 定义部门类型结构，方便API返回或前端使用
type DeptType struct {
	Code string `json:"code"` // 类型编码 (存储值)
	Name string `json:"name"` // 类型名称 (显示值)
}

// TypeList 返回所有预定义的部门类型列表
// 注意：这里的顺序可能影响前端下拉框的显示顺序
// 使用 g.Slice 来定义，方便使用框架的内置函数
var TypeList = g.Slice{
	DeptType{Code: TypeCompany, Name: "公司"},
	DeptType{Code: TypeCenter, Name: "中心/事业部"},
	DeptType{Code: TypeDepartment, Name: "部门"},
	DeptType{Code: TypeGroup, Name: "小组"},
	DeptType{Code: TypeOther, Name: "其他"},
}

// TypeMap 提供 Code到Name 的快速查找映射
var TypeMap = func() g.MapStrStr {
	m := g.MapStrStr{}
	for _, v := range TypeList {
		item := v.(DeptType) // 断言为 DeptType
		m[item.Code] = item.Name
	}
	return m
}()

// IsValidType 检查给定的类型字符串是否是预定义的有效类型
func IsValidType(t string) bool {
	_, ok := TypeMap[t]
	return ok
}

// GetTypeName 根据类型编码获取类型名称，如果无效则返回空字符串
func GetTypeName(code string) string {
	return TypeMap[code] // 如果 key 不存在，map 会返回零值 (空字符串)
}
