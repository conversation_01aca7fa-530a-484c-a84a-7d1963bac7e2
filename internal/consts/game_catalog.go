package consts

// ProductType constants
const (
	ProductTypeEG5 = "EG5" // Electronic Games
	ProductTypePG  = "PG"  // Poker Games
	ProductTypePP  = "PP"  // PVP Games
)

// ProductType codes
const (
	ProductTypeEG5Code = 191 // Electronic Games
	ProductTypePGCode  = 98  // Poker Games
	ProductTypePPCode  = 39  // PVP Games
)

// Platform constants
const (
	PlatformFlash = "flash"
	PlatformHTML5 = "html5"
	PlatformAll   = "all"
)

// GameType constants
const (
	GameTypeRNG  = "RNG"
	GameTypeLive = "LIVE"
	GameTypePVP  = "PVP"
)

// ProductTypeMap maps ProductType to its code
var ProductTypeMap = map[string]int{
	ProductTypeEG5: ProductTypeEG5Code,
	ProductTypePG:  ProductTypePGCode,
	ProductTypePP:  ProductTypePPCode,
}

// ProductTypeCodeMap maps code to ProductType
var ProductTypeCodeMap = map[int]string{
	ProductTypeEG5Code: ProductTypeEG5,
	ProductTypePGCode:  ProductTypePG,
	ProductTypePPCode:  ProductTypePP,
}

// ValidProductTypes list of valid product types
var ValidProductTypes = []string{ProductTypeEG5, ProductTypePG, ProductTypePP}

// ValidPlatforms list of valid platforms
var ValidPlatforms = []string{PlatformFlash, PlatformHTML5, PlatformAll}

// ValidGameTypes list of valid game types
var ValidGameTypes = []string{GameTypeRNG, GameTypeLive, GameTypePVP}