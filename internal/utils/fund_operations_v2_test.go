package utils

import (
	"admin-api/internal/constants"
	"testing"
)

func TestFundOperationDescriptor_FormatBasicDescription(t *testing.T) {
	tests := []struct {
		name      string
		language  string
		operation constants.FundOperationType
		amount    string
		symbol    string
		expected  string
	}{
		{
			name:      "Transfer out Chinese",
			language:  "zh",
			operation: constants.FundOpTransferOut,
			amount:    "100.00",
			symbol:    "USDT",
			expected:  "转账扣除: 100.00 USDT",
		},
		{
			name:      "Transfer out English",
			language:  "en",
			operation: constants.FundOpTransferOut,
			amount:    "100.00",
			symbol:    "USDT",
			expected:  "Transfer debit: 100.00 USDT",
		},
		{
			name:      "Red packet claim Chinese",
			language:  "zh",
			operation: constants.FundOpRedPacketClaim,
			amount:    "50.00",
			symbol:    "USDT",
			expected:  "红包领取: 50.00 USDT",
		},
		{
			name:      "Red packet claim English",
			language:  "en",
			operation: constants.FundOpRedPacketClaim,
			amount:    "50.00",
			symbol:    "USDT",
			expected:  "Red packet claim: 50.00 USDT",
		},
		{
			name:      "Payment in Chinese",
			language:  "zh",
			operation: constants.FundOpPaymentIn,
			amount:    "25.50",
			symbol:    "BTC",
			expected:  "支付收入: 25.50 BTC",
		},
		{
			name:      "Commission English",
			language:  "en",
			operation: constants.FundOpCommission,
			amount:    "5.00",
			symbol:    "USDT",
			expected:  "Commission: 5.00 USDT",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			descriptor := NewFundOperationDescriptor(tt.language)
			result := descriptor.FormatBasicDescription(tt.operation, tt.amount, tt.symbol)
			if result != tt.expected {
				t.Errorf("FormatBasicDescription() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFundOperationDescriptor_FormatDescriptionWithTarget(t *testing.T) {
	tests := []struct {
		name      string
		language  string
		operation constants.FundOperationType
		target    string
		amount    string
		symbol    string
		expected  string
	}{
		{
			name:      "Transfer to user Chinese",
			language:  "zh",
			operation: constants.FundOpTransferOut,
			target:    "张三",
			amount:    "100.00",
			symbol:    "USDT",
			expected:  "转账给 张三: 100.00 USDT",
		},
		{
			name:      "Transfer to user English",
			language:  "en",
			operation: constants.FundOpTransferOut,
			target:    "John",
			amount:    "100.00",
			symbol:    "USDT",
			expected:  "Transfer to John: 100.00 USDT",
		},
		{
			name:      "Received from user Chinese",
			language:  "zh",
			operation: constants.FundOpTransferIn,
			target:    "李四",
			amount:    "50.00",
			symbol:    "USDT",
			expected:  "收到 李四 转账: 50.00 USDT",
		},
		{
			name:      "Payment to merchant English",
			language:  "en",
			operation: constants.FundOpPaymentOut,
			target:    "Merchant",
			amount:    "25.00",
			symbol:    "USDT",
			expected:  "Pay to Merchant: 25.00 USDT",
		},
		{
			name:      "Withdraw to address Chinese",
			language:  "zh",
			operation: constants.FundOpWithdrawReq,
			target:    "**********************************",
			amount:    "0.001",
			symbol:    "BTC",
			expected:  "提现到 **********************************: 0.001 BTC",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			descriptor := NewFundOperationDescriptor(tt.language)
			result := descriptor.FormatDescriptionWithTarget(tt.operation, tt.target, tt.amount, tt.symbol)
			if result != tt.expected {
				t.Errorf("FormatDescriptionWithTarget() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFundOperationDescriptor_FormatDescriptionWithMemo(t *testing.T) {
	tests := []struct {
		name      string
		language  string
		operation constants.FundOperationType
		amount    string
		symbol    string
		memo      string
		expected  string
	}{
		{
			name:      "Transfer with memo Chinese",
			language:  "zh",
			operation: constants.FundOpTransferOut,
			amount:    "100.00",
			symbol:    "USDT",
			memo:      "生日礼物",
			expected:  "转账扣除: 100.00 USDT - 生日礼物",
		},
		{
			name:      "Transfer without memo Chinese",
			language:  "zh",
			operation: constants.FundOpTransferOut,
			amount:    "100.00",
			symbol:    "USDT",
			memo:      "",
			expected:  "转账扣除: 100.00 USDT",
		},
		{
			name:      "Payment with memo English",
			language:  "en",
			operation: constants.FundOpPaymentOut,
			amount:    "50.00",
			symbol:    "USDT",
			memo:      "Service fee",
			expected:  "Payment debit: 50.00 USDT - Service fee",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			descriptor := NewFundOperationDescriptor(tt.language)
			result := descriptor.FormatDescriptionWithMemo(tt.operation, tt.amount, tt.symbol, tt.memo)
			if result != tt.expected {
				t.Errorf("FormatDescriptionWithMemo() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFundOperationDescriptor_FormatSwapDescription(t *testing.T) {
	tests := []struct {
		name       string
		language   string
		fromAmount string
		fromSymbol string
		toAmount   string
		toSymbol   string
		expected   string
	}{
		{
			name:       "Swap USDT to BTC Chinese",
			language:   "zh",
			fromAmount: "100.00",
			fromSymbol: "USDT",
			toAmount:   "0.003",
			toSymbol:   "BTC",
			expected:   "兑换 100.00 USDT 为 0.003 BTC",
		},
		{
			name:       "Swap USDT to BTC English",
			language:   "en",
			fromAmount: "100.00",
			fromSymbol: "USDT",
			toAmount:   "0.003",
			toSymbol:   "BTC",
			expected:   "Swap 100.00 USDT for 0.003 BTC",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			descriptor := NewFundOperationDescriptor(tt.language)
			result := descriptor.FormatSwapDescription(tt.fromAmount, tt.fromSymbol, tt.toAmount, tt.toSymbol)
			if result != tt.expected {
				t.Errorf("FormatSwapDescription() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFundOperationDescriptor_GenerateBusinessID(t *testing.T) {
	tests := []struct {
		name        string
		operation   constants.FundOperationType
		identifiers []interface{}
		expected    string
	}{
		{
			name:        "Transfer business ID",
			operation:   constants.FundOpTransferOut,
			identifiers: []interface{}{12345, 67890},
			expected:    "transfer_out_12345_67890",
		},
		{
			name:        "Red packet business ID",
			operation:   constants.FundOpRedPacketCreate,
			identifiers: []interface{}{"uuid-123", 456},
			expected:    "rp_create_uuid-123_456",
		},
		{
			name:        "Payment business ID",
			operation:   constants.FundOpPaymentOut,
			identifiers: []interface{}{789},
			expected:    "payment_out_789",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			descriptor := NewFundOperationDescriptor("zh")
			result := descriptor.GenerateBusinessID(tt.operation, tt.identifiers...)
			if result != tt.expected {
				t.Errorf("GenerateBusinessID() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFundOperationType_IsValid(t *testing.T) {
	tests := []struct {
		name      string
		operation constants.FundOperationType
		expected  bool
	}{
		{
			name:      "Valid transfer operation",
			operation: constants.FundOpTransferOut,
			expected:  true,
		},
		{
			name:      "Valid red packet operation",
			operation: constants.FundOpRedPacketClaim,
			expected:  true,
		},
		{
			name:      "Invalid operation",
			operation: constants.FundOperationType("invalid_operation"),
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.operation.IsValid()
			if result != tt.expected {
				t.Errorf("IsValid() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// 测试向后兼容性
func TestBackwardCompatibility(t *testing.T) {
	tests := []struct {
		name     string
		function func() string
		expected string
	}{
		{
			name: "FormatTransferDescription out with username",
			function: func() string {
				return FormatTransferDescription("out", "张三", "100.00", "USDT")
			},
			expected: "转账给 张三: 100.00 USDT",
		},
		{
			name: "FormatTransferDescription in without username",
			function: func() string {
				return FormatTransferDescription("in", "", "50.00", "USDT")
			},
			expected: "转账收入: 50.00 USDT",
		},
		{
			name: "FormatRedPacketDescription claim",
			function: func() string {
				return FormatRedPacketDescription("claim", "uuid-123", "25.00", "USDT")
			},
			expected: "红包领取: 25.00 USDT",
		},
		{
			name: "FormatPaymentDescriptionEN out",
			function: func() string {
				return FormatPaymentDescriptionEN("out", "John", "75.00", "USDT")
			},
			expected: "Pay to John: 75.00 USDT",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.function()
			if result != tt.expected {
				t.Errorf("Backward compatibility test failed: got %v, want %v", result, tt.expected)
			}
		})
	}
}
