package utils

import (
	"admin-api/internal/constants"
	"fmt"
	"strings"
)

// FundOperationDescriptor 统一的资金操作描述生成器
type FundOperationDescriptor struct {
	language string // 默认语言
}

// NewFundOperationDescriptor 创建新的资金操作描述生成器
func NewFundOperationDescriptor(language string) *FundOperationDescriptor {
	return &FundOperationDescriptor{
		language: language,
	}
}

// GenerateBusinessID 生成标准化的业务ID
func (f *FundOperationDescriptor) GenerateBusinessID(operation constants.FundOperationType, identifiers ...interface{}) string {
	prefix := operation.GetBusinessIDPrefix()
	parts := []string{prefix}
	for _, id := range identifiers {
		parts = append(parts, fmt.Sprintf("%v", id))
	}
	return strings.Join(parts, "_")
}

// FormatBasicDescription 格式化基础描述 (操作: 金额 币种)
func (f *FundOperationDescriptor) FormatBasicDescription(operation constants.FundOperationType, amount string, symbol string) string {
	return operation.FormatDescription(f.language, amount, symbol)
}

// FormatDescriptionWithTarget 格式化带目标的描述 (操作给/从 目标: 金额 币种)
func (f *FundOperationDescriptor) FormatDescriptionWithTarget(operation constants.FundOperationType, target string, amount string, symbol string) string {
	return operation.FormatDescriptionWithTarget(f.language, target, amount, symbol)
}

// FormatDescriptionWithMemo 格式化带备注的描述 (操作: 金额 币种 - 备注)
func (f *FundOperationDescriptor) FormatDescriptionWithMemo(operation constants.FundOperationType, amount string, symbol string, memo string) string {
	baseDesc := operation.FormatDescription(f.language, amount, symbol)
	if memo != "" {
		return fmt.Sprintf("%s - %s", baseDesc, memo)
	}
	return baseDesc
}

// FormatDescriptionWithTargetAndMemo 格式化带目标和备注的描述
func (f *FundOperationDescriptor) FormatDescriptionWithTargetAndMemo(operation constants.FundOperationType, target string, amount string, symbol string, memo string) string {
	baseDesc := operation.FormatDescriptionWithTarget(f.language, target, amount, symbol)
	if memo != "" {
		return fmt.Sprintf("%s - %s", baseDesc, memo)
	}
	return baseDesc
}

// FormatSwapDescription 格式化兑换描述 (兑换 源金额 源币种 为 目标金额 目标币种)
func (f *FundOperationDescriptor) FormatSwapDescription(fromAmount, fromSymbol, toAmount, toSymbol string) string {
	if f.language == "en" || f.language == "EN" {
		return fmt.Sprintf("Swap %s %s for %s %s", fromAmount, fromSymbol, toAmount, toSymbol)
	}
	return fmt.Sprintf("兑换 %s %s 为 %s %s", fromAmount, fromSymbol, toAmount, toSymbol)
}

// FormatPaymentRequestDescription 格式化支付请求描述
func (f *FundOperationDescriptor) FormatPaymentRequestDescription(operation constants.FundOperationType, requestID int64, amount string, symbol string) string {
	var templateCN, templateEN string

	switch operation {
	case constants.FundOpPaymentOut:
		templateCN = "支付请求 #%d: %s %s"
		templateEN = "Pay request #%d: %s %s"
	case constants.FundOpPaymentIn:
		templateCN = "收款请求 #%d: %s %s"
		templateEN = "Receive request #%d: %s %s"
	default:
		return operation.FormatDescription(f.language, amount, symbol)
	}

	if f.language == "en" || f.language == "EN" {
		return fmt.Sprintf(templateEN, requestID, amount, symbol)
	}
	return fmt.Sprintf(templateCN, requestID, amount, symbol)
}

// 全局实例，用于向后兼容
var (
	DefaultDescriptorCN = NewFundOperationDescriptor("zh")
	DefaultDescriptorEN = NewFundOperationDescriptor("en")
)

// 注意：向后兼容的函数已经在 fund_operations.go 中定义
// 这里不重复定义以避免冲突
// 新代码应该使用 FundOperationDescriptor 和枚举类型
