package s3

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"path"
	"strings"

	"admin-api/internal/library/storage" // Import the unified interface
	"admin-api/internal/model"

	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	types "github.com/aws/aws-sdk-go-v2/service/s3/types" // Import s3 types for ACL

	// "github.com/aws/smithy-go" // For error handling (needed if bucket check is enabled)
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// Service manages AWS S3 operations.
type Service struct {
	client *s3.Client
	config model.S3Config
}

// NewClient initializes and returns a new S3 service client.
func NewClient(ctx context.Context, cfg model.S3Config) (storage.IStorage, error) {
	if cfg.Region == "" || cfg.AccessKeyID == "" || cfg.SecretAccessKey == "" || cfg.BucketName == "" {
		g.Log().Errorf(ctx, "S3配置不完整: Region=%s, AccessKeyID=%s, SecretAccessKey=****, BucketName=%s",
			cfg.Region, cfg.AccessKeyID, cfg.BucketName)
		return nil, gerror.New("S3 configuration is incomplete (region, accessKeyID, secretAccessKey, bucketName are required)")
	}

	g.Log().Infof(ctx, "正在初始化AWS S3客户端, Region=%s, Bucket=%s", cfg.Region, cfg.BucketName)

	awsSDKConfig, err := awsConfig.LoadDefaultConfig(ctx,
		awsConfig.WithRegion(cfg.Region),
		awsConfig.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(cfg.AccessKeyID, cfg.SecretAccessKey, "")),
	)
	if err != nil {
		g.Log().Errorf(ctx, "初始化AWS SDK配置失败: %v", err)
		return nil, gerror.Wrap(err, "Failed to load AWS SDK configuration")
	}

	// Create an S3 client from the configuration
	// Use path-style addressing if configured, otherwise default to virtual-hosted style
	s3Client := s3.NewFromConfig(awsSDKConfig, func(o *s3.Options) {
		o.UsePathStyle = cfg.UsePathStyleEndpoint
	})

	// Optional: Verify bucket existence (consider permissions needed)
	// _, err = s3Client.HeadBucket(ctx, &s3.HeadBucketInput{Bucket: &cfg.BucketName})
	// if err != nil {
	// 	g.Log().Errorf(ctx, "检查S3 bucket '%s'是否存在失败: %v", cfg.BucketName, err)
	// 	// Check if the error is NotFound vs other errors like AccessDenied
	// 	var apiErr smithy.APIError
	// 	if errors.As(err, &apiErr) && apiErr.ErrorCode() == "NotFound" {
	// 		return nil, gerror.Newf("S3 bucket '%s' does not exist", cfg.BucketName)
	// 	}
	// 	return nil, gerror.Wrapf(err, "Failed to verify S3 bucket '%s'", cfg.BucketName)
	// }
	// g.Log().Infof(ctx, "S3 bucket '%s' 验证成功", cfg.BucketName)

	g.Log().Infof(ctx, "AWS S3客户端初始化成功")
	return &Service{
		client: s3Client,
		config: cfg,
	}, nil
}

// UploadObject uploads a file stream to S3.
func (s *Service) UploadObject(ctx context.Context, objectKey string, reader io.Reader, size int64, contentType string) (string, error) {
	if s.client == nil {
		return "", gerror.New("S3 client is not initialized")
	}

	// S3 object keys should not start with a slash
	cleanObjectKey := strings.TrimPrefix(objectKey, "/")

	_, err := s.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:        &s.config.BucketName,
		Key:           &cleanObjectKey,
		Body:          reader,
		ContentLength: &size, // Use pointer for ContentLength
		ContentType:   &contentType,
		ACL:           types.ObjectCannedACLPublicRead, // Set objects to be public by default
	})

	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload object '%s' to S3 bucket '%s': %v", cleanObjectKey, s.config.BucketName, err)
		return "", gerror.Wrapf(err, "Failed to upload object '%s' to S3 bucket '%s'", cleanObjectKey, s.config.BucketName)
	}

	g.Log().Infof(ctx, "Successfully uploaded object '%s' to S3 bucket '%s', Size: %d", cleanObjectKey, s.config.BucketName, size)

	// Construct the public URL
	publicURL, err := s.GetObjectURL(ctx, cleanObjectKey)
	if err != nil {
		// Log the error but return the key as a fallback, or handle differently
		g.Log().Warningf(ctx, "Failed to construct public URL for uploaded object '%s': %v. Returning key.", cleanObjectKey, err)
		// Depending on requirements, you might return an empty string and the error,
		// or just the key if a URL isn't strictly necessary for the immediate return.
		// For now, return the constructed URL even if there was an error generating it previously.
		return publicURL, nil // Or return "", err based on strictness
	}

	return publicURL, nil
}

// GetObjectURL returns the permanent public URL for a given object key in S3.
// This assumes the object has public read access or the bucket policy allows it,
// or PublicURLPrefix points to a CDN/proxy.
func (s *Service) GetObjectURL(ctx context.Context, objectKey string) (string, error) {
	// S3 object keys should not start with a slash
	cleanObjectKey := strings.TrimPrefix(objectKey, "/")

	// Use configured prefix if available
	if s.config.PublicURLPrefix != "" {
		baseURL, err := url.Parse(s.config.PublicURLPrefix)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse configured S3 PublicURLPrefix '%s': %v", s.config.PublicURLPrefix, err)
			return "", gerror.Wrapf(err, "Failed to parse configured S3 PublicURLPrefix: %s", s.config.PublicURLPrefix)
		}
		// Use path.Join for cleaner path segment joining
		baseURL.Path = path.Join(baseURL.Path, cleanObjectKey)
		return baseURL.String(), nil
	}

	// Construct standard S3 URL otherwise
	// Format: https://<bucket-name>.s3.<region>.amazonaws.com/<key> (virtual-hosted style)
	// Or: https://s3.<region>.amazonaws.com/<bucket-name>/<key> (path style)
	var publicURL string
	if s.config.UsePathStyleEndpoint {
		// Path-style: https://s3.Region.amazonaws.com/Bucket/Key
		publicURL = fmt.Sprintf("https://s3.%s.amazonaws.com/%s/%s", s.config.Region, s.config.BucketName, cleanObjectKey)
	} else {
		// Virtual-hosted style: https://Bucket.s3.Region.amazonaws.com/Key
		publicURL = fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s.config.BucketName, s.config.Region, cleanObjectKey)
	}

	// Validate the constructed URL
	_, err := url.ParseRequestURI(publicURL)
	if err != nil {
		g.Log().Errorf(ctx, "Constructed invalid S3 URL '%s': %v", publicURL, err)
		return "", gerror.Wrapf(err, "Constructed invalid S3 URL")
	}

	return publicURL, nil
}
