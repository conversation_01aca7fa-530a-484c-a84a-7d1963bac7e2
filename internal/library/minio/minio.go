package minio

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"path"
	"strings"

	"admin-api/internal/library/storage" // Import the unified interface
	"admin-api/internal/model"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// Service manages Minio operations and implements storage.IStorage.
type Service struct {
	client *minio.Client
	config model.MinioConfig
}

// NewClient initializes and returns a new Minio service client based on provided config.
// It now implements the storage.IStorage interface factory pattern.
func NewClient(ctx context.Context, cfg model.MinioConfig) (storage.IStorage, error) {
	// 记录配置信息（不包含敏感信息）
	g.Log().Debugf(ctx, "Minio配置: Endpoint=%s, BucketName=%s, UseSSL=%v, PublicURLPrefix=%s",
		cfg.Endpoint, cfg.BucketName, cfg.UseSSL, cfg.PublicURLPrefix)

	if cfg.Endpoint == "" || cfg.AccessKeyID == "" || cfg.SecretAccessKey == "" || cfg.BucketName == "" {
		g.Log().Errorf(ctx, "Minio配置不完整: Endpoint=%s, AccessKeyID=%s, SecretAccessKey=****, BucketName=%s",
			cfg.Endpoint, cfg.AccessKeyID, cfg.BucketName)
		return nil, gerror.New("Minio configuration is incomplete (endpoint, accessKeyID, secretAccessKey, bucketName are required)")
	}

	// 处理endpoint中可能存在的http/https前缀
	// 注意：这里修改了传入的 cfg 的 UseSSL 字段，如果 cfg 是共享的，可能会有副作用。
	// 更好的做法是创建一个本地副本或不修改原始 cfg。但为了保持与原逻辑一致，暂时保留。
	endpoint := cfg.Endpoint
	useSSL := cfg.UseSSL // Use local variable to avoid modifying cfg directly if possible
	if strings.HasPrefix(endpoint, "http://") {
		endpoint = strings.TrimPrefix(endpoint, "http://")
		useSSL = false
	} else if strings.HasPrefix(endpoint, "https://") {
		endpoint = strings.TrimPrefix(endpoint, "https://")
		useSSL = true
	}

	// Initialize minio client object.
	g.Log().Infof(ctx, "正在初始化Minio客户端, Endpoint=%s, UseSSL=%v", endpoint, useSSL)
	minioClient, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.AccessKeyID, cfg.SecretAccessKey, ""),
		Secure: useSSL,
	})
	if err != nil {
		g.Log().Errorf(ctx, "初始化Minio客户端失败: %v", err)
		return nil, gerror.Wrapf(err, "Failed to initialize Minio client with endpoint: %s", cfg.Endpoint)
	}

	// 验证bucket是否存在
	g.Log().Infof(ctx, "正在检查Minio bucket '%s'是否存在", cfg.BucketName)
	exists, err := minioClient.BucketExists(ctx, cfg.BucketName)
	if err != nil {
		g.Log().Errorf(ctx, "检查Minio bucket '%s'是否存在失败: %v", cfg.BucketName, err)
		return nil, gerror.Wrapf(err, "Failed to check if bucket '%s' exists", cfg.BucketName)
	}
	if !exists {
		g.Log().Errorf(ctx, "Minio bucket '%s'不存在", cfg.BucketName)
		return nil, gerror.Newf("Minio bucket '%s' does not exist", cfg.BucketName)
	}

	g.Log().Infof(ctx, "Minio客户端初始化成功")
	// Assign potentially modified useSSL back to config if needed, or adjust logic
	// For now, we pass the original cfg but use the derived useSSL for connection.
	// The stored config retains the original UseSSL value unless explicitly updated.
	return &Service{
		client: minioClient,
		config: cfg, // Store the original config
	}, nil
}

// UploadObject uploads a file stream to Minio.
func (s *Service) UploadObject(ctx context.Context, objectKey string, reader io.Reader, size int64, contentType string) (string, error) {
	if s.client == nil {
		return "", gerror.New("Minio client is not initialized")
	}

	// Minio object keys might or might not need leading slash trimming depending on usage,
	// but consistency with S3 (no leading slash) is generally good.
	cleanObjectKey := strings.TrimPrefix(objectKey, "/")

	uploadInfo, err := s.client.PutObject(ctx, s.config.BucketName, cleanObjectKey, reader, size, minio.PutObjectOptions{ContentType: contentType})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload object '%s' to Minio bucket '%s': %v", cleanObjectKey, s.config.BucketName, err)
		return "", gerror.Wrapf(err, "Failed to upload object '%s' to Minio bucket '%s'", cleanObjectKey, s.config.BucketName)
	}

	g.Log().Infof(ctx, "Successfully uploaded object '%s' to Minio bucket '%s', ETag: %s, Size: %d", uploadInfo.Key, uploadInfo.Bucket, uploadInfo.ETag, uploadInfo.Size)

	// Construct the public URL using the GetObjectURL method for consistency
	publicURL, err := s.GetObjectURL(ctx, cleanObjectKey)
	if err != nil {
		// Log the error but return the key or potentially incorrect URL as fallback?
		g.Log().Warningf(ctx, "Failed to construct public URL for uploaded Minio object '%s': %v. Returning potentially incorrect URL.", cleanObjectKey, err)
		// Return the URL even if GetObjectURL had an issue, mirroring previous logic.
		// Consider returning "", err for stricter error handling.
		return publicURL, nil
	}

	return publicURL, nil
}

// GetObjectURL returns the permanent public URL for a given object key in Minio.
func (s *Service) GetObjectURL(ctx context.Context, objectKey string) (string, error) {
	// Minio object keys might or might not need leading slash trimming.
	cleanObjectKey := strings.TrimPrefix(objectKey, "/")

	// Use configured prefix if available
	if s.config.PublicURLPrefix != "" {
		prefixURL, err := url.Parse(s.config.PublicURLPrefix)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse configured Minio PublicURLPrefix '%s': %v", s.config.PublicURLPrefix, err)
			// Return empty string and error as we cannot construct the URL
			return "", gerror.Wrapf(err, "Failed to parse configured Minio PublicURLPrefix: %s", s.config.PublicURLPrefix)
		}
		// Use path.Join for cleaner path segment joining
		prefixURL.Path = path.Join(prefixURL.Path, cleanObjectKey)
		return prefixURL.String(), nil
	}

	// Attempt to construct URL based on endpoint if prefix is missing
	g.Log().Warningf(ctx, "Minio PublicURLPrefix is not configured for object '%s'. Attempting to construct URL based on endpoint, but it might be incorrect.", cleanObjectKey)

	// Need to determine the correct scheme and host from the original endpoint config
	// Re-parse the endpoint string stored in config
	endpointURL, parseErr := url.Parse(s.config.Endpoint) // Parse the raw endpoint string
	if parseErr != nil {
		// If the raw endpoint itself is unparseable, we have a problem
		g.Log().Errorf(ctx, "Failed to parse Minio endpoint '%s' from config to construct URL: %v", s.config.Endpoint, parseErr)
		return "", gerror.Wrapf(parseErr, "Failed to parse Minio endpoint '%s' to construct URL", s.config.Endpoint)
	}

	// Determine scheme based on UseSSL config
	scheme := "http"
	if s.config.UseSSL {
		scheme = "https"
	}

	// Construct the URL: scheme://host/bucket/key
	// Ensure host includes port if it was present in the original endpoint string
	host := endpointURL.Host // Host might be empty if endpoint was just "host:port"
	if host == "" {
		host = s.config.Endpoint // Fallback to using the raw endpoint string if parsing failed to get host
		// Remove scheme prefix if present in the raw endpoint string again, just in case
		host = strings.TrimPrefix(host, "http://")
		host = strings.TrimPrefix(host, "https://")
	}

	publicURL := fmt.Sprintf("%s://%s/%s/%s", scheme, host, s.config.BucketName, cleanObjectKey)

	// Validate the constructed URL
	_, err := url.ParseRequestURI(publicURL)
	if err != nil {
		g.Log().Errorf(ctx, "Constructed invalid Minio URL '%s': %v", publicURL, err)
		return "", gerror.Wrapf(err, "Constructed invalid Minio URL")
	}

	return publicURL, nil
}
