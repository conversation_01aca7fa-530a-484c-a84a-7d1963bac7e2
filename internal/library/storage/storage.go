package storage

import (
	"context"
	"io"
)

// IStorage defines the interface for object storage operations.
// It allows for seamless switching between different storage providers like Minio and AWS S3.
type IStorage interface {
	// UploadObject uploads an object to the storage.
	// objectKey: The full path and filename for the object in the bucket.
	// reader: The content stream of the object.
	// size: The size of the content.
	// contentType: The MIME type of the object (e.g., "image/jpeg").
	// Returns the public URL of the uploaded object and an error if any occurred.
	UploadObject(ctx context.Context, objectKey string, reader io.Reader, size int64, contentType string) (uploadURL string, err error)

	// GetObjectURL returns the permanent public URL for a given object key.
	// objectKey: The full path and filename for the object in the bucket.
	// Returns the public URL and an error if any occurred (e.g., object not found, configuration error).
	GetObjectURL(ctx context.Context, objectKey string) (url string, err error)
}
