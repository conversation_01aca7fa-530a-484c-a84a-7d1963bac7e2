package config

import (
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

// Config 价格监控服务配置
type Config struct {
	Service    ServiceConfig    `yaml:"service"`
	WebSocket  WebSocketConfig  `yaml:"websocket"`
	C2C        C2CConfig        `yaml:"c2c"`
	Redis      RedisConfig      `yaml:"redis"`
	Symbols    []string         `yaml:"symbols"`
	FiatPairs  []FiatPairConfig `yaml:"fiat_pairs"`
	Validation ValidationConfig `yaml:"validation"`
	Monitoring MonitoringConfig `yaml:"monitoring"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Name     string `yaml:"name"`
	Port     int    `yaml:"port"`
	LogLevel string `yaml:"log_level"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	Providers map[string]ProviderConfig `yaml:"providers"`
}

// ProviderConfig 提供商配置
type ProviderConfig struct {
	URL            string        `yaml:"url"`
	ReconnectDelay time.Duration `yaml:"reconnect_delay"`
	PingInterval   time.Duration `yaml:"ping_interval"`
	PongTimeout    time.Duration `yaml:"pong_timeout"`
	Enabled        bool          `yaml:"enabled"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	ConfigName string `yaml:"config_name"` // 使用项目现有的Redis配置名称
	PoolSize   int    `yaml:"pool_size"`
	MaxRetries int    `yaml:"max_retries"`
}

// ValidationConfig 验证配置
type ValidationConfig struct {
	MaxPriceChange     float64       `yaml:"max_price_change"`     // 最大价格变动百分比
	MinUpdateInterval  time.Duration `yaml:"min_update_interval"`  // 最小更新间隔
	StaleDataThreshold time.Duration `yaml:"stale_data_threshold"` // 过期数据阈值
}

// C2CConfig C2C API配置
type C2CConfig struct {
	Enabled        bool          `yaml:"enabled"`
	UpdateInterval time.Duration `yaml:"update_interval"` // 更新间隔
	Timeout        time.Duration `yaml:"timeout"`         // 请求超时
	MaxRetries     int           `yaml:"max_retries"`     // 最大重试次数
}

// FiatPairConfig 法币交易对配置
type FiatPairConfig struct {
	Asset        string `yaml:"asset"`         // 资产符号 (USDT)
	FiatCurrency string `yaml:"fiat_currency"` // 法币符号 (CNY)
	Enabled      bool   `yaml:"enabled"`       // 是否启用
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	PrometheusPort      int           `yaml:"prometheus_port"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
}

// 默认配置
var defaultConfig = &Config{
	Service: ServiceConfig{
		Name:     "price-monitor-service",
		Port:     8080,
		LogLevel: "info",
	},
	WebSocket: WebSocketConfig{
		Providers: map[string]ProviderConfig{
			"binance": {
				URL:            "wss://stream.binance.com:9443/ws",
				ReconnectDelay: 5 * time.Second,
				PingInterval:   30 * time.Second,
				PongTimeout:    10 * time.Second,
				Enabled:        true,
			},
		},
	},
	C2C: C2CConfig{
		Enabled:        true,
		UpdateInterval: 30 * time.Second, // 30秒更新一次
		Timeout:        10 * time.Second,
		MaxRetries:     3,
	},
	Redis: RedisConfig{
		ConfigName: "default",
		PoolSize:   10,
		MaxRetries: 3,
	},
	Symbols: []string{
		"ETHUSDT",
		"BTCUSDT",
		"BNBUSDT",
	},
	FiatPairs: []FiatPairConfig{
		{
			Asset:        "USDT",
			FiatCurrency: "CNY",
			Enabled:      true,
		},
	},
	Validation: ValidationConfig{
		MaxPriceChange:     0.1, // 10%
		MinUpdateInterval:  1 * time.Second,
		StaleDataThreshold: 30 * time.Second,
	},
	Monitoring: MonitoringConfig{
		PrometheusPort:      9090,
		HealthCheckInterval: 10 * time.Second,
	},
}

// LoadConfig 加载配置
func LoadConfig() (*Config, error) {
	cfg := &Config{}
	ctx := gctx.New()

	// 从配置文件加载
	v, err := g.Cfg().GetWithEnv(ctx, "priceMonitor")
	if err != nil || v.IsNil() {
		g.Log().Warning(ctx, "Failed to load price monitor config from file, using default config", err)
		cfg = defaultConfig
	} else {
		if err := v.Scan(cfg); err != nil {
			g.Log().Warning(ctx, "Failed to scan price monitor config, using default config", err)
			cfg = defaultConfig
		}
	}

	// 合并默认配置
	if cfg.Service.Name == "" {
		cfg.Service = defaultConfig.Service
	}
	if len(cfg.WebSocket.Providers) == 0 {
		cfg.WebSocket = defaultConfig.WebSocket
	}
	if cfg.C2C.UpdateInterval == 0 {
		cfg.C2C = defaultConfig.C2C
	}
	if cfg.Redis.ConfigName == "" {
		cfg.Redis = defaultConfig.Redis
	}
	if len(cfg.Symbols) == 0 {
		cfg.Symbols = defaultConfig.Symbols
	}
	if len(cfg.FiatPairs) == 0 {
		cfg.FiatPairs = defaultConfig.FiatPairs
	}
	if cfg.Validation.StaleDataThreshold == 0 {
		cfg.Validation = defaultConfig.Validation
	}
	if cfg.Monitoring.PrometheusPort == 0 {
		cfg.Monitoring = defaultConfig.Monitoring
	}

	return cfg, nil
}

// GetStreamURL 获取WebSocket流URL
func (c *Config) GetStreamURL(provider string) string {
	if pc, ok := c.WebSocket.Providers[provider]; ok && pc.Enabled {
		// 构建流URL，包含所有symbols
		streams := make([]string, len(c.Symbols))
		for i, symbol := range c.Symbols {
			// Binance需要小写的symbol
			streams[i] = strings.ToLower(symbol) + "@ticker"
		}

		if len(streams) == 0 {
			return ""
		}

		// 如果只有一个symbol，使用单流格式
		if len(streams) == 1 {
			return pc.URL + "/ws/" + streams[0]
		}

		// 多个symbol使用组合流格式
		streamURL := pc.URL + "/stream?streams="
		streamURL += strings.Join(streams, "/")

		return streamURL
	}
	return ""
}
