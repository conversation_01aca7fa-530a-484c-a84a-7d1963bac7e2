package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// PriceData 价格数据结构
type PriceData struct {
	Symbol      string          `json:"symbol"`       // 交易对符号
	Price       decimal.Decimal `json:"price"`        // 价格
	Volume24h   decimal.Decimal `json:"volume_24h"`   // 24小时交易量
	Change24h   decimal.Decimal `json:"change_24h"`   // 24小时涨跌幅
	High24h     decimal.Decimal `json:"high_24h"`     // 24小时最高价
	Low24h      decimal.Decimal `json:"low_24h"`      // 24小时最低价
	Provider    string          `json:"provider"`     // 价格提供商
	Timestamp   time.Time       `json:"timestamp"`    // 更新时间戳
	LastUpdated int64           `json:"last_updated"` // Unix时间戳
}

// PriceUpdateEvent 价格更新事件
type PriceUpdateEvent struct {
	Type      string     `json:"type"`      // 事件类型
	Symbol    string     `json:"symbol"`    // 交易对符号
	PriceData *PriceData `json:"priceData"` // 价格数据
}

// BinanceTickerEvent Binance Ticker消息结构
type BinanceTickerEvent struct {
	EventType          string `json:"e"` // "24hrTicker"
	EventTime          int64  `json:"E"` // Event time
	Symbol             string `json:"s"` // Symbol
	PriceChange        string `json:"p"` // Price change
	PriceChangePercent string `json:"P"` // Price change percent
	WeightedAvgPrice   string `json:"w"` // Weighted average price
	PrevClosePrice     string `json:"x"` // Previous day's close price
	LastPrice          string `json:"c"` // Last price
	LastQty            string `json:"Q"` // Last quantity
	BidPrice           string `json:"b"` // Best bid price
	BidQty             string `json:"B"` // Best bid quantity
	AskPrice           string `json:"a"` // Best ask price
	AskQty             string `json:"A"` // Best ask quantity
	OpenPrice          string `json:"o"` // Open price
	HighPrice          string `json:"h"` // High price
	LowPrice           string `json:"l"` // Low price
	Volume             string `json:"v"` // Total traded base asset volume
	QuoteVolume        string `json:"q"` // Total traded quote asset volume
	OpenTime           int64  `json:"O"` // Statistics open time
	CloseTime          int64  `json:"C"` // Statistics close time
	FirstID            int64  `json:"F"` // First trade ID
	LastID             int64  `json:"L"` // Last trade Id
	Count              int64  `json:"n"` // Total number of trades
}

// FiatPriceData 法币价格数据结构
type FiatPriceData struct {
	Asset          string          `json:"asset"`           // 资产符号 (USDT)
	Currency       string          `json:"currency"`        // 法币符号 (CNY)
	CurrencySymbol string          `json:"currency_symbol"` // 法币符号 (￥)
	BuyPrice       decimal.Decimal `json:"buy_price"`       // 买入价格
	SellPrice      decimal.Decimal `json:"sell_price"`      // 卖出价格
	CurrencyScale  int             `json:"currency_scale"`  // 法币精度
	AssetScale     int             `json:"asset_scale"`     // 资产精度
	PriceScale     int             `json:"price_scale"`     // 价格精度
	Provider       string          `json:"provider"`        // 价格提供商
	Timestamp      time.Time       `json:"timestamp"`       // 更新时间戳
	LastUpdated    int64           `json:"last_updated"`    // Unix时间戳
}

// BinanceC2CResponse Binance C2C API响应结构
type BinanceC2CResponse struct {
	Code          string            `json:"code"`
	Message       interface{}       `json:"message"`
	MessageDetail interface{}       `json:"messageDetail"`
	Data          []BinanceC2CPrice `json:"data"`
	Success       bool              `json:"success"`
}

// BinanceC2CPrice Binance C2C价格数据
type BinanceC2CPrice struct {
	Asset          string  `json:"asset"`
	Currency       string  `json:"currency"`
	CurrencyScale  int     `json:"currencyScale"`
	CurrencySymbol string  `json:"currencySymbol"`
	ReferencePrice float64 `json:"referencePrice"`
	AssetScale     int     `json:"assetScale"`
	PriceScale     int     `json:"priceScale"`
}

// BinanceC2CRequest Binance C2C API请求结构
type BinanceC2CRequest struct {
	Assets       []string `json:"assets"`
	FiatCurrency string   `json:"fiatCurrency"`
	TradeType    string   `json:"tradeType"` // "BUY" or "SELL"
	FromUserRole string   `json:"fromUserRole"`
}

// FiatPriceUpdateEvent 法币价格更新事件
type FiatPriceUpdateEvent struct {
	Type          string         `json:"type"`
	Asset         string         `json:"asset"`
	Currency      string         `json:"currency"`
	TradeType     string         `json:"trade_type"` // "BUY" or "SELL"
	FiatPriceData *FiatPriceData `json:"fiat_price_data"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string                 `json:"status"` // "healthy", "unhealthy", "degraded"
	Message   string                 `json:"message"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}
