package model

import (
	"fmt"
	"time"
)

// ErrorType 错误类型枚举
type ErrorType string

const (
	ErrorTypeConnection ErrorType = "connection"
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeStaleData  ErrorType = "stale_data"
	ErrorTypeRateLimit  ErrorType = "rate_limit"
	ErrorTypeInternal   ErrorType = "internal"
)

// PriceError 价格服务错误
type PriceError struct {
	Type      ErrorType              `json:"type"`
	Code      string                 `json:"code"`
	Message   string                 `json:"message"`
	Symbol    string                 `json:"symbol,omitempty"`
	Provider  string                 `json:"provider,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

func (e *PriceError) Error() string {
	return fmt.Sprintf("[%s:%s] %s", e.Type, e.Code, e.Message)
}

// NewPriceError 创建新的价格错误
func NewPriceError(errType ErrorType, code, message string) *PriceError {
	return &PriceError{
		Type:      errType,
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
	}
}

// WithSymbol 添加symbol信息
func (e *PriceError) WithSymbol(symbol string) *PriceError {
	e.Symbol = symbol
	return e
}

// WithProvider 添加provider信息
func (e *PriceError) WithProvider(provider string) *PriceError {
	e.Provider = provider
	return e
}

// WithMetadata 添加元数据
func (e *PriceError) WithMetadata(key string, value interface{}) *PriceError {
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	e.Metadata[key] = value
	return e
}

// 常用错误定义
var (
	ErrStaleData = NewPriceError(
		ErrorTypeStaleData,
		"STALE_DATA",
		"Price data is too old",
	)

	ErrConnectionLost = NewPriceError(
		ErrorTypeConnection,
		"CONNECTION_LOST",
		"WebSocket connection lost",
	)

	ErrPriceAnomaly = NewPriceError(
		ErrorTypeValidation,
		"PRICE_ANOMALY",
		"Price change exceeds threshold",
	)

	ErrProviderUnavailable = NewPriceError(
		ErrorTypeConnection,
		"PROVIDER_UNAVAILABLE",
		"Price provider is unavailable",
	)

	ErrInvalidSymbol = NewPriceError(
		ErrorTypeValidation,
		"INVALID_SYMBOL",
		"Invalid trading symbol",
	)
)
