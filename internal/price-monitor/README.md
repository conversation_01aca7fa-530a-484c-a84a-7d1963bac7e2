# 价格监控服务 (Price Monitor Service)

## 概述

价格监控服务是一个独立的微服务，通过WebSocket连接到Binance等交易所，实时获取加密货币价格数据，并将数据存储在Redis中供其他服务使用。

## 功能特性

- 🔄 **实时价格订阅**：通过WebSocket获取实时价格数据
- 💱 **法币兑换价格**：通过Binance C2C API获取USDT法币价格
- 📊 **Redis存储**：高效存储和检索价格数据
- ✅ **数据验证**：防止异常价格数据和过期数据
- 🔍 **健康监控**：实时监控服务状态和数据新鲜度
- 📈 **Prometheus指标**：完整的性能和状态监控
- 🛠️ **客户端SDK**：方便其他服务集成使用

## 架构组件

### 1. WebSocket管理器
- 管理与交易所的WebSocket连接
- 自动重连机制
- 心跳检测

### 2. 价格处理器
- 解析和验证价格数据
- 转换数据格式
- 异常检测

### 3. 法币价格获取器 (新增)
- 定时从Binance C2C API获取法币价格
- 支持买价和卖价同时获取
- 可配置更新间隔和重试机制

### 4. Redis写入器
- 批量写入优化
- 数据TTL管理
- 历史数据维护
- 支持法币价格存储

### 5. 健康监控
- WebSocket连接状态
- Redis连接状态
- 数据新鲜度检查
- C2C API可用性检查

### 6. 指标收集
- Prometheus格式的指标
- 连接状态、消息统计、延迟等

## 快速开始

### 1. 配置

在 `manifest/config/config.yaml` 中添加价格监控配置：

```yaml
priceMonitor:
  service:
    name: "price-monitor-service"
    port: 8090
    logLevel: "debug"
  
  websocket:
    providers:
      binance:
        url: "wss://stream.binance.com:9443"
        reconnectDelay: "5s"
        pingInterval: "30s"
        pongTimeout: "10s"
        enabled: true
  
  symbols:
    - "ETHUSDT"
    - "TRXUSDT"
  
  # C2C 法币价格配置
  c2c:
    enabled: true                    # 是否启用C2C法币价格获取
    updateInterval: "30s"            # 价格更新间隔 (可配置: 10s, 30s, 1m, 5m等)
    timeout: "10s"                   # API请求超时时间
    maxRetries: 3                    # 最大重试次数
  
  # 法币交易对配置
  fiatPairs:
    - asset: "USDT"                  # 资产符号
      fiatCurrency: "CNY"            # 法币符号
      enabled: true                  # 是否启用此交易对
    - asset: "USDT"                  # 可以添加更多法币支持
      fiatCurrency: "USD"            # 美元
      enabled: true                  # 启用USD支持
  
  redis:
    configName: "default"
    poolSize: 10
    maxRetries: 3
```

### C2C价格更新间隔配置指南

#### 推荐配置

| 场景 | 更新间隔 | 说明 |
|------|----------|------|
| **生产环境** | `"30s"` - `"1m"` | 平衡性能和数据新鲜度，推荐30秒 |
| **开发测试** | `"10s"` - `"15s"` | 快速测试，但会增加API调用频率 |
| **低频使用** | `"2m"` - `"5m"` | 降低API调用，适合非实时场景 |
| **高频交易** | `"10s"` - `"15s"` | 需要更新鲜的价格数据 |

#### 配置示例

```yaml
# 快速更新 (高频场景)
c2c:
  updateInterval: "10s"

# 标准更新 (推荐)
c2c:
  updateInterval: "30s"

# 节能更新 (低频场景)  
c2c:
  updateInterval: "2m"
```

### 2. 运行服务

```bash
# 构建服务
make build-price-monitor

# 运行服务
make run-price-monitor

# 或使用Docker
docker-compose up price-monitor
```

### 3. 其他服务使用价格客户端

项目中的其他服务可以通过 `service.PriceClientInstance()` 获取价格数据：

#### 快速开始

```go
package main

import (
    "context"
    "fmt"
    "admin-api/internal/service"
    "admin-api/internal/boot"
)

func main() {
    // 1. 初始化应用程序（在服务启动时调用一次）
    boot.Initialize(context.Background())
    
    // 2. 获取价格客户端实例
    priceClient := service.PriceClientInstance()
    ctx := context.Background()
    
    // 3. 获取加密货币价格
    ethPrice, err := priceClient.GetRealTimePrice(ctx, "ETHUSDT")
    if err != nil {
        panic(err)
    }
    fmt.Printf("ETH价格: $%s\n", ethPrice.Price.String())
    
    // 4. 获取法币价格
    usdtCnyPrice, err := priceClient.GetFiatPrice(ctx, "USDT", "CNY")
    if err != nil {
        panic(err)
    }
    fmt.Printf("USDT/CNY: 买入=%s, 卖出=%s %s\n", 
        usdtCnyPrice.BuyPrice.String(),
        usdtCnyPrice.SellPrice.String(),
        usdtCnyPrice.CurrencySymbol)
}
```

#### 完整功能示例

```go
// 批量获取加密货币价格
symbols := []string{"ETHUSDT", "TRXUSDT"}
cryptoPrices, err := priceClient.GetMultiplePrices(ctx, symbols)
if err != nil {
    log.Printf("批量获取价格失败: %v", err)
}

for symbol, data := range cryptoPrices {
    fmt.Printf("%s: $%s (24h变动: %s%%)\n", 
        symbol, 
        data.Price.String(), 
        data.Change24h.Mul(decimal.NewFromInt(100)).String())
}

// 批量获取法币价格
fiatPairs := []service.FiatPair{
    {Asset: "USDT", Currency: "CNY"},
    {Asset: "USDT", Currency: "USD"},
}
fiatPrices, err := priceClient.GetMultipleFiatPrices(ctx, fiatPairs)
if err != nil {
    log.Printf("批量获取法币价格失败: %v", err)
}

for pair, data := range fiatPrices {
    fmt.Printf("%s: 中间价=%s %s (价差=%.2f%%)\n", 
        pair, 
        data.MidPrice.String(),
        data.CurrencySymbol,
        data.SpreadPercent.InexactFloat64())
}

// 获取价格历史
from := time.Now().Add(-1 * time.Hour)
to := time.Now()
history, err := priceClient.GetPriceHistory(ctx, "ETHUSDT", from, to)
if err != nil {
    log.Printf("获取历史价格失败: %v", err)
}
fmt.Printf("获取到 %d 条历史记录\n", len(history))
```

#### 在现有服务中集成

如果是在现有的服务逻辑中使用，通常在服务初始化时获取客户端实例：

```go
type YourService struct {
    priceClient service.IPriceClient
    // 其他字段...
}

func NewYourService() *YourService {
    return &YourService{
        priceClient: service.PriceClientInstance(),
        // 初始化其他字段...
    }
}

func (s *YourService) SomeBusinessLogic(ctx context.Context) {
    // 在业务逻辑中使用价格数据
    ethPrice, err := s.priceClient.GetRealTimePrice(ctx, "ETHUSDT")
    if err != nil {
        return err
    }
    
    // 使用价格数据进行业务计算...
    fmt.Printf("当前ETH价格: %s\n", ethPrice.Price.String())
}
```

## API端点

### 健康检查
```
GET /health
```
返回服务的健康状态，包括各个组件的状态。

### 服务信息
```
GET /info
```
返回服务的基本信息，包括版本、支持的交易对、C2C状态等。

### 加密货币价格查询
```
GET /debug/price/:symbol
```
获取指定交易对的当前价格（仅供调试使用）。
示例：`/debug/price/ETHUSDT`

### 法币价格查询
```
GET /debug/fiat/:asset/:currency
```
获取指定法币交易对的当前价格。
示例：`/debug/fiat/USDT/CNY`

### 所有法币价格
```
GET /debug/fiat/all
```
获取所有缓存的法币价格数据。

### Prometheus指标
```
GET :9091/metrics
```
Prometheus格式的监控指标。

### 示例响应

#### 法币价格响应
```json
{
  "asset": "USDT",
  "currency": "CNY",
  "currency_symbol": "￥",
  "buy_price": "7.15",
  "sell_price": "7.19",
  "currency_scale": 2,
  "asset_scale": 2,
  "price_scale": 2,
  "provider": "binance_c2c",
  "timestamp": "2025-06-09T12:00:00Z",
  "last_updated": **********
}
```

## Redis数据结构

### 实时价格数据 (加密货币)
```
Key: price:live:{symbol}
Type: Hash
Fields:
  - price: 当前价格
  - volume_24h: 24小时交易量
  - change_24h: 24小时涨跌幅
  - high_24h: 24小时最高价
  - low_24h: 24小时最低价
  - provider: 数据提供商
  - timestamp: 更新时间戳
  - last_updated: Unix时间戳
TTL: 5分钟
```

### 法币价格数据 (新增)
```
Key: fiat:price:{asset}_{currency}
Type: Hash
Fields:
  - asset: 资产符号
  - currency: 法币符号
  - currency_symbol: 法币符号 (￥)
  - buy_price: 买入价格
  - sell_price: 卖出价格
  - currency_scale: 法币精度
  - asset_scale: 资产精度
  - price_scale: 价格精度
  - provider: 数据提供商
  - timestamp: 更新时间戳
  - last_updated: Unix时间戳
TTL: 10分钟
```

### 价格历史
```
Key: price:history:{symbol}
Key: fiat:history:{asset}_{currency}
Type: Sorted Set
Score: 时间戳
Member: JSON格式的价格数据
Max Size: 1000条记录
TTL: 7天
```

### 健康状态
```
Key: price:health:{provider}
Type: Hash
Fields:
  - status: 状态
  - last_ping: 最后心跳时间
  - message_count: 消息计数
  - error_count: 错误计数
TTL: 1分钟
```

## 监控指标

主要的Prometheus指标包括：

- `price_monitor_websocket_connections`: WebSocket连接状态
- `price_monitor_websocket_messages_total`: 接收的消息总数
- `price_monitor_price_updates_total`: 处理的价格更新总数
- `price_monitor_redis_operations_total`: Redis操作统计
- `price_monitor_health_status`: 各组件健康状态

## 故障处理

### WebSocket断线
- 服务会自动重连
- 重连延迟可配置（默认5秒）
- 支持指数退避策略

### Redis连接失败
- 自动重试机制
- 批量写入失败时降级为单条写入
- 错误会被记录但不会影响其他数据

### 数据异常
- 价格变动超过阈值（默认10%）会被拒绝
- 更新频率过高会被限制
- 过期数据会被自动清理

## 性能优化

1. **批量处理**：累积多条价格数据后批量写入Redis
2. **内存缓存**：价格验证器维护最近价格的内存缓存
3. **连接复用**：WebSocket和Redis连接池
4. **并发处理**：价格处理使用工作池模式

## 扩展性

- 支持多实例部署
- 可以添加新的价格源（交易所）
- 支持动态添加交易对
- 水平扩展能力

## 注意事项

1. 确保Redis有足够的内存存储价格历史
2. 监控WebSocket连接稳定性
3. 定期检查数据新鲜度
4. 合理设置价格验证阈值
5. 注意API限流和使用配额