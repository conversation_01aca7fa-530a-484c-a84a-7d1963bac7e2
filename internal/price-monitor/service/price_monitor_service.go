package service

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"admin-api/internal/price-monitor/config"
)

// PriceMonitorService 价格监控服务
type PriceMonitorService struct {
	config           *config.Config
	wsManager        *WebSocketManager
	priceProcessor   *PriceProcessor
	fiatPriceFetcher *FiatPriceFetcher
	redisWriter      *RedisWriter
	healthMonitor    *HealthMonitor
	metrics          *Metrics
	httpServer       *ghttp.Server
	metricsServer    *http.Server
}

// NewPriceMonitorService 创建价格监控服务
func NewPriceMonitorService(cfg *config.Config) (*PriceMonitorService, error) {
	// 创建指标收集器
	metrics := NewMetrics()

	// 创建Redis写入器
	redisWriter, err := NewRedisWriter(&cfg.Redis)
	if err != nil {
		return nil, fmt.Errorf("failed to create redis writer: %w", err)
	}

	// 创建价格处理器
	priceProcessor := NewPriceProcessor(cfg, redisWriter)

	// 创建法币价格获取器
	fiatPriceFetcher := NewFiatPriceFetcher(cfg, redisWriter)

	// 创建WebSocket管理器
	wsManager := NewWebSocketManager(cfg, priceProcessor)

	// 创建健康监控器
	healthMonitor := NewHealthMonitor(cfg, redisWriter)

	// 注册健康检查
	healthMonitor.RegisterCheck(NewWebSocketHealthCheck(wsManager))
	healthMonitor.RegisterCheck(NewRedisHealthCheck(redisWriter))
	healthMonitor.RegisterCheck(NewDataFreshnessHealthCheck(
		redisWriter,
		cfg.Symbols,
		cfg.Validation.StaleDataThreshold,
	))
	// 如果启用了C2C，注册C2C健康检查
	if cfg.C2C.Enabled {
		healthMonitor.RegisterCheck(NewC2CHealthCheck(fiatPriceFetcher))
	}

	// 创建HTTP服务器
	httpServer := g.Server()
	httpServer.SetPort(cfg.Service.Port)

	// 创建指标服务器
	metricsMux := http.NewServeMux()
	metricsMux.Handle("/metrics", promhttp.Handler())
	metricsServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Monitoring.PrometheusPort),
		Handler: metricsMux,
	}

	service := &PriceMonitorService{
		config:           cfg,
		wsManager:        wsManager,
		priceProcessor:   priceProcessor,
		fiatPriceFetcher: fiatPriceFetcher,
		redisWriter:      redisWriter,
		healthMonitor:    healthMonitor,
		metrics:          metrics,
		httpServer:       httpServer,
		metricsServer:    metricsServer,
	}

	// 注册HTTP路由
	service.registerRoutes()

	return service, nil
}

// Start 启动服务
func (s *PriceMonitorService) Start(ctx context.Context) error {
	g.Log().Infof(ctx, "Starting price monitor service on port %d", s.config.Service.Port)

	// 启动Redis写入器
	if err := s.redisWriter.Start(ctx); err != nil {
		return fmt.Errorf("failed to start redis writer: %w", err)
	}

	// 启动价格处理器
	if err := s.priceProcessor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start price processor: %w", err)
	}

	// 启动WebSocket管理器
	if err := s.wsManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start websocket manager: %w", err)
	}

	// 启动法币价格获取器
	if err := s.fiatPriceFetcher.Start(ctx); err != nil {
		return fmt.Errorf("failed to start fiat price fetcher: %w", err)
	}

	// 启动健康监控器
	if err := s.healthMonitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start health monitor: %w", err)
	}

	// 启动指标服务器
	go func() {
		g.Log().Infof(ctx, "Starting metrics server on port %d", s.config.Monitoring.PrometheusPort)
		if err := s.metricsServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			g.Log().Errorf(ctx, "Metrics server error: %v", err)
		}
	}()

	// 启动HTTP服务器
	go func() {
		s.httpServer.Run()
	}()

	// 记录服务启动
	s.metrics.SetHealthStatus("service", "healthy")

	g.Log().Info(ctx, "Price monitor service started successfully")
	return nil
}

// Stop 停止服务
func (s *PriceMonitorService) Stop(ctx context.Context) error {
	g.Log().Info(ctx, "Stopping price monitor service")

	// 停止健康监控器
	if err := s.healthMonitor.Stop(ctx); err != nil {
		g.Log().Errorf(ctx, "Failed to stop health monitor: %v", err)
	}

	// 停止WebSocket管理器
	if err := s.wsManager.Stop(ctx); err != nil {
		g.Log().Errorf(ctx, "Failed to stop websocket manager: %v", err)
	}

	// 停止法币价格获取器
	if err := s.fiatPriceFetcher.Stop(ctx); err != nil {
		g.Log().Errorf(ctx, "Failed to stop fiat price fetcher: %v", err)
	}

	// 停止价格处理器
	if err := s.priceProcessor.Stop(ctx); err != nil {
		g.Log().Errorf(ctx, "Failed to stop price processor: %v", err)
	}

	// 停止Redis写入器
	if err := s.redisWriter.Stop(ctx); err != nil {
		g.Log().Errorf(ctx, "Failed to stop redis writer: %v", err)
	}

	// 停止指标服务器
	if err := s.metricsServer.Shutdown(ctx); err != nil {
		g.Log().Errorf(ctx, "Failed to stop metrics server: %v", err)
	}

	// 停止HTTP服务器
	if err := s.httpServer.Shutdown(); err != nil {
		g.Log().Errorf(ctx, "Failed to stop http server: %v", err)
	}

	g.Log().Info(ctx, "Price monitor service stopped")
	return nil
}

// registerRoutes 注册HTTP路由
func (s *PriceMonitorService) registerRoutes() {
	// 健康检查端点
	s.httpServer.BindHandler("/health", func(r *ghttp.Request) {
		healthStatus := s.healthMonitor.GetStatus(r.Context())

		// 判断整体健康状态
		overallStatus := "healthy"
		statusCode := http.StatusOK

		for _, status := range healthStatus {
			if status.Status == "unhealthy" {
				overallStatus = "unhealthy"
				statusCode = http.StatusServiceUnavailable
				break
			} else if status.Status == "degraded" && overallStatus == "healthy" {
				overallStatus = "degraded"
			}
		}

		r.Response.WriteJson(g.Map{
			"status":    overallStatus,
			"checks":    healthStatus,
			"timestamp": time.Now(),
		})
		r.Response.Status = statusCode
	})

	// 价格查询端点（仅供内部调试使用）
	s.httpServer.BindHandler("/debug/price/:symbol", func(r *ghttp.Request) {
		symbol := r.GetRouter("symbol").String()
		if symbol == "" {
			r.Response.WriteJson(g.Map{
				"error": "symbol is required",
			})
			r.Response.Status = http.StatusBadRequest
			return
		}

		priceData, err := s.redisWriter.GetLatestPrice(r.Context(), symbol)
		if err != nil {
			r.Response.WriteJson(g.Map{
				"error": err.Error(),
			})
			r.Response.Status = http.StatusNotFound
			return
		}

		r.Response.WriteJson(priceData)
	})

	// 法币价格查询端点
	s.httpServer.BindHandler("/debug/fiat/:asset/:currency", func(r *ghttp.Request) {
		asset := r.GetRouter("asset").String()
		currency := r.GetRouter("currency").String()

		if asset == "" || currency == "" {
			r.Response.WriteJson(g.Map{
				"error": "asset and currency are required",
			})
			r.Response.Status = http.StatusBadRequest
			return
		}

		fiatPriceData, err := s.redisWriter.GetLatestFiatPrice(r.Context(), asset, currency)
		if err != nil {
			r.Response.WriteJson(g.Map{
				"error": err.Error(),
			})
			r.Response.Status = http.StatusNotFound
			return
		}

		r.Response.WriteJson(fiatPriceData)
	})

	// 所有法币价格端点
	s.httpServer.BindHandler("/debug/fiat/all", func(r *ghttp.Request) {
		allPrices := s.fiatPriceFetcher.GetAllCachedPrices()
		r.Response.WriteJson(g.Map{
			"prices": allPrices,
			"count":  len(allPrices),
		})
	})

	// 服务信息端点
	s.httpServer.BindHandler("/info", func(r *ghttp.Request) {
		info := g.Map{
			"service":   s.config.Service.Name,
			"version":   "1.0.0",
			"symbols":   s.config.Symbols,
			"providers": s.wsManager.GetAllProviders(),
			"uptime":    time.Since(time.Now()).Seconds(), // 简化版，实际应记录启动时间
		}

		// 添加法币相关信息
		if s.config.C2C.Enabled {
			info["fiat_pairs"] = s.config.FiatPairs
			info["c2c_enabled"] = true
			info["c2c_update_interval"] = s.config.C2C.UpdateInterval.String()

			// 添加缓存的法币价格数量
			cachedPrices := s.fiatPriceFetcher.GetAllCachedPrices()
			info["cached_fiat_prices"] = len(cachedPrices)
		} else {
			info["c2c_enabled"] = false
		}

		r.Response.WriteJson(info)
	})
}
