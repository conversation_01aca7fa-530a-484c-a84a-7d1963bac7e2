package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	"admin-api/internal/price-monitor/config"
	"admin-api/internal/price-monitor/model"
)

// FiatPriceFetcher 法币价格获取器
type FiatPriceFetcher struct {
	config      *config.Config
	c2cClient   *C2CClient
	redisWriter *RedisWriter
	eventBus    chan *model.FiatPriceUpdateEvent
	ticker      *time.Ticker
	stopCh      chan struct{}
	wg          sync.WaitGroup
	mu          sync.RWMutex
	lastPrices  map[string]*model.FiatPriceData // 缓存最新价格
}

// NewFiatPriceFetcher 创建法币价格获取器
func NewFiatPriceFetcher(cfg *config.Config, redisWriter *RedisWriter) *FiatPriceFetcher {
	c2cClient := NewC2CClient(cfg.C2C.Timeout, cfg.C2C.MaxRetries)

	return &FiatPriceFetcher{
		config:      cfg,
		c2cClient:   c2cClient,
		redisWriter: redisWriter,
		eventBus:    make(chan *model.FiatPriceUpdateEvent, 100),
		stopCh:      make(chan struct{}),
		lastPrices:  make(map[string]*model.FiatPriceData),
	}
}

// Start 启动法币价格获取器
func (f *FiatPriceFetcher) Start(ctx context.Context) error {
	if !f.config.C2C.Enabled {
		g.Log().Info(ctx, "C2C price fetching is disabled")
		return nil
	}

	g.Log().Infof(ctx, "Starting fiat price fetcher with update interval: %v", f.config.C2C.UpdateInterval)

	// 启动事件处理器
	f.wg.Add(1)
	go f.processEvents(ctx)

	// 启动定时获取器
	f.ticker = time.NewTicker(f.config.C2C.UpdateInterval)
	f.wg.Add(1)
	go f.fetchLoop(ctx)

	// 立即获取一次价格
	go f.fetchAllPrices(ctx)

	return nil
}

// Stop 停止法币价格获取器
func (f *FiatPriceFetcher) Stop(ctx context.Context) error {
	g.Log().Info(ctx, "Stopping fiat price fetcher")

	close(f.stopCh)

	if f.ticker != nil {
		f.ticker.Stop()
	}

	f.wg.Wait()
	close(f.eventBus)

	g.Log().Info(ctx, "Fiat price fetcher stopped")
	return nil
}

// fetchLoop 定时获取循环
func (f *FiatPriceFetcher) fetchLoop(ctx context.Context) {
	defer f.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-f.stopCh:
			return
		case <-f.ticker.C:
			f.fetchAllPrices(ctx)
		}
	}
}

// fetchAllPrices 获取所有配置的法币价格
func (f *FiatPriceFetcher) fetchAllPrices(ctx context.Context) {
	for _, pair := range f.config.FiatPairs {
		if !pair.Enabled {
			continue
		}

		go func(asset, fiatCurrency string) {
			if err := f.fetchPairPrice(ctx, asset, fiatCurrency); err != nil {
				g.Log().Errorf(ctx, "Failed to fetch price for %s/%s: %v", asset, fiatCurrency, err)
			}
		}(pair.Asset, pair.FiatCurrency)
	}
}

// fetchPairPrice 获取特定交易对的价格
func (f *FiatPriceFetcher) fetchPairPrice(ctx context.Context, asset, fiatCurrency string) error {
	g.Log().Debugf(ctx, "Fetching price for %s/%s", asset, fiatCurrency)

	// 获取买价和卖价
	priceData, err := f.c2cClient.GetBothPrices(ctx, asset, fiatCurrency)
	if err != nil {
		return fmt.Errorf("failed to get prices: %w", err)
	}

	// 创建更新事件
	event := &model.FiatPriceUpdateEvent{
		Type:          "fiat_price_update",
		Asset:         asset,
		Currency:      fiatCurrency,
		TradeType:     "BOTH",
		FiatPriceData: priceData,
	}

	// 发送事件到处理器
	select {
	case f.eventBus <- event:
		g.Log().Debugf(ctx, "Fiat price event queued for %s/%s", asset, fiatCurrency)
	default:
		g.Log().Warningf(ctx, "Event bus full, dropping fiat price event for %s/%s", asset, fiatCurrency)
	}

	return nil
}

// processEvents 处理价格事件
func (f *FiatPriceFetcher) processEvents(ctx context.Context) {
	defer f.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-f.stopCh:
			return
		case event := <-f.eventBus:
			if event != nil && event.FiatPriceData != nil {
				if err := f.processFiatPriceData(ctx, event.FiatPriceData); err != nil {
					g.Log().Errorf(ctx, "Failed to process fiat price data: %v", err)
				}
			}
		}
	}
}

// processFiatPriceData 处理法币价格数据
func (f *FiatPriceFetcher) processFiatPriceData(ctx context.Context, data *model.FiatPriceData) error {
	// 验证价格数据
	if err := f.validateFiatPrice(data); err != nil {
		g.Log().Warningf(ctx, "Fiat price validation failed for %s/%s: %v", data.Asset, data.Currency, err)
		return fmt.Errorf("fiat price validation failed: %w", err)
	}

	// 写入Redis
	if err := f.redisWriter.WriteFiatPrice(ctx, data); err != nil {
		g.Log().Errorf(ctx, "Failed to write fiat price to redis for %s/%s: %v", data.Asset, data.Currency, err)
		return fmt.Errorf("failed to write to redis: %w", err)
	}

	// 更新缓存
	f.updateCache(data)

	g.Log().Infof(ctx, "Successfully processed fiat price update for %s/%s: buy=%s, sell=%s %s",
		data.Asset, data.Currency,
		data.BuyPrice.String(), data.SellPrice.String(), data.CurrencySymbol)

	return nil
}

// validateFiatPrice 验证法币价格
func (f *FiatPriceFetcher) validateFiatPrice(data *model.FiatPriceData) error {
	if data.Asset == "" {
		return fmt.Errorf("asset is empty")
	}
	if data.Currency == "" {
		return fmt.Errorf("currency is empty")
	}
	if data.BuyPrice.IsZero() && data.SellPrice.IsZero() {
		return fmt.Errorf("both buy and sell prices are zero")
	}
	if data.BuyPrice.IsNegative() || data.SellPrice.IsNegative() {
		return fmt.Errorf("prices cannot be negative")
	}
	return nil
}

// updateCache 更新价格缓存
func (f *FiatPriceFetcher) updateCache(data *model.FiatPriceData) {
	f.mu.Lock()
	defer f.mu.Unlock()

	key := fmt.Sprintf("%s_%s", data.Asset, data.Currency)
	f.lastPrices[key] = data
}

// GetCachedPrice 获取缓存的价格
func (f *FiatPriceFetcher) GetCachedPrice(asset, currency string) (*model.FiatPriceData, bool) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	key := fmt.Sprintf("%s_%s", asset, currency)
	price, exists := f.lastPrices[key]
	return price, exists
}

// GetAllCachedPrices 获取所有缓存的价格
func (f *FiatPriceFetcher) GetAllCachedPrices() map[string]*model.FiatPriceData {
	f.mu.RLock()
	defer f.mu.RUnlock()

	result := make(map[string]*model.FiatPriceData)
	for key, value := range f.lastPrices {
		result[key] = value
	}
	return result
}

// HealthCheck 健康检查
func (f *FiatPriceFetcher) HealthCheck(ctx context.Context) error {
	if !f.config.C2C.Enabled {
		return nil
	}

	// 检查C2C客户端健康状态
	return f.c2cClient.HealthCheck(ctx)
}

// GetC2CClient 获取C2C客户端（用于健康检查）
func (f *FiatPriceFetcher) GetC2CClient() *C2CClient {
	return f.c2cClient
}
