package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"

	"admin-api/internal/price-monitor/model"
)

// C2CClient Binance C2C API客户端
type C2CClient struct {
	httpClient *http.Client
	baseURL    string
	timeout    time.Duration
	retries    int
}

// NewC2CClient 创建C2C客户端
func NewC2CClient(timeout time.Duration, retries int) *C2CClient {
	return &C2CClient{
		httpClient: &http.Client{
			Timeout: timeout,
		},
		baseURL: "https://c2c.binance.com",
		timeout: timeout,
		retries: retries,
	}
}

// GetFiatPrice 获取法币价格
func (c *C2CClient) GetFiatPrice(ctx context.Context, asset, fiatCurrency, tradeType string) (*model.FiatPriceData, error) {
	request := model.BinanceC2CRequest{
		Assets:       []string{asset},
		FiatCurrency: fiatCurrency,
		TradeType:    tradeType,
		FromUserRole: "USER",
	}

	response, err := c.makeRequest(ctx, "/bapi/c2c/v2/public/c2c/adv/quoted-price", request)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}

	if !response.Success || response.Code != "000000" {
		return nil, fmt.Errorf("API returned error: code=%s, message=%v", response.Code, response.Message)
	}

	if len(response.Data) == 0 {
		return nil, fmt.Errorf("no data returned for %s/%s %s", asset, fiatCurrency, tradeType)
	}

	data := response.Data[0]
	price := decimal.NewFromFloat(data.ReferencePrice)
	now := time.Now()

	fiatPrice := &model.FiatPriceData{
		Asset:          data.Asset,
		Currency:       data.Currency,
		CurrencySymbol: data.CurrencySymbol,
		CurrencyScale:  data.CurrencyScale,
		AssetScale:     data.AssetScale,
		PriceScale:     data.PriceScale,
		Provider:       "binance_c2c",
		Timestamp:      now,
		LastUpdated:    now.Unix(),
	}

	// 根据交易类型设置买价或卖价
	if tradeType == "BUY" {
		fiatPrice.BuyPrice = price
	} else {
		fiatPrice.SellPrice = price
	}

	return fiatPrice, nil
}

// GetBothPrices 同时获取买价和卖价
func (c *C2CClient) GetBothPrices(ctx context.Context, asset, fiatCurrency string) (*model.FiatPriceData, error) {
	// 获取买价
	buyPriceData, err := c.GetFiatPrice(ctx, asset, fiatCurrency, "BUY")
	if err != nil {
		return nil, fmt.Errorf("failed to get buy price: %w", err)
	}

	// 获取卖价
	sellPriceData, err := c.GetFiatPrice(ctx, asset, fiatCurrency, "SELL")
	if err != nil {
		return nil, fmt.Errorf("failed to get sell price: %w", err)
	}

	// 合并买价和卖价数据
	combinedData := &model.FiatPriceData{
		Asset:          buyPriceData.Asset,
		Currency:       buyPriceData.Currency,
		CurrencySymbol: buyPriceData.CurrencySymbol,
		BuyPrice:       buyPriceData.BuyPrice,
		SellPrice:      sellPriceData.SellPrice,
		CurrencyScale:  buyPriceData.CurrencyScale,
		AssetScale:     buyPriceData.AssetScale,
		PriceScale:     buyPriceData.PriceScale,
		Provider:       "binance_c2c",
		Timestamp:      time.Now(),
		LastUpdated:    time.Now().Unix(),
	}

	return combinedData, nil
}

// makeRequest 发起HTTP请求
func (c *C2CClient) makeRequest(ctx context.Context, endpoint string, request model.BinanceC2CRequest) (*model.BinanceC2CResponse, error) {
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url := c.baseURL + endpoint
	var response *model.BinanceC2CResponse
	var lastErr error

	// 重试机制
	for attempt := 0; attempt <= c.retries; attempt++ {
		if attempt > 0 {
			// 等待重试间隔
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(attempt) * time.Second):
			}
			g.Log().Warningf(ctx, "Retrying C2C API request, attempt %d/%d", attempt+1, c.retries+1)
		}

		req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
		if err != nil {
			lastErr = fmt.Errorf("failed to create request: %w", err)
			continue
		}

		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; price-monitor-service/1.0)")

		resp, err := c.httpClient.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("request failed: %w", err)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			resp.Body.Close()
			lastErr = fmt.Errorf("HTTP error: %d %s", resp.StatusCode, resp.Status)
			continue
		}

		response = &model.BinanceC2CResponse{}
		if err := json.NewDecoder(resp.Body).Decode(response); err != nil {
			resp.Body.Close()
			lastErr = fmt.Errorf("failed to decode response: %w", err)
			continue
		}

		resp.Body.Close()

		// 请求成功
		if attempt > 0 {
			g.Log().Infof(ctx, "C2C API request succeeded after %d retries", attempt)
		}
		return response, nil
	}

	return nil, fmt.Errorf("all retry attempts failed, last error: %w", lastErr)
}

// HealthCheck 健康检查
func (c *C2CClient) HealthCheck(ctx context.Context) error {
	// 尝试获取USDT/CNY买价进行健康检查
	_, err := c.GetFiatPrice(ctx, "USDT", "CNY", "BUY")
	return err
}
