package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gorilla/websocket"

	"admin-api/internal/price-monitor/config"
	"admin-api/internal/price-monitor/model"
)

// WebSocketManager WebSocket连接管理器
type WebSocketManager struct {
	config      *config.Config
	connections map[string]*WebSocketConnection
	mu          sync.RWMutex
	reconnectCh chan string
	stopCh      chan struct{}
	handler     PriceEventHandler
	wg          sync.WaitGroup
}

// WebSocketConnection WebSocket连接
type WebSocketConnection struct {
	providerName   string
	conn           *websocket.Conn
	config         config.ProviderConfig
	symbols        []string
	isConnected    atomic.Bool
	lastPong       atomic.Value // time.Time
	reconnectDelay time.Duration
	mu             sync.Mutex
}

// PriceEventHandler 价格事件处理器接口
type PriceEventHandler interface {
	OnPriceUpdate(event *model.PriceUpdateEvent) error
	OnConnectionEstablished(provider string) error
	OnConnectionLost(provider string) error
	OnError(provider string, err error) error
}

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager(cfg *config.Config, handler PriceEventHandler) *WebSocketManager {
	return &WebSocketManager{
		config:      cfg,
		connections: make(map[string]*WebSocketConnection),
		reconnectCh: make(chan string, 10),
		stopCh:      make(chan struct{}),
		handler:     handler,
	}
}

// Start 启动WebSocket管理器
func (m *WebSocketManager) Start(ctx context.Context) error {
	// 启动重连goroutine
	m.wg.Add(1)
	go m.reconnectWorker(ctx)

	// 连接所有启用的提供商
	for name, providerConfig := range m.config.WebSocket.Providers {
		if providerConfig.Enabled {
			if err := m.connect(ctx, name, providerConfig); err != nil {
				g.Log().Errorf(ctx, "Failed to connect to %s: %v", name, err)
				// 加入重连队列
				m.reconnectCh <- name
			}
		}
	}

	return nil
}

// Stop 停止WebSocket管理器
func (m *WebSocketManager) Stop(ctx context.Context) error {
	close(m.stopCh)

	// 关闭所有连接
	m.mu.Lock()
	for name, conn := range m.connections {
		if err := conn.Close(); err != nil {
			g.Log().Errorf(ctx, "Failed to close connection %s: %v", name, err)
		}
	}
	m.mu.Unlock()

	// 等待所有goroutine退出
	m.wg.Wait()
	close(m.reconnectCh)

	return nil
}

// connect 连接到指定提供商
func (m *WebSocketManager) connect(ctx context.Context, name string, cfg config.ProviderConfig) error {
	streamURL := m.config.GetStreamURL(name)
	if streamURL == "" {
		return fmt.Errorf("no stream URL for provider %s", name)
	}

	g.Log().Infof(ctx, "Connecting to %s: %s", name, streamURL)

	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	conn, _, err := dialer.DialContext(ctx, streamURL, nil)
	if err != nil {
		return fmt.Errorf("failed to dial websocket: %w", err)
	}

	wsConn := &WebSocketConnection{
		providerName:   name,
		conn:           conn,
		config:         cfg,
		symbols:        m.config.Symbols,
		reconnectDelay: cfg.ReconnectDelay,
	}
	wsConn.isConnected.Store(true)
	wsConn.lastPong.Store(time.Now())

	m.mu.Lock()
	m.connections[name] = wsConn
	m.mu.Unlock()

	// 通知连接建立
	if err := m.handler.OnConnectionEstablished(name); err != nil {
		g.Log().Errorf(ctx, "Failed to handle connection established event: %v", err)
	}

	// 启动读取和ping goroutines
	m.wg.Add(2)
	go m.readWorker(ctx, wsConn)
	go m.pingWorker(ctx, wsConn)

	return nil
}

// readWorker 读取WebSocket消息
func (m *WebSocketManager) readWorker(ctx context.Context, conn *WebSocketConnection) {
	defer m.wg.Done()
	defer func() {
		conn.isConnected.Store(false)
		m.reconnectCh <- conn.providerName
		if err := m.handler.OnConnectionLost(conn.providerName); err != nil {
			g.Log().Errorf(ctx, "Failed to handle connection lost event: %v", err)
		}
	}()

	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopCh:
			return
		default:
			messageType, message, err := conn.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					g.Log().Errorf(ctx, "WebSocket read error from %s: %v", conn.providerName, err)
				}
				return
			}

			if messageType == websocket.TextMessage {
				if err := m.handleMessage(ctx, conn.providerName, message); err != nil {
					g.Log().Errorf(ctx, "Failed to handle message from %s: %v", conn.providerName, err)
				}
			} else if messageType == websocket.PongMessage {
				conn.lastPong.Store(time.Now())
			}
		}
	}
}

// pingWorker 发送ping消息
func (m *WebSocketManager) pingWorker(ctx context.Context, conn *WebSocketConnection) {
	defer m.wg.Done()

	ticker := time.NewTicker(conn.config.PingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopCh:
			return
		case <-ticker.C:
			if !conn.isConnected.Load() {
				return
			}

			// 检查最后pong时间
			lastPong := conn.lastPong.Load().(time.Time)
			if time.Since(lastPong) > conn.config.PongTimeout {
				g.Log().Warningf(ctx, "Pong timeout for %s, closing connection", conn.providerName)
				conn.Close()
				return
			}

			// 发送ping
			conn.mu.Lock()
			err := conn.conn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(5*time.Second))
			conn.mu.Unlock()

			if err != nil {
				g.Log().Errorf(ctx, "Failed to send ping to %s: %v", conn.providerName, err)
				return
			}
		}
	}
}

// reconnectWorker 处理重连
func (m *WebSocketManager) reconnectWorker(ctx context.Context) {
	defer m.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopCh:
			return
		case providerName := <-m.reconnectCh:
			cfg, exists := m.config.WebSocket.Providers[providerName]
			if !exists || !cfg.Enabled {
				continue
			}

			g.Log().Infof(ctx, "Attempting to reconnect to %s in %v", providerName, cfg.ReconnectDelay)
			time.Sleep(cfg.ReconnectDelay)

			if err := m.connect(ctx, providerName, cfg); err != nil {
				g.Log().Errorf(ctx, "Failed to reconnect to %s: %v", providerName, err)
				// 重新加入重连队列
				select {
				case m.reconnectCh <- providerName:
				case <-m.stopCh:
					return
				}
			}
		}
	}
}

// handleMessage 处理WebSocket消息
func (m *WebSocketManager) handleMessage(ctx context.Context, provider string, message []byte) error {
	// 对于Binance，需要处理ticker事件
	if provider == "binance" {
		return m.handleBinanceMessage(ctx, message)
	}

	return fmt.Errorf("unknown provider: %s", provider)
}

// handleBinanceMessage 处理Binance消息
func (m *WebSocketManager) handleBinanceMessage(ctx context.Context, message []byte) error {
	// 首先尝试解析为组合流格式
	var combinedEvent struct {
		Stream string          `json:"stream"`
		Data   json.RawMessage `json:"data"`
	}

	if err := json.Unmarshal(message, &combinedEvent); err == nil && combinedEvent.Stream != "" {
		// 这是组合流格式，解析内部数据
		//g.Log().Debugf(ctx, "Processing combined stream message for: %s", combinedEvent.Stream)
		message = combinedEvent.Data
	}

	// 解析ticker事件
	var event model.BinanceTickerEvent
	if err := json.Unmarshal(message, &event); err != nil {
		return fmt.Errorf("failed to unmarshal binance ticker message: %w", err)
	}

	// 检查事件类型
	if event.EventType != "24hrTicker" {
		//g.Log().Debugf(ctx, "Ignoring non-ticker event: %s", event.EventType)
		return nil
	}

	// g.Log().Debugf(ctx, "Received Binance ticker for %s: price=%s, volume=%s",
	// 	event.Symbol, event.LastPrice, event.Volume)

	// 转换为通用价格事件
	priceData := m.convertBinanceEventToPriceData(&event)
	if priceData == nil {
		return fmt.Errorf("failed to convert binance event to price data")
	}

	priceEvent := &model.PriceUpdateEvent{
		Type:      "price_update",
		Symbol:    strings.ToUpper(event.Symbol),
		PriceData: priceData,
	}

	return m.handler.OnPriceUpdate(priceEvent)
}

// convertBinanceEventToPriceData 转换Binance事件为价格数据
func (m *WebSocketManager) convertBinanceEventToPriceData(event *model.BinanceTickerEvent) *model.PriceData {
	priceData, err := ConvertBinanceEventToPriceData(event)
	if err != nil {
		g.Log().Errorf(context.Background(), "Failed to convert binance event: %v", err)
		return nil
	}
	return priceData
}

// GetUnhealthyProviders 获取不健康的提供商列表
func (m *WebSocketManager) GetUnhealthyProviders() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var unhealthy []string
	for name, conn := range m.connections {
		if !conn.isConnected.Load() {
			unhealthy = append(unhealthy, name)
		}
	}
	return unhealthy
}

// GetAllProviders 获取所有提供商
func (m *WebSocketManager) GetAllProviders() []string {
	var providers []string
	for name, cfg := range m.config.WebSocket.Providers {
		if cfg.Enabled {
			providers = append(providers, name)
		}
	}
	return providers
}

// Close 关闭连接
func (c *WebSocketConnection) Close() error {
	c.isConnected.Store(false)
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.conn.Close()
}
