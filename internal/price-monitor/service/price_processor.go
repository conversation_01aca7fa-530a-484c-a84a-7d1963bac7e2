package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"

	"admin-api/internal/price-monitor/config"
	"admin-api/internal/price-monitor/model"
)

// PriceProcessor 价格数据处理器
type PriceProcessor struct {
	config      *config.Config
	redisWriter *RedisWriter
	validator   *PriceValidator
	eventBus    chan *model.PriceUpdateEvent
	stopCh      chan struct{}
	wg          sync.WaitGroup
}

// PriceValidator 价格验证器
type PriceValidator struct {
	maxPriceChange    float64                  // 最大价格变动百分比
	minUpdateInterval time.Duration            // 最小更新间隔
	priceHistory      map[string]*PriceHistory // 价格历史
	mu                sync.RWMutex
}

// PriceHistory 价格历史记录
type PriceHistory struct {
	LastPrice  decimal.Decimal
	LastUpdate time.Time
	Updates    int64
}

// NewPriceProcessor 创建价格处理器
func NewPriceProcessor(cfg *config.Config, redisWriter *RedisWriter) *PriceProcessor {
	return &PriceProcessor{
		config:      cfg,
		redisWriter: redisWriter,
		validator: &PriceValidator{
			maxPriceChange:    cfg.Validation.MaxPriceChange,
			minUpdateInterval: cfg.Validation.MinUpdateInterval,
			priceHistory:      make(map[string]*PriceHistory),
		},
		eventBus: make(chan *model.PriceUpdateEvent, 1000),
		stopCh:   make(chan struct{}),
	}
}

// Start 启动价格处理器
func (p *PriceProcessor) Start(ctx context.Context) error {
	p.wg.Add(1)
	go p.processEvents(ctx)
	return nil
}

// Stop 停止价格处理器
func (p *PriceProcessor) Stop(ctx context.Context) error {
	close(p.stopCh)
	p.wg.Wait()
	close(p.eventBus)
	return nil
}

// processEvents 处理价格事件
func (p *PriceProcessor) processEvents(ctx context.Context) {
	defer p.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-p.stopCh:
			return
		case event := <-p.eventBus:
			if event != nil && event.PriceData != nil {
				if err := p.processPriceData(ctx, event.PriceData); err != nil {
					g.Log().Errorf(ctx, "Failed to process price data: %v", err)
				}
			}
		}
	}
}

// processPriceData 处理价格数据
func (p *PriceProcessor) processPriceData(ctx context.Context, data *model.PriceData) error {
	// g.Log().Debugf(ctx, "Processing price data for %s: price=%s, provider=%s",
	// data.Symbol, data.Price.String(), data.Provider)

	// 验证价格数据
	if err := p.validator.Validate(data); err != nil {
		g.Log().Warningf(ctx, "Price validation failed for %s: %v", data.Symbol, err)
		return fmt.Errorf("price validation failed: %w", err)
	}

	// 写入Redis
	if err := p.redisWriter.Write(ctx, data); err != nil {
		g.Log().Errorf(ctx, "Failed to write to redis for %s: %v", data.Symbol, err)
		return fmt.Errorf("failed to write to redis: %w", err)
	}

	// 更新价格历史
	p.validator.UpdateHistory(data)

	g.Log().Infof(ctx, "Successfully processed price update for %s: %s", data.Symbol, data.Price.String())
	return nil
}

// OnPriceUpdate 处理价格更新事件
func (p *PriceProcessor) OnPriceUpdate(event *model.PriceUpdateEvent) error {
	// g.Log().Debugf(context.Background(), "OnPriceUpdate called with event: symbol=%s, type=%s",
	// event.Symbol, event.Type)

	select {
	case p.eventBus <- event:
		// g.Log().Debugf(context.Background(), "Event queued for processing: %s", event.Symbol)
		return nil
	default:
		g.Log().Errorf(context.Background(), "Event bus is full, dropping event for %s", event.Symbol)
		return fmt.Errorf("event bus is full")
	}
}

// OnConnectionEstablished 处理连接建立事件
func (p *PriceProcessor) OnConnectionEstablished(provider string) error {
	g.Log().Infof(context.Background(), "Connection established: %s", provider)
	return nil
}

// OnConnectionLost 处理连接丢失事件
func (p *PriceProcessor) OnConnectionLost(provider string) error {
	g.Log().Warningf(context.Background(), "Connection lost: %s", provider)
	return nil
}

// OnError 处理错误事件
func (p *PriceProcessor) OnError(provider string, err error) error {
	g.Log().Errorf(context.Background(), "Provider %s error: %v", provider, err)
	return nil
}

// ProcessBinanceMessage 处理Binance消息
func (p *PriceProcessor) ProcessBinanceMessage(ctx context.Context, data []byte) error {
	var event model.BinanceTickerEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return fmt.Errorf("failed to unmarshal binance message: %w", err)
	}

	priceData, err := p.convertBinanceEventToPriceData(&event)
	if err != nil {
		return fmt.Errorf("failed to convert binance event: %w", err)
	}

	return p.processPriceData(ctx, priceData)
}

// convertBinanceEventToPriceData 转换Binance事件为价格数据
func (p *PriceProcessor) convertBinanceEventToPriceData(event *model.BinanceTickerEvent) (*model.PriceData, error) {
	// 解析价格
	price, err := decimal.NewFromString(event.LastPrice)
	if err != nil {
		return nil, fmt.Errorf("failed to parse price: %w", err)
	}

	// 解析24小时交易量
	volume, err := decimal.NewFromString(event.Volume)
	if err != nil {
		return nil, fmt.Errorf("failed to parse volume: %w", err)
	}

	// 解析24小时涨跌幅
	changePercent, err := decimal.NewFromString(event.PriceChangePercent)
	if err != nil {
		return nil, fmt.Errorf("failed to parse change percent: %w", err)
	}

	// 解析最高价
	high, err := decimal.NewFromString(event.HighPrice)
	if err != nil {
		return nil, fmt.Errorf("failed to parse high price: %w", err)
	}

	// 解析最低价
	low, err := decimal.NewFromString(event.LowPrice)
	if err != nil {
		return nil, fmt.Errorf("failed to parse low price: %w", err)
	}

	// 转换为毫秒时间戳
	timestamp := time.Unix(0, event.EventTime*int64(time.Millisecond))

	return &model.PriceData{
		Symbol:      event.Symbol,
		Price:       price,
		Volume24h:   volume,
		Change24h:   changePercent.Div(decimal.NewFromInt(100)), // 转换为小数
		High24h:     high,
		Low24h:      low,
		Provider:    "binance",
		Timestamp:   timestamp,
		LastUpdated: timestamp.Unix(),
	}, nil
}

// Validate 验证价格数据
func (v *PriceValidator) Validate(data *model.PriceData) error {
	v.mu.Lock()
	defer v.mu.Unlock()

	history, exists := v.priceHistory[data.Symbol]
	if !exists {
		// 第一次更新，直接通过
		return nil
	}

	// 检查更新间隔
	if time.Since(history.LastUpdate) < v.minUpdateInterval {
		return model.NewPriceError(
			model.ErrorTypeValidation,
			"UPDATE_TOO_FREQUENT",
			"Price update too frequent",
		).WithSymbol(data.Symbol)
	}

	// 检查价格变动
	if !history.LastPrice.IsZero() {
		change := data.Price.Sub(history.LastPrice).Div(history.LastPrice).Abs()
		maxChange := decimal.NewFromFloat(v.maxPriceChange)

		if change.GreaterThan(maxChange) {
			return model.NewPriceError(
				model.ErrorTypeValidation,
				"PRICE_CHANGE_TOO_LARGE",
				fmt.Sprintf("Price change %.2f%% exceeds maximum %.2f%%",
					change.Mul(decimal.NewFromInt(100)).InexactFloat64(),
					v.maxPriceChange*100),
			).WithSymbol(data.Symbol).
				WithMetadata("old_price", history.LastPrice.String()).
				WithMetadata("new_price", data.Price.String())
		}
	}

	return nil
}

// UpdateHistory 更新价格历史
func (v *PriceValidator) UpdateHistory(data *model.PriceData) {
	v.mu.Lock()
	defer v.mu.Unlock()

	history, exists := v.priceHistory[data.Symbol]
	if !exists {
		history = &PriceHistory{}
		v.priceHistory[data.Symbol] = history
	}

	history.LastPrice = data.Price
	history.LastUpdate = data.Timestamp
	history.Updates++
}

// ConvertBinanceEventToPriceData WebSocketManager使用的转换函数
func ConvertBinanceEventToPriceData(event *model.BinanceTickerEvent) (*model.PriceData, error) {
	price, _ := strconv.ParseFloat(event.LastPrice, 64)
	volume, _ := strconv.ParseFloat(event.Volume, 64)
	changePercent, _ := strconv.ParseFloat(event.PriceChangePercent, 64)
	high, _ := strconv.ParseFloat(event.HighPrice, 64)
	low, _ := strconv.ParseFloat(event.LowPrice, 64)

	timestamp := time.Unix(0, event.EventTime*int64(time.Millisecond))

	return &model.PriceData{
		Symbol:      event.Symbol,
		Price:       decimal.NewFromFloat(price),
		Volume24h:   decimal.NewFromFloat(volume),
		Change24h:   decimal.NewFromFloat(changePercent / 100),
		High24h:     decimal.NewFromFloat(high),
		Low24h:      decimal.NewFromFloat(low),
		Provider:    "binance",
		Timestamp:   timestamp,
		LastUpdated: timestamp.Unix(),
	}, nil
}
