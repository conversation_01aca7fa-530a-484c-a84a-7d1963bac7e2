package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"

	"admin-api/internal/price-monitor/config"
	"admin-api/internal/price-monitor/model"
	"admin-api/internal/service"
)

// Redis key constants
const (
	PriceLiveKeyPrefix    = "price:live:"
	PriceHistoryKeyPrefix = "price:history:"
	FiatPriceKeyPrefix    = "fiat:price:"
	FiatHistoryKeyPrefix  = "fiat:history:"
	HealthStatusKeyPrefix = "price:health:"
	PriceSourcesKey       = "price:sources"

	// TTL settings
	PriceLiveTTL     = 5 * time.Minute
	FiatPriceTTL     = 10 * time.Minute // 法币价格更新间隔较长，TTL也更长
	PriceHistoryTTL  = 7 * 24 * time.Hour
	HealthStatusTTL  = 1 * time.Minute
	MaxHistoryPoints = 1000
)

// RedisWriter Redis数据写入器
type RedisWriter struct {
	client      *gredis.Redis
	config      *config.RedisConfig
	batchSize   int
	batchBuffer chan *model.PriceData
	flushTicker *time.Ticker
	stopCh      chan struct{}
	wg          sync.WaitGroup
}

// NewRedisWriter 创建Redis写入器
func NewRedisWriter(cfg *config.RedisConfig) (*RedisWriter, error) {
	// 使用项目现有的Redis服务
	client := service.Redis().Client(cfg.ConfigName)
	if client == nil {
		return nil, fmt.Errorf("failed to get redis client with config name: %s", cfg.ConfigName)
	}

	writer := &RedisWriter{
		client:      client,
		config:      cfg,
		batchSize:   100,
		batchBuffer: make(chan *model.PriceData, 1000),
		flushTicker: time.NewTicker(100 * time.Millisecond),
		stopCh:      make(chan struct{}),
	}

	return writer, nil
}

// Start 启动Redis写入器
func (r *RedisWriter) Start(ctx context.Context) error {
	r.wg.Add(1)
	go r.batchWorker(ctx)
	return nil
}

// Stop 停止Redis写入器
func (r *RedisWriter) Stop(ctx context.Context) error {
	close(r.stopCh)
	r.flushTicker.Stop()
	r.wg.Wait()

	// 刷新剩余的数据
	r.flushRemaining(ctx)
	close(r.batchBuffer)

	return nil
}

// Write 写入价格数据
func (r *RedisWriter) Write(ctx context.Context, data *model.PriceData) error {
	// g.Log().Debugf(ctx, "Writing price data for %s to Redis", data.Symbol)

	select {
	case r.batchBuffer <- data:
		// g.Log().Debugf(ctx, "Added %s to batch buffer", data.Symbol)
		return nil
	case <-ctx.Done():
		return ctx.Err()
	default:
		// 缓冲区满，直接写入
		// g.Log().Debugf(ctx, "Buffer full, writing %s directly", data.Symbol)
		return r.writeSingle(ctx, data)
	}
}

// batchWorker 批量写入工作协程
func (r *RedisWriter) batchWorker(ctx context.Context) {
	defer r.wg.Done()

	batch := make([]*model.PriceData, 0, r.batchSize)

	for {
		select {
		case <-ctx.Done():
			return

		case <-r.stopCh:
			return

		case data := <-r.batchBuffer:
			batch = append(batch, data)
			if len(batch) >= r.batchSize {
				if err := r.writeBatch(ctx, batch); err != nil {
					g.Log().Errorf(ctx, "Failed to write batch: %v", err)
				}
				batch = batch[:0]
			}

		case <-r.flushTicker.C:
			if len(batch) > 0 {
				if err := r.writeBatch(ctx, batch); err != nil {
					g.Log().Errorf(ctx, "Failed to write batch: %v", err)
				}
				batch = batch[:0]
			}
		}
	}
}

// writeSingle 写入单个价格数据
func (r *RedisWriter) writeSingle(ctx context.Context, data *model.PriceData) error {
	// 主要价格数据
	liveKey := PriceLiveKeyPrefix + data.Symbol
	fields := map[string]interface{}{
		"price":        data.Price.String(),
		"volume_24h":   data.Volume24h.String(),
		"change_24h":   data.Change24h.String(),
		"high_24h":     data.High24h.String(),
		"low_24h":      data.Low24h.String(),
		"provider":     data.Provider,
		"timestamp":    data.Timestamp.UnixMilli(),
		"last_updated": data.LastUpdated,
	}

	// 使用HMSet设置多个字段
	if err := r.client.HMSet(ctx, liveKey, fields); err != nil {
		return fmt.Errorf("failed to set live price: %w", err)
	}

	// 设置TTL
	if _, err := r.client.Expire(ctx, liveKey, int64(PriceLiveTTL.Seconds())); err != nil {
		g.Log().Warningf(ctx, "Failed to set TTL for %s: %v", liveKey, err)
	}

	// 写入历史数据
	historyKey := PriceHistoryKeyPrefix + data.Symbol
	historyData, _ := json.Marshal(data)

	// 使用ZAdd添加到有序集合
	if _, err := r.client.ZAdd(ctx, historyKey, nil, gredis.ZAddMember{
		Score:  float64(data.Timestamp.UnixMilli()),
		Member: string(historyData),
	}); err != nil {
		g.Log().Warningf(ctx, "Failed to add history: %v", err)
	}

	// 删除旧的历史数据
	if _, err := r.client.ZRemRangeByRank(ctx, historyKey, 0, -MaxHistoryPoints-1); err != nil {
		g.Log().Warningf(ctx, "Failed to trim history: %v", err)
	}

	// 更新价格源状态
	if _, err := r.client.HSet(ctx, PriceSourcesKey, g.Map{data.Provider: "active"}); err != nil {
		g.Log().Warningf(ctx, "Failed to update source status: %v", err)
	}

	return nil
}

// writeBatch 批量写入
func (r *RedisWriter) writeBatch(ctx context.Context, batch []*model.PriceData) error {
	// 由于gredis不支持Pipeline，我们使用事务或逐个执行
	// 为了性能，这里直接逐个执行，错误会被记录但不会中断整个批次
	var lastErr error

	for _, data := range batch {
		if err := r.writeSingle(ctx, data); err != nil {
			g.Log().Errorf(ctx, "Failed to write price data for %s: %v", data.Symbol, err)
			lastErr = err
		}
	}

	return lastErr
}

// flushRemaining 刷新剩余数据
func (r *RedisWriter) flushRemaining(ctx context.Context) {
	batch := make([]*model.PriceData, 0, r.batchSize)

	for {
		select {
		case data := <-r.batchBuffer:
			batch = append(batch, data)
		default:
			if len(batch) > 0 {
				if err := r.writeBatch(ctx, batch); err != nil {
					g.Log().Errorf(ctx, "Failed to flush remaining data: %v", err)
				}
			}
			return
		}
	}
}

// UpdateHealthStatus 更新健康状态
func (r *RedisWriter) UpdateHealthStatus(ctx context.Context, provider string, status *model.HealthStatus) error {
	key := HealthStatusKeyPrefix + provider
	fields := map[string]interface{}{
		"status":      status.Status,
		"message":     status.Message,
		"timestamp":   status.Timestamp.UnixMilli(),
		"last_update": time.Now().Unix(),
	}

	// 添加元数据
	if status.Metadata != nil {
		for k, v := range status.Metadata {
			fields[k] = fmt.Sprintf("%v", v)
		}
	}

	if err := r.client.HMSet(ctx, key, fields); err != nil {
		return fmt.Errorf("failed to update health status: %w", err)
	}

	if _, err := r.client.Expire(ctx, key, int64(HealthStatusTTL.Seconds())); err != nil {
		g.Log().Warningf(ctx, "Failed to set TTL for health status: %v", err)
	}

	return nil
}

// GetLatestPrice 获取最新价格（供内部使用）
func (r *RedisWriter) GetLatestPrice(ctx context.Context, symbol string) (*model.PriceData, error) {
	key := PriceLiveKeyPrefix + symbol

	result, err := r.client.HGetAll(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get price data: %w", err)
	}

	// 将gvar结果转换为map
	resultMap := result.MapStrStr()

	if len(resultMap) == 0 {
		return nil, model.NewPriceError(
			model.ErrorTypeStaleData,
			"NO_DATA",
			"No price data available",
		).WithSymbol(symbol)
	}

	// 解析数据
	priceData := &model.PriceData{
		Symbol:   symbol,
		Provider: resultMap["provider"],
	}

	// 解析价格字段
	if priceStr, ok := resultMap["price"]; ok {
		priceData.Price, _ = decimal.NewFromString(priceStr)
	}
	if volumeStr, ok := resultMap["volume_24h"]; ok {
		priceData.Volume24h, _ = decimal.NewFromString(volumeStr)
	}
	if changeStr, ok := resultMap["change_24h"]; ok {
		priceData.Change24h, _ = decimal.NewFromString(changeStr)
	}
	if highStr, ok := resultMap["high_24h"]; ok {
		priceData.High24h, _ = decimal.NewFromString(highStr)
	}
	if lowStr, ok := resultMap["low_24h"]; ok {
		priceData.Low24h, _ = decimal.NewFromString(lowStr)
	}

	// 解析时间戳
	if tsStr, ok := resultMap["timestamp"]; ok {
		if ts, err := strconv.ParseInt(tsStr, 10, 64); err == nil {
			priceData.Timestamp = time.Unix(0, ts*int64(time.Millisecond))
		}
	}
	if luStr, ok := resultMap["last_updated"]; ok {
		priceData.LastUpdated, _ = strconv.ParseInt(luStr, 10, 64)
	}

	return priceData, nil
}

// WriteFiatPrice 写入法币价格数据
func (r *RedisWriter) WriteFiatPrice(ctx context.Context, data *model.FiatPriceData) error {
	// 主要法币价格数据
	liveKey := fmt.Sprintf("%s%s_%s", FiatPriceKeyPrefix, data.Asset, data.Currency)
	fields := map[string]interface{}{
		"asset":           data.Asset,
		"currency":        data.Currency,
		"currency_symbol": data.CurrencySymbol,
		"buy_price":       data.BuyPrice.String(),
		"sell_price":      data.SellPrice.String(),
		"currency_scale":  data.CurrencyScale,
		"asset_scale":     data.AssetScale,
		"price_scale":     data.PriceScale,
		"provider":        data.Provider,
		"timestamp":       data.Timestamp.UnixMilli(),
		"last_updated":    data.LastUpdated,
	}

	// 使用HMSet设置多个字段
	if err := r.client.HMSet(ctx, liveKey, fields); err != nil {
		return fmt.Errorf("failed to set fiat price: %w", err)
	}

	// 设置TTL
	if _, err := r.client.Expire(ctx, liveKey, int64(FiatPriceTTL.Seconds())); err != nil {
		g.Log().Warningf(ctx, "Failed to set TTL for %s: %v", liveKey, err)
	}

	// 写入历史数据
	historyKey := fmt.Sprintf("%s%s_%s", FiatHistoryKeyPrefix, data.Asset, data.Currency)
	historyData, _ := json.Marshal(data)

	// 使用ZAdd添加到有序集合
	if _, err := r.client.ZAdd(ctx, historyKey, nil, gredis.ZAddMember{
		Score:  float64(data.Timestamp.UnixMilli()),
		Member: string(historyData),
	}); err != nil {
		g.Log().Warningf(ctx, "Failed to add fiat history: %v", err)
	}

	// 删除旧的历史数据
	if _, err := r.client.ZRemRangeByRank(ctx, historyKey, 0, -MaxHistoryPoints-1); err != nil {
		g.Log().Warningf(ctx, "Failed to trim fiat history: %v", err)
	}

	// 更新价格源状态
	sourceKey := fmt.Sprintf("fiat_%s", data.Provider)
	if _, err := r.client.HSet(ctx, PriceSourcesKey, g.Map{sourceKey: "active"}); err != nil {
		g.Log().Warningf(ctx, "Failed to update fiat source status: %v", err)
	}

	return nil
}

// GetLatestFiatPrice 获取最新法币价格
func (r *RedisWriter) GetLatestFiatPrice(ctx context.Context, asset, currency string) (*model.FiatPriceData, error) {
	key := fmt.Sprintf("%s%s_%s", FiatPriceKeyPrefix, asset, currency)

	result, err := r.client.HGetAll(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get fiat price data: %w", err)
	}

	// 将gvar结果转换为map
	resultMap := result.MapStrStr()

	if len(resultMap) == 0 {
		return nil, fmt.Errorf("no fiat price data available for %s/%s", asset, currency)
	}

	// 解析数据
	fiatData := &model.FiatPriceData{
		Asset:          resultMap["asset"],
		Currency:       resultMap["currency"],
		CurrencySymbol: resultMap["currency_symbol"],
		Provider:       resultMap["provider"],
	}

	// 解析价格字段
	if buyPriceStr, ok := resultMap["buy_price"]; ok {
		fiatData.BuyPrice, _ = decimal.NewFromString(buyPriceStr)
	}
	if sellPriceStr, ok := resultMap["sell_price"]; ok {
		fiatData.SellPrice, _ = decimal.NewFromString(sellPriceStr)
	}

	// 解析整数字段
	if currencyScale, ok := resultMap["currency_scale"]; ok {
		fiatData.CurrencyScale, _ = strconv.Atoi(currencyScale)
	}
	if assetScale, ok := resultMap["asset_scale"]; ok {
		fiatData.AssetScale, _ = strconv.Atoi(assetScale)
	}
	if priceScale, ok := resultMap["price_scale"]; ok {
		fiatData.PriceScale, _ = strconv.Atoi(priceScale)
	}

	// 解析时间戳
	if tsStr, ok := resultMap["timestamp"]; ok {
		if ts, err := strconv.ParseInt(tsStr, 10, 64); err == nil {
			fiatData.Timestamp = time.Unix(0, ts*int64(time.Millisecond))
		}
	}
	if luStr, ok := resultMap["last_updated"]; ok {
		fiatData.LastUpdated, _ = strconv.ParseInt(luStr, 10, 64)
	}

	return fiatData, nil
}
