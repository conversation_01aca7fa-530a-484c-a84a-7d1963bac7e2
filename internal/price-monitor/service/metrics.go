package service

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Metrics Prometheus指标
type Metrics struct {
	// WebSocket连接指标
	wsConnections    *prometheus.GaugeVec
	wsReconnects     *prometheus.CounterVec
	wsMessagesTotal  *prometheus.CounterVec
	wsMessageLatency *prometheus.HistogramVec

	// 价格数据指标
	priceUpdatesTotal *prometheus.CounterVec
	priceAnomalies    *prometheus.CounterVec
	priceLatency      *prometheus.HistogramVec
	staleDataCount    *prometheus.GaugeVec

	// Redis操作指标
	redisOpsTotal *prometheus.CounterVec
	redisLatency  *prometheus.HistogramVec
	redisErrors   *prometheus.CounterVec

	// 健康状态指标
	healthStatus *prometheus.GaugeVec
	uptimeTotal  *prometheus.CounterVec
}

// NewMetrics 创建新的指标收集器
func NewMetrics() *Metrics {
	return &Metrics{
		wsConnections: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "price_monitor_websocket_connections",
				Help: "Number of active WebSocket connections",
			},
			[]string{"provider", "status"},
		),
		wsReconnects: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "price_monitor_websocket_reconnects_total",
				Help: "Total number of WebSocket reconnection attempts",
			},
			[]string{"provider"},
		),
		wsMessagesTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "price_monitor_websocket_messages_total",
				Help: "Total number of WebSocket messages received",
			},
			[]string{"provider", "type"},
		),
		wsMessageLatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "price_monitor_websocket_message_latency_seconds",
				Help:    "WebSocket message processing latency",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"provider"},
		),
		priceUpdatesTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "price_monitor_price_updates_total",
				Help: "Total number of price updates processed",
			},
			[]string{"symbol", "provider", "status"},
		),
		priceAnomalies: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "price_monitor_price_anomalies_total",
				Help: "Total number of price anomalies detected",
			},
			[]string{"symbol", "type"},
		),
		priceLatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "price_monitor_price_processing_latency_seconds",
				Help:    "Price processing latency",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"symbol"},
		),
		staleDataCount: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "price_monitor_stale_data_count",
				Help: "Number of symbols with stale data",
			},
			[]string{"provider"},
		),
		redisOpsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "price_monitor_redis_operations_total",
				Help: "Total number of Redis operations",
			},
			[]string{"operation", "status"},
		),
		redisLatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "price_monitor_redis_operation_latency_seconds",
				Help:    "Redis operation latency",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation"},
		),
		redisErrors: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "price_monitor_redis_errors_total",
				Help: "Total number of Redis errors",
			},
			[]string{"operation", "error_type"},
		),
		healthStatus: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "price_monitor_health_status",
				Help: "Health status of various components (0=unhealthy, 1=degraded, 2=healthy)",
			},
			[]string{"component"},
		),
		uptimeTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "price_monitor_uptime_seconds_total",
				Help: "Total uptime of the price monitor service",
			},
			[]string{"component"},
		),
	}
}

// RecordWebSocketConnection 记录WebSocket连接状态
func (m *Metrics) RecordWebSocketConnection(provider string, connected bool) {
	if connected {
		m.wsConnections.WithLabelValues(provider, "connected").Set(1)
		m.wsConnections.WithLabelValues(provider, "disconnected").Set(0)
	} else {
		m.wsConnections.WithLabelValues(provider, "connected").Set(0)
		m.wsConnections.WithLabelValues(provider, "disconnected").Set(1)
	}
}

// RecordWebSocketReconnect 记录WebSocket重连
func (m *Metrics) RecordWebSocketReconnect(provider string) {
	m.wsReconnects.WithLabelValues(provider).Inc()
}

// RecordWebSocketMessage 记录WebSocket消息
func (m *Metrics) RecordWebSocketMessage(provider, messageType string) {
	m.wsMessagesTotal.WithLabelValues(provider, messageType).Inc()
}

// RecordWebSocketLatency 记录WebSocket消息延迟
func (m *Metrics) RecordWebSocketLatency(provider string, seconds float64) {
	m.wsMessageLatency.WithLabelValues(provider).Observe(seconds)
}

// RecordPriceUpdate 记录价格更新
func (m *Metrics) RecordPriceUpdate(symbol, provider, status string) {
	m.priceUpdatesTotal.WithLabelValues(symbol, provider, status).Inc()
}

// RecordPriceAnomaly 记录价格异常
func (m *Metrics) RecordPriceAnomaly(symbol, anomalyType string) {
	m.priceAnomalies.WithLabelValues(symbol, anomalyType).Inc()
}

// RecordPriceLatency 记录价格处理延迟
func (m *Metrics) RecordPriceLatency(symbol string, seconds float64) {
	m.priceLatency.WithLabelValues(symbol).Observe(seconds)
}

// SetStaleDataCount 设置过期数据数量
func (m *Metrics) SetStaleDataCount(provider string, count float64) {
	m.staleDataCount.WithLabelValues(provider).Set(count)
}

// RecordRedisOperation 记录Redis操作
func (m *Metrics) RecordRedisOperation(operation, status string) {
	m.redisOpsTotal.WithLabelValues(operation, status).Inc()
}

// RecordRedisLatency 记录Redis操作延迟
func (m *Metrics) RecordRedisLatency(operation string, seconds float64) {
	m.redisLatency.WithLabelValues(operation).Observe(seconds)
}

// RecordRedisError 记录Redis错误
func (m *Metrics) RecordRedisError(operation, errorType string) {
	m.redisErrors.WithLabelValues(operation, errorType).Inc()
}

// SetHealthStatus 设置健康状态
func (m *Metrics) SetHealthStatus(component string, status string) {
	var value float64
	switch status {
	case "healthy":
		value = 2
	case "degraded":
		value = 1
	case "unhealthy":
		value = 0
	}
	m.healthStatus.WithLabelValues(component).Set(value)
}

// RecordUptime 记录运行时间
func (m *Metrics) RecordUptime(component string, seconds float64) {
	m.uptimeTotal.WithLabelValues(component).Add(seconds)
}
