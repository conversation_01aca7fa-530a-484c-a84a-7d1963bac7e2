package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	"admin-api/internal/price-monitor/config"
	"admin-api/internal/price-monitor/model"
)

// HealthMonitor 健康监控器
type HealthMonitor struct {
	config      *config.Config
	checks      map[string]HealthCheck
	interval    time.Duration
	redisWriter *RedisWriter
	stopCh      chan struct{}
	wg          sync.WaitGroup
	mu          sync.RWMutex
}

// HealthCheck 健康检查接口
type HealthCheck interface {
	Name() string
	Check(ctx context.Context) model.HealthStatus
}

// NewHealthMonitor 创建健康监控器
func NewHealthMonitor(cfg *config.Config, redisWriter *RedisWriter) *HealthMonitor {
	return &HealthMonitor{
		config:      cfg,
		checks:      make(map[string]HealthCheck),
		interval:    cfg.Monitoring.HealthCheckInterval,
		redisWriter: redisWriter,
		stopCh:      make(chan struct{}),
	}
}

// RegisterCheck 注册健康检查
func (h *HealthMonitor) RegisterCheck(check HealthCheck) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.checks[check.Name()] = check
}

// Start 启动健康监控
func (h *HealthMonitor) Start(ctx context.Context) error {
	h.wg.Add(1)
	go h.monitorWorker(ctx)
	return nil
}

// Stop 停止健康监控
func (h *HealthMonitor) Stop(ctx context.Context) error {
	close(h.stopCh)
	h.wg.Wait()
	return nil
}

// monitorWorker 监控工作协程
func (h *HealthMonitor) monitorWorker(ctx context.Context) {
	defer h.wg.Done()

	ticker := time.NewTicker(h.interval)
	defer ticker.Stop()

	// 立即执行一次检查
	h.runHealthChecks(ctx)

	for {
		select {
		case <-ctx.Done():
			return
		case <-h.stopCh:
			return
		case <-ticker.C:
			h.runHealthChecks(ctx)
		}
	}
}

// runHealthChecks 运行所有健康检查
func (h *HealthMonitor) runHealthChecks(ctx context.Context) {
	h.mu.RLock()
	checks := make([]HealthCheck, 0, len(h.checks))
	for _, check := range h.checks {
		checks = append(checks, check)
	}
	h.mu.RUnlock()

	for _, check := range checks {
		status := check.Check(ctx)

		// 更新Redis中的健康状态
		if err := h.redisWriter.UpdateHealthStatus(ctx, check.Name(), &status); err != nil {
			g.Log().Errorf(ctx, "Failed to update health status for %s: %v", check.Name(), err)
		}

		// 记录状态变化
		if status.Status != "healthy" {
			g.Log().Warningf(ctx, "Health check %s status: %s - %s",
				check.Name(), status.Status, status.Message)
		}
	}
}

// GetStatus 获取当前健康状态
func (h *HealthMonitor) GetStatus(ctx context.Context) map[string]model.HealthStatus {
	h.mu.RLock()
	defer h.mu.RUnlock()

	results := make(map[string]model.HealthStatus)
	for name, check := range h.checks {
		results[name] = check.Check(ctx)
	}

	return results
}

// WebSocketHealthCheck WebSocket连接健康检查
type WebSocketHealthCheck struct {
	manager *WebSocketManager
}

// NewWebSocketHealthCheck 创建WebSocket健康检查
func NewWebSocketHealthCheck(manager *WebSocketManager) *WebSocketHealthCheck {
	return &WebSocketHealthCheck{
		manager: manager,
	}
}

// Name 返回检查名称
func (w *WebSocketHealthCheck) Name() string {
	return "websocket_connections"
}

// Check 执行健康检查
func (w *WebSocketHealthCheck) Check(ctx context.Context) model.HealthStatus {
	unhealthyProviders := w.manager.GetUnhealthyProviders()
	allProviders := w.manager.GetAllProviders()

	if len(unhealthyProviders) == 0 {
		return model.HealthStatus{
			Status:    "healthy",
			Message:   "All WebSocket connections are healthy",
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"total_providers":   len(allProviders),
				"healthy_providers": len(allProviders),
			},
		}
	}

	if len(unhealthyProviders) == len(allProviders) {
		return model.HealthStatus{
			Status:    "unhealthy",
			Message:   "All WebSocket connections are down",
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"unhealthy_providers": unhealthyProviders,
				"total_providers":     len(allProviders),
			},
		}
	}

	return model.HealthStatus{
		Status:    "degraded",
		Message:   fmt.Sprintf("Some WebSocket connections are down: %v", unhealthyProviders),
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"unhealthy_providers": unhealthyProviders,
			"healthy_providers":   len(allProviders) - len(unhealthyProviders),
			"total_providers":     len(allProviders),
		},
	}
}

// RedisHealthCheck Redis连接健康检查
type RedisHealthCheck struct {
	redisWriter *RedisWriter
}

// NewRedisHealthCheck 创建Redis健康检查
func NewRedisHealthCheck(redisWriter *RedisWriter) *RedisHealthCheck {
	return &RedisHealthCheck{
		redisWriter: redisWriter,
	}
}

// Name 返回检查名称
func (r *RedisHealthCheck) Name() string {
	return "redis_connection"
}

// Check 执行健康检查
func (r *RedisHealthCheck) Check(ctx context.Context) model.HealthStatus {
	// 测试Redis连接
	testKey := "price:health:ping"
	testValue := time.Now().Unix()

	// 尝试写入
	if _, err := r.redisWriter.client.Set(ctx, testKey, testValue); err != nil {
		return model.HealthStatus{
			Status:    "unhealthy",
			Message:   fmt.Sprintf("Redis write failed: %v", err),
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"error": err.Error(),
			},
		}
	}

	// 尝试读取
	result, err := r.redisWriter.client.Get(ctx, testKey)
	if err != nil {
		return model.HealthStatus{
			Status:    "unhealthy",
			Message:   fmt.Sprintf("Redis read failed: %v", err),
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"error": err.Error(),
			},
		}
	}

	// 验证值
	if result.Int64() != testValue {
		return model.HealthStatus{
			Status:    "unhealthy",
			Message:   "Redis read/write mismatch",
			Timestamp: time.Now(),
		}
	}

	// 删除测试键
	r.redisWriter.client.Del(ctx, testKey)

	return model.HealthStatus{
		Status:    "healthy",
		Message:   "Redis connection is healthy",
		Timestamp: time.Now(),
	}
}

// DataFreshnessHealthCheck 数据新鲜度健康检查
type DataFreshnessHealthCheck struct {
	redisWriter    *RedisWriter
	symbols        []string
	staleThreshold time.Duration
}

// NewDataFreshnessHealthCheck 创建数据新鲜度健康检查
func NewDataFreshnessHealthCheck(redisWriter *RedisWriter, symbols []string, threshold time.Duration) *DataFreshnessHealthCheck {
	return &DataFreshnessHealthCheck{
		redisWriter:    redisWriter,
		symbols:        symbols,
		staleThreshold: threshold,
	}
}

// Name 返回检查名称
func (d *DataFreshnessHealthCheck) Name() string {
	return "data_freshness"
}

// Check 执行健康检查
func (d *DataFreshnessHealthCheck) Check(ctx context.Context) model.HealthStatus {
	staleSymbols := []string{}
	freshSymbols := 0

	for _, symbol := range d.symbols {
		priceData, err := d.redisWriter.GetLatestPrice(ctx, symbol)
		if err != nil {
			staleSymbols = append(staleSymbols, symbol)
			continue
		}

		if time.Since(priceData.Timestamp) > d.staleThreshold {
			staleSymbols = append(staleSymbols, symbol)
		} else {
			freshSymbols++
		}
	}

	if len(staleSymbols) == 0 {
		return model.HealthStatus{
			Status:    "healthy",
			Message:   "All price data is fresh",
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"fresh_symbols": freshSymbols,
				"total_symbols": len(d.symbols),
			},
		}
	}

	if len(staleSymbols) == len(d.symbols) {
		return model.HealthStatus{
			Status:    "unhealthy",
			Message:   "All price data is stale",
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"stale_symbols": staleSymbols,
				"total_symbols": len(d.symbols),
			},
		}
	}

	return model.HealthStatus{
		Status:    "degraded",
		Message:   fmt.Sprintf("Some price data is stale: %v", staleSymbols),
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"stale_symbols": staleSymbols,
			"fresh_symbols": freshSymbols,
			"total_symbols": len(d.symbols),
		},
	}
}

// C2CHealthCheck C2C API健康检查
type C2CHealthCheck struct {
	fetcher *FiatPriceFetcher
}

// NewC2CHealthCheck 创建C2C健康检查
func NewC2CHealthCheck(fetcher *FiatPriceFetcher) *C2CHealthCheck {
	return &C2CHealthCheck{
		fetcher: fetcher,
	}
}

// Name 返回检查名称
func (c *C2CHealthCheck) Name() string {
	return "c2c_api"
}

// Check 执行健康检查
func (c *C2CHealthCheck) Check(ctx context.Context) model.HealthStatus {
	if err := c.fetcher.HealthCheck(ctx); err != nil {
		return model.HealthStatus{
			Status:    "unhealthy",
			Message:   fmt.Sprintf("C2C API health check failed: %v", err),
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"error": err.Error(),
			},
		}
	}

	// 检查缓存的价格数据
	cachedPrices := c.fetcher.GetAllCachedPrices()
	if len(cachedPrices) == 0 {
		return model.HealthStatus{
			Status:    "degraded",
			Message:   "C2C API is reachable but no cached prices available",
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"cached_pairs": 0,
			},
		}
	}

	// 检查价格数据的新鲜度
	stalePairs := 0
	for pair, priceData := range cachedPrices {
		if time.Since(priceData.Timestamp) > 5*time.Minute {
			stalePairs++
			g.Log().Debugf(ctx, "Stale fiat price for %s: %v old", pair, time.Since(priceData.Timestamp))
		}
	}

	if stalePairs == len(cachedPrices) {
		return model.HealthStatus{
			Status:    "degraded",
			Message:   "All cached fiat prices are stale",
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"cached_pairs": len(cachedPrices),
				"stale_pairs":  stalePairs,
			},
		}
	}

	return model.HealthStatus{
		Status:    "healthy",
		Message:   "C2C API is healthy",
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"cached_pairs": len(cachedPrices),
			"fresh_pairs":  len(cachedPrices) - stalePairs,
			"stale_pairs":  stalePairs,
		},
	}
}
