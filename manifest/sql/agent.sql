/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : localhost:3306
 Source Schema         : game

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 15/07/2025 01:00:22
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;


-- ----------------------------
-- Table structure for commission_records
-- ----------------------------
DROP TABLE IF EXISTS `commission_records`;
CREATE TABLE `commission_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `beneficiary_id` bigint unsigned NOT NULL COMMENT '受益人ID（收到佣金的用户）',
  `source_user_id` bigint unsigned NOT NULL COMMENT '来源用户ID（产生投注的用户）',
  `game_transaction_id` bigint unsigned NOT NULL COMMENT '游戏流水ID',
  `bet_amount` decimal(20,4) NOT NULL COMMENT '投注金额',
  `commission_rate` decimal(5,4) NOT NULL COMMENT '佣金比例（0.003表示0.3%）',
  `commission_amount` decimal(20,4) NOT NULL COMMENT '佣金金额',
  `commission_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '佣金类型（direct:直接邀请，indirect:间接邀请）',
  `level` int NOT NULL DEFAULT '1' COMMENT '推荐层级（1:直接，2+:间接）',
  `symbol` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0:待发放，1:已发放，2:发放失败）',
  `transaction_id` bigint unsigned DEFAULT NULL COMMENT '关联的资金交易ID',
  `notified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已发送通知',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `distributed_at` timestamp NULL DEFAULT NULL COMMENT '发放时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_game_transaction_beneficiary` (`game_transaction_id`,`beneficiary_id`),
  KEY `idx_beneficiary_status` (`beneficiary_id`,`status`),
  KEY `idx_source_user` (`source_user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金记录表';

-- ----------------------------
-- Table structure for commission_summary
-- ----------------------------
DROP TABLE IF EXISTS `commission_summary`;
CREATE TABLE `commission_summary` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '日期',
  `direct_commission` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '直接邀请佣金',
  `indirect_commission` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '间接邀请佣金',
  `total_commission` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '总佣金',
  `direct_count` int NOT NULL DEFAULT '0' COMMENT '直接邀请佣金笔数',
  `indirect_count` int NOT NULL DEFAULT '0' COMMENT '间接邀请佣金笔数',
  `symbol` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date_symbol` (`user_id`,`date`,`symbol`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金汇总表';

-- ----------------------------
-- Table structure for deposit_rewards
-- ----------------------------
DROP TABLE IF EXISTS `deposit_rewards`;
CREATE TABLE `deposit_rewards` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `recharge_id` bigint NOT NULL,
  `deposit_amount` decimal(18,4) NOT NULL,
  `reward_percentage` decimal(5,2) NOT NULL,
  `reward_amount` decimal(18,4) NOT NULL,
  `flow_multiplier` decimal(5,2) NOT NULL,
  `required_turnover` decimal(18,4) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for game_info
-- ----------------------------
DROP TABLE IF EXISTS `game_info`;
CREATE TABLE `game_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '游戏信息ID',
  `provider_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏提供商代码',
  `game_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏代码',
  `game_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏名称',
  `game_name_en` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏英文名称',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏分类',
  `subcategory` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏子分类',
  `status` enum('active','inactive','maintenance','disabled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '游戏状态',
  `min_bet` decimal(20,8) DEFAULT NULL COMMENT '最小投注金额',
  `max_bet` decimal(20,8) DEFAULT NULL COMMENT '最大投注金额',
  `max_win` decimal(20,8) DEFAULT NULL COMMENT '最大赢取金额',
  `rtp_percentage` decimal(5,2) DEFAULT NULL COMMENT 'RTP百分比',
  `volatility` enum('low','medium','high') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '波动性',
  `supported_currencies` json DEFAULT NULL COMMENT '支持的货币（JSON数组）',
  `supported_languages` json DEFAULT NULL COMMENT '支持的语言（JSON数组）',
  `game_thumbnail` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏缩略图URL',
  `game_icon` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏图标URL',
  `game_description` text COLLATE utf8mb4_unicode_ci COMMENT '游戏描述',
  `features` json DEFAULT NULL COMMENT '游戏特色功能（JSON数组）',
  `tags` json DEFAULT NULL COMMENT '游戏标签（JSON数组）',
  `is_mobile_supported` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否支持移动端',
  `is_demo_available` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否提供试玩模式',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为推荐游戏',
  `is_new` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为新游戏',
  `launch_count` bigint unsigned NOT NULL DEFAULT '0' COMMENT '启动次数',
  `last_launched_at` timestamp NULL DEFAULT NULL COMMENT '最后启动时间',
  `metadata` json DEFAULT NULL COMMENT '额外元数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_game_code` (`provider_code`,`game_code`),
  KEY `idx_status` (`status`),
  KEY `idx_category` (`category`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_is_new` (`is_new`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_provider_status` (`provider_code`,`status`),
  CONSTRAINT `fk_game_info_provider_code` FOREIGN KEY (`provider_code`) REFERENCES `game_providers` (`code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏信息表';

-- ----------------------------
-- Table structure for game_providers
-- ----------------------------
DROP TABLE IF EXISTS `game_providers`;
CREATE TABLE `game_providers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '游戏提供商唯一ID',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提供商代码（如TCG、PP、PG）',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提供商名称',
  `api_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API接口地址',
  `status` enum('active','inactive','maintenance') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '提供商状态',
  `merchant_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商户代码',
  `secret_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '签名密钥',
  `encrypt_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '加密密钥',
  `encryption_method` enum('DES','AES','none') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'DES' COMMENT '加密方式',
  `timeout_seconds` int unsigned NOT NULL DEFAULT '30' COMMENT 'API超时时间（秒）',
  `retry_count` tinyint unsigned NOT NULL DEFAULT '3' COMMENT '重试次数',
  `rate_limit` int unsigned DEFAULT NULL COMMENT '速率限制（每分钟请求数）',
  `ip_whitelist` text COLLATE utf8mb4_unicode_ci COMMENT 'IP白名单（JSON数组）',
  `webhook_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '回调地址',
  `webhook_secret` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '回调验签密钥',
  `config_data` json DEFAULT NULL COMMENT '额外配置数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏提供商配置表';

-- ----------------------------
-- Table structure for game_sessions
-- ----------------------------
DROP TABLE IF EXISTS `game_sessions`;
CREATE TABLE `game_sessions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `session_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话唯一标识符',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `provider_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏提供商代码',
  `game_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏代码',
  `currency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
  `initial_balance` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '初始余额',
  `current_balance` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '当前余额',
  `status` enum('active','inactive','expired','closed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '会话状态',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `login_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏登录URL',
  `session_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话令牌',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `last_activity_at` timestamp NULL DEFAULT NULL COMMENT '最后活动时间',
  `metadata` json DEFAULT NULL COMMENT '会话元数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_provider_code` (`provider_code`),
  KEY `idx_status` (`status`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_user_provider` (`user_id`,`provider_code`),
  KEY `idx_cleanup` (`status`,`expires_at`),
  CONSTRAINT `fk_game_sessions_provider_code` FOREIGN KEY (`provider_code`) REFERENCES `game_providers` (`code`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_game_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏会话表';

-- ----------------------------
-- Table structure for game_transaction_records
-- ----------------------------
DROP TABLE IF EXISTS `game_transaction_records`;
CREATE TABLE `game_transaction_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '交易记录ID',
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易唯一标识符',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `session_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话ID',
  `provider_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏提供商代码',
  `game_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏代码',
  `type` enum('bet','win','refund','bonus','jackpot','rollback','promotion') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易类型',
  `status` enum('pending','completed','failed','cancelled','processing') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '交易状态',
  `currency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
  `amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '交易金额',
  `win_amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '赢取金额',
  `net_amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '净金额（输赢差值）',
  `balance_before` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '交易前余额',
  `balance_after` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '交易后余额',
  `provider_transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '提供商交易ID',
  `reference_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联交易ID',
  `round_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏轮次ID',
  `bet_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '投注ID',
  `commission_status` enum('unprocessed','processed','not_applicable') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unprocessed' COMMENT '佣金处理状态',
  `betting_bonus_status` enum('unprocessed','processed','not_applicable') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unprocessed' COMMENT '投注反水状态',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '交易描述',
  `error_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '错误代码',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `request_data` json DEFAULT NULL COMMENT '请求数据（JSON）',
  `response_data` json DEFAULT NULL COMMENT '响应数据（JSON）',
  `metadata` json DEFAULT NULL COMMENT '额外元数据',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_id` (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_provider_code` (`provider_code`),
  KEY `idx_game_code` (`game_code`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_processed_at` (`processed_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_user_provider` (`user_id`,`provider_code`),
  KEY `idx_provider_transaction_id` (`provider_transaction_id`),
  KEY `idx_reference_id` (`reference_id`),
  KEY `idx_round_id` (`round_id`),
  KEY `idx_user_date` (`user_id`,`created_at`),
  KEY `idx_provider_date` (`provider_code`,`created_at`),
  KEY `idx_amount_range` (`amount`,`type`),
  KEY `idx_user_status_date` (`user_id`,`status`,`created_at`),
  KEY `idx_provider_type_date` (`provider_code`,`type`,`created_at`),
  KEY `idx_session_type` (`session_id`,`type`),
  CONSTRAINT `fk_game_transactions_provider_code` FOREIGN KEY (`provider_code`) REFERENCES `game_providers` (`code`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_game_transactions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏交易记录表';

-- ----------------------------
-- Table structure for game_transactions
-- ----------------------------
DROP TABLE IF EXISTS `game_transactions`;
CREATE TABLE `game_transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `amount` decimal(20,4) NOT NULL COMMENT '投注金额',
  `symbol` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `game_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏类型',
  `game_result` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '游戏结果（win/lose/draw）',
  `commission_processed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '佣金是否已处理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_created` (`user_id`,`created_at`),
  KEY `idx_commission_processed` (`commission_processed`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏投注流水表';

-- ----------------------------
-- Table structure for images
-- ----------------------------
DROP TABLE IF EXISTS `images`;
CREATE TABLE `images` (
  `images_id` bigint NOT NULL AUTO_INCREMENT COMMENT '红包 ID (主键)',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片状态 pending_review, fail, success',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `refuse_reason_zh` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因 (中文)',
  `refuse_reason_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因 (英文)',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (Foreign key to users table recommended)',
  `images_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`images_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='红包信息表';

-- ----------------------------
-- Table structure for import_tasks
-- ----------------------------
DROP TABLE IF EXISTS `import_tasks`;
CREATE TABLE `import_tasks` (
  `task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `progress` double DEFAULT '0',
  `processed_rows` int DEFAULT '0',
  `total_rows` int DEFAULT '0',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- ----------------------------
-- Table structure for paybot_auth_orders
-- ----------------------------
DROP TABLE IF EXISTS `paybot_auth_orders`;
CREATE TABLE `paybot_auth_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `paybot_order_no` varchar(64) NOT NULL COMMENT 'PayBot系统订单号',
  `merchant_order_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `user_account` varchar(255) NOT NULL COMMENT '用户账户标识符（关联users.account）',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID（关联users.id）',
  `order_type` enum('add','deduct') NOT NULL COMMENT '订单类型：add-加款，deduct-扣款',
  `token_symbol` varchar(20) NOT NULL COMMENT '代币符号（如USDT）',
  `amount` decimal(36,18) NOT NULL COMMENT '交易金额',
  `auth_reason` text NOT NULL COMMENT '授权原因',
  `status` enum('pending','completed','expired','cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `callback_bot` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回调机器人',
  `callback_status` enum('pending','success','failed') NOT NULL DEFAULT 'pending' COMMENT '回调状态',
  `expire_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `error_message` text COMMENT '错误信息',
  `message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '内联消息 ID，用于后续编辑',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `notification_sent` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否已发送通知: 0-未发送, 1-已发送',
  `notification_sent_at` timestamp NULL DEFAULT NULL COMMENT '通知发送时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_paybot_order_no` (`paybot_order_no`),
  KEY `idx_user_account` (`user_account`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_merchant_order_no` (`merchant_order_no`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_created` (`status`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PayBot授权支付订单表';

-- ----------------------------
-- Table structure for paybot_callbacks
-- ----------------------------
DROP TABLE IF EXISTS `paybot_callbacks`;
CREATE TABLE `paybot_callbacks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型（deposit_confirmed/withdraw_completed等）',
  `order_no` varchar(64) NOT NULL COMMENT '关联的订单号',
  `related_table` varchar(50) DEFAULT NULL COMMENT '关联的表名',
  `related_id` bigint unsigned DEFAULT NULL COMMENT '关联表的记录ID',
  `merchant_id` bigint unsigned DEFAULT NULL COMMENT '商户ID',
  `payload` json NOT NULL COMMENT '回调数据（JSON格式）',
  `signature` varchar(255) DEFAULT NULL COMMENT '回调签名',
  `callback_time` timestamp NOT NULL COMMENT '回调接收时间',
  `processed_status` enum('pending','success','failed') NOT NULL DEFAULT 'pending' COMMENT '处理状态',
  `process_attempts` int NOT NULL DEFAULT '0' COMMENT '处理尝试次数',
  `error_message` text COMMENT '处理错误信息',
  `next_retry_at` timestamp NULL DEFAULT NULL COMMENT '下次重试时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_processed_status` (`processed_status`),
  KEY `idx_next_retry_at` (`next_retry_at`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_retry` (`processed_status`,`next_retry_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PayBot回调记录表';

-- ----------------------------
-- Table structure for payment_requests
-- ----------------------------
DROP TABLE IF EXISTS `payment_requests`;
CREATE TABLE `payment_requests` (
  `request_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '收款请求 ID (主键)',
  `requester_user_id` bigint unsigned NOT NULL COMMENT '收款发起者用户 ID (外键, 指向 users.user_id)',
  `requester_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收款发起者telegram username',
  `payer_user_id` bigint unsigned DEFAULT NULL COMMENT '指定付款人用户 ID (如果为空则任何人可付, 外键, 指向 users.user_id)',
  `payer_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指定付款人用户telegram username',
  `token_id` int unsigned NOT NULL COMMENT '收款代币 ID (外键, 指向 tokens.token_id)',
  `amount` decimal(36,6) NOT NULL COMMENT '收款金额',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收款说明/备注',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '请求状态: 1-待支付(pending), 2-已支付(paid), 3-已过期(expired), 4-已取消(cancelled)',
  `payment_transaction_id` bigint unsigned DEFAULT NULL COMMENT '关联的支付交易记录ID (外键, 指向 transactions.transaction_id)',
  `requester_transaction_id` bigint unsigned DEFAULT NULL COMMENT '关联的支付交易记录ID (外键, 指向 transactions.transaction_id)',
  `telegram_chat_id` bigint DEFAULT NULL COMMENT '发起请求的 Telegram 聊天 ID (用于更新消息)',
  `telegram_message_id` int DEFAULT NULL COMMENT '原始收款请求消息的 Telegram 消息 ID (用于更新消息)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间 (例如: 创建时间 + 24小时)',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `cancelled_at` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `inline_message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '内联消息 ID，用于后续编辑',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`request_id`) USING BTREE,
  KEY `idx_requester_user_id` (`requester_user_id`) USING BTREE,
  KEY `idx_payer_user_id` (`payer_user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_token_id` (`token_id`) USING BTREE,
  KEY `idx_expires_at` (`expires_at`) USING BTREE,
  KEY `idx_telegram_message` (`telegram_chat_id`,`telegram_message_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收款请求表 (v2 Optimized)';

-- Table structure for red_packet_claims
-- ----------------------------
DROP TABLE IF EXISTS `red_packet_claims`;
CREATE TABLE `red_packet_claims` (
  `claim_id` bigint NOT NULL AUTO_INCREMENT COMMENT '领取记录 ID (主键)',
  `red_packet_id` bigint NOT NULL COMMENT '红包 ID (外键, 指向 red_packets.red_packet_id)',
  `claimer_user_id` bigint NOT NULL DEFAULT '0' COMMENT '领取者用户 ID (外键, 指向 users.user_id)',
  `amount` decimal(20,8) NOT NULL COMMENT '领取金额',
  `transaction_id` bigint DEFAULT NULL COMMENT '关联的资金入账交易 ID (外键, 指向 transactions.transaction_id)',
  `claimed_at` timestamp NULL DEFAULT NULL COMMENT '领取时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `sender_user_id` bigint unsigned DEFAULT NULL COMMENT '发送方用户 ID (外键, 指向 users.user_id)',
  `receiver_user_id` bigint unsigned DEFAULT NULL COMMENT '接收方用户 ID (外键, 指向 users.user_id)',
  `sender_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送方用户名',
  `receiver_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '接收方用户名',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '红包状态 (pending: 待领取, claimed: 已领取, cancelled: 已取消)',
  `symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代币符号 (例如: USDT, BTC, ETH)',
  `notification_sent` tinyint unsigned DEFAULT '0' COMMENT '是否已发送通知: 0-未发送, 1-已发送',
  `notification_sent_at` timestamp NULL DEFAULT NULL COMMENT '通知发送时间',
  PRIMARY KEY (`claim_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='红包领取记录表';

-- ----------------------------
-- Table structure for red_packet_images
-- ----------------------------
DROP TABLE IF EXISTS `red_packet_images`;
CREATE TABLE `red_packet_images` (
  `red_packet_images_id` bigint NOT NULL AUTO_INCREMENT COMMENT '红包 ID (主键)',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片状态 pending_review, fail, success',
  `processing_status` enum('pending','processing','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '处理状态: pending-待处理, processing-处理中, completed-已完成,\n   failed-处理失败',
  `processing_attempts` int unsigned NOT NULL DEFAULT '0' COMMENT '处理尝试次数',
  `processing_started_at` timestamp NULL DEFAULT NULL COMMENT '开始处理时间',
  `processing_completed_at` timestamp NULL DEFAULT NULL COMMENT '处理完成时间',
  `processing_error` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '处理错误信息',
  `original_file_size` bigint unsigned DEFAULT NULL COMMENT '原始文件大小(字节)',
  `processed_file_size` bigint unsigned DEFAULT NULL COMMENT '处理后文件大小(字节)',
  `image_width` int unsigned DEFAULT NULL COMMENT '图片宽度',
  `image_height` int unsigned DEFAULT NULL COMMENT '图片高度',
  `image_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片格式(jpeg, png等)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `refuse_reason_zh` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因 (中文)',
  `refuse_reason_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因 (英文)',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (Foreign key to users table recommended)',
  `images_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notification_sent` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否已发送通知: 0-未发送, 1-已发送',
  `notification_sent_at` timestamp NULL DEFAULT NULL COMMENT '通知发送时间',
  PRIMARY KEY (`red_packet_images_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_processing_status_created` (`processing_status`,`created_at`) USING BTREE,
  KEY `idx_processing_attempts_status` (`processing_attempts`,`processing_status`) USING BTREE,
  KEY `idx_user_processing_status` (`user_id`,`processing_status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='红包信息表';

-- ----------------------------
-- Table structure for red_packets
-- ----------------------------
DROP TABLE IF EXISTS `red_packets`;
CREATE TABLE `red_packets` (
  `red_packet_id` bigint NOT NULL AUTO_INCREMENT COMMENT '红包 ID (主键)',
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '红包唯一id',
  `red_packet_images_id` bigint NOT NULL COMMENT '图片id 外键id 指向red_packet_images.red_packet_images_id',
  `creator_user_id` bigint NOT NULL COMMENT '创建者用户 ID (外键, 指向 users.user_id)',
  `creator_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建者用户 ID (外键, 指向 users.user_id)',
  `cover_file_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建者用户 ID (外键, 指向 users.user_id)',
  `thumb_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建者用户 ID (外键, 指向 users.user_id)',
  `token_id` int NOT NULL COMMENT '红包代币 ID (外键, 指向 tokens.token_id)',
  `total_amount` decimal(36,4) NOT NULL COMMENT '红包总金额',
  `quantity` int NOT NULL COMMENT '红包总个数',
  `remaining_amount` decimal(36,4) NOT NULL COMMENT '剩余金额',
  `remaining_quantity` int NOT NULL COMMENT '剩余个数',
  `type` enum('random','fixed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '红包类型: random-随机金额, fixed-固定金额',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '红包留言',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `sender_user_id` bigint unsigned NOT NULL COMMENT '发送方用户 ID (外键, 指向 users.user_id)',
  `symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代币符号 (例如: USDT, BTC, ETH)',
  `transaction_id` bigint unsigned DEFAULT NULL COMMENT '关联的扣款交易流水 ID (外键, 指向 transactions.id)',
  `is_premium` int DEFAULT '0' COMMENT '是否需要会员',
  `message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '内联消息 ID，用于后续编辑',
  PRIMARY KEY (`red_packet_id`) USING BTREE,
  KEY `idx_creator_user_id` (`red_packet_images_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_expires_at` (`expires_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='红包信息表';

-- ----------------------------
-- Table structure for red_packets_infors
-- ----------------------------
DROP TABLE IF EXISTS `red_packets_infors`;
CREATE TABLE `red_packets_infors` (
  `red_packet_id` bigint NOT NULL AUTO_INCREMENT COMMENT '红包 ID (主键)',
  `nfor_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '红包唯一id',
  `inline_message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '红包唯一id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`red_packet_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='红包信息表';

-- ----------------------------
-- Table structure for referral_commissions
-- ----------------------------
DROP TABLE IF EXISTS `referral_commissions`;
CREATE TABLE `referral_commissions` (
  `commission_id` bigint NOT NULL AUTO_INCREMENT COMMENT '佣金记录 ID (主键)',
  `transaction_id` bigint DEFAULT NULL COMMENT '关联的触发佣金的交易 ID (例如: 下级的某笔操作, 外键指向 transactions.transaction_id)',
  `referrer_id` bigint NOT NULL COMMENT '获得佣金的用户 ID (外键, 指向 users.user_id)',
  `invitee_id` bigint NOT NULL COMMENT '产生佣金的被推荐人用户 ID (外键, 指向 users.user_id)',
  `level` int NOT NULL COMMENT '佣金产生的推荐层级',
  `commission_amount` decimal(36,18) NOT NULL COMMENT '佣金金额',
  `commission_rate` decimal(5,4) NOT NULL COMMENT '佣金比率 (例如: 0.01 表示 1%)',
  `token_id` int NOT NULL COMMENT '佣金代币 ID (外键, 指向 tokens.token_id)',
  `status` enum('pending','paid','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '佣金状态: pending-待发放, paid-已发放, cancelled-已取消',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`commission_id`) USING BTREE,
  KEY `idx_referrer_invitee` (`referrer_id`,`invitee_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐佣金记录表';

-- ----------------------------
-- Table structure for referral_relationships
-- ----------------------------
DROP TABLE IF EXISTS `referral_relationships`;
CREATE TABLE `referral_relationships` (
  `type` enum('ag','') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '上级类型 代理，用户',
  `relationship_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关系 ID (主键)',
  `user_id` bigint NOT NULL COMMENT '被推荐人用户 ID (外键, 指向 users.user_id)',
  `referrer_id` bigint NOT NULL COMMENT '推荐人用户 ID (外键, 指向 users.user_id)',
  `level` int NOT NULL COMMENT '推荐层级 (1 表示直接推荐, 2 表示间接推荐, 以此类推)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关系创建时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  PRIMARY KEY (`relationship_id`) USING BTREE,
  UNIQUE KEY `uq_user_level` (`user_id`,`level`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_referrer_id` (`referrer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户推荐关系表';

-- ----------------------------
-- Table structure for remote_operation_intents
-- ----------------------------
DROP TABLE IF EXISTS `remote_operation_intents`;
CREATE TABLE `remote_operation_intents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `transaction_id` bigint unsigned NOT NULL COMMENT '关联的交易记录ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `token_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '代币符号',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型: credit, debit',
  `amount` decimal(65,18) NOT NULL COMMENT '操作金额',
  `new_balance` decimal(65,18) NOT NULL COMMENT '操作后的新余额',
  `business_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务ID',
  `metadata` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '元数据JSON',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pending' COMMENT '状态: pending, processing, completed, failed',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `last_error` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '最后一次错误信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `processed_at` datetime DEFAULT NULL COMMENT '处理完成时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_transaction_id` (`transaction_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_business_id` (`business_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='远程操作意图表';


-- ----------------------------
-- Table structure for transactions
-- ----------------------------
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `transaction_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '交易记录 ID (主键)',
  `user_id` int unsigned NOT NULL COMMENT '关联用户 ID',
  `username` int unsigned NOT NULL COMMENT 'telegra username',
  `token_id` int unsigned NOT NULL COMMENT '关联代币 ID',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易类型: deposit, withdrawal, transfer, red_packet, payment, commission, system_adjust, etc.',
  `wallet_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'available' COMMENT '钱包类型:  冻结frozen ，余额 available',
  `direction` enum('in','out') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资金方向: in-增加, out-减少',
  `amount` decimal(36,8) NOT NULL COMMENT '交易金额 (绝对值)',
  `balance_before` decimal(36,8) NOT NULL COMMENT '交易前余额快照 (对应钱包)',
  `balance_after` decimal(36,8) NOT NULL COMMENT '交易后余额快照 (对应钱包)',
  `related_transaction_id` bigint unsigned DEFAULT NULL COMMENT '关联交易 ID (例如: 转账的对方记录)',
  `related_entity_id` bigint unsigned DEFAULT NULL COMMENT '关联实体 ID (例如: 红包 ID, 提现订单 ID)',
  `related_entity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联实体类型 (例如: red_packet, withdrawal_order)',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '交易状态: 1-成功, 0-失败',
  `memo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '交易备注/消息 (例如: 管理员调账原因)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  `symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代币符号 (例如: USDT, BTC, ETH)',
  `business_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务唯一标识符，用于幂等性检查',
  `request_amount` decimal(36,8) DEFAULT NULL COMMENT '用户请求的原始金额 (用户输入的金额)',
  `request_reference` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户请求的参考信息 (如转账备注、提现地址等)',
  `request_metadata` json DEFAULT NULL COMMENT '用户请求的元数据 (JSON格式存储扩展信息)',
  `request_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求来源 (telegram, web, api, admin等)',
  `request_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户请求的IP地址',
  `request_user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户请求的User-Agent',
  `request_timestamp` timestamp NULL DEFAULT NULL COMMENT '用户发起请求的时间戳',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '交易处理完成时间',
  `fee_amount` decimal(36,8) DEFAULT '0.00000000' COMMENT '手续费金额',
  `fee_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手续费类型 (fixed, percentage)',
  `exchange_rate` decimal(36,8) DEFAULT NULL COMMENT '汇率 (如果涉及币种转换)',
  `target_user_id` int unsigned DEFAULT NULL COMMENT '目标用户ID (转账、红包等操作的接收方)',
  `target_username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标用户名 (转账、红包等操作的接收方用户名)',
  `dtm_gid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'DTM全局事务ID',
  `dtm_branch_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'DTM分支ID',
  PRIMARY KEY (`transaction_id`) USING BTREE,
  KEY `idx_user_token_type` (`user_id`,`token_id`,`type`) USING BTREE,
  KEY `idx_user_token_direction` (`user_id`,`token_id`,`direction`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_related_entity` (`related_entity_id`,`related_entity_type`) USING BTREE,
  KEY `idx_dtm_gid` (`dtm_gid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资金交易记录表 (v2)';

-- ----------------------------
-- Table structure for transfers
-- ----------------------------
DROP TABLE IF EXISTS `transfers`;
CREATE TABLE `transfers` (
  `transfer_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '转账记录 ID (主键)',
  `message_id` bigint NOT NULL COMMENT '转账记录 ID (主键)',
  `chat_id` bigint NOT NULL COMMENT '转账记录 ID (主键)',
  `sender_user_id` bigint unsigned NOT NULL COMMENT '发送方用户 ID (外键, 指向 users.user_id)',
  `receiver_user_id` bigint unsigned NOT NULL COMMENT '接收方用户 ID (外键, 指向 users.user_id)',
  `token_id` int unsigned NOT NULL COMMENT '代币 ID (外键, 指向 tokens.token_id)',
  `amount` bigint unsigned NOT NULL COMMENT '转账金额 (最小单位)',
  `sender_transaction_id` bigint unsigned DEFAULT NULL COMMENT '关联的发送方资金扣除交易 ID (外键, 指向 transactions.transaction_id)',
  `receiver_transaction_id` bigint unsigned DEFAULT NULL COMMENT '关联的接收方资金增加交易 ID (外键, 指向 transactions.transaction_id)',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '转账备注',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态 (pending_pass, pending_collection, completed, expired)',
  `hold_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '钱包服务返回的冻结 ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '转账发起时间 (记录创建时间)',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间 (created_at + 24 小时)',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  `need_pass` tinyint DEFAULT NULL COMMENT '是否需要支付密码',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `inline_message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '内联消息 ID，用于后续编辑',
  `sender_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发送方用户名',
  `receiver_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接收方用户名',
  `notification_sent` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否已发送通知: 0-未发送, 1-已发送',
  `notification_sent_at` timestamp NULL DEFAULT NULL COMMENT '通知发送时间',
  PRIMARY KEY (`transfer_id`) USING BTREE,
  KEY `idx_sender_user_token` (`sender_user_id`,`token_id`,`created_at`) USING BTREE,
  KEY `idx_receiver_user_token` (`receiver_user_id`,`token_id`,`created_at`) USING BTREE,
  KEY `idx_token_id` (`token_id`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_sender_transaction` (`sender_transaction_id`) USING BTREE,
  KEY `idx_receiver_transaction` (`receiver_transaction_id`) USING BTREE,
  KEY `idx_status_expires_at` (`status`,`expires_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='转账记录表';

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address` (
  `user_address_id` int unsigned NOT NULL AUTO_INCREMENT,
  `token_id` int unsigned NOT NULL DEFAULT '0' COMMENT '币种ID',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户id',
  `lable` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备注',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '币种',
  `chan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二维码url',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `paybot_address_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'PayBot系统中的地址ID',
  `is_reused` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否复用地址：0-新生成，1-复用',
  `qr_code_data` text COLLATE utf8mb4_unicode_ci COMMENT '二维码数据（Base64格式）',
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '地址状态',
  `deposit_count` int NOT NULL DEFAULT '0' COMMENT '充值次数统计',
  `last_deposit_at` timestamp NULL DEFAULT NULL COMMENT '最后一次充值时间',
  PRIMARY KEY (`user_address_id`) USING BTREE,
  UNIQUE KEY `uk_u_c_a` (`user_address_id`,`user_id`,`name`,`chan`,`address`) USING BTREE,
  KEY `idx_paybot_address_id` (`paybot_address_id`),
  KEY `idx_user_chain_token` (`user_id`,`chan`,`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for user_ass
-- ----------------------------
DROP TABLE IF EXISTS `user_ass`;
CREATE TABLE `user_ass` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '备用账户主键ID',
  `a_user_id` bigint NOT NULL COMMENT '主账户',
  `b_user_id` bigint NOT NULL COMMENT '备账户',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '验证时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户备用账户信息表';

-- ----------------------------
-- Table structure for user_backup_accounts
-- ----------------------------
DROP TABLE IF EXISTS `user_backup_accounts`;
CREATE TABLE `user_backup_accounts` (
  `backup_account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '备用账户主键ID',
  `telegram_id` bigint NOT NULL,
  `chat_id` bigint DEFAULT NULL,
  `telegram_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL COMMENT '关联的主用户ID',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '验证时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `is_master` tinyint DEFAULT '0',
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`backup_account_id`) USING BTREE,
  UNIQUE KEY `ik_telegram_account` (`telegram_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户备用账户信息表';

-- ----------------------------
-- Table structure for user_pass_frees
-- ----------------------------
DROP TABLE IF EXISTS `user_pass_frees`;
CREATE TABLE `user_pass_frees` (
  `user_pass_frees_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (Foreign key to users table recommended)',
  `symbol` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '币种名称',
  `amount` decimal(36,4) NOT NULL DEFAULT '0.0000' COMMENT '免密金额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`user_pass_frees_id`) USING BTREE,
  UNIQUE KEY `uk_t_u` (`user_id`,`symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户免密额度表';

-- ----------------------------
-- Table structure for user_recharges
-- ----------------------------
DROP TABLE IF EXISTS `user_recharges`;
CREATE TABLE `user_recharges` (
  `user_recharges_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (Foreign key to users table recommended)',
  `token_id` int unsigned NOT NULL DEFAULT '0' COMMENT '币种ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '币种ID',
  `chan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rechange_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '充值类型auth_pay address_pay',
  `token_contract_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代币合约地址 (for non-native assets)',
  `from_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '来源地址 (发送方地址)',
  `to_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标地址 (平台分配的充值地址)',
  `tx_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链上交易哈希/ID (Should be unique)',
  `error` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '失败原因',
  `amount` decimal(36,6) NOT NULL COMMENT '充值数量',
  `state` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)',
  `failure_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因',
  `confirmations` int unsigned NOT NULL DEFAULT '1' COMMENT '状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间 (e.g., 链上发现时间)',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间 (状态变为Completed的时间)',
  `notification_sent` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否已发送通知: 0-未发送, 1-已发送',
  `notification_sent_at` timestamp NULL DEFAULT NULL COMMENT '通知发送时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`user_recharges_id`) USING BTREE,
  UNIQUE KEY `uk_tx_hash` (`tx_hash`) USING BTREE,
  KEY `idx_uid_coin_state` (`user_id`,`token_id`,`state`) USING BTREE,
  KEY `idx_to_address_coin_network` (`to_address`,`token_id`,`chan`) USING BTREE,
  KEY `idx_state_created` (`state`,`created_at`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_state` (`state`) USING BTREE,
  KEY `idx_notification_status` (`state`,`notification_sent`,`completed_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户充值记录表';

-- ----------------------------
-- Table structure for user_withdraws
-- ----------------------------
DROP TABLE IF EXISTS `user_withdraws`;
CREATE TABLE `user_withdraws` (
  `user_withdraws_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (Foreign key to users table recommended)',
  `token_id` int unsigned NOT NULL DEFAULT '0' COMMENT '币种ID',
  `wallet_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户钱包ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '币种ID',
  `withdraw_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '充值类型auth_pay address_pay',
  `chan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提现订单号 (Should be unique)',
  `paybot_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'PayBot系统订单号',
  `paybot_sync_status` enum('pending','synced','failed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT 'PayBot同步状态',
  `paybot_sync_at` timestamp NULL DEFAULT NULL COMMENT 'PayBot同步时间',
  `paybot_error_message` text COLLATE utf8mb4_unicode_ci COMMENT 'PayBot同步错误信息',
  `address` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提币目标地址',
  `recipient_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '法币收款人姓名',
  `recipient_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '法币收款账户',
  `recipient_qrcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '法币收款账户',
  `amount` decimal(36,6) NOT NULL COMMENT '申请提现金额',
  `handling_fee` decimal(36,6) DEFAULT '0.000000' COMMENT '提现手续费',
  `actual_amount` decimal(36,6) DEFAULT NULL COMMENT '实际到账金额',
  `state` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)',
  `refuse_reason_zh` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因 (中文)',
  `refuse_reason_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因 (英文)',
  `tx_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链上交易哈希/ID',
  `error_message` json DEFAULT NULL COMMENT '失败或错误信息',
  `user_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户提现备注',
  `admin_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员审核备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `checked_at` timestamp NULL DEFAULT NULL COMMENT '审核时间 (审核通过或拒绝的时间)',
  `processing_at` timestamp NULL DEFAULT NULL COMMENT '开始处理时间 (进入“处理中”状态的时间)',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间 (变为“已完成”或“失败”状态的时间)',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `retries` int DEFAULT '0',
  `nergy_state` int DEFAULT '0' COMMENT '0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了',
  `notification_sent` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否已发送通知: 0-未发送, 1-已发送',
  `notification_sent_at` timestamp NULL DEFAULT NULL COMMENT '通知发送时间',
  `fiat_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '法币提现类型 alipay_account 支付宝账号  alipay_qr 支付宝二维码  wechat_qr 微信二维码',
  `confirmation_message_id` bigint DEFAULT NULL COMMENT 'Telegram message \n  ID of the withdrawal confirmation message',
  `confirmation_chat_id` bigint DEFAULT NULL COMMENT 'Telegram chat ID \n  where confirmation was sent',
  PRIMARY KEY (`user_withdraws_id`) USING BTREE,
  UNIQUE KEY `uk_order_no` (`order_no`) USING BTREE,
  KEY `idx_approved_notification` (`state`,`notification_sent`,`completed_at`) USING BTREE,
  KEY `idx_rejected_notification` (`state`,`notification_sent`,`checked_at`) USING BTREE,
  KEY `idx_paybot_order_no` (`paybot_order_no`),
  KEY `idx_paybot_sync_status` (`paybot_sync_status`),
  KEY `idx_user_withdraws_message_id` (`confirmation_message_id`),
  KEY `idx_user_withdraws_chat_id` (`confirmation_chat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户提现记录表';

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户登录密码（经过哈希处理）',
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '唯一用户账户标识符（例如，用于登录）',
  `account_type` int NOT NULL DEFAULT '1' COMMENT '账户类型 1 用户 2商户 3 代理',
  `invite_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户唯一邀请码（用于分享给其他人）',
  `area_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '86' COMMENT '电话区号',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户电话号码',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户头像URL或路径',
  `payment_password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付密码（经过哈希处理，未设置时为NULL）',
  `recommend_id` bigint unsigned DEFAULT NULL COMMENT '推荐该用户的用户ID（关联users.id）',
  `deep` int NOT NULL DEFAULT '0' COMMENT '推荐结构中的层级深度',
  `recommend_relationship` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '表示客户推荐层级关系的路径（例如，/1/5/10/）',
  `agent_relationship` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '表示代理推荐层级关系的路径',
  `first_id` int unsigned DEFAULT NULL COMMENT '关联的代理一级ID',
  `second_id` int unsigned DEFAULT NULL COMMENT '关联的代理二级ID',
  `third_id` int unsigned DEFAULT NULL COMMENT '关联的代理三级ID',
  `is_stop` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户账户暂停状态：0=活跃，1=已暂停',
  `current_token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后使用的认证令牌（例如，API令牌）',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后一次成功登录的时间戳',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '暂停或其他状态变更的原因',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `google2fa_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '谷歌2fa密钥',
  `google2fa_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '谷歌2fa是否启用',
  `is_payment_password` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启免密支付',
  `payment_password_amount` decimal(15,4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '免密支付额度',
  `backup_accounts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备用账户',
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `red_packet_permission` tinyint(1) NOT NULL DEFAULT '1' COMMENT '红包权限',
  `transfer_permission` tinyint(1) NOT NULL DEFAULT '1' COMMENT '转账权限',
  `withdraw_permission` tinyint(1) NOT NULL DEFAULT '1' COMMENT '提现权限',
  `flash_trade_permission` tinyint(1) NOT NULL DEFAULT '1' COMMENT '闪兑权限',
  `recharge_permission` tinyint(1) NOT NULL DEFAULT '1' COMMENT '充值权限',
  `receive_permission` tinyint(1) NOT NULL DEFAULT '1' COMMENT '收款权限',
  `reset_payment_password_permission` tinyint(1) NOT NULL DEFAULT '0' COMMENT '重置支付密码权限：0=无权限，1=有权限',
  `main_wallet_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '钱包id',
  `direct_invites_count` int DEFAULT '0' COMMENT '直接邀请人数',
  `indirect_invites_count` int DEFAULT '0' COMMENT '间接邀请人数',
  `total_invites_count` int DEFAULT '0' COMMENT '总邀请人数',
  `first_invite_at` timestamp NULL DEFAULT NULL COMMENT '首次邀请时间',
  `last_invite_at` timestamp NULL DEFAULT NULL COMMENT '最近邀请时间',
  `total_deposit_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '用户总存款金额',
  `total_withdraw_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '用户总成功取款金额',
  `total_betting_volume` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '用户总投注流水',
  `total_commission_generated` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '为推荐人产生的总佣金',
  `deposit_reward_multiplier` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '存款奖励倍数选择',
  `withdraw_betting_volume` decimal(20,2) unsigned DEFAULT '0.00' COMMENT '用户提现需要的流水金额',
  `locked_reward_amount` decimal(20,2) DEFAULT NULL COMMENT '锁定的奖励金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_agent_relationship` (`agent_relationship`(50)) USING BTREE,
  KEY `idx_deleted_at_agent_rel` (`deleted_at`,`agent_relationship`(50)) USING BTREE,
  KEY `idx_recommend_id` (`recommend_id`),
  KEY `idx_recommend_relationship` (`recommend_relationship`(50)),
  KEY `idx_recommend_commission` (`recommend_id`,`total_commission_generated`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Table structure for wallets
-- ----------------------------
DROP TABLE IF EXISTS `wallets`;
CREATE TABLE `wallets` (
  `wallet_id` int NOT NULL AUTO_INCREMENT COMMENT '钱包记录唯一 ID',
  `ledger_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ledger 钱包id',
  `user_id` int unsigned NOT NULL COMMENT '用户 ID (外键, 关联 users.user_id)',
  `token_id` int unsigned NOT NULL COMMENT '用户 ID (外键, 关联 users.user_id)',
  `available_balance` bigint NOT NULL DEFAULT '0' COMMENT '可用余额',
  `frozen_balance` bigint NOT NULL DEFAULT '0' COMMENT '冻结余额 (例如: 挂单中, 提现处理中)',
  `decimal_places` smallint unsigned NOT NULL DEFAULT '2' COMMENT '精度',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '钱包记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '余额最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除的时间戳',
  `telegram_id` bigint NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型',
  `symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代币符号 (例如: USDT, BTC, ETH)',
  PRIMARY KEY (`wallet_id`) USING BTREE,
  UNIQUE KEY `idx_wallet_id` (`wallet_id`) USING BTREE,
  UNIQUE KEY `uk_u_t` (`user_id`,`token_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户钱包余额表';


SET FOREIGN_KEY_CHECKS = 1;
