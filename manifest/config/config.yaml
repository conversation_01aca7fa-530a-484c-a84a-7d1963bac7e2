# https://goframe.org/docs/web/server-config-file-template
server:
  address: ":7999" # Webhook 监听地址 (如果使用)
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  logPath: "logs" # Gateway 日志路径 (从规划添加)
  dumpRouterMap: false

# 开发模式切换 (从规划添加)
dev_mode: true # true: Polling, false: Webhook

# GoFrame i18n 配置 (https://goframe.org/pages/viewpage.action?pageId=1114250)
i18n:
  path: "manifest/i18n"
  language: "zh-CN"  # 默认语言

# https://goframe.org/docs/core/gdb-config-file
database:
  logger:
    path: "logs/database" # 日志文件路径。默认为空，表示关闭，仅输出到终端
    level: "all"
    stdout: true
  default: # 使用 MySQL
    link: "mysql:root:root@tcp(127.0.0.1:3306)/game?loc=Local&parseTime=true&charset=utf8mb4" # 确认或修改为您的 MySQL 连接信息
    debug: true # 开发环境建议开启

redis: # (从现有配置确认并从规划补充)
  default:
    address: "127.0.0.1:6379" # 确认或修改为您的 Redis 地址
    db: 0 # 使用默认数据库以确保 xpay-config 兼容性
    pass: "valkey_password" # 确认或修改密码
    idleTimeout: 20s # 补充单位 (从规划建议)



# https://goframe.org/docs/core/glog-config
logger: # (从现有配置确认并从规划补充)
  path: "logs" # 统一日志目录，各应用可分子目录 (从规划添加)
  level: "all"
  stdout: true
  rotateSize: "100M" # (从规划添加)
  rotateExpire: "7d" # (从规划添加)
  format: "json" # 使用 JSON 格式方便收集 (从规划添加)

# walletsApi:
#   baseUrl: "http://127.0.0.1:8080" # 替换为 Wallet API 的实际 Base URL

certificate: |
  -----BEGIN CERTIFICATE-----
  MIIE3TCCAsWgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCgxDjAMBgNVBAoTBWFk
  bWluMRYwFAYDVQQDEw1jZXJ0LWJ1aWx0LWluMB4XDTI1MDUzMDA5MjkwNloXDTQ1
  MDUzMDA5MjkwNlowKDEOMAwGA1UEChMFYWRtaW4xFjAUBgNVBAMTDWNlcnQtYnVp
  bHQtaW4wggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDbUQw5MjDVUhPU
  kqWH/l7S+XJfNgUCHL65Dul3BsJjD1bVOcjPD09XvEalfVcShZVNPXdojm0V2wcL
  cNDMgG7AlALss2/t/dt9Jd/SFZBmMlM+im99Ubi717ZSfCbews1eV4ItZLidkCUz
  +vW39uOnmn3Y816Bz816fPcZQNP0HkI9i1mx6pYLyfgqP6Pp3r2uy5E93JP0IZga
  Z8oiY9iWjWvLJ7/oCzqUtCu0GWjlXS5sE23dnHpcU41r2nSoIELI/kx+gigQ9mHN
  I7eynjZS+cG0Kr+PVpHkbIjHCmuWxNzKUW8kWgWsiEfLsL6sGHXh2K+d4AAf7Nnh
  JiC5h06lQtilBPSV7UPF8wzmkRtTg8N7GZZG2GLRJ5FTQTkklEJG68UIqS64XN5z
  DzliI7wX7bAVs/ShFNeDYtFmGuY+xZ0u0qCZm5dTQB+UyGSXT+xdQD4nTWCt2O8i
  xvzmPdlavM8e84HY4fmAAzstov/szT130Kfk+JBm3K34jl9gZ7Cxgq5oC4YXq0JD
  NZaj092Gsmg1iWyjt1cMtL7r+JwL0UG0IK49M2NjfvBGoc2V5ede0seui8Xi9jMg
  Hav9qraIxKVJqs/2Cr/1IHMsSwTNXlX3UnzMaDUUQJGggX2q2ucsn6gsyccmmaA4
  ctFN3ad0u0yJLUvytu8KrQbR6K2hzwIDAQABoxAwDjAMBgNVHRMBAf8EAjAAMA0G
  CSqGSIb3DQEBCwUAA4ICAQC2gC5QgWo22b5k8grINiUL3uXuCztB5wZB5dFIjIsi
  KrgtHsEEBh666UKFCW2FahudhbQAn5Ng2BWAZfBsG3iA8vx2Xv5NStTeDnDN/V8c
  td5O7QqgmALUvEMsO0gEfLLzJncASdmwLFHHg/zLYG/VCRdsK+ibcHcmmB0sVYVX
  ugtKlEQo1Pf8NRpGHG1X6YOmzpW3wk49mSuQrOogiFen82A141D/3H1PFXx/eZA3
  mPFG6/R8BApzs6pgipsI6J4dL50rP+5qqTflSOYohcbxMJ2MnsonGQUlvHm4rasj
  kekZJ2m1xSRT8CdZWr/jut/HbIO/UPwX8Z2CEw60B73YWAlCZbM/gVTPtucsJ5aq
  rfQMYaRY4wD2VAxKD47D96X1NBXCEkkE69fraeSdzL9m7UzdSdqt1SbBTOQvVi7j
  eWTPkMzmx61YJFiGCwEErOIYLRNlCSUKGGwZAna+9hKJkTHgDw0FZ0xS+S8EcnTp
  lirU8tq7acXCHKz5JQ7f7+0KMs7j3zHi7oaWVnwksF8RGl/DFBWdRn2iqR7oGZH5
  uCYNELUAAB7HU5nZzS5tvUbS+dOptLPOKhR39NORoqOWdMN15KY32jVsaugm+0mN
  9iMWOULg2VohsUj4Sa+2CNG3DyXTzkQIi4CsQJ/uMiRadwNtZhLwjX9Q8PCNw84i
  6w==
  -----END CERTIFICATE-----
casdoor_server:
  endpoint: "https://sso-dev.jjpay.co"
  client_id: "80756163da773c15fa03"
  client_secret: "0f319ddd842a7bdb25834aa85b6268f43f5ff686"
  organization: "organization_game"
  owner: "organization_game"
  application: "application_game"
  frontend_url: "https://admin-game-sit.jjpay.co"
  user: "organization_game/game"
  role: "organization_game/role_game"
  model: "model_srgv6d"

# agent_casdoor_server:
#   endpoint: "http://127.0.0.1:8000"
#   client_id: "c4994b2c1e44f27ce1b5"
#   client_secret: "383d7198c217e4924765cbcb8d586980a321f7bc"
#   organization: "organization_agent"
#   owner: "organization_agent"
#   application: "application_agent"
#   frontend_url: "http://127.0.0.1"
#   user: "organization_agent/agent"
#   role: "organization_agent/role_agent"
#   model: "model_srgv6d"
#   default_avatar: "https://cdn.casbin.org/img/casbin.svg"

# merchant_casdoor_server:
#   endpoint: "http://127.0.0.1:8000"
#   client_id: "f0a8b6891ae4f6d2abff"
#   client_secret: "9557c5f346bf7d37e0e611dd76e333cf4605a17b"
#   organization: "organization_merchant"
#   owner: "organization_merchant"
#   application: "application_merchant"
#   frontend_url: "http://127.0.0.1"
#   user: "organization_merchant/merchant"
#   role: "organization_merchant/role_merchant"
#   model: "model_srgv6d"
#   default_avatar: "https://cdn.casbin.org/img/casbin.svg"

# Consul 配置（用于配置同步）
consul:
  address: "127.0.0.1:8500" # Consul 服务器地址
  token: "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874" # ACL Token（生产环境请修改）
  config_prefix: "xpay/config" # 配置存储前缀

# CORS 跨域配置
cors:
  # 允许的源配置（二选一）：
  # 1. 使用通配符（开发环境）
  allowOrigin: "*" # 允许所有域名，注意：使用 * 时 allowCredentials 必须为 false
  
  # 2. 使用域名列表（生产环境推荐）
  # allowDomain: "https://admin-dev.jjpay.co,https://admin.jjpay.co,http://localhost:3000,http://localhost:8080" # 具体域名列表，逗号分隔
  
  # 凭据配置
  allowCredentials: "true" # 是否允许携带凭据（cookies, authorization headers等）
  # 注意：当 allowCredentials 为 true 时，allowOrigin 不能为 *，中间件会自动处理
  
  # 响应头配置
  exposeHeaders: "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type" # 暴露给前端的响应头
  
  # 预检请求配置
  maxAge: 86400 # OPTIONS 预检请求的缓存时间（秒），24小时
  
  # 允许的 HTTP 方法
  allowMethods: "GET, POST, PUT, DELETE, OPTIONS, PATCH"
  
  # 允许的请求头
  allowHeaders: "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Token"


storage:
  provider: "s3" # 指定存储提供商: "minio" 或 "s3"
  # minio:
  #   endpoint: "*************:9000" # 移除 http:// 前缀，让库自己处理
  #   accessKeyID: "minio_Ri46Yi" # Access Key ID
  #   secretAccessKey: "minio_2cbfCm" # Secret Access Key
  #   bucketName: "xpay-uploads" # Bucket 名称
  #   useSSL: false # 是否使用 HTTPS (true/false)
  #   publicURLPrefix: "https://image.cexyespro.com/xpay-uploads/" # 用于构建文件访问 URL 的前缀，确保末尾有斜杠 /
  # AWS S3 配置
  s3:
    accessKeyID: "********************" # AWS Access Key ID
    secretAccessKey: "jRlPC3qDWMNvW7IX8MThWEalYSxeLupBcUKZCQHO" # AWS Secret Access Key
    region: "ap-northeast-1" # AWS Region
    bucketName: "yescex1" # AWS Bucket Name
    usePathStyleEndpoint: false # Whether to use path style endpoint
    publicURLPrefix: "" # (可选) 用于构建文件访问 URL 的前缀，如果为空，则使用 S3 默认 URL 格式
