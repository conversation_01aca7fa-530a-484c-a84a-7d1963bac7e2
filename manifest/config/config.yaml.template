server:
  address: "${GAME_ADMIN_API_SERVER_ADDRESS}"
  openapiPath: "${GAME_ADMIN_API_SERVER_OPENAPIPATH}"
  swaggerPath: "${GAME_ADMIN_API_SERVER_SWAGGERPATH}"
  logPath: "${GAME_ADMIN_API_SERVER_LOGPATH}"
  dumpRouterMap: ${GAME_ADMIN_API_SERVER_DUMPROUTERMAP}
dev_mode: ${GAME_ADMIN_API_DEV_MODE}
i18n:
  path: "${GAME_ADMIN_API_I18N_PATH}"
  language: "${GAME_ADMIN_API_I18N_LANGUAGE}"
database:
  logger:
    path: "${GAME_ADMIN_API_DATABASE_LOGGER_PATH}"
    level: "${GAME_ADMIN_API_DATABASE_LOGGER_LEVEL}"
    stdout: ${GAME_ADMIN_API_DATABASE_LOGGER_STDOUT}
  default:
    link: "${GAME_ADMIN_API_DATABASE_DEFAULT_LINK}"
    debug: ${GAME_ADMIN_API_DATABASE_DEFAULT_DEBUG}
redis:
  default:
    address: "${GAME_ADMIN_API_REDIS_DEFAULT_ADDRESS}"
    db: ${GAME_ADMIN_API_REDIS_DEFAULT_DB}
    pass: "${GAME_ADMIN_API_REDIS_DEFAULT_PASS}"
    idleTimeout: "${GAME_ADMIN_API_REDIS_DEFAULT_IDLETIMEOUT}"
logger:
  path: "${GAME_ADMIN_API_LOGGER_PATH}"
  level: "${GAME_ADMIN_API_LOGGER_LEVEL}"
  stdout: ${GAME_ADMIN_API_LOGGER_STDOUT}
  rotateSize: "${GAME_ADMIN_API_LOGGER_ROTATESIZE}"
  rotateExpire: "${GAME_ADMIN_API_LOGGER_ROTATEEXPIRE}"
  format: "${GAME_ADMIN_API_LOGGER_FORMAT}"
certificate: |
  -----BEGIN CERTIFICATE-----
  MIIE3TCCAsWgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCgxDjAMBgNVBAoTBWFk
  bWluMRYwFAYDVQQDEw1jZXJ0LWJ1aWx0LWluMB4XDTI1MDUzMDA5MjkwNloXDTQ1
  MDUzMDA5MjkwNlowKDEOMAwGA1UEChMFYWRtaW4xFjAUBgNVBAMTDWNlcnQtYnVp
  bHQtaW4wggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDbUQw5MjDVUhPU
  kqWH/l7S+XJfNgUCHL65Dul3BsJjD1bVOcjPD09XvEalfVcShZVNPXdojm0V2wcL
  cNDMgG7AlALss2/t/dt9Jd/SFZBmMlM+im99Ubi717ZSfCbews1eV4ItZLidkCUz
  +vW39uOnmn3Y816Bz816fPcZQNP0HkI9i1mx6pYLyfgqP6Pp3r2uy5E93JP0IZga
  Z8oiY9iWjWvLJ7/oCzqUtCu0GWjlXS5sE23dnHpcU41r2nSoIELI/kx+gigQ9mHN
  I7eynjZS+cG0Kr+PVpHkbIjHCmuWxNzKUW8kWgWsiEfLsL6sGHXh2K+d4AAf7Nnh
  JiC5h06lQtilBPSV7UPF8wzmkRtTg8N7GZZG2GLRJ5FTQTkklEJG68UIqS64XN5z
  DzliI7wX7bAVs/ShFNeDYtFmGuY+xZ0u0qCZm5dTQB+UyGSXT+xdQD4nTWCt2O8i
  xvzmPdlavM8e84HY4fmAAzstov/szT130Kfk+JBm3K34jl9gZ7Cxgq5oC4YXq0JD
  NZaj092Gsmg1iWyjt1cMtL7r+JwL0UG0IK49M2NjfvBGoc2V5ede0seui8Xi9jMg
  Hav9qraIxKVJqs/2Cr/1IHMsSwTNXlX3UnzMaDUUQJGggX2q2ucsn6gsyccmmaA4
  ctFN3ad0u0yJLUvytu8KrQbR6K2hzwIDAQABoxAwDjAMBgNVHRMBAf8EAjAAMA0G
  CSqGSIb3DQEBCwUAA4ICAQC2gC5QgWo22b5k8grINiUL3uXuCztB5wZB5dFIjIsi
  KrgtHsEEBh666UKFCW2FahudhbQAn5Ng2BWAZfBsG3iA8vx2Xv5NStTeDnDN/V8c
  td5O7QqgmALUvEMsO0gEfLLzJncASdmwLFHHg/zLYG/VCRdsK+ibcHcmmB0sVYVX
  ugtKlEQo1Pf8NRpGHG1X6YOmzpW3wk49mSuQrOogiFen82A141D/3H1PFXx/eZA3
  mPFG6/R8BApzs6pgipsI6J4dL50rP+5qqTflSOYohcbxMJ2MnsonGQUlvHm4rasj
  kekZJ2m1xSRT8CdZWr/jut/HbIO/UPwX8Z2CEw60B73YWAlCZbM/gVTPtucsJ5aq
  rfQMYaRY4wD2VAxKD47D96X1NBXCEkkE69fraeSdzL9m7UzdSdqt1SbBTOQvVi7j
  eWTPkMzmx61YJFiGCwEErOIYLRNlCSUKGGwZAna+9hKJkTHgDw0FZ0xS+S8EcnTp
  lirU8tq7acXCHKz5JQ7f7+0KMs7j3zHi7oaWVnwksF8RGl/DFBWdRn2iqR7oGZH5
  uCYNELUAAB7HU5nZzS5tvUbS+dOptLPOKhR39NORoqOWdMN15KY32jVsaugm+0mN
  9iMWOULg2VohsUj4Sa+2CNG3DyXTzkQIi4CsQJ/uMiRadwNtZhLwjX9Q8PCNw84i
  6w==
  -----END CERTIFICATE-----
casdoor_server:
  endpoint: "${GAME_ADMIN_API_CASDOOR_SERVER_ENDPOINT}"
  client_id: "${GAME_ADMIN_API_CASDOOR_SERVER_CLIENT_ID}"
  client_secret: "${GAME_ADMIN_API_CASDOOR_SERVER_CLIENT_SECRET}"
  organization: "${GAME_ADMIN_API_CASDOOR_SERVER_ORGANIZATION}"
  owner: "${GAME_ADMIN_API_CASDOOR_SERVER_OWNER}"
  application: "${GAME_ADMIN_API_CASDOOR_SERVER_APPLICATION}"
  frontend_url: "${GAME_ADMIN_API_CASDOOR_SERVER_FRONTEND_URL}"
  user: "${GAME_ADMIN_API_CASDOOR_SERVER_USER}"
  role: "${GAME_ADMIN_API_CASDOOR_SERVER_ROLE}"
  model: "${GAME_ADMIN_API_CASDOOR_SERVER_MODEL}"
consul:
  address: "${GAME_ADMIN_API_CONSUL_ADDRESS}"
  token: "${GAME_ADMIN_API_CONSUL_TOKEN}"
  config_prefix: "${GAME_ADMIN_API_CONSUL_CONFIG_PREFIX}"
cors:
  allowOrigin: "${GAME_ADMIN_API_CORS_ALLOWORIGIN}"
  allowCredentials: "${GAME_ADMIN_API_CORS_ALLOWCREDENTIALS}"
  exposeHeaders: "${GAME_ADMIN_API_CORS_EXPOSEHEADERS}"
  maxAge: ${GAME_ADMIN_API_CORS_MAXAGE}
  allowMethods: "${GAME_ADMIN_API_CORS_ALLOWMETHODS}"
  allowHeaders: "${GAME_ADMIN_API_CORS_ALLOWHEADERS}"
storage:
  provider: "${GAME_ADMIN_API_STORAGE_PROVIDER}"
  s3:
    accessKeyID: "${GAME_ADMIN_API_STORAGE_S3_ACCESSKEYID}"
    secretAccessKey: "${GAME_ADMIN_API_STORAGE_S3_SECRETACCESSKEY}"
    region: "${GAME_ADMIN_API_STORAGE_S3_REGION}"
    bucketName: "${GAME_ADMIN_API_STORAGE_S3_BUCKETNAME}"
    usePathStyleEndpoint: ${GAME_ADMIN_API_STORAGE_S3_USEPATHSTYLEENDPOINT}
    publicURLPrefix: ''
