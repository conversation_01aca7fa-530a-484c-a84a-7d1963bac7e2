package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SearchTelegramGroupsReq defines the request for searching telegram groups
type SearchTelegramGroupsReq struct {
	g.Meta `path:"/telegram-groups/search" method:"get" tags:"TelegramGroups" summary:"Search telegram groups by title and chat_id"`
	Q      string `json:"q" dc:"Search query for title and chat_id (minimum 1 character)" v:"required|min-length:1"`
	Page   int    `json:"page" dc:"Page number" d:"1" v:"min:1"`
	Limit  int    `json:"limit" dc:"Results per page" d:"20" v:"min:1|max:100"`
}

// SearchTelegramGroupsRes defines the response for searching telegram groups
type SearchTelegramGroupsRes struct {
	Data       []*TelegramGroupSearchItem `json:"data" dc:"Telegram group search results"`
	Pagination PaginationInfo             `json:"pagination" dc:"Pagination information"`
}

// TelegramGroupSearchItem defines the structure for a telegram group item in search results
type TelegramGroupSearchItem struct {
	Id          uint64 `json:"id" dc:"Telegram group ID"`
	ChatId      int64  `json:"chatId" dc:"Telegram chat ID"`
	Title       string `json:"title" dc:"Group title"`
	BotIsAdmin  bool   `json:"botIsAdmin" dc:"Whether the bot is an admin"`
	DisplayText string `json:"displayText" dc:"Formatted display text for UI"`
}
