package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// --- Address Statistics API Definitions ---

// GetAddressStatisticsReq defines the request structure for getting address statistics.
type GetAddressStatisticsReq struct {
	g.Meta `path:"/address-statistics" method:"get" tags:"SystemAddress" summary:"获取地址统计信息"`
}

// AddressStatisticsItem defines the structure for a single chain's address statistics.
type AddressStatisticsItem struct {
	Chain     string `json:"chain" dc:"链名称"`
	BindNum   int    `json:"bind_num" dc:"已绑定地址数量"`
	UnbindNum int    `json:"unbind_num" dc:"未绑定地址数量"`
}

// GetAddressStatisticsRes defines the response structure for the address statistics query.
type GetAddressStatisticsRes struct {
	Data []*AddressStatisticsItem `json:"data" dc:"地址统计信息列表"`
}

// --- Address Import API Definitions ---

// ImportAddressesReq defines the request structure for importing addresses from a CSV file.
type ImportAddressesReq struct {
	g.Meta `path:"/addresses/import" method:"post" mime:"multipart/form-data" tags:"SystemAddress" summary:"导入地址数据"`
	File   *ghttp.UploadFile `json:"file" type:"string" format:"binary" dc:"CSV文件 (包含地址数据)"`
}

// ImportAddressesRes defines the response structure for the address import operation.
type ImportAddressesRes struct {
	TaskId string `json:"taskId" dc:"导入任务ID (用于查询进度)"`
}

// GetImportProgressReq defines the request structure for getting the import progress.
type GetImportProgressReq struct {
	g.Meta `path:"/addresses/import/{taskId}/progress" method:"get" tags:"SystemAddress" summary:"获取地址导入进度"`
	TaskId string `json:"taskId" in:"path" dc:"导入任务ID"`
}

// ImportProgressItem defines the structure for import progress information.
type ImportProgressItem struct {
	TaskId        string  `json:"taskId" dc:"导入任务ID"`
	Status        string  `json:"status" dc:"任务状态 (pending, validating, importing, completed, failed)"`
	Progress      float64 `json:"progress" dc:"导入进度 (0-100)"`
	ProcessedRows int     `json:"processedRows" dc:"已处理行数"`
	TotalRows     int     `json:"totalRows" dc:"总行数"`
	ErrorMessage  string  `json:"errorMessage" dc:"错误信息 (如果有)"`
}

// GetImportProgressRes defines the response structure for the import progress query.
type GetImportProgressRes struct {
	Data *ImportProgressItem `json:"data" dc:"导入进度信息"`
}
