package v1

import "github.com/gogf/gf/v2/frame/g"

// UploadAvatarReq defines the request structure for uploading an avatar.
// The actual file is expected via multipart/form-data.
type UploadAvatarReq struct {
	g.Meta `path:"/upload/avatar" method:"post" tags:"SystemUpload" summary:"上传头像"`
	// No specific fields needed here if the file is the only input via form-data and passed implicitly.
	// If you need other form fields along with the file, define them here.
	// Example:
	// UserID string `json:"userId" v:"required#UserID is required"`
}

// UploadAvatarRes defines the response structure after successfully uploading an avatar.
type UploadAvatarRes struct {
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
}

// UploadFileReq defines the request structure for uploading a generic file.
type UploadFileReq struct {
	g.Meta `path:"/upload/file" method:"post" tags:"SystemUpload" summary:"上传文件"`
	// File type validation will be done in controller based on allowed file extensions
}

// UploadFileRes defines the response structure after successfully uploading a file.
type UploadFileRes struct {
	FileUrl  string `json:"fileUrl" dc:"文件URL"`
	FileName string `json:"fileName" dc:"文件名"`
	FileSize int64  `json:"fileSize" dc:"文件大小(字节)"`
}

// UploadImageReq defines the request structure for uploading an image.
type UploadImageReq struct {
	g.Meta `path:"/upload/image" method:"post" tags:"SystemUpload" summary:"上传图片"`
	// Image validation will be done in controller (JPEG, PNG, GIF, WebP)
}

// UploadImageRes defines the response structure after successfully uploading an image.
type UploadImageRes struct {
	ImageUrl string `json:"imageUrl" dc:"图片URL"`
	Width    int    `json:"width,omitempty" dc:"图片宽度"`
	Height   int    `json:"height,omitempty" dc:"图片高度"`
}

// UploadVideoReq defines the request structure for uploading a video.
type UploadVideoReq struct {
	g.Meta `path:"/upload/video" method:"post" tags:"SystemUpload" summary:"上传视频"`
	// Video validation will be done in controller (MP4, AVI, MOV, MKV, WebM)
}

// UploadVideoRes defines the response structure after successfully uploading a video.
type UploadVideoRes struct {
	VideoUrl string `json:"videoUrl" dc:"视频URL"`
	FileName string `json:"fileName" dc:"文件名"`
	FileSize int64  `json:"fileSize" dc:"文件大小(字节)"`
	Duration int    `json:"duration,omitempty" dc:"视频时长(秒)"`
}
