package v1

import (
	"admin-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
)

// --- 获取所有备用账户列表 ---

// GetBackupAccountsReq defines the request structure for getting all backup accounts list.
type GetBackupAccountsReq struct {
	g.Meta             `path:"/backup-accounts" method:"get" tags:"SystemUserBackupAccount" summary:"获取备用账户列表"`
	common.PageRequest        // 分页参数
	Export             int    `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`
	TelegramUsername   string `json:"telegramUsername" dc:"Telegram用户名"`
	TelegramId         int64  `json:"telegramId" dc:"Telegram用户ID"`
	UserId             int64  `json:"userId" dc:"用户ID"`
	DateRange          string `json:"dateRange"  dc:"创建时间范围，格式：2025-01-01,2025-01-31"`

}

// GetBackupAccountsRes defines the response structure for getting all backup accounts list.
type GetBackupAccountsRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// List contains the list of backup accounts.
	List []*BackupAccountItem `json:"list" dc:"备用账户列表"`
}

// --- 获取用户备用账户列表 ---

// GetUserBackupAccountsReq defines the request structure for getting a user's backup accounts list.
type GetUserBackupAccountsReq struct {
	g.Meta `path:"/users/{userId}/backup-accounts" method:"get" tags:"SystemUserBackupAccount" summary:"获取用户备用账户列表"`
	// UserId is the ID of the user whose backup accounts are being requested.
	UserId             int64  `json:"userId" v:"required" dc:"用户ID"`
	common.PageRequest        // 分页参数
	Export             int    `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`
	TelegramUsername   string `json:"telegramUsername" dc:"Telegram用户名"`
	TelegramId         int64  `json:"telegramId" dc:"Telegram用户ID"`
	DateRange          string `json:"dateRange"  dc:"创建时间范围，格式：2025-01-01,2025-01-31"`

}

// BackupAccountItem defines the structure for a single backup account item in a list.
type BackupAccountItem struct {
	Id               uint64 `json:"id" dc:"关联ID"`
	AUserId          int64  `json:"aUserId" dc:"主用户ID"`
	BUserId          int64  `json:"bUserId" dc:"备用用户ID"`
	TelegramUsername string `json:"telegramUsername" dc:"备用用户Telegram用户名"`
	TelegramId       int64  `json:"telegramId" dc:"备用用户Telegram ID"`
	CreatedAt        string `json:"createdAt" dc:"创建时间"`
	UpdatedAt        string `json:"updatedAt" dc:"更新时间"`
	VerifiedAt       string `json:"verifiedAt" dc:"验证时间"`

	// 新增：主用户telegram信息
	MainTelegramId       string `json:"mainTelegramId" dc:"主用户Telegram ID"`
	MainTelegramUsername string `json:"mainTelegramUsername" dc:"主用户Telegram用户名"`
	FirstName            string `json:"firstName" dc:"主用户真实姓名"`
}

// GetUserBackupAccountsRes defines the response structure for getting a user's backup accounts list.
type GetUserBackupAccountsRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// List contains the list of backup accounts.
	List []*BackupAccountItem `json:"list" dc:"备用账户列表"`
}

// --- 添加用户备用账户 ---

// AddUserBackupAccountReq defines the request structure for adding a backup account for a user.
type AddUserBackupAccountReq struct {
	g.Meta  `path:"/users/{userId}/backup-accounts" method:"post" tags:"SystemUserBackupAccount" summary:"添加用户备用账户"`
	UserId  int64 `json:"userId" v:"required#用户ID不能为空,integer#用户ID必须为整数" dc:"用户ID"`
	BUserId int64 `json:"bUserId" v:"required#备用用户ID不能为空,integer#备用用户ID必须为整数" dc:"备用用户ID"`
}

// AddUserBackupAccountRes defines the response structure after adding a backup account.
type AddUserBackupAccountRes struct {
	// Id is the ID of the newly created backup account.
	Id int64 `json:"id" dc:"关联ID"`
}

// --- 删除用户备用账户 ---

// DeleteUserBackupAccountReq defines the request structure for deleting a user's backup account.
type DeleteUserBackupAccountReq struct {
	g.Meta        `path:"/backup-accounts/{associationId}" method:"delete" tags:"SystemUserBackupAccount" summary:"删除用户备用账户"`
	AssociationId int64 `json:"associationId" v:"required" dc:"关联ID"`
}

// DeleteUserBackupAccountRes defines the response structure after deleting a backup account.
type DeleteUserBackupAccountRes struct {
	// Success indicates whether the deletion was successful.
	Success bool `json:"success" dc:"删除结果"`
}

// --- 设置用户备用账户验证状态 ---

// SetUserBackupAccountVerificationReq defines the request structure for setting backup account verification status.
type SetUserBackupAccountVerificationReq struct {
	g.Meta        `path:"/backup-accounts/{associationId}/verification" method:"put" tags:"SystemUserBackupAccount" summary:"设置备用账户验证状态"`
	AssociationId int64 `json:"associationId" v:"required" dc:"关联ID"`
	Verified      bool  `json:"verified" v:"required" dc:"验证状态：true-已验证，false-未验证"`
}

// SetUserBackupAccountVerificationRes defines the response structure after setting verification status.
type SetUserBackupAccountVerificationRes struct {
	// Success indicates whether the update was successful.
	Success bool `json:"success" dc:"更新结果"`
}
