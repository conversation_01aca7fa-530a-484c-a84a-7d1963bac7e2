package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// --- 收款请求列表查询 ---

// ListPaymentRequestReq defines the request structure for querying the payment request list.
type ListPaymentRequestReq struct {
	g.<PERSON>a `path:"/payment-requests" method:"get" tags:"SystemPaymentRequest" summary:"查询收款请求列表"`
	common.PageRequest
	RequestId         int64  `json:"requestId" dc:"收款请求ID"`
	RequesterUsername string `json:"requesterUsername" dc:"收款发起者用户名 (模糊查询)"`
	PayerUsername     string `json:"payerUsername" dc:"付款人用户名 (模糊查询)"`
	RequesterAccount  string `json:"requesterAccount" dc:"收款发起者账号 (模糊查询)"`
	PayerAccount      string `json:"payerAccount" dc:"付款人账号 (模糊查询)"`
	TokenSymbol       string `json:"tokenSymbol" dc:"代币符号 (模糊查询)"`
	Status            *int   `json:"status" v:"in:1,2,3,4" dc:"收款请求状态 (1: 待支付, 2: 已支付, 3: 已过期, 4: 已取消)"`
	DateRange         string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export            int    `json:"export" d:"0" dc:"是否导出:0不导出,1导出"`
	
	// 新增：三级代理模糊查询
	FirstAgentName     string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName    string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName     string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`
	
	// 新增：telegram信息查询
	TelegramId         string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername   string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName          string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// PaymentRequestListItem defines the structure for an item in the payment request list.
type PaymentRequestListItem struct {
	// RequestId is the unique identifier for the payment request.
	RequestId int64 `json:"requestId" dc:"收款请求ID"`
	// RequesterUserId is the ID of the user who initiated the request.
	RequesterUserId int64 `json:"requesterUserId" dc:"收款发起者用户ID"`
	// RequesterUsername is the username of the user who initiated the request.
	RequesterUsername string `json:"requesterUsername" dc:"收款发起者用户名"`
	// RequesterAccount is the account of the user who initiated the request.
	RequesterAccount string `json:"requesterAccount" dc:"收款发起者账号"`
	// PayerUserId is the ID of the user who is requested to pay.
	PayerUserId int64 `json:"payerUserId" dc:"付款人用户ID"`
	// PayerUsername is the username of the user who is requested to pay.
	PayerUsername string `json:"payerUsername" dc:"付款人用户名"`
	// PayerAccount is the account of the user who is requested to pay.
	PayerAccount string `json:"payerAccount" dc:"付款人账号"`
	// TokenId is the ID of the token requested for payment.
	TokenId int `json:"tokenId" dc:"代币ID"`
	// TokenSymbol is the symbol of the token requested for payment.
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号"`
	// TokenName is the name of the token requested for payment.
	TokenName string `json:"tokenName" dc:"代币名称"`
	// Amount is the requested payment amount as a string.
	Amount string `json:"amount" dc:"收款金额"`
	// Memo is an optional description or note for the payment request.
	Memo string `json:"memo" dc:"收款说明/备注"`
	// Status indicates the current status of the payment request (1: Pending, 2: Paid, 3: Expired, 4: Cancelled).
	Status int `json:"status" dc:"状态 (1:待支付, 2:已支付, 3:已过期, 4:已取消)"`
	// StatusText provides a human-readable representation of the status.
	StatusText string `json:"statusText" dc:"状态文本"`
	// CreatedAt is the timestamp when the payment request was created.
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	// ExpiresAt is the timestamp when the payment request expires.
	ExpiresAt string `json:"expiresAt" dc:"过期时间"`
	
	// 新增：发起者三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"发起者一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"发起者二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"发起者三级代理名称"`
	
	// 新增：发起者telegram信息
	TelegramId       string `json:"telegramId" dc:"发起者Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"发起者Telegram用户名"`
	FirstName        string `json:"firstName" dc:"发起者真实姓名"`
	
	// 新增：付款人三级代理信息
	PayerFirstAgentName  string `json:"payerFirstAgentName" dc:"付款人一级代理名称"`
	PayerSecondAgentName string `json:"payerSecondAgentName" dc:"付款人二级代理名称"`
	PayerThirdAgentName  string `json:"payerThirdAgentName" dc:"付款人三级代理名称"`
	
	// 新增：付款人telegram信息
	PayerTelegramId       string `json:"payerTelegramId" dc:"付款人Telegram ID"`
	PayerTelegramUsername string `json:"payerTelegramUsername" dc:"付款人Telegram用户名"`
	PayerFirstName        string `json:"payerFirstName" dc:"付款人真实姓名"`
}

// ListPaymentRequestRes defines the response structure for the payment request list query.
type ListPaymentRequestRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of payment requests.
	Data []*PaymentRequestListItem `json:"data" dc:"收款请求列表"`
}

// --- 收款请求详情查询 ---

// GetPaymentRequestDetailReq defines the request structure for querying payment request details.
type GetPaymentRequestDetailReq struct {
	g.Meta `path:"/payment-requests/{requestId}" method:"get" tags:"SystemPaymentRequest" summary:"查询收款请求详情"`
	// RequestId is the ID of the payment request to retrieve details for (obtained from path).
	RequestId int64 `json:"requestId" v:"required#收款请求ID不能为空" dc:"收款请求ID"`
}

// PaymentRequestDetail defines the structure for payment request details, including associated information.
type PaymentRequestDetail struct {
	*entity.PaymentRequests // Embeds the base payment request entity.
	// RequesterUsername is the username of the user who initiated the request.
	RequesterUsername string `json:"requesterUsername" dc:"收款发起者用户名"`
	// PayerUsername is the username of the user who is requested to pay.
	PayerUsername string `json:"payerUsername" dc:"付款人用户名"`
	// TokenSymbol is the symbol of the token requested for payment.
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号"`
	// TokenName is the name of the token requested for payment.
	TokenName string `json:"tokenName" dc:"代币名称"`
	// StatusText provides a human-readable representation of the status.
	StatusText string `json:"statusText" dc:"状态文本"`
	// TransactionId is the ID of the associated transaction (if paid).
	TransactionId int64 `json:"transactionId" dc:"关联交易ID"`
	// TransactionStatus is the status of the associated transaction.
	TransactionStatus int `json:"transactionStatus" dc:"交易状态"`
	// TransactionTime is the timestamp of the associated transaction.
	TransactionTime string `json:"transactionTime" dc:"交易时间"`
}

// GetPaymentRequestDetailRes defines the response structure for the payment request details query.
type GetPaymentRequestDetailRes struct {
	// Data contains the payment request details.
	Data *PaymentRequestDetail `json:"data" dc:"收款请求详情"`
}

// --- 更新收款请求状态 ---

// UpdatePaymentRequestStatusReq defines the request structure for updating the status of a payment request.
// Consider using PATCH /payment-requests/{requestId} instead.
type UpdatePaymentRequestStatusReq struct {
	g.Meta       `path:"/payment-requests/{requestId}/status" method:"put" tags:"SystemPaymentRequest" summary:"更新收款请求状态"`
	RequestId    int64  `json:"requestId" v:"required#收款请求ID不能为空" dc:"收款请求ID"`
	TargetStatus int    `json:"targetStatus" v:"required|in:3,4#状态不能为空|状态只能为已过期(3)或已取消(4)" dc:"目标状态 (3:已过期, 4:已取消)"`
	Remark       string `json:"remark" dc:"操作备注"`
}

// UpdatePaymentRequestStatusRes defines the response structure after updating a payment request's status.
type UpdatePaymentRequestStatusRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"操作是否成功"`
}
