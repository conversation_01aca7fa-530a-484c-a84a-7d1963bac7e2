package v1

import (
	"admin-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 获取提现记录列表 ---

// ListUserWithdrawsReq defines the request structure for getting the user withdraws list.
type ListUserWithdrawsReq struct {
	g.Meta `path:"/user-withdraws" method:"get" tags:"SystemUserWithdraw" summary:"获取提现记录列表"`
	common.PageRequest
	UserId    *uint64  `json:"userId" dc:"用户ID"`
	Account   *string  `json:"account" dc:"用户账号"`
	Username  *string  `json:"username" dc:"用户名"`
	TokenId   *uint    `json:"tokenId" dc:"币种ID"`
	Symbol    *string  `json:"symbol" dc:"币种符号"`
	Chain     *string  `json:"chain" dc:"链名称"`
	Address   *string  `json:"address" dc:"提币地址"`
	OrderNo   *string  `json:"orderNo" dc:"订单号"`
	State     *uint    `json:"state" dc:"状态:0-全部, 1-待自动放币处理, 2-待人工处理, 3-已拒绝, 4-已完成, 5-失败" v:"in:0,1,2,3,4,5"`
	DateRange string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	AmountMin *float64 `json:"amountMin" dc:"最小金额"`
	AmountMax *float64 `json:"amountMax" dc:"最大金额"`

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram查询条件
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"名字 (模糊搜索)"`

	// 新增：法币提现查询条件
	FiatType         string `json:"fiatType" dc:"法币提现类型: alipay_account-支付宝账号, alipay_qr-支付宝二维码, wechat_qr-微信二维码" v:"in:alipay_account,alipay_qr,wechat_qr"`
	RecipientName    string `json:"recipientName" dc:"法币收款人姓名 (模糊搜索)"`
	RecipientAccount string `json:"recipientAccount" dc:"法币收款账户 (模糊搜索)"`
}

// ListUserWithdrawsRes defines the response structure for getting the user withdraws list.
type ListUserWithdrawsRes struct {
	Page common.PageResponse      `json:"page" dc:"分页信息"`
	Data []*UserWithdrawsListItem `json:"data" dc:"提现记录列表"`
}

// UserWithdrawsListItem defines the structure for a user withdraw list item.
type UserWithdrawsListItem struct {
	UserWithdrawsId       uint        `json:"userWithdrawsId" dc:"提现记录ID"`
	UserId                uint64      `json:"userId" dc:"用户ID"`
	Account               string      `json:"account" dc:"用户账号"`
	Nickname              string      `json:"nickname" dc:"用户昵称"`
	TokenId               uint        `json:"tokenId" dc:"币种ID"`
	Symbol                string      `json:"symbol" dc:"币种符号"`
	Chain                 string      `json:"chain" dc:"链名称"`
	WalletId              string      `json:"walletId" dc:"钱包ID"`
	OrderNo               string      `json:"orderNo" dc:"订单号"`
	Address               string      `json:"address" dc:"提币地址"`
	RecipientName         string      `json:"recipientName" dc:"法币收款人姓名"`
	RecipientAccount      string      `json:"recipientAccount" dc:"法币收款账户"`
	RecipientQrcode       string      `json:"recipientQrcode" dc:"法币收款二维码"`
	FiatType              string      `json:"fiatType" dc:"法币提现类型"`
	Amount                float64     `json:"amount" dc:"申请提现金额"`
	HandlingFee           float64     `json:"handlingFee" dc:"手续费"`
	ActualAmount          float64     `json:"actualAmount" dc:"实际到账金额"`
	ConversionTokenSymbol string      `json:"conversionTokenSymbol" dc:"折合前代币符号, 例如 CNY"`
	ConversionRate        string      `json:"conversionRate" dc:"折合汇率 (折合前代币与提现代币之间的汇率)"`
	ConvertedAmount       string      `json:"convertedAmount" dc:"折合前的数量 (实际扣除的金额)"`
	State                 uint        `json:"state" dc:"状态: 1-待自动放币处理, 2-待人工处理, 3-已拒绝, 4-已完成, 5-失败"`
	TxHash                string      `json:"txHash" dc:"交易哈希"`
	UserRemark            string      `json:"userRemark" dc:"用户备注"`
	AdminRemark           string      `json:"adminRemark" dc:"管理员备注"`
	CreatedAt             *gtime.Time `json:"createdAt" dc:"创建时间"`
	CheckedAt             *gtime.Time `json:"checkedAt" dc:"审核时间"`
	ProcessingAt          *gtime.Time `json:"processingAt" dc:"处理时间"`
	CompletedAt           *gtime.Time `json:"completedAt" dc:"完成时间"`
	NotificationSent      uint        `json:"notificationSent" dc:"是否已发送通知: 0-未发送, 1-已发送"`
	NotificationSentAt    *gtime.Time `json:"notificationSentAt" dc:"通知发送时间"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// --- 获取提现记录详情 ---

// GetUserWithdrawDetailReq defines the request structure for getting a user withdraw detail.
type GetUserWithdrawDetailReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}" method:"get" tags:"SystemUserWithdraw" summary:"获取提现记录详情"`
	UserWithdrawsId uint `json:"withdrawId"  dc:"提现记录ID"`
}

// GetUserWithdrawDetailRes defines the response structure for getting a user withdraw detail.
type GetUserWithdrawDetailRes struct {
	Data *UserWithdrawDetailItem `json:"data" dc:"提现记录详情"`
}

// UserWithdrawDetailItem defines the structure for a user withdraw detail item.
type UserWithdrawDetailItem struct {
	UserWithdrawsId       uint        `json:"userWithdrawsId" dc:"提现记录ID"`
	UserId                uint64      `json:"userId" dc:"用户ID"`
	Account               string      `json:"account" dc:"用户账号"`
	Nickname              string      `json:"nickname" dc:"用户昵称"`
	TokenId               uint        `json:"tokenId" dc:"币种ID"`
	Symbol                string      `json:"symbol" dc:"币种符号"`
	Chain                 string      `json:"chain" dc:"链名称"`
	WalletId              string      `json:"walletId" dc:"钱包ID"`
	OrderNo               string      `json:"orderNo" dc:"订单号"`
	Address               string      `json:"address" dc:"提币地址"`
	RecipientName         string      `json:"recipientName" dc:"法币收款人姓名"`
	RecipientAccount      string      `json:"recipientAccount" dc:"法币收款账户"`
	RecipientQrcode       string      `json:"recipientQrcode" dc:"法币收款二维码"`
	FiatType              string      `json:"fiatType" dc:"法币提现类型"`
	Amount                float64     `json:"amount" dc:"申请提现金额"`
	HandlingFee           float64     `json:"handlingFee" dc:"手续费"`
	ActualAmount          float64     `json:"actualAmount" dc:"实际到账金额"`
	ConversionTokenSymbol string      `json:"conversionTokenSymbol" dc:"折合前代币符号, 例如 CNY"`
	ConversionRate        string      `json:"conversionRate" dc:"折合汇率 (折合前代币与提现代币之间的汇率)"`
	ConvertedAmount       string      `json:"convertedAmount" dc:"折合前的数量 (实际扣除的金额)"`
	State                 uint        `json:"state" dc:"状态: 1-待自动放币处理, 2-待人工处理, 3-已拒绝, 4-已完成, 5-失败"`
	RefuseReasonZh        string      `json:"refuseReasonZh" dc:"拒绝原因(中文)"`
	RefuseReasonEn        string      `json:"refuseReasonEn" dc:"拒绝原因(英文)"`
	TxHash                string      `json:"txHash" dc:"交易哈希"`
	ErrorMessage          string      `json:"errorMessage" dc:"错误信息"`
	UserRemark            string      `json:"userRemark" dc:"用户备注"`
	AdminRemark           string      `json:"adminRemark" dc:"管理员备注"`
	CreatedAt             *gtime.Time `json:"createdAt" dc:"创建时间"`
	CheckedAt             *gtime.Time `json:"checkedAt" dc:"审核时间"`
	ProcessingAt          *gtime.Time `json:"processingAt" dc:"处理时间"`
	CompletedAt           *gtime.Time `json:"completedAt" dc:"完成时间"`
	NotificationSent      uint        `json:"notificationSent" dc:"是否已发送通知: 0-未发送, 1-已发送"`
	NotificationSentAt    *gtime.Time `json:"notificationSentAt" dc:"通知发送时间"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// --- 审核提现记录 ---

// ReviewUserWithdrawReq defines the request structure for reviewing a user withdraw.
type ReviewUserWithdrawReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}/review" method:"put" tags:"SystemUserWithdraw" summary:"审核提现记录"`
	UserWithdrawsId uint   `json:"withdrawId"  dc:"提现记录ID"`
	Action          string `json:"action" v:"required|in:approve,reject#操作类型不能为空|操作类型必须是approve或reject" dc:"操作类型: approve-通过, reject-拒绝"`
	RefuseReasonZh  string `json:"refuseReasonZh" dc:"拒绝原因(中文)"`
	RefuseReasonEn  string `json:"refuseReasonEn" dc:"拒绝原因(英文)"`
	AdminRemark     string `json:"adminRemark" dc:"管理员备注"`
}

// ReviewUserWithdrawRes defines the response structure for reviewing a user withdraw.
type ReviewUserWithdrawRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新提现记录状态 ---

// UpdateUserWithdrawStatusReq defines the request structure for updating a user withdraw status.
type UpdateUserWithdrawStatusReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}/status" method:"put" tags:"SystemUserWithdraw" summary:"更新提现记录状态"`
	UserWithdrawsId uint   `json:"withdrawId"  dc:"提现记录ID"`
	State           uint   `json:"state" v:"required|in:2,4,5#状态不能为空|状态值无效" dc:"状态: 2-待人工处理, 4-已完成, 5-失败"`
	TxHash          string `json:"txHash" dc:"交易哈希"`
	ErrorMessage    string `json:"errorMessage" dc:"错误信息"`
	AdminRemark     string `json:"adminRemark" dc:"管理员备注"`
}

// UpdateUserWithdrawStatusRes defines the response structure for updating a user withdraw status.
type UpdateUserWithdrawStatusRes struct {
	Success bool `json:"success" dc:"是否成功"`
}
