package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity" // Import the entity package

	"github.com/gogf/gf/v2/frame/g"
)

const (
	// TenantTag defines the tag for tenant management APIs in Swagger.
	TenantTag = "Tenant Management"
)

// --- 添加租户 ---

// AddTenantReq defines the request structure for adding a tenant.
type AddTenantReq struct {
	g.Meta     `path:"/tenants" method:"post" tags:"Tenant Management" summary:"添加租户"`
	Username   string `json:"username" v:"required|length:4,30#请输入用户名|用户名长度为4到30位" dc:"用户名"`
	Password   string `json:"password" v:"required|length:6,30#请输入密码|密码长度为6到30位" dc:"密码"`
	TenantName string `json:"tenantName" v:"required|length:2,50#请输入租户名称|租户名称长度为2到50位" dc:"租户名称"`
	Email      string `json:"email" v:"email#邮箱格式不正确" dc:"邮箱"`
	Status     *int   `json:"status" v:"required|in:0,1#请选择状态|状态值无效" dc:"状态 (0:禁用, 1:启用)"`
	// 业务相关字段
	BusinessName     string `json:"businessName" v:"max-length:100#公司/业务名称不能超过100个字符" dc:"公司/业务注册名称"`
	TelegramAccount  int64  `json:"telegramAccount" dc:"Telegram账户ID"`
	TelegramBotName  string `json:"telegramBotName" v:"max-length:50#机器人名称不能超过50个字符" dc:"Telegram机器人名称"`
	TelegramBotToken string `json:"telegramBotToken" v:"max-length:200#Token不能超过200个字符" dc:"Telegram机器人Token"`
	Level            *uint  `json:"level" dc:"租户级别"`
	// 新增字段
	Group            string `json:"group" v:"regex:^https://t\\.me/[a-zA-Z0-9_]+$#官方群组格式必须为 https://t.me/群组名" dc:"官方群组"`
	Customer         string `json:"customer" v:"regex:^https://t\\.me/[a-zA-Z0-9_]+$#客服格式必须为 https://t.me/用户名" dc:"客服"`
	TelegramGroupsId string `json:"telegramGroupsId" dc:"Telegram群组ID"`
}

// AddTenantRes defines the response structure for adding a tenant.
type AddTenantRes struct{}

// --- 获取租户列表 ---

// GetTenantListReq defines the request structure for getting the tenant list.
type GetTenantListReq struct {
	g.Meta `path:"/tenants" method:"get" tags:"Tenant Management" summary:"获取租户列表"`
	common.PageRequest
	Username   string `json:"username" dc:"用户名"`
	TenantName string `json:"tenantName" dc:"租户名称"`
	Email      string `json:"email" dc:"邮箱"`
	Level      *int   `json:"level" dc:"租户层级"`
	Status     *int   `json:"status" dc:"状态 (0:禁用, 1:启用)"`
	DateRange  string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export     int    `json:"export" d:"0" dc:"是否导出:0不导出,1导出"`
}

// TenantListItem defines the structure for a tenant item in the list.
type TenantListItem struct {
	*entity.Tenants
	InvitationUrl string `json:"invitationUrl" dc:"邀请链接 (Telegram机器人邀请URL)"`

	// Telegram information
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名"`
	TelegramId       int64  `json:"telegramId" dc:"Telegram ID"`

	// Timestamps (if not already in entity.Tenants)
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	UpdatedAt string `json:"updatedAt" dc:"更新时间"`
}

// GetTenantListRes defines the response structure for getting the tenant list.
type GetTenantListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	Data []*TenantListItem   `json:"data" dc:"租户列表"`
}

// --- 获取单个租户详情 ---

// GetTenantReq defines the request structure for getting a single tenant's details.
type GetTenantReq struct {
	g.Meta   `path:"/tenants/{tenantId}" method:"get" tags:"Tenant Management" summary:"获取单个租户详情"`
	TenantId int64 `json:"tenantId" v:"required#请输入租户ID" dc:"租户ID"`
}

// GetTenantRes defines the response structure for getting a single tenant's details.
type GetTenantRes struct {
	*entity.Tenants
	InvitationUrl string `json:"invitationUrl" dc:"邀请链接 (Telegram机器人邀请URL)"`
}

// --- 编辑租户基础信息 ---

// EditTenantReq defines the request structure for editing a tenant's basic information.
type EditTenantReq struct {
	g.Meta     `path:"/tenants/{tenantId}" method:"put" tags:"Tenant Management" summary:"编辑租户基础信息"`
	TenantId   int64  `json:"tenantId" v:"required#请输入租户ID" dc:"租户ID"`
	TenantName string `json:"tenantName" v:"required|length:2,50#请输入租户名称|租户名称长度为2到50位" dc:"租户名称"`
	Email      string `json:"email" v:"email#邮箱格式不正确" dc:"邮箱"`
	Status     *int   `json:"status" v:"required|in:0,1#请选择状态|状态值无效" dc:"状态 (0:禁用, 1:启用)"`
	// 业务相关字段
	BusinessName     string `json:"businessName" v:"max-length:100#公司/业务名称不能超过100个字符" dc:"公司/业务注册名称"`
	TelegramAccount  int64  `json:"telegramAccount" dc:"Telegram账户ID"`
	TelegramBotName  string `json:"telegramBotName" v:"max-length:50#机器人名称不能超过50个字符" dc:"Telegram机器人名称"`
	TelegramBotToken string `json:"telegramBotToken" v:"max-length:200#Token不能超过200个字符" dc:"Telegram机器人Token"`
	Level            *uint  `json:"level" dc:"租户级别"`
	// 新增字段
	Group            string `json:"group" v:"regex:^https://t\\.me/[a-zA-Z0-9_]+$#官方群组格式必须为 https://t.me/群组名" dc:"官方群组"`
	Customer         string `json:"customer" v:"regex:^https://t\\.me/[a-zA-Z0-9_]+$#客服格式必须为 https://t.me/用户名" dc:"客服"`
	TelegramGroupsId string `json:"telegramGroupsId" dc:"Telegram群组ID"`
}

// EditTenantRes defines the response structure for editing a tenant's basic information.
type EditTenantRes struct{}

// --- 批量软删除租户 ---

// DeleteTenantReq defines the request structure for batch soft deleting tenants.
type DeleteTenantReq struct {
	g.Meta    `path:"/tenants" method:"delete" tags:"Tenant Management" summary:"批量软删除租户"`
	TenantIds []int64 `json:"tenantIds" v:"required|min-length:1#请选择要删除的租户" dc:"租户ID列表"`
}

// DeleteTenantRes defines the response structure for batch soft deleting tenants.
type DeleteTenantRes struct{}

// --- 批量更新租户状态 ---

// UpdateTenantStatusReq defines the request structure for batch updating tenant statuses.
type UpdateTenantStatusReq struct {
	g.Meta    `path:"/tenants/batch/status" method:"put" tags:"Tenant Management" summary:"批量更新租户状态"`
	TenantIds []int64 `json:"tenantIds" v:"required|min-length:1#请选择要更新状态的租户" dc:"租户ID列表"`
	Status    int     `json:"status" v:"required|in:0,1#请选择状态|状态值无效" dc:"状态 (0:禁用, 1:启用)"`
}

// UpdateTenantStatusRes defines the response structure for batch updating tenant statuses.
type UpdateTenantStatusRes struct{}

// --- 修改指定租户密码 ---

// UpdateTenantPasswordReq defines the request structure for updating a specific tenant's password.
type UpdateTenantPasswordReq struct {
	g.Meta      `path:"/tenants/{tenantId}/password" method:"put" tags:"Tenant Management" summary:"修改指定租户密码"`
	TenantId    int64  `json:"tenantId" v:"required#请输入租户ID" dc:"租户ID"`
	NewPassword string `json:"newPassword" v:"required|length:6,30#请输入新密码|新密码长度为6到30位" dc:"新密码"`
}

// UpdateTenantPasswordRes defines the response structure for updating a specific tenant's password.
type UpdateTenantPasswordRes struct{}

// --- 重置指定租户的 Google Authenticator ---

// ResetTenant2FAReq defines the request structure for resetting a specific tenant's Google Authenticator.
type ResetTenant2FAReq struct {
	g.Meta   `path:"/tenants/{tenantId}/reset-2fa" method:"put" tags:"Tenant Management" summary:"重置指定租户的 Google Authenticator"`
	TenantId int64 `json:"tenantId" v:"required#请输入租户ID" dc:"租户ID"`
}

// ResetTenant2FARes defines the response structure for resetting a specific tenant's Google Authenticator.
type ResetTenant2FARes struct{}
