package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GetGameRngBetDetailsListReq defines the request for RNG/FISH game bet details list
type GetGameRngBetDetailsListReq struct {
	g.Meta            `path:"/game-rng-bet-details/list" method:"get" tags:"GameRngBetDetails" summary:"Get RNG/FISH game bet details list"`
	Username          string    `json:"username" dc:"Game account username filter"`
	GameCode          string    `json:"gameCode" dc:"Game code filter"`
	GameName          string    `json:"gameName" dc:"Game name filter"`
	GameCategory      string    `json:"gameCategory" dc:"Game category filter (RNG, FISH)"`
	ProductType       *int      `json:"productType" dc:"Product type filter (16=RNG, others=FISH)"`
	BetOrderNo        string    `json:"betOrderNo" dc:"Bet order number filter"`
	Currency          string    `json:"currency" dc:"Currency filter (CNY, USD, etc.)"`
	BetTimeStart      *gtime.Time `json:"betTimeStart" dc:"Bet time start filter"`
	BetTimeEnd        *gtime.Time `json:"betTimeEnd" dc:"Bet time end filter"`
	TransactionTimeStart *gtime.Time `json:"transactionTimeStart" dc:"Transaction time start filter"`
	TransactionTimeEnd   *gtime.Time `json:"transactionTimeEnd" dc:"Transaction time end filter"`
	MinBetAmount      *decimal.Decimal `json:"minBetAmount" dc:"Minimum bet amount filter"`
	MaxBetAmount      *decimal.Decimal `json:"maxBetAmount" dc:"Maximum bet amount filter"`
	MinWinAmount      *decimal.Decimal `json:"minWinAmount" dc:"Minimum win amount filter"`
	MaxWinAmount      *decimal.Decimal `json:"maxWinAmount" dc:"Maximum win amount filter"`
	WinLossType       string    `json:"winLossType" dc:"Win/Loss type filter (win, loss, all)"`
	Keyword           string    `json:"keyword" dc:"Search keyword for username, game name, or bet order"`
	TelegramId        *int64    `json:"telegramId" dc:"Filter by Telegram ID"`
	TelegramUsername  string    `json:"telegramUsername" dc:"Filter by Telegram username"`
	FirstName         string    `json:"firstName" dc:"Filter by first name"`
	TenantUsername    string    `json:"tenantUsername" dc:"Filter by tenant username"`
	Page              int       `json:"page" dc:"Page number" d:"1" v:"min:1"`
	PageSize          int       `json:"pageSize" dc:"Page size" d:"20" v:"min:1|max:100"`
}

// GetGameRngBetDetailsListRes defines the response for RNG/FISH game bet details list
type GetGameRngBetDetailsListRes struct {
	Total int                           `json:"total" dc:"Total count"`
	List  []*GameRngBetDetailsListItem  `json:"list" dc:"RNG/FISH game bet details list"`
	Stats *GameRngBetDetailsStats       `json:"stats" dc:"Statistical summary"`
}

// GameRngBetDetailsListItem defines the RNG/FISH game bet details list item
type GameRngBetDetailsListItem struct {
	Id                uint64          `json:"id" dc:"Bet details ID"`
	Username          string          `json:"username" dc:"Game account username"`
	BetAmount         decimal.Decimal `json:"betAmount" dc:"Bet amount"`
	ValidBetAmount    decimal.Decimal `json:"validBetAmount" dc:"Valid bet amount"`
	WinAmount         decimal.Decimal `json:"winAmount" dc:"Win amount"`
	NetPnl            decimal.Decimal `json:"netPnl" dc:"Net profit and loss (positive=win, negative=loss)"`
	Currency          string          `json:"currency" dc:"Currency (CNY, USD, etc.)"`
	GameCode          string          `json:"gameCode" dc:"Game code"`
	GameName          string          `json:"gameName" dc:"Game name (RNG/FISH specific field)"`
	ProductType       int             `json:"productType" dc:"Product type (16=RNG, others=FISH)"`
	GameCategory      string          `json:"gameCategory" dc:"Game category (RNG, FISH)"`
	BetOrderNo        string          `json:"betOrderNo" dc:"Bet order number"`
	SessionId         string          `json:"sessionId" dc:"Session identifier"`
	BetTime           *gtime.Time     `json:"betTime" dc:"Bet time"`
	TransactionTime   *gtime.Time     `json:"transactionTime" dc:"Transaction time"`
	ApiStatus         int             `json:"apiStatus" dc:"API response status code"`
	ApiErrorDesc      string          `json:"apiErrorDesc" dc:"API error description"`
	CreatedAt         *gtime.Time     `json:"createdAt" dc:"Record creation time"`
	UpdatedAt         *gtime.Time     `json:"updatedAt" dc:"Last update time"`
	TelegramId        *int64          `json:"telegramId" dc:"Telegram ID"`
	TelegramUsername  *string         `json:"telegramUsername" dc:"Telegram username"`
	FirstName         *string         `json:"firstName" dc:"First name"`
	TenantUsername    *string         `json:"tenantUsername" dc:"Tenant username"`
}

// GameRngBetDetailsStats defines the statistical summary
type GameRngBetDetailsStats struct {
	TotalBetAmount     decimal.Decimal `json:"totalBetAmount" dc:"Total bet amount"`
	TotalValidBetAmount decimal.Decimal `json:"totalValidBetAmount" dc:"Total valid bet amount"`
	TotalWinAmount     decimal.Decimal `json:"totalWinAmount" dc:"Total win amount"`
	TotalNetPnl        decimal.Decimal `json:"totalNetPnl" dc:"Total net profit and loss"`
	TotalBetCount      int             `json:"totalBetCount" dc:"Total bet count"`
	WinBetCount        int             `json:"winBetCount" dc:"Winning bet count"`
	LossBetCount       int             `json:"lossBetCount" dc:"Losing bet count"`
	WinRate            decimal.Decimal `json:"winRate" dc:"Win rate percentage"`
}

// GetGameRngBetDetailsDetailReq defines the request for RNG/FISH game bet details detail
type GetGameRngBetDetailsDetailReq struct {
	g.Meta `path:"/game-rng-bet-details/detail" method:"get" tags:"GameRngBetDetails" summary:"Get RNG/FISH game bet details detail"`
	Id     uint64 `json:"id" dc:"Bet details ID" v:"required|min:1"`
}

// GetGameRngBetDetailsDetailRes defines the response for RNG/FISH game bet details detail
type GetGameRngBetDetailsDetailRes struct {
	*GameRngBetDetailsDetailItem
}

// GameRngBetDetailsDetailItem defines the detailed RNG/FISH game bet details item
type GameRngBetDetailsDetailItem struct {
	Id                uint64          `json:"id" dc:"Bet details ID"`
	Username          string          `json:"username" dc:"Game account username"`
	BetAmount         decimal.Decimal `json:"betAmount" dc:"Bet amount"`
	ValidBetAmount    decimal.Decimal `json:"validBetAmount" dc:"Valid bet amount"`
	WinAmount         decimal.Decimal `json:"winAmount" dc:"Win amount"`
	NetPnl            decimal.Decimal `json:"netPnl" dc:"Net profit and loss (positive=win, negative=loss)"`
	Currency          string          `json:"currency" dc:"Currency (CNY, USD, etc.)"`
	GameCode          string          `json:"gameCode" dc:"Game code"`
	GameName          string          `json:"gameName" dc:"Game name (RNG/FISH specific field)"`
	ProductType       int             `json:"productType" dc:"Product type (16=RNG, others=FISH)"`
	GameCategory      string          `json:"gameCategory" dc:"Game category (RNG, FISH)"`
	BetOrderNo        string          `json:"betOrderNo" dc:"Bet order number"`
	SessionId         string          `json:"sessionId" dc:"Session identifier"`
	BetTime           *gtime.Time     `json:"betTime" dc:"Bet time"`
	TransactionTime   *gtime.Time     `json:"transactionTime" dc:"Transaction time"`
	AdditionalDetails interface{}     `json:"additionalDetails" dc:"Additional bet details (gamehall, gamePlat, status, createTime, endRoundTime, balance)"`
	ApiStatus         int             `json:"apiStatus" dc:"API response status code"`
	ApiErrorDesc      string          `json:"apiErrorDesc" dc:"API error description"`
	CreatedAt         *gtime.Time     `json:"createdAt" dc:"Record creation time"`
	UpdatedAt         *gtime.Time     `json:"updatedAt" dc:"Last update time"`
	TelegramId        *int64          `json:"telegramId" dc:"Telegram ID"`
	TelegramUsername  *string         `json:"telegramUsername" dc:"Telegram username"`
	FirstName         *string         `json:"firstName" dc:"First name"`
	TenantUsername    *string         `json:"tenantUsername" dc:"Tenant username"`
}

// GetGameRngBetDetailsStatsReq defines the request for RNG/FISH game bet details statistics
type GetGameRngBetDetailsStatsReq struct {
	g.Meta            `path:"/game-rng-bet-details/stats" method:"get" tags:"GameRngBetDetails" summary:"Get RNG/FISH game bet details statistics"`
	Username          string    `json:"username" dc:"Game account username filter"`
	GameCode          string    `json:"gameCode" dc:"Game code filter"`
	GameCategory      string    `json:"gameCategory" dc:"Game category filter (RNG, FISH)"`
	ProductType       *int      `json:"productType" dc:"Product type filter (16=RNG, others=FISH)"`
	Currency          string    `json:"currency" dc:"Currency filter"`
	BetTimeStart      *gtime.Time `json:"betTimeStart" dc:"Bet time start filter"`
	BetTimeEnd        *gtime.Time `json:"betTimeEnd" dc:"Bet time end filter"`
	TransactionTimeStart *gtime.Time `json:"transactionTimeStart" dc:"Transaction time start filter"`
	TransactionTimeEnd   *gtime.Time `json:"transactionTimeEnd" dc:"Transaction time end filter"`
	GroupBy           string    `json:"groupBy" dc:"Group by field (game, user, date, currency)" d:"game"`
}

// GetGameRngBetDetailsStatsRes defines the response for RNG/FISH game bet details statistics
type GetGameRngBetDetailsStatsRes struct {
	Overall   *GameRngBetDetailsStats      `json:"overall" dc:"Overall statistics"`
	GroupData []*GameRngBetDetailsGroupStats `json:"groupData" dc:"Grouped statistics"`
}

// GameRngBetDetailsGroupStats defines grouped statistics
type GameRngBetDetailsGroupStats struct {
	GroupKey          string          `json:"groupKey" dc:"Group key (game code, username, date, currency)"`
	GroupName         string          `json:"groupName" dc:"Group display name"`
	TotalBetAmount    decimal.Decimal `json:"totalBetAmount" dc:"Total bet amount"`
	TotalValidBetAmount decimal.Decimal `json:"totalValidBetAmount" dc:"Total valid bet amount"`
	TotalWinAmount    decimal.Decimal `json:"totalWinAmount" dc:"Total win amount"`
	TotalNetPnl       decimal.Decimal `json:"totalNetPnl" dc:"Total net profit and loss"`
	TotalBetCount     int             `json:"totalBetCount" dc:"Total bet count"`
	WinBetCount       int             `json:"winBetCount" dc:"Winning bet count"`
	LossBetCount      int             `json:"lossBetCount" dc:"Losing bet count"`
	WinRate           decimal.Decimal `json:"winRate" dc:"Win rate percentage"`
}

// ExportGameRngBetDetailsReq defines the request for exporting RNG/FISH game bet details
type ExportGameRngBetDetailsReq struct {
	g.Meta            `path:"/game-rng-bet-details/export" method:"post" tags:"GameRngBetDetails" summary:"Export RNG/FISH game bet details"`
	Username          string    `json:"username" dc:"Game account username filter"`
	GameCode          string    `json:"gameCode" dc:"Game code filter"`
	GameName          string    `json:"gameName" dc:"Game name filter"`
	GameCategory      string    `json:"gameCategory" dc:"Game category filter (RNG, FISH)"`
	ProductType       *int      `json:"productType" dc:"Product type filter (16=RNG, others=FISH)"`
	BetOrderNo        string    `json:"betOrderNo" dc:"Bet order number filter"`
	Currency          string    `json:"currency" dc:"Currency filter"`
	BetTimeStart      *gtime.Time `json:"betTimeStart" dc:"Bet time start filter"`
	BetTimeEnd        *gtime.Time `json:"betTimeEnd" dc:"Bet time end filter"`
	TransactionTimeStart *gtime.Time `json:"transactionTimeStart" dc:"Transaction time start filter"`
	TransactionTimeEnd   *gtime.Time `json:"transactionTimeEnd" dc:"Transaction time end filter"`
	MinBetAmount      *decimal.Decimal `json:"minBetAmount" dc:"Minimum bet amount filter"`
	MaxBetAmount      *decimal.Decimal `json:"maxBetAmount" dc:"Maximum bet amount filter"`
	MinWinAmount      *decimal.Decimal `json:"minWinAmount" dc:"Minimum win amount filter"`
	MaxWinAmount      *decimal.Decimal `json:"maxWinAmount" dc:"Maximum win amount filter"`
	WinLossType       string    `json:"winLossType" dc:"Win/Loss type filter (win, loss, all)"`
	Keyword           string    `json:"keyword" dc:"Search keyword"`
	ExportFormat      string    `json:"exportFormat" dc:"Export format (xlsx, csv)" d:"xlsx" v:"in:xlsx,csv"`
	ExportFields      []string  `json:"exportFields" dc:"Fields to export (if empty, export all)"`
}

// ExportGameRngBetDetailsRes defines the response for exporting RNG/FISH game bet details
type ExportGameRngBetDetailsRes struct {
	FileUrl   string `json:"fileUrl" dc:"Download URL for the exported file"`
	FileName  string `json:"fileName" dc:"Name of the exported file"`
	RecordCount int  `json:"recordCount" dc:"Number of exported records"`
}