package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
)

// UserInfo defines the structure for user information used in notice read/unread lists.
type UserInfo struct {
	// Id is the user's unique identifier.
	Id int64 `json:"id"`
	// Username is the user's login name.
	Username string `json:"username"`
	// Nickname is the user's display name.
	Nickname string `json:"nickname"`
	// Avatar is the URL of the user's avatar image.
	Avatar string `json:"avatar"`
	// DeptName is the name of the user's department.
	DeptName string `json:"deptName"`
}

// --- Admin 端 API ---

// --- 获取公告列表(管理端) ---

// GetAdminNoticeListReq defines the request structure for getting the notice list (admin).
type GetAdminNoticeListReq struct {
	g.Meta `path:"/admin/notices" method:"get" tags:"SystemNoticeAdmin" summary:"获取公告列表(管理端)"`
	common.PageRequest
	Title     string `json:"title" dc:"公告标题 (模糊查询)"`
	Type      *int   `json:"type" v:"in:1,2" dc:"公告类型 (1:通知, 2:私信)"`
	Tag       *int   `json:"tag" dc:"标签 (0:普通, 1:重要, 2:活动)"`
	Status    *int   `json:"status" dc:"公告状态 (0:草稿, 1:发布, 2:停用)"`
	CreatedBy *int64 `json:"createdBy" dc:"创建人ID"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetAdminNoticeListRes defines the response structure for getting the notice list (admin).
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetAdminNoticeListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of notices.
	Data []*entity.AdminNotice `json:"data" dc:"公告列表"`
}

// --- 获取公告详情(管理端) ---

// GetAdminNoticeReq defines the request structure for getting notice details (admin).
type GetAdminNoticeReq struct {
	g.Meta `path:"/admin/notices/{id}" method:"get" tags:"SystemNoticeAdmin" summary:"获取公告详情(管理端)"`
	Id     int64 `json:"id" v:"required#公告ID不能为空" dc:"公告ID"`
}

// GetAdminNoticeRes defines the response structure for getting notice details (admin).
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetAdminNoticeRes struct {
	// Data contains the notice details.
	Data *entity.AdminNotice `json:"data" dc:"公告详情"`
}

// --- 新增公告(管理端) ---

// AddAdminNoticeReq defines the request structure for adding a new notice (admin).
type AddAdminNoticeReq struct {
	g.Meta   `path:"/admin/notices" method:"post" tags:"SystemNoticeAdmin" summary:"新增公告(管理端)"`
	Title    string      `json:"title" v:"required|length:1,100#公告标题不能为空|标题长度限制1-100" dc:"公告标题"`
	Type     int64       `json:"type" v:"required|in:1,2#公告类型不能为空|类型值必须是1或2" dc:"公告类型(1:通知, 2:私信)"`
	Tag      int         `json:"tag" v:"in:0,1,2" d:"0" dc:"标签(0:普通, 1:重要, 2:活动)"`
	Content  string      `json:"content" v:"required#公告内容不能为空" dc:"公告内容 (HTML)"`
	Receiver *gjson.Json `json:"receiver" dc:"接收者ID列表 (类型为私信时必填, JSON数组 [1, 2, 3])"` // 使用 *gjson.Json 接收
	Remark   string      `json:"remark" v:"length:0,200" dc:"备注"`
	Sort     int         `json:"sort" v:"min:0" d:"100" dc:"排序"`
	Status   int         `json:"status"  v:"required|in:0,1,2#状态不能为空|状态值必须是0,1或2" d:"0" dc:"公告状态(0:草稿, 1:发布, 2:停用)"`
}

// AddAdminNoticeRes defines the response structure after adding a new notice (admin).
type AddAdminNoticeRes struct {
	// Id is the ID of the newly created notice.
	Id int64 `json:"id" dc:"新增的公告ID"`
}

// --- 编辑公告(管理端) ---

// EditAdminNoticeReq defines the request structure for editing an existing notice (admin).
type EditAdminNoticeReq struct {
	g.Meta   `path:"/admin/notices/{id}" method:"put" tags:"SystemNoticeAdmin" summary:"编辑公告(管理端)"`
	Id       int64       `json:"id" v:"required#公告ID不能为空" dc:"公告ID"` // 从路径获取
	Title    string      `json:"title" v:"required|length:1,100#公告标题不能为空|标题长度限制1-100" dc:"公告标题"`
	Type     int64       `json:"type" v:"required|in:1,2#公告类型不能为空|类型值必须是1或2" dc:"公告类型(1:通知, 2:私信)"`
	Tag      int         `json:"tag" v:"in:0,1,2" d:"0" dc:"标签(0:普通, 1:重要, 2:活动)"`
	Content  string      `json:"content" v:"required#公告内容不能为空" dc:"公告内容 (HTML)"`
	Receiver *gjson.Json `json:"receiver" dc:"接收者ID列表 (类型为私信时必填, JSON数组 [1, 2, 3])"`
	Remark   string      `json:"remark" v:"length:0,200" dc:"备注"`
	Sort     int         `json:"sort" v:"min:0" d:"100" dc:"排序"`
	Status   int         `json:"status" v:"required|in:0,1,2#状态不能为空|状态值必须是0,1或2" d:"0" dc:"公告状态(0:草稿, 1:发布, 2:停用)"`
}

// EditAdminNoticeRes defines the response structure after editing a notice (admin).
type EditAdminNoticeRes struct {
	// Typically empty on success.
}

// --- 删除公告(管理端) ---

// DeleteAdminNoticeReq defines the request structure for deleting notices (admin).
type DeleteAdminNoticeReq struct {
	g.Meta `path:"/admin/notices" method:"delete" tags:"SystemNoticeAdmin" summary:"删除公告(管理端)"`
	Ids    []int64 `json:"ids" v:"required|min-length:1#请选择要删除的公告" dc:"公告ID列表"`
}

// DeleteAdminNoticeRes defines the response structure after deleting notices (admin).
type DeleteAdminNoticeRes struct {
	// Typically empty on success.
}

// --- 获取公告已读/未读用户列表(管理端) ---

// GetAdminNoticeReadStatusReq defines the request structure for getting the read/unread user list for a notice (admin).
type GetAdminNoticeReadStatusReq struct {
	g.Meta             `path:"/admin/notices/{id}/read-status" method:"get" tags:"SystemNoticeAdmin" summary:"获取公告已读未读用户列表"`
	common.PageRequest        // 包含分页参数
	Id                 int64  `json:"id" v:"required#公告ID不能为空" dc:"公告ID"`           // 从路径获取
	ReadStatus         *int   `json:"readStatus" v:"in:0,1" dc:"阅读状态 (0:未读, 1:已读)"` // 0 未读, 1 已读
	Username           string `json:"username" dc:"用户名 (模糊查询)"`
	DeptId             *int64 `json:"deptId" dc:"部门ID"`
	DateRange          string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetAdminNoticeReadStatusRes defines the response structure for getting the read/unread user list for a notice (admin).
type GetAdminNoticeReadStatusRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of users (filtered by readStatus if provided).
	Data []*UserInfo `json:"data" dc:"用户列表"`
}

// --- 获取用户列表供选择器使用(管理端) ---

// GetMemberListForNoticeReq defines the request structure for getting a member list for notice selection (admin).
type GetMemberListForNoticeReq struct {
	g.Meta    `path:"/admin/notices/members/selector" method:"get" tags:"SystemNoticeAdmin" summary:"获取用户列表(公告选择器)"` // More descriptive path
	common.PageRequest
	Username  string `json:"username" dc:"用户名 (模糊查询)"`
	DeptId    *int64 `json:"deptId" dc:"部门ID"`
	RoleId    *int64 `json:"roleId" dc:"角色ID"`
	PostId    *int64 `json:"postId" dc:"岗位ID"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetMemberListForNoticeRes defines the response structure for getting the member list for notice selection (admin).
type GetMemberListForNoticeRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of users suitable for selection.
	Data []*UserInfo `json:"data" dc:"用户列表"`
}

// --- User 端 API ---

// --- 获取我的公告列表(用户端) ---

// NoticeListItem defines the structure for an item in the user's notice list.
type NoticeListItem struct {
	*entity.AdminNotice // Embeds the base notice entity.
	// IsRead indicates whether the current user has read this notice.
	IsRead bool `json:"isRead"`
	// ReadClicks indicates how many times the user has read this notice.
	ReadClicks int `json:"readClicks"`
}

// GetMyNoticeListReq defines the request structure for getting the current user's notice list.
type GetMyNoticeListReq struct {
	g.Meta    `path:"/my/notices" method:"get" tags:"SystemNoticeUser" summary:"获取我的公告列表"`
	common.PageRequest
	Type      *int   `json:"type" dc:"公告类型 (1:通知, 2:私信)"`
	Tag       *int   `json:"tag" dc:"标签 (0:普通, 1:重要, 2:活动)"`
	IsRead    *int   `json:"isRead" v:"in:0,1" dc:"阅读状态 (0:未读, 1:已读)"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetMyNoticeListRes defines the response structure for getting the current user's notice list.
type GetMyNoticeListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of notices for the user.
	Data []*NoticeListItem `json:"data" dc:"公告列表"`
}

// --- 标记公告已读(用户端) ---

// MarkNoticeReadReq defines the request structure for marking a notice as read by the current user.
// Consider using PUT or PATCH on /my/notices/{noticeId}/read-status instead.
type MarkNoticeReadReq struct {
	g.Meta   `path:"/my/notices/read-status" method:"post" tags:"SystemNoticeUser" summary:"标记公告已读"` // Path adjusted, method POST is less RESTful here.
	NoticeId int64                                                                                   `json:"noticeId" v:"required#公告ID不能为空" dc:"公告ID"`
}

// MarkNoticeReadRes defines the response structure after marking a notice as read.
type MarkNoticeReadRes struct {
	// Typically empty on success.
}

// --- 获取我的未读公告数(用户端) ---

// GetMyUnreadNoticeCountReq defines the request structure for getting the current user's unread notice count.
type GetMyUnreadNoticeCountReq struct {
	g.Meta `path:"/my/notices/unread-count" method:"get" tags:"SystemNoticeUser" summary:"获取我的未读公告数"`
	// 通常不需要参数
}

// GetMyUnreadNoticeCountRes defines the response structure for getting the unread notice count.
type GetMyUnreadNoticeCountRes struct {
	// Count is the number of unread notices for the current user.
	Count int `json:"count" dc:"未读公告数量"`
}
