package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// 兑换订单相关请求定义

// ExchangeOrderListReq 兑换订单列表请求
type ExchangeOrderListReq struct {
	g.<PERSON>a    `path:"/exchange/orders" method:"get" tags:"ExchangeOrder" summary:"获取兑换订单列表"`
	OrderSn   string `json:"orderSn" dc:"订单号"`
	UserId    uint64 `json:"userId" dc:"用户ID"`
	ProductId uint   `json:"productId" dc:"产品ID"`
	Symbol    string `json:"symbol" dc:"交易对符号"`
	TradeType string `json:"tradeType" dc:"交易类型(buy/sell)"`
	Status    string `json:"status" dc:"订单状态"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export    int    `json:"export" d:"0" dc:"是否导出:0不导出,1导出"`
	Page      int    `json:"page" d:"1" dc:"页码"`
	PageSize  int    `json:"pageSize" d:"20" dc:"每页数量"`

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram信息查询
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// ExchangeOrderListRes 兑换订单列表响应
type ExchangeOrderListRes struct {
	List     []*ExchangeOrderItem `json:"list" dc:"订单列表"`
	Total    int                  `json:"total" dc:"总数"`
	Page     int                  `json:"page" dc:"当前页"`
	PageSize int                  `json:"pageSize" dc:"每页数量"`
}

// ExchangeOrderItem 兑换订单项
type ExchangeOrderItem struct {
	OrderId               uint64          `json:"orderId" dc:"订单ID"`
	OrderSn               string          `json:"orderSn" dc:"订单号"`
	UserId                uint64          `json:"userId" dc:"用户ID"`
	ProductId             uint            `json:"productId" dc:"产品ID"`
	BaseToken             string          `json:"baseToken" dc:"基础代币"`
	QuoteToken            string          `json:"quoteToken" dc:"计价代币"`
	Symbol                string          `json:"symbol" dc:"交易对符号"`
	TradeType             string          `json:"tradeType" dc:"交易类型"`
	AmountBase            decimal.Decimal `json:"amountBase" dc:"基础代币数量（截断后）"`
	AmountQuote           decimal.Decimal `json:"amountQuote" dc:"计价代币数量（截断后）"`
	OriginalFromAmount    decimal.Decimal `json:"originalFromAmount" dc:"原始兑换金额（未截断）"`
	OriginalToAmount      decimal.Decimal `json:"originalToAmount" dc:"原始收到金额（未截断）"`
	Price                 decimal.Decimal `json:"price" dc:"成交价格"`
	SpreadAmount          decimal.Decimal `json:"spreadAmount" dc:"点差金额"`
	SpreadRate            decimal.Decimal `json:"spreadRate" dc:"点差率"`
	FeeAmount             decimal.Decimal `json:"feeAmount" dc:"手续费金额"`
	FeeTokenId            uint            `json:"feeTokenId" dc:"手续费币种ID"`
	OutputAmountBeforeFee decimal.Decimal `json:"outputAmountBeforeFee" dc:"扣费前输出金额"`
	OutputAmountAfterFee  decimal.Decimal `json:"outputAmountAfterFee" dc:"扣费后输出金额"`
	FeeCalculationMethod  string          `json:"feeCalculationMethod" dc:"手续费计算方法"`
	TransactionHash       string          `json:"transactionHash" dc:"交易哈希"`
	QuoteId               string          `json:"quoteId" dc:"关联的报价ID"`
	Status                string          `json:"status" dc:"订单状态"`
	ErrorMessage          string          `json:"errorMessage" dc:"错误信息"`
	ClientOrderId         string          `json:"clientOrderId" dc:"客户端订单ID"`
	CreatedAt             string          `json:"createdAt" dc:"创建时间"`
	ExecutedAt            string          `json:"executedAt" dc:"实际执行时间"`
	CompletedAt           string          `json:"completedAt" dc:"完成时间"`
	UpdatedAt             string          `json:"updatedAt" dc:"更新时间"`

	// 用户信息
	Account  string `json:"account" dc:"用户账号"`

	// 三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// ExchangeOrderDetailReq 获取兑换订单详情请求
type ExchangeOrderDetailReq struct {
	g.Meta  `path:"/exchange/orders/{orderId}" method:"get" tags:"ExchangeOrder" summary:"获取兑换订单详情"`
	OrderId uint64 `json:"orderId" in:"path" dc:"订单ID"`
}

// ExchangeOrderDetailRes 获取兑换订单详情响应
type ExchangeOrderDetailRes struct {
	*ExchangeOrderItem
}
