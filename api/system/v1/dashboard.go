package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// GetDashboardStatsReq defines the request structure for getting dashboard statistics.
type GetDashboardStatsReq struct {
	g.Meta `path:"/dashboard/stats" method:"get" tags:"SystemDashboard" summary:"获取管理后台首页统计信息"`
}

// GetDashboardStatsRes defines the response structure for dashboard statistics.
type GetDashboardStatsRes struct {
	// User statistics
	UserStats UserStats `json:"userStats" dc:"用户统计信息"`

	// Token deposit statistics
	DepositStats TokenAmountStats `json:"depositStats" dc:"充值统计信息"`

	// Token withdrawal statistics
	WithdrawalStats WithdrawalStats `json:"withdrawalStats" dc:"提现统计信息"`

	// Transfer statistics
	TransferStats TransferStats `json:"transferStats" dc:"转账统计信息"`

	// Payment statistics
	PaymentStats TokenAmountStats `json:"paymentStats" dc:"收款统计信息"`

	// Red packet statistics
	RedPacketStats RedPacketStats `json:"redPacketStats" dc:"红包统计信息"`
}

// UserStats defines user-related statistics.
type UserStats struct {
	TotalCount      int `json:"totalCount" dc:"用户总数量"`
	TodayLoginCount int `json:"todayLoginCount" dc:"今日登录用户数量"`
	TodayRegCount   int `json:"todayRegCount" dc:"今日注册用户数量"`
}

// TokenAmount defines a token name and amount pair.
type TokenAmount struct {
	Name   string  `json:"name" dc:"代币名称"`
	Amount float64 `json:"amount" dc:"金额"`
}

// TokenAmountStats defines token amount statistics with total and today's amounts.
type TokenAmountStats struct {
	Total []TokenAmount `json:"total" dc:"累计金额"`
	Today []TokenAmount `json:"today" dc:"今日金额"`
}

// WithdrawalStats extends TokenAmountStats with pending review amounts.
type WithdrawalStats struct {
	TokenAmountStats
	PendingReview []TokenAmount `json:"pendingReview" dc:"待审核金额"`
}

// TransferStats extends TokenAmountStats with pending collection amounts.
type TransferStats struct {
	TokenAmountStats
	PendingCollection []TokenAmount `json:"pendingCollection" dc:"待领取金额"`
}

// RedPacketStats defines red packet statistics by token.
type RedPacketStats struct {
	Total             []TokenAmount `json:"total" dc:"累计红包金额"`
	Today             []TokenAmount `json:"today" dc:"今日红包金额"`
	PendingCollection []TokenAmount `json:"pendingCollection" dc:"待领取金额"`
}
