package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// MemberListItem defines the structure for an item in the member list, including associated information.
type MemberListItem struct {
	*entity.AdminMember // Embeds the base member information.
	// PrimaryRoleName is the name of the member's primary role.
	PrimaryRoleName string `json:"primaryRoleName"`
	// PostNames is a list of names of the member's assigned posts.
	PostNames []string `json:"postNames"`
}

// MemberDetail defines the structure for member details, including full associated information.
type MemberDetail struct {
	*entity.AdminMember
	// Roles contains information about all roles assigned to the member.
	Roles []*entity.AdminRole `json:"roles"`
	// Posts contains information about all posts assigned to the member.
	Posts []*entity.AdminPost `json:"posts"`
}

// --- 获取用户列表 ---

// GetMemberListReq defines the request structure for getting the member list.
type GetMemberListReq struct {
	g.Meta    `path:"/members" method:"get" tags:"SystemMember" summary:"获取用户列表"`
	common.PageRequest
	Username  string `json:"username"  dc:"用户名 (模糊查询)"`
	RealName  string `json:"realName" dc:"真实姓名 (模糊查询)"`
	Mobile    string `json:"mobile" dc:"手机号 (精确查询)"`
	Status    *int   `json:"status" dc:"状态 (0:禁用, 1:启用)"`
	RoleId    *int64 `json:"roleId" dc:"角色ID"`
	PostId    *int64 `json:"postId" dc:"岗位ID"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetMemberListRes defines the response structure for getting the member list.
type GetMemberListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of members.
	Data []*MemberListItem `json:"data" dc:"用户列表"`
}

// --- 获取用户详情 ---

// GetMemberReq defines the request structure for getting member details.
type GetMemberReq struct {
	g.Meta `path:"/members/{id}" method:"get" tags:"SystemMember" summary:"获取用户详情"`
	Id     int64 `json:"id"  v:"required#用户ID不能为空" dc:"用户ID"`
}

// GetMemberRes defines the response structure for getting member details.
type GetMemberRes struct {
	// Data contains the member details.
	Data *MemberDetail `json:"data" dc:"用户详情"`
}

// --- 新增用户 ---

// AddMemberReq defines the request structure for adding a new member.
type AddMemberReq struct {
	g.Meta        `path:"/members" method:"post" tags:"SystemMember" summary:"新增用户"`
	Username      string  `json:"username" v:"required|length:4,20#用户名不能为空|用户名长度需在4-20之间" dc:"用户名 (唯一)"`
	Password      string  `json:"password" v:"required|length:6,32#密码不能为空|密码长度需在6-32之间" dc:"密码"`
	RealName      string  `json:"realName" v:"length:0,50" dc:"真实姓名"`
	RoleIds       []int64 `json:"roleIds" v:"required|min-length:1#请至少选择一个角色" dc:"角色ID列表"`
	PrimaryRoleId int64   `json:"primaryRoleId" v:"required|integer#主角色ID不能为空|主角色ID必须为整数" dc:"主角色ID (必须在RoleIds中)"` // 校验是否在 RoleIds 中在 Logic 层处理
	PostIds       []int64 `json:"postIds" dc:"岗位ID列表"`
	Pid           int64   `json:"pid" v:"integer" d:"0" dc:"上级管理员ID (0表示无上级)"` // 校验上级有效性和循环引用在 Logic 层处理
	Email         string  `json:"email"  v:"required|email#邮箱不能为空|邮箱格式不正确" dc:"邮箱 (唯一)"`
	Mobile        string  `json:"mobile" v:"integer#手机号不能为空|手机号格式不正确" dc:"手机号 (唯一)"`
	Avatar        string  `json:"avatar" v:"url" dc:"头像URL"`
	InviteCode    string  `json:"inviteCode" v:"length:0,10" dc:"邀请码 (唯一, 可选)"` // 唯一性校验在 Logic 层处理
	Remark        string  `json:"remark" v:"length:0,200" dc:"备注"`
	Status        int     `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"状态(0:禁用, 1:启用)"`
}

// AddMemberRes defines the response structure after adding a new member.
type AddMemberRes struct {
	// Id is the ID of the newly created member.
	Id int64 `json:"id" dc:"新增的用户ID"`
}

// --- 编辑用户 ---

// EditMemberReq defines the request structure for editing an existing member.
type EditMemberReq struct {
	g.Meta        `path:"/members/{id}" method:"put" tags:"SystemMember" summary:"编辑用户"`
	Id            int64   `json:"id"  v:"required#用户ID不能为空" dc:"用户ID"` // 从路径获取
	RealName      string  `json:"realName" v:"length:0,50" dc:"真实姓名"`
	RoleIds       []int64 `json:"roleIds" v:"required|min-length:1#请至少选择一个角色" dc:"角色ID列表"`
	PrimaryRoleId int64   `json:"primaryRoleId" v:"required|integer#主角色ID不能为空|主角色ID必须为整数" dc:"主角色ID (必须在RoleIds中)"`
	PostIds       []int64 `json:"postIds" dc:"岗位ID列表"`
	Pid           int64   `json:"pid" v:"integer" d:"0" dc:"上级管理员ID (0表示无上级)"`
	Email         string  `json:"email" v:"required|email#邮箱不能为空|邮箱格式不正确" dc:"邮箱 (唯一)"`
	Mobile        string  `json:"mobile" v:"integer#手机号不能为空|手机号格式不正确" dc:"手机号 (唯一)"`
	Avatar        string  `json:"avatar" v:"url" dc:"头像URL"`
	InviteCode    string  `json:"inviteCode" v:"length:0,10" dc:"邀请码 (唯一, 可选)"`
	Remark        string  `json:"remark" v:"length:0,200" dc:"备注"`
	Status        int     `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"状态(0:禁用, 1:启用)"`
	// Username 通常不允许修改
}

// EditMemberRes defines the response structure after editing a member.
type EditMemberRes struct {
	// Typically empty on success.
}

// --- 删除用户 ---

// DeleteMemberReq defines the request structure for deleting members.
type DeleteMemberReq struct {
	g.Meta `path:"/members" method:"delete" tags:"SystemMember" summary:"删除用户"`
	Ids    []int64 `json:"ids" v:"required|min-length:1#请选择要删除的用户" dc:"用户ID列表"`
}

// DeleteMemberRes defines the response structure after deleting members.
type DeleteMemberRes struct {
	// Typically empty on success.
}

// --- 更新用户状态 ---

// UpdateMemberStatusReq defines the request structure for updating a member's status.
// Consider using PATCH for partial updates.
type UpdateMemberStatusReq struct {
	g.Meta `path:"/members/{id}/status" method:"put" tags:"SystemMember" summary:"更新用户状态"`
	Id     int64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"` // 从路径获取
	Status int   `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" dc:"状态(0:禁用, 1:启用)"`
}

// UpdateMemberStatusRes defines the response structure after updating a member's status.
type UpdateMemberStatusRes struct {
	// Typically empty on success.
}

// --- 重置用户密码 ---

// ResetMemberPasswordReq defines the request structure for resetting a member's password.
type ResetMemberPasswordReq struct {
	g.Meta   `path:"/members/{id}/password" method:"put" tags:"SystemMember" summary:"重置用户密码"` // Changed path slightly for consistency
	Id       int64                                                                             `json:"id" v:"required#用户ID不能为空" dc:"用户ID"` // 从路径获取
	Password string                                                                            `json:"password" v:"required|length:6,32#密码不能为空|密码长度需在6-32之间" dc:"新密码"`
}

// ResetMemberPasswordRes defines the response structure after resetting a member's password.
type ResetMemberPasswordRes struct {
	// Typically empty on success.
}

// --- 分配用户角色 ---

// AssignRolesToUserReq defines the request structure for assigning roles to a user.
type AssignRolesToUserReq struct {
	g.Meta   `path:"/members/{id}/roles" method:"put" tags:"SystemMember" summary:"分配用户角色"` // 使用 PUT 方法更新用户角色
	Id       int64                                                                          `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`             // 从路径获取
	RoleKeys []string                                                                       `json:"roleKeys" v:"required|min-length:0#角色Key列表不能为空"` // 允许传空数组以清空角色
}

// AssignRolesToUserRes defines the response structure after assigning roles to a user.
type AssignRolesToUserRes struct {
	// Typically empty on success.
}
