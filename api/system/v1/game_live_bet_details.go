package v1

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// GetGameLiveBetDetailsListReq defines the request for game live bet details list
type GetGameLiveBetDetailsListReq struct {
	g.Meta          `path:"/game-live-bet-details/list" method:"get" tags:"GameLiveBetDetails" summary:"Get game live bet details list"`
	Username        string `json:"username" dc:"Filter by username"`
	GameCode        string `json:"gameCode" dc:"Filter by game code"`
	GameCategory    string `json:"gameCategory" dc:"Filter by game category"`
	BetOrderNo      string `json:"betOrderNo" dc:"Filter by bet order number"`
	SessionId       string `json:"sessionId" dc:"Filter by session ID"`
	Currency        string `json:"currency" dc:"Filter by currency"`
	ProductType     *int   `json:"productType" dc:"Filter by product type"`
	ApiStatus       *int   `json:"apiStatus" dc:"Filter by API status"`
	BetTimeStart    string `json:"betTimeStart" dc:"Filter by bet time start (format: 2006-01-02 15:04:05)"`
	BetTimeEnd      string `json:"betTimeEnd" dc:"Filter by bet time end (format: 2006-01-02 15:04:05)"`
	MinBetAmount    string `json:"minBetAmount" dc:"Filter by minimum bet amount"`
	MaxBetAmount    string `json:"maxBetAmount" dc:"Filter by maximum bet amount"`
	MinWinAmount    string `json:"minWinAmount" dc:"Filter by minimum win amount"`
	MaxWinAmount    string `json:"maxWinAmount" dc:"Filter by maximum win amount"`
	TelegramId      *int64 `json:"telegramId" dc:"Filter by Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"Filter by Telegram username"`
	FirstName       string `json:"firstName" dc:"Filter by first name"`
	TenantUsername  string `json:"tenantUsername" dc:"Filter by tenant username"`
	Page            int    `json:"page" dc:"Page number" d:"1" v:"min:1"`
	PageSize        int    `json:"pageSize" dc:"Page size" d:"20" v:"min:1|max:100"`
}

// GetGameLiveBetDetailsListRes defines the response for game live bet details list
type GetGameLiveBetDetailsListRes struct {
	Total int                            `json:"total" dc:"Total count"`
	List  []*GameLiveBetDetailsListItem `json:"list" dc:"Game live bet details list"`
}

// GameLiveBetDetailsListItem defines the game live bet details list item
type GameLiveBetDetailsListItem struct {
	Id                uint64          `json:"id" dc:"Record ID"`
	Username          string          `json:"username" dc:"Game account username"`
	BetAmount         decimal.Decimal `json:"betAmount" dc:"Bet amount"`
	ValidBetAmount    decimal.Decimal `json:"validBetAmount" dc:"Valid bet amount"`
	WinAmount         decimal.Decimal `json:"winAmount" dc:"Win amount"`
	NetPnl            decimal.Decimal `json:"netPnl" dc:"Net profit/loss (positive for win, negative for loss)"`
	Currency          string          `json:"currency" dc:"Currency (e.g., CNY, USD)"`
	GameCode          string          `json:"gameCode" dc:"Game code"`
	ProductType       int             `json:"productType" dc:"Product type (191=Live game)"`
	GameCategory      string          `json:"gameCategory" dc:"Game category"`
	BetOrderNo        string          `json:"betOrderNo" dc:"Bet order number"`
	SessionId         string          `json:"sessionId" dc:"Session ID"`
	BetTime           *gtime.Time     `json:"betTime" dc:"Bet time"`
	TransactionTime   *gtime.Time     `json:"transactionTime" dc:"Transaction time"`
	AdditionalDetails *gjson.Json     `json:"additionalDetails" dc:"Additional bet details"`
	ApiStatus         int             `json:"apiStatus" dc:"API response status code"`
	ApiErrorDesc      string          `json:"apiErrorDesc" dc:"API error description"`
	CreatedAt         *gtime.Time     `json:"createdAt" dc:"Creation time"`
	UpdatedAt         *gtime.Time     `json:"updatedAt" dc:"Update time"`
	TelegramId        *int64          `json:"telegramId" dc:"Telegram ID"`
	TelegramUsername  *string         `json:"telegramUsername" dc:"Telegram username"`
	FirstName         *string         `json:"firstName" dc:"First name"`
	TenantUsername    *string         `json:"tenantUsername" dc:"Tenant username"`
}

// GetGameLiveBetDetailsDetailReq defines the request for game live bet details detail
type GetGameLiveBetDetailsDetailReq struct {
	g.Meta `path:"/game-live-bet-details/detail" method:"get" tags:"GameLiveBetDetails" summary:"Get game live bet details detail"`
	Id     uint64 `json:"id" dc:"Record ID" v:"required|min:1"`
}

// GetGameLiveBetDetailsDetailRes defines the response for game live bet details detail
type GetGameLiveBetDetailsDetailRes struct {
	*GameLiveBetDetailsListItem
}

// GetGameLiveBetDetailsStatsReq defines the request for game live bet details statistics
type GetGameLiveBetDetailsStatsReq struct {
	g.Meta       `path:"/game-live-bet-details/stats" method:"get" tags:"GameLiveBetDetails" summary:"Get game live bet details statistics"`
	Username     string `json:"username" dc:"Filter by username"`
	GameCode     string `json:"gameCode" dc:"Filter by game code"`
	GameCategory string `json:"gameCategory" dc:"Filter by game category"`
	Currency     string `json:"currency" dc:"Filter by currency"`
	ProductType  *int   `json:"productType" dc:"Filter by product type"`
	BetTimeStart string `json:"betTimeStart" dc:"Filter by bet time start (format: 2006-01-02 15:04:05)"`
	BetTimeEnd   string `json:"betTimeEnd" dc:"Filter by bet time end (format: 2006-01-02 15:04:05)"`
}

// GetGameLiveBetDetailsStatsRes defines the response for game live bet details statistics
type GetGameLiveBetDetailsStatsRes struct {
	TotalRecords      int             `json:"totalRecords" dc:"Total number of records"`
	TotalBetAmount    decimal.Decimal `json:"totalBetAmount" dc:"Total bet amount"`
	TotalValidBet     decimal.Decimal `json:"totalValidBet" dc:"Total valid bet amount"`
	TotalWinAmount    decimal.Decimal `json:"totalWinAmount" dc:"Total win amount"`
	TotalNetPnl       decimal.Decimal `json:"totalNetPnl" dc:"Total net profit/loss"`
	UniqueUsers       int             `json:"uniqueUsers" dc:"Number of unique users"`
	UniqueGames       int             `json:"uniqueGames" dc:"Number of unique games"`
	WinRate           float64         `json:"winRate" dc:"Win rate percentage"`
	AvgBetAmount      decimal.Decimal `json:"avgBetAmount" dc:"Average bet amount"`
	AvgWinAmount      decimal.Decimal `json:"avgWinAmount" dc:"Average win amount"`
}

// ExportGameLiveBetDetailsReq defines the request for exporting game live bet details
type ExportGameLiveBetDetailsReq struct {
	g.Meta          `path:"/game-live-bet-details/export" method:"post" tags:"GameLiveBetDetails" summary:"Export game live bet details"`
	Username        string `json:"username" dc:"Filter by username"`
	GameCode        string `json:"gameCode" dc:"Filter by game code"`
	GameCategory    string `json:"gameCategory" dc:"Filter by game category"`
	BetOrderNo      string `json:"betOrderNo" dc:"Filter by bet order number"`
	SessionId       string `json:"sessionId" dc:"Filter by session ID"`
	Currency        string `json:"currency" dc:"Filter by currency"`
	ProductType     *int   `json:"productType" dc:"Filter by product type"`
	ApiStatus       *int   `json:"apiStatus" dc:"Filter by API status"`
	BetTimeStart    string `json:"betTimeStart" dc:"Filter by bet time start (format: 2006-01-02 15:04:05)"`
	BetTimeEnd      string `json:"betTimeEnd" dc:"Filter by bet time end (format: 2006-01-02 15:04:05)"`
	MinBetAmount    string `json:"minBetAmount" dc:"Filter by minimum bet amount"`
	MaxBetAmount    string `json:"maxBetAmount" dc:"Filter by maximum bet amount"`
	MinWinAmount    string `json:"minWinAmount" dc:"Filter by minimum win amount"`
	MaxWinAmount    string `json:"maxWinAmount" dc:"Filter by maximum win amount"`
	ExportFormat    string `json:"exportFormat" dc:"Export format (excel/csv)" d:"excel" v:"in:excel,csv"`
}

// ExportGameLiveBetDetailsRes defines the response for exporting game live bet details
type ExportGameLiveBetDetailsRes struct {
	DownloadUrl string `json:"downloadUrl" dc:"Download URL for the exported file"`
	FileName    string `json:"fileName" dc:"Name of the exported file"`
}