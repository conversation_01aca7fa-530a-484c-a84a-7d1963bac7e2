package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 获取我的回调记录列表 ---

// GetMyCallbacksReq defines the request structure for getting merchant's own callback records.
type GetMyCallbacksReq struct {
	g.Meta `path:"/my/callbacks" method:"get" tags:"MerchantCallbacks" summary:"获取我的回调记录"`
	common.PageRequest
	MerchantId   *uint64  `json:"merchantId" dc:"商户ID (可选，不传则查询所有商户)"`
	CallbackType string   `json:"callbackType" dc:"回调类型 (deposit_success, withdraw_success等)"`
	Status       string   `json:"status" dc:"状态 (pending-待发送, success-成功, failed-失败)"`
	RelatedId    *uint64  `json:"relatedId" dc:"关联记录ID (如交易ID)"`
	DateRange    string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt    []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export       bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantCallbackInfoType defines the structure for callback information in a list.
type MerchantCallbackInfoType struct {
	Id            uint64      `json:"id" dc:"回调记录ID"`
	MerchantId    uint64      `json:"merchantId" dc:"商户ID"`
	MerchantName  string      `json:"merchantName" dc:"商户名称"`
	CallbackType  string      `json:"callbackType" dc:"回调事件类型"`
	RelatedId     uint64      `json:"relatedId" dc:"关联记录ID"`
	CallbackUrl   string      `json:"callbackUrl" dc:"回调URL"`
	Status        string      `json:"status" dc:"回调状态"`
	StatusText    string      `json:"statusText" dc:"状态描述"`
	RetryCount    int         `json:"retryCount" dc:"重试次数"`
	ResponseCode  int         `json:"responseCode" dc:"HTTP响应状态码"`
	LastAttemptAt *gtime.Time `json:"lastAttemptAt" dc:"最后尝试时间"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt     *gtime.Time `json:"updatedAt" dc:"更新时间"`
}

// GetMyCallbacksRes defines the response structure for getting merchant's own callback records.
type GetMyCallbacksRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of callback records.
	Data []*MerchantCallbackInfoType `json:"data" dc:"回调记录列表数据"`
}

// --- 获取我的回调记录详情 ---

// GetMyCallbackDetailReq defines the request structure for getting specific callback record details.
type GetMyCallbackDetailReq struct {
	g.Meta `path:"/my/callbacks/{id}" method:"get" tags:"MerchantCallbacks" summary:"获取我的回调记录详情"`
	Id     uint64 `json:"id" v:"required#回调记录ID不能为空" dc:"回调记录ID"`
}

// MerchantCallbackDetailType defines the structure for detailed callback information.
type MerchantCallbackDetailType struct {
	entity.MerchantCallbacks
	StatusText string `json:"statusText" dc:"状态描述"`
}

// GetMyCallbackDetailRes defines the response structure for getting callback record details.
type GetMyCallbackDetailRes struct {
	// Data contains the callback record details.
	Data *MerchantCallbackDetailType `json:"data" dc:"回调记录详情数据"`
}

// --- 重试回调 ---

// RetryCallbackReq defines the request structure for retrying a failed callback.
type RetryCallbackReq struct {
	g.Meta `path:"/my/callbacks/{id}/retry" method:"post" tags:"MerchantCallbacks" summary:"重试失败的回调"`
	Id     uint64 `json:"id" v:"required#回调记录ID不能为空" dc:"回调记录ID"`
}

// RetryCallbackRes defines the response structure for retrying a callback.
type RetryCallbackRes struct {
	// Success indicates whether the retry was successful.
	Success bool   `json:"success" dc:"是否重试成功"`
	Message string `json:"message" dc:"处理结果消息"`
}
