package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity" // 确保导入 entity 包

	"github.com/gogf/gf/v2/frame/g"
)

// PermissionTreeNode represents a node in the permission tree structure.
type PermissionTreeNode struct {
	*entity.AdminPermissions                       // 嵌入 AdminPermissions 实体
	Children                 []*PermissionTreeNode `json:"children"`
}

// --- 获取权限列表 ---

// GetPermissionListReq defines the request structure for getting the permission list.
type GetPermissionListReq struct {
	g.Meta    `path:"/permissions" method:"get" tags:"SystemPermission" summary:"获取权限列表"`
	common.PageRequest
	Name      string `json:"name"   dc:"权限名称 (模糊查询)"`
	Key       string `json:"key"    dc:"权限标识 Key (模糊查询)"`
	Status    *int   `json:"status" dc:"权限状态 (0:禁用, 1:启用)"` // 使用指针区分未传和传0
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetPermissionListRes defines the response structure for getting the permission list.
type GetPermissionListRes struct {
	Page common.PageResponse   `json:"page" dc:"分页信息"`
	Data []*PermissionTreeNode `json:"data" dc:"权限树形列表"`
}

// --- 获取全部权限 ---

// GetAllPermissionListReq defines the request structure for getting all permissions.
type GetAllPermissionListReq struct {
	g.Meta `path:"/permissions/all" method:"get" tags:"SystemPermission" summary:"获取全部权限"`
}

// GetAllPermissionListRes defines the response structure for getting all permissions.
type GetAllPermissionListRes struct {
	Data []*PermissionTreeNode `json:"data" dc:"权限树形列表"`
}

// --- 新增权限 ---

// AddPermissionReq defines the request structure for adding a new permission item.
type AddPermissionReq struct {
	g.Meta `path:"/permissions" method:"post" tags:"SystemPermission" summary:"新增权限"`
	Pid    int64  `json:"pid"    v:"required" dc:"父权限ID (0为根权限)"`
	Name   string `json:"name"   v:"required|length:1,100#权限名称不能为空|名称长度限制1-100" dc:"权限名称"`
	Key    string `json:"key"    v:"required|length:1,100#权限标识Key不能为空|Key长度限制1-100" dc:"权限标识Key (全局唯一)"`
	Type   int    `json:"type"   v:"required|in:1,2,3,4#权限类型不能为空|类型值必须是1,2,3或4" dc:"权限类型 (1:目录, 2:菜单, 3:按钮, 4:API)"`
	Remark string `json:"remark" v:"length:0,200" dc:"备注"`
	Status int    `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"权限状态(0:禁用, 1:启用)"`
	Sort   int    `json:"sort"   v:"required|min:0#排序值不能为空|排序值不能为负" d:"0" dc:"排序"`
}

// AddPermissionRes defines the response structure after adding a new permission item.
type AddPermissionRes struct {
	Id int64 `json:"id" dc:"新增的权限ID"`
}

// --- 编辑权限 ---

// EditPermissionReq defines the request structure for editing an existing permission item.
type EditPermissionReq struct {
	g.Meta `path:"/permissions/{id}" method:"put" tags:"SystemPermission" summary:"编辑权限"`
	Id     int64  `json:"id"     v:"required#权限ID不能为空" dc:"权限ID"` // 从路径获取
	Pid    int64  `json:"pid"    v:"required" dc:"父权限ID (0为根权限)"`
	Name   string `json:"name"   v:"required|length:1,100#权限名称不能为空|名称长度限制1-100" dc:"权限名称"`
	Key    string `json:"key"    v:"required|length:1,100#权限标识Key不能为空|Key长度限制1-100" dc:"权限标识Key (全局唯一)"`
	Type   int    `json:"type"   v:"required|in:1,2,3,4#权限类型不能为空|类型值必须是1,2,3或4" dc:"权限类型 (1:目录, 2:菜单, 3:按钮, 4:API)"`
	Remark string `json:"remark" v:"length:0,200" dc:"备注"`
	Status int    `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"权限状态(0:禁用, 1:启用)"`
	Sort   int    `json:"sort"   v:"required|min:0#排序值不能为空|排序值不能为负" d:"0" dc:"排序"`
}

// EditPermissionRes defines the response structure after editing a permission item.
type EditPermissionRes struct {
	// Typically empty on success.
}

// --- 删除权限 ---

// DeletePermissionReq defines the request structure for deleting a permission item.
type DeletePermissionReq struct {
	g.Meta `path:"/permissions/{id}" method:"delete" tags:"SystemPermission" summary:"删除权限"`
	Id     int64 `json:"id" v:"required#权限ID不能为空" dc:"权限ID"` // 从路径获取
}

// DeletePermissionRes defines the response structure after deleting a permission item.
type DeletePermissionRes struct {
	// Typically empty on success.
}

// --- 同步菜单权限 ---

// SyncMenuPermissionsReq defines the request for syncing menu permissions.
type SyncMenuPermissionsReq struct {
	g.Meta `path:"/menus/sync-permissions" method:"post" tags:"SystemPermission" summary:"一键同步菜单到权限表"`
	// 可以添加过滤条件，例如：
	// OnlySyncActive bool `json:"onlySyncActive" dc:"是否只同步启用状态的菜单"`
}

// SyncMenuPermissionsRes defines the response for syncing menu permissions.
type SyncMenuPermissionsRes struct {
	CreatedCount  int `json:"createdCount" dc:"本次新创建的权限数量"`
	ExistingCount int `json:"existingCount" dc:"已存在的权限数量"`
	TotalScanned  int `json:"totalScanned" dc:"总共扫描的菜单数量"`
}

// --- 同步API权限 ---

// SyncApiPermissionsReq defines the request for syncing API permissions.
type SyncApiPermissionsReq struct {
	g.Meta `path:"/apis/sync-permissions" method:"post" tags:"SystemPermission" summary:"一键同步API到权限表"`
	// 可以添加过滤条件，例如：
	// OnlySyncActive bool `json:"onlySyncActive" dc:"是否只同步启用状态的API"`
}

// SyncApiPermissionsRes defines the response for syncing API permissions.
type SyncApiPermissionsRes struct {
	CreatedCount  int `json:"createdCount" dc:"本次新创建的权限数量"`
	ExistingCount int `json:"existingCount" dc:"已存在的权限数量"`
	TotalScanned  int `json:"totalScanned" dc:"总共扫描的API数量"`
}
