package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// --- Wallet API Definitions ---

// --- 获取钱包余额 ---

// GetWalletBalanceReq 获取钱包余额请求
// Note: This is an example API definition. Wallet operations might primarily be internal.
// GetWalletBalanceReq defines the request structure for getting wallet balance.
type GetWalletBalanceReq struct {
	g.Meta `path:"/wallets/balance" method:"get" tags:"SystemWallet" summary:"获取钱包余额"` // Path changed to plural
	// UserId is the ID of the user whose wallet balance is being requested.
	UserId uint32 `json:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
	// Symbol is the symbol of the token for which the balance is requested.
	Symbol string `json:"symbol" v:"required#代币符号不能为空" dc:"代币符号"`
}

// GetWalletBalanceRes defines the response structure for the wallet balance query.
type GetWalletBalanceRes struct {
	// AvailableBalance is the available balance as a formatted string.
	AvailableBalance string `json:"availableBalance" dc:"可用余额 (格式化后的字符串)"`
	// FrozenBalance is the frozen balance as a formatted string.
	FrozenBalance string `json:"frozenBalance"    dc:"冻结余额 (格式化后的字符串)"`
	// DecimalPlaces indicates the number of decimal places for the token.
	DecimalPlaces uint8 `json:"decimalPlaces"    dc:"代币小数位数"`
}

// --- 钱包列表 ---

// ListWalletsReq defines the request structure for getting the wallet list.
type ListWalletsReq struct {
	g.Meta    `path:"/wallets" method:"get" tags:"SystemWallet" summary:"获取钱包列表"`
	common.PageRequest
	Account   string `json:"account,omitempty" dc:"用户账号 (可选)"`
	Symbol    string `json:"symbol,omitempty" dc:"币种名称 (可选, 模糊查询)"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	
	// 新增：三级代理模糊查询
	FirstAgentName     string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName    string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName     string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`
	
	// 新增：telegram信息查询
	TelegramId         string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername   string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName          string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
	
	// 导出标识
	Export int `json:"export" dc:"导出标识 (1: 导出Excel)"`
}

// ListWalletsRes defines the response structure for the wallet list query.
type ListWalletsRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of wallets.
	Data []*WalletListItem `json:"data" dc:"钱包列表"`
}

// WalletListItem defines the structure for an item in the wallet list, including user information.
type WalletListItem struct {
	*entity.Wallets
	// Account is the account of the user who owns the wallet.
	Account string `json:"account" dc:"用户账号"`
	// FormattedAvailableBalance is the available balance as a formatted string.
	FormattedAvailableBalance string `json:"formattedAvailableBalance" dc:"格式化后的可用余额"`
	// FormattedFrozenBalance is the frozen balance as a formatted string.
	FormattedFrozenBalance string `json:"formattedFrozenBalance" dc:"格式化后的冻结余额"`
	
	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`
	
	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// --- 调整余额 ---

// AdjustBalanceReq defines the request structure for adjusting a wallet balance.
type AdjustBalanceReq struct {
	g.Meta `path:"/wallets/adjust-balance" method:"post" tags:"SystemWallet" summary:"调整钱包余额"`
	// WalletId is the ID of the wallet to be adjusted.
	WalletId uint `json:"walletId" v:"required#钱包ID不能为空" dc:"钱包ID"`
	// Amount is the amount to adjust the balance.
	Amount float64 `json:"amount" v:"required#金额不能为空" dc:"调整金额"`
	// Type indicates whether to increase or decrease the balance (1: increase, 2: decrease).
	Type int `json:"type" v:"required|in:1,2#类型不能为空|类型只能是1或2" dc:"类型 (1: 增加, 2: 减少)"`
	// ChangeReason is the reason for the balance adjustment.
	ChangeReason string `json:"changeReason" v:"required#变更原因不能为空" dc:"变更原因"`
}

// AdjustBalanceRes defines the response structure for the balance adjustment.
type AdjustBalanceRes struct {
	// Success indicates whether the balance adjustment was successful.
	Success bool `json:"success" dc:"是否成功"`
	// NewBalance is the new available balance after the adjustment.
	NewBalance int64 `json:"newBalance" dc:"新的可用余额"`
}
