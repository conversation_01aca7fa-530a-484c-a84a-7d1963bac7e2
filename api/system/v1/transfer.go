package v1

import (
	"admin-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 转账记录列表查询 ---

// ListAdminTransfersReq defines the request structure for querying the transfer list (admin).
type ListAdminTransfersReq struct {
	g.Meta `path:"/transfers" method:"get" tags:"SystemTransfer" summary:"查询转账记录列表(后台)"`
	common.PageRequest
	// Export indicates whether to export the results (0: no, 1: yes).
	Export int `json:"export" d:"0" dc:"是否导出: 0不导出, 1导出"`
	// SenderUserId filters by the sender's user ID.
	SenderUserId int64 `json:"senderUserId" dc:"发送方用户ID"`
	// SenderUsername filters by the sender's username (fuzzy search).
	SenderUsername string `json:"senderUsername" dc:"发送方用户名(模糊查询)"`
	// ReceiverUserId filters by the receiver's user ID.
	ReceiverUserId int64 `json:"receiverUserId" dc:"接收方用户ID"`
	// ReceiverUsername filters by the receiver's username (fuzzy search).
	ReceiverUsername string `json:"receiverUsername" dc:"接收方用户名(模糊查询)"`
	// TokenId filters by the token ID.
	TokenId int `json:"tokenId" dc:"代币ID"`
	// TokenSymbol filters by the token symbol (fuzzy search).
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号(模糊查询)"`
	// DateRange filters by date range (format: YYYY-MM-DD,YYYY-MM-DD).
	DateRange string `json:"dateRange" dc:"日期范围 (YYYY-MM-DD,YYYY-MM-DD)"`
	// AmountMin filters by minimum transfer amount.
	AmountMin string `json:"amountMin" dc:"最小金额"`
	// AmountMax filters by maximum transfer amount.
	AmountMax string `json:"amountMax" dc:"最大金额"`
	// TransferId filters by transfer ID.
	TransferId int64 `json:"transferId" dc:"转账ID"`
	// Status filters by transfer status.
	Status int `json:"status" dc:"转账状态"`
	// SenderAccount filters by the sender's account (fuzzy search).
	SenderAccount string `json:"senderAccount" dc:"发送方账户(模糊查询)"`
	// ReceiverAccount filters by the receiver's account (fuzzy search).
	ReceiverAccount string `json:"receiverAccount" dc:"接收方账户(模糊查询)"`

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram信息查询
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"真实姓名 (模糊搜索)"`

	// Key filters by a unique key (exact match).
	Key string `json:"key" dc:"转账密钥/唯一标识(精确查询)"`
}

// TransferAdminInfoItem defines the structure for an item in the admin transfer list.
type TransferAdminInfoItem struct {
	// TransferId is the unique identifier for the transfer.
	TransferId int64 `json:"transferId" dc:"转账记录ID"`
	// SenderUserId is the ID of the user who sent the funds.
	SenderUserId int64 `json:"senderUserId" dc:"发送方用户ID"`
	// SenderUsername is the username of the user who sent the funds.
	SenderUsername string `json:"senderUsername" dc:"发送方用户名"`
	// SenderAccount is the account of the user who sent the funds.
	SenderAccount string `json:"senderAccount" dc:"发送方账户"`
	// ReceiverUserId is the ID of the user who received the funds.
	ReceiverUserId int64 `json:"receiverUserId" dc:"接收方用户ID"`
	// ReceiverUsername is the username of the user who received the funds.
	ReceiverUsername string `json:"receiverUsername" dc:"接收方用户名"`
	// ReceiverAccount is the account of the user who received the funds.
	ReceiverAccount string `json:"receiverAccount" dc:"接收方账户"`
	// TokenId is the ID of the transferred token.
	TokenId int `json:"tokenId" dc:"代币ID"`
	// TokenSymbol is the symbol of the transferred token.
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号"`
	// TokenDecimals is the number of decimal places for the transferred token.
	TokenDecimals uint8 `json:"tokenDecimals" dc:"代币小数位数"`
	// AmountStr is the formatted string representation of the transfer amount.
	AmountStr string `json:"amountStr" dc:"转账金额(格式化后)"`
	// Memo is an optional note associated with the transfer.
	Memo string `json:"memo" dc:"转账备注"`
	// SenderTxId is the transaction ID for the sender's side of the transfer (if applicable).
	SenderTxId int64 `json:"senderTxId" dc:"发送方交易ID"`
	// ReceiverTxId is the transaction ID for the receiver's side of the transfer (if applicable).
	ReceiverTxId int64 `json:"receiverTxId" dc:"接收方交易ID"`
	// CreatedAt is the timestamp when the transfer occurred.
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
	// MessageId is the message ID associated with the transfer.
	MessageId int64 `json:"messageId,omitempty" dc:"消息ID"`
	// ChatId is the chat ID associated with the transfer.
	ChatId int64 `json:"chatId,omitempty" dc:"聊天ID"`
	// Status is the current status of the transfer.
	Status string `json:"status" dc:"转账状态"`
	// HoldId is the hold ID if the transfer is on hold.
	HoldId string `json:"holdId,omitempty" dc:"冻结ID"`
	// ExpiresAt is the timestamp when the transfer expires (if applicable).
	ExpiresAt *gtime.Time `json:"expiresAt,omitempty" dc:"过期时间"`
	// UpdatedAt is the timestamp when the transfer was last updated.
	UpdatedAt *gtime.Time `json:"updatedAt,omitempty" dc:"更新时间"`
	// NeedPass indicates if a password is required for the transfer.
	NeedPass bool `json:"needPass" dc:"是否需要密码"`
	// Key is a unique key or identifier for the transfer.
	Key string `json:"key,omitempty" dc:"转账密钥/唯一标识"`
	// TransferSymbol is the symbol from the transfers table.
	TransferSymbol string `json:"transferSymbol,omitempty" dc:"转账记录的币种符号"`
	// Message is any message associated with the transfer.
	Message string `json:"message,omitempty" dc:"消息内容"`
	// InlineMessageId is the ID for an inline message related to the transfer.
	InlineMessageId string `json:"inlineMessageId,omitempty" dc:"内联消息ID"`

	// 新增：发送方三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"发送方一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"发送方二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"发送方三级代理名称"`

	// 新增：发送方主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"发送方Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"发送方Telegram用户名"`
	FirstName        string `json:"firstName" dc:"发送方备份账户名字"`

	// 新增：接收方三级代理信息
	ReceiverFirstAgentName  string `json:"receiverFirstAgentName" dc:"接收方一级代理名称"`
	ReceiverSecondAgentName string `json:"receiverSecondAgentName" dc:"接收方二级代理名称"`
	ReceiverThirdAgentName  string `json:"receiverThirdAgentName" dc:"接收方三级代理名称"`

	// 新增：接收方主备份账户telegram信息
	ReceiverTelegramId       string `json:"receiverTelegramId" dc:"接收方Telegram ID"`
	ReceiverTelegramUsername string `json:"receiverTelegramUsername" dc:"接收方Telegram用户名"`
	ReceiverFirstName        string `json:"receiverFirstName" dc:"接收方备份账户名字"`
}

// ListAdminTransfersRes defines the response structure for the transfer list query (admin).
type ListAdminTransfersRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of transfer records. Using a DTO (TransferAdminInfoItem) is good practice.
	Data []*TransferAdminInfoItem `json:"data" dc:"转账记录列表"`
}

// --- 转账记录详情查询 ---

// GetAdminTransferDetailReq defines the request structure for getting transfer details (admin).
type GetAdminTransferDetailReq struct {
	g.Meta     `path:"/transfers/{transferId}" method:"get" tags:"SystemTransfer" summary:"获取转账记录详情(后台)"`
	TransferId int64 `json:"transferId" v:"required#转账ID不能为空" dc:"转账记录ID"`
}

// GetAdminTransferDetailRes defines the response structure for getting transfer details (admin).
type GetAdminTransferDetailRes struct {
	// Embeds the transfer details DTO.
	TransferAdminInfoItem `json:",inline"`
}

// --- 获取代币符号列表 (用于搜索条件下拉框) ---
