package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// --- User Address API Definitions ---

// ListUserAddressesReq defines the request structure for querying the user recharge address list.
type ListUserAddressesReq struct {
	g.Meta             `path:"/user-addresses" method:"get" tags:"SystemUserAddress" summary:"查询用户充值地址列表"`
	common.PageRequest        // Embeds common pagination request parameters.
	UserId             uint64 `json:"userId" dc:"用户ID"`
	Account            string `json:"account" dc:"用户账号"`
	TokenId            uint   `json:"tokenId" dc:"币种ID"`
	Symbol             string `json:"symbol" dc:"币种名称 (模糊查询)"`
	Chan               string `json:"chan" dc:"链名称"`
	Address            string `json:"address" dc:"地址 (模糊查询)"`
	Type               string `json:"type" dc:"地址类型"`
	DateRange          string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	
	// 新增：三级代理模糊查询
	FirstAgentName     string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName    string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName     string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`
	
	// 新增：telegram查询条件
	TelegramId         string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername   string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName          string `json:"firstName" dc:"名字 (模糊搜索)"`
}

// UserAddressListItem defines the structure for a single user address record in the list.
type UserAddressListItem struct {
	*entity.UserAddress
	Account  string `json:"account" dc:"用户账号"`
	
	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`
	
	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// ListUserAddressesRes defines the response structure for the user address list query.
type ListUserAddressesRes struct {
	Page common.PageResponse    `json:"page" dc:"分页信息"`
	Data []*UserAddressListItem `json:"data" dc:"充值地址列表"`
}
