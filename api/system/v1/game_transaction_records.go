package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetGameTransactionRecordsListReq defines the request for game transaction records list
type GetGameTransactionRecordsListReq struct {
	g.Meta             `path:"/game-transaction-records/list" method:"get" tags:"GameTransactionRecords" summary:"Get game transaction records list"`
	TransactionId      string  `json:"transactionId" dc:"Transaction ID filter"`
	UserId             *uint64 `json:"userId" dc:"User ID filter"`
	GameCatalogId      *uint64 `json:"gameCatalogId" dc:"Game catalog ID filter"`
	ProviderCode       string  `json:"providerCode" dc:"Provider code filter (EG5/PG/PP)"`
	GameCode           string  `json:"gameCode" dc:"Game code filter"`
	Type               string  `json:"type" dc:"Transaction type filter (bet/win/refund/cancel)"`
	Status             string  `json:"status" dc:"Transaction status filter (success/failed/pending)"`
	Currency           string  `json:"currency" dc:"Currency filter"`
	CommissionStatus   string  `json:"commissionStatus" dc:"Commission status filter (pending/processed/failed)"`
	BettingBonusStatus string  `json:"bettingBonusStatus" dc:"Betting bonus status filter (unprocessed/processed/not_applicable)"`
	AmountMin          string  `json:"amountMin" dc:"Minimum amount filter"`
	AmountMax          string  `json:"amountMax" dc:"Maximum amount filter"`
	DateStart          string  `json:"dateStart" dc:"Start date filter (YYYY-MM-DD)"`
	DateEnd            string  `json:"dateEnd" dc:"End date filter (YYYY-MM-DD)"`
	Keyword            string  `json:"keyword" dc:"Search keyword for game name, transaction ID, or user ID"`
	TelegramId         *int64  `json:"telegramId" dc:"Filter by Telegram ID"`
	TelegramUsername   string  `json:"telegramUsername" dc:"Filter by Telegram username"`
	FirstName          string  `json:"firstName" dc:"Filter by first name"`
	TenantUsername     string  `json:"tenantUsername" dc:"Filter by tenant username"`
	Page               int     `json:"page" dc:"Page number" d:"1" v:"min:1"`
	PageSize           int     `json:"pageSize" dc:"Page size" d:"20" v:"min:1|max:100"`
	ExportFormat       string  `json:"exportFormat" dc:"Export format (excel/csv), if provided will export instead of paginate"`
}

// GetGameTransactionRecordsListRes defines the response for game transaction records list
type GetGameTransactionRecordsListRes struct {
	Total int                               `json:"total" dc:"Total count"`
	List  []*GameTransactionRecordsListItem `json:"list" dc:"Transaction records list"`
	Stats *GameTransactionRecordsStats      `json:"stats,omitempty" dc:"Statistics summary"`
}

// GameTransactionRecordsListItem defines the transaction record list item
type GameTransactionRecordsListItem struct {
	Id                    uint64      `json:"id" dc:"Record ID"`
	TransactionId         string      `json:"transactionId" dc:"Transaction unique identifier"`
	UserId                uint64      `json:"userId" dc:"User ID"`
	GameCatalogId         *uint64     `json:"gameCatalogId" dc:"Game catalog ID"`
	SnapshotGameName      string      `json:"snapshotGameName" dc:"Game name snapshot"`
	SnapshotGameType      string      `json:"snapshotGameType" dc:"Game type snapshot"`
	ProviderCode          string      `json:"providerCode" dc:"Game provider code"`
	GameCode              string      `json:"gameCode" dc:"Provider game code"`
	SessionId             string      `json:"sessionId" dc:"Session ID"`
	Type                  string      `json:"type" dc:"Transaction type"`
	Status                string      `json:"status" dc:"Transaction status"`
	Currency              string      `json:"currency" dc:"Currency type"`
	Amount                string      `json:"amount" dc:"Transaction amount"`
	WinAmount             string      `json:"winAmount" dc:"Win amount"`
	NetAmount             string      `json:"netAmount" dc:"Net amount (win-loss)"`
	BalanceBefore         string      `json:"balanceBefore" dc:"Balance before transaction"`
	BalanceAfter          string      `json:"balanceAfter" dc:"Balance after transaction"`
	ProviderTransactionId string      `json:"providerTransactionId" dc:"Provider transaction ID"`
	ReferenceId           string      `json:"referenceId" dc:"Reference transaction ID"`
	RoundId               string      `json:"roundId" dc:"Game round ID"`
	BetId                 string      `json:"betId" dc:"Bet ID"`
	CommissionStatus      string      `json:"commissionStatus" dc:"Commission processing status"`
	BettingBonusStatus    string      `json:"bettingBonusStatus" dc:"Betting bonus processing status"`
	CreatedAt             *gtime.Time `json:"createdAt" dc:"Creation time"`
	UpdatedAt             *gtime.Time `json:"updatedAt" dc:"Update time"`
	TelegramId            *int64      `json:"telegramId" dc:"Telegram ID"`
	TelegramUsername      *string     `json:"telegramUsername" dc:"Telegram username"`
	FirstName             *string     `json:"firstName" dc:"First name"`
	TenantUsername        *string     `json:"tenantUsername" dc:"Tenant username"`
}

// GameTransactionRecordsStats defines transaction statistics
type GameTransactionRecordsStats struct {
	TotalTransactions        int                   `json:"totalTransactions" dc:"Total number of transactions"`
	TotalBetAmount           string                `json:"totalBetAmount" dc:"Total bet amount"`
	TotalWinAmount           string                `json:"totalWinAmount" dc:"Total win amount"`
	TotalNetAmount           string                `json:"totalNetAmount" dc:"Total net amount"`
	SuccessfulCount          int                   `json:"successfulCount" dc:"Number of successful transactions"`
	FailedCount              int                   `json:"failedCount" dc:"Number of failed transactions"`
	PendingCount             int                   `json:"pendingCount" dc:"Number of pending transactions"`
	ProviderBreakdown        []ProviderStat        `json:"providerBreakdown" dc:"Statistics by provider"`
	GameTypeBreakdown        []GameTypeStat        `json:"gameTypeBreakdown" dc:"Statistics by game type"`
	TransactionTypeBreakdown []TransactionTypeStat `json:"transactionTypeBreakdown" dc:"Statistics by transaction type"`
}

// ProviderStat defines provider statistics
type ProviderStat struct {
	ProviderCode     string `json:"providerCode" dc:"Provider code"`
	TransactionCount int    `json:"transactionCount" dc:"Transaction count"`
	TotalAmount      string `json:"totalAmount" dc:"Total amount"`
}

// GameTypeStat defines game type statistics
type GameTypeStat struct {
	GameType         string `json:"gameType" dc:"Game type"`
	TransactionCount int    `json:"transactionCount" dc:"Transaction count"`
	TotalAmount      string `json:"totalAmount" dc:"Total amount"`
}

// TransactionTypeStat defines transaction type statistics
type TransactionTypeStat struct {
	TransactionType  string `json:"transactionType" dc:"Transaction type"`
	TransactionCount int    `json:"transactionCount" dc:"Transaction count"`
	TotalAmount      string `json:"totalAmount" dc:"Total amount"`
}

// GetGameTransactionRecordsDetailReq defines the request for transaction record detail
type GetGameTransactionRecordsDetailReq struct {
	g.Meta `path:"/game-transaction-records/detail" method:"get" tags:"GameTransactionRecords" summary:"Get game transaction record detail"`
	Id     uint64 `json:"id" dc:"Transaction record ID" v:"required|min:1"`
}

// GetGameTransactionRecordsDetailRes defines the response for transaction record detail
type GetGameTransactionRecordsDetailRes struct {
	*GameTransactionRecordsListItem
	RelatedTransactions []*GameTransactionRecordsListItem `json:"relatedTransactions,omitempty" dc:"Related transactions (same round/session)"`
}

// GetGameTransactionRecordsStatsReq defines the request for transaction statistics
type GetGameTransactionRecordsStatsReq struct {
	g.Meta          `path:"/game-transaction-records/stats" method:"get" tags:"GameTransactionRecords" summary:"Get game transaction records statistics"`
	ProviderCode    string `json:"providerCode" dc:"Provider code filter"`
	GameType        string `json:"gameType" dc:"Game type filter"`
	TransactionType string `json:"type" dc:"Transaction type filter"`
	Currency        string `json:"currency" dc:"Currency filter"`
	DateStart       string `json:"dateStart" dc:"Start date filter (YYYY-MM-DD)"`
	DateEnd         string `json:"dateEnd" dc:"End date filter (YYYY-MM-DD)"`
	GroupBy         string `json:"groupBy" dc:"Group by field (provider/gameType/transactionType/date)" d:"provider"`
}

// GetGameTransactionRecordsStatsRes defines the response for transaction statistics
type GetGameTransactionRecordsStatsRes struct {
	*GameTransactionRecordsStats
}

// ExportGameTransactionRecordsReq defines the request for exporting transaction records
type ExportGameTransactionRecordsReq struct {
	g.Meta             `path:"/game-transaction-records/export" method:"post" tags:"GameTransactionRecords" summary:"Export game transaction records"`
	TransactionId      string  `json:"transactionId" dc:"Transaction ID filter"`
	UserId             *uint64 `json:"userId" dc:"User ID filter"`
	GameCatalogId      *uint64 `json:"gameCatalogId" dc:"Game catalog ID filter"`
	ProviderCode       string  `json:"providerCode" dc:"Provider code filter"`
	GameCode           string  `json:"gameCode" dc:"Game code filter"`
	Type               string  `json:"type" dc:"Transaction type filter"`
	Status             string  `json:"status" dc:"Transaction status filter"`
	Currency           string  `json:"currency" dc:"Currency filter"`
	CommissionStatus   string  `json:"commissionStatus" dc:"Commission status filter"`
	BettingBonusStatus string  `json:"bettingBonusStatus" dc:"Betting bonus status filter"`
	AmountMin          string  `json:"amountMin" dc:"Minimum amount filter"`
	AmountMax          string  `json:"amountMax" dc:"Maximum amount filter"`
	DateStart          string  `json:"dateStart" dc:"Start date filter (YYYY-MM-DD)"`
	DateEnd            string  `json:"dateEnd" dc:"End date filter (YYYY-MM-DD)"`
	Keyword            string  `json:"keyword" dc:"Search keyword"`
	Format             string  `json:"format" dc:"Export format (excel/csv)" d:"excel" v:"required|in:excel,csv"`
	IncludeStats       bool    `json:"includeStats" dc:"Include statistics in export" d:"true"`
}

// ExportGameTransactionRecordsRes defines the response for exporting transaction records
type ExportGameTransactionRecordsRes struct {
	FileUrl  string `json:"fileUrl" dc:"Export file download URL"`
	FileName string `json:"fileName" dc:"Export file name"`
	FileSize int64  `json:"fileSize" dc:"Export file size in bytes"`
}

// UpdateGameTransactionRecordsCommissionStatusReq defines the request for updating commission status
type UpdateGameTransactionRecordsCommissionStatusReq struct {
	g.Meta           `path:"/game-transaction-records/update-commission-status" method:"put" tags:"GameTransactionRecords" summary:"Update transaction commission status"`
	Ids              []uint64 `json:"ids" dc:"Transaction record IDs" v:"required|min-length:1"`
	CommissionStatus string   `json:"commissionStatus" dc:"Commission status" v:"required|in:pending,processed,failed"`
	Remark           string   `json:"remark" dc:"Update remark"`
}

// UpdateGameTransactionRecordsCommissionStatusRes defines the response for updating commission status
type UpdateGameTransactionRecordsCommissionStatusRes struct {
	UpdatedCount int `json:"updatedCount" dc:"Number of records updated"`
}

// GetGameTransactionRecordsAggregateReq defines the request for aggregate data
type GetGameTransactionRecordsAggregateReq struct {
	g.Meta       `path:"/game-transaction-records/aggregate" method:"get" tags:"GameTransactionRecords" summary:"Get aggregated transaction data"`
	ProviderCode string `json:"providerCode" dc:"Provider code filter"`
	GameType     string `json:"gameType" dc:"Game type filter"`
	Currency     string `json:"currency" dc:"Currency filter"`
	DateStart    string `json:"dateStart" dc:"Start date filter (YYYY-MM-DD)"`
	DateEnd      string `json:"dateEnd" dc:"End date filter (YYYY-MM-DD)"`
	Interval     string `json:"interval" dc:"Time interval (hour/day/week/month)" d:"day" v:"in:hour,day,week,month"`
}

// GetGameTransactionRecordsAggregateRes defines the response for aggregate data
type GetGameTransactionRecordsAggregateRes struct {
	Data []*GameTransactionRecordsAggregateItem `json:"data" dc:"Aggregated data points"`
}

// GameTransactionRecordsAggregateItem defines an aggregate data point
type GameTransactionRecordsAggregateItem struct {
	Period           string `json:"period" dc:"Time period (e.g., 2024-01-01, 2024-01-01 10:00)"`
	TransactionCount int    `json:"transactionCount" dc:"Number of transactions"`
	TotalBetAmount   string `json:"totalBetAmount" dc:"Total bet amount"`
	TotalWinAmount   string `json:"totalWinAmount" dc:"Total win amount"`
	TotalNetAmount   string `json:"totalNetAmount" dc:"Total net amount"`
	UniqueUsers      int    `json:"uniqueUsers" dc:"Number of unique users"`
}
