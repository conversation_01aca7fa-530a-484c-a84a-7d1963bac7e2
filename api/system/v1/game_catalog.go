package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetGameCatalogListReq defines the request for game catalog list
type GetGameCatalogListReq struct {
	g.Meta       `path:"/game-catalog/list" method:"get" tags:"GameCatalog" summary:"Get game catalog list"`
	ProductType  string `json:"productType" dc:"Product type filter (EG5/PG/PP)"`
	Platform     string `json:"platform" dc:"Platform filter (flash/html5/all)"`
	GameType     string `json:"gameType" dc:"Game type filter (RNG/LIVE/PVP)"`
	DisplayStatus *int   `json:"displayStatus" dc:"Display status filter (1=active, 0=inactive)"`
	Keyword      string `json:"keyword" dc:"Search keyword for game name or code"`
	Page         int    `json:"page" dc:"Page number" d:"1" v:"min:1"`
	PageSize     int    `json:"pageSize" dc:"Page size" d:"20" v:"min:1|max:100"`
}

// GetGameCatalogListRes defines the response for game catalog list
type GetGameCatalogListRes struct {
	Total int                   `json:"total" dc:"Total count"`
	List  []*GameCatalogListItem `json:"list" dc:"Game catalog list"`
}

// GameCatalogListItem defines the game catalog list item
type GameCatalogListItem struct {
	Id            uint64      `json:"id" dc:"Game catalog ID"`
	TcgGameCode   string      `json:"tcgGameCode" dc:"TC Gaming game code"`
	GameName      string      `json:"gameName" dc:"Game display name"`
	ProductCode   string      `json:"productCode" dc:"Product code"`
	ProductType   string      `json:"productType" dc:"Product type"`
	Platform      string      `json:"platform" dc:"Supported platform"`
	GameType      string      `json:"gameType" dc:"Game type category"`
	DisplayStatus int         `json:"displayStatus" dc:"Display status (1=active, 0=inactive)"`
	TrialSupport  int         `json:"trialSupport" dc:"Trial mode support"`
	Sort          int         `json:"sort" dc:"Sort order"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"Creation time"`
	UpdatedAt     *gtime.Time `json:"updatedAt" dc:"Update time"`
}

// GetGameCatalogDetailReq defines the request for game catalog detail
type GetGameCatalogDetailReq struct {
	g.Meta `path:"/game-catalog/detail" method:"get" tags:"GameCatalog" summary:"Get game catalog detail"`
	Id     uint64 `json:"id" dc:"Game catalog ID" v:"required|min:1"`
}

// GetGameCatalogDetailRes defines the response for game catalog detail
type GetGameCatalogDetailRes struct {
	*GameCatalogListItem
}

// CreateGameCatalogReq defines the request for creating game catalog
type CreateGameCatalogReq struct {
	g.Meta        `path:"/game-catalog/create" method:"post" tags:"GameCatalog" summary:"Create game catalog"`
	TcgGameCode   string `json:"tcgGameCode" dc:"TC Gaming game code" v:"required|max-length:50"`
	GameName      string `json:"gameName" dc:"Game display name" v:"required|max-length:100"`
	ProductCode   string `json:"productCode" dc:"Product code" v:"required|max-length:50"`
	ProductType   string `json:"productType" dc:"Product type" v:"required|in:EG5,PG,PP"`
	Platform      string `json:"platform" dc:"Supported platform" v:"required|in:flash,html5,all"`
	GameType      string `json:"gameType" dc:"Game type category" v:"required|in:RNG,LIVE,PVP"`
	DisplayStatus int    `json:"displayStatus" dc:"Display status (1=active, 0=inactive)" v:"required|in:0,1" d:"1"`
	TrialSupport  int    `json:"trialSupport" dc:"Trial mode support" v:"required|in:0,1" d:"0"`
	Sort          int    `json:"sort" dc:"Sort order" d:"0"`
}

// CreateGameCatalogRes defines the response for creating game catalog
type CreateGameCatalogRes struct {
	Id uint64 `json:"id" dc:"Created game catalog ID"`
}

// UpdateGameCatalogReq defines the request for updating game catalog
type UpdateGameCatalogReq struct {
	g.Meta        `path:"/game-catalog/update" method:"put" tags:"GameCatalog" summary:"Update game catalog"`
	Id            uint64 `json:"id" dc:"Game catalog ID" v:"required|min:1"`
	TcgGameCode   string `json:"tcgGameCode" dc:"TC Gaming game code" v:"required|max-length:50"`
	GameName      string `json:"gameName" dc:"Game display name" v:"required|max-length:100"`
	ProductCode   string `json:"productCode" dc:"Product code" v:"required|max-length:50"`
	ProductType   string `json:"productType" dc:"Product type" v:"required|in:EG5,PG,PP"`
	Platform      string `json:"platform" dc:"Supported platform" v:"required|in:flash,html5,all"`
	GameType      string `json:"gameType" dc:"Game type category" v:"required|in:RNG,LIVE,PVP"`
	DisplayStatus int    `json:"displayStatus" dc:"Display status (1=active, 0=inactive)" v:"required|in:0,1"`
	TrialSupport  int    `json:"trialSupport" dc:"Trial mode support" v:"required|in:0,1"`
	Sort          int    `json:"sort" dc:"Sort order"`
}

// UpdateGameCatalogRes defines the response for updating game catalog
type UpdateGameCatalogRes struct{}

// DeleteGameCatalogReq defines the request for deleting game catalog
type DeleteGameCatalogReq struct {
	g.Meta `path:"/game-catalog/delete" method:"delete" tags:"GameCatalog" summary:"Delete game catalog"`
	Id     uint64 `json:"id" dc:"Game catalog ID" v:"required|min:1"`
}

// DeleteGameCatalogRes defines the response for deleting game catalog
type DeleteGameCatalogRes struct{}

// UpdateGameCatalogStatusReq defines the request for updating game catalog status
type UpdateGameCatalogStatusReq struct {
	g.Meta        `path:"/game-catalog/update-status" method:"put" tags:"GameCatalog" summary:"Update game catalog status"`
	Id            uint64 `json:"id" dc:"Game catalog ID" v:"required|min:1"`
	DisplayStatus int    `json:"displayStatus" dc:"Display status (1=active, 0=inactive)" v:"required|in:0,1"`
}

// UpdateGameCatalogStatusRes defines the response for updating game catalog status
type UpdateGameCatalogStatusRes struct{}