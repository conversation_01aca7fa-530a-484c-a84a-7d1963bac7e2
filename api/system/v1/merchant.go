package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 获取商户列表 ---

// GetMerchantListReq defines the request structure for getting the merchant list.
type GetMerchantListReq struct {
	g.Meta `path:"/merchants" method:"get" tags:"SystemMerchant" summary:"获取商户列表"`
	common.PageRequest
	MerchantName string `json:"merchantName" dc:"商户名称 (模糊搜索)"`
	BusinessName string `json:"businessName" dc:"公司/业务注册名称 (模糊搜索)"`
	Email        string `json:"email" dc:"商户邮箱 (模糊搜索)"`
	Phone        string `json:"phone" dc:"联系电话 (模糊搜索)"`
	Status       *int   `json:"status" dc:"状态 (-1:待审核, 0:禁用, 1:启用)"`
	DateRange    string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export       bool   `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantInfoType defines the structure for merchant information in a list.
type MerchantInfoType struct {
	entity.Merchants
	// Additional computed fields for display
	PaymentPasswordEnabled bool `json:"paymentPasswordEnabled" dc:"是否设置支付密码"`
}

// GetMerchantListRes defines the response structure for getting the merchant list.
type GetMerchantListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of merchants.
	Data []*MerchantInfoType `json:"data" dc:"商户列表数据"`
}

// --- 获取商户详情 ---

// GetMerchantReq defines the request structure for getting merchant details.
type GetMerchantReq struct {
	g.Meta     `path:"/merchants/{merchantId}" method:"get" tags:"SystemMerchant" summary:"获取商户详情"`
	MerchantId uint `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
}

// MerchantDetailType defines the structure for merchant details, including API key info.
type MerchantDetailType struct {
	entity.Merchants
	// ApiKeys lists the API keys associated with the merchant.
	ApiKeys []*entity.MerchantApiKeys `json:"apiKeys" dc:"API密钥列表"`
	// Additional computed fields for display
	PaymentPasswordEnabled bool `json:"paymentPasswordEnabled" dc:"是否设置支付密码"`
}

// GetMerchantRes defines the response structure for getting merchant details.
type GetMerchantRes struct {
	// Data contains the merchant details.
	Data *MerchantDetailType `json:"data" dc:"商户详情数据"`
}

// --- 添加商户 ---

// AddMerchantReq defines the request structure for adding a new merchant.
type AddMerchantReq struct {
	g.Meta          `path:"/merchants" method:"post" tags:"SystemMerchant" summary:"添加商户"`
	MerchantName    string `json:"merchantName" v:"required|length:2,150|regex:^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$#商户名称不能为空|商户名称长度必须在2-150之间|商户名称只能包含字母、数字、下划线或连字符，且必须以字母或数字开头和结尾" dc:"商户名称（只能包含字母、数字、下划线或连字符）"`
	BusinessName    string `json:"businessName" v:"length:0,255" dc:"公司/业务注册名称 (可选)"`
	Email           string `json:"email" v:"required|email#邮箱不能为空|邮箱格式不正确" dc:"商户邮箱"`
	Phone           string `json:"phone" v:"length:0,50" dc:"联系电话"`
	WebsiteUrl      string `json:"websiteUrl" v:"length:0,255" dc:"商户网站URL (可选)"`
	ContactEmail    string `json:"contactEmail" v:"email" dc:"备用联系邮箱"`
	Notes           string `json:"notes" v:"length:0,200" dc:"备注"`
	Password        string `json:"password" v:"required|length:6,50#密码不能为空|密码长度必须在6-50之间" dc:"登录密码"`
	ConfirmPassword string `json:"confirmPassword" v:"required|same:password#确认密码不能为空|两次输入的密码不一致" dc:"确认密码"`
}

// AddMerchantRes defines the response structure after adding a new merchant.
type AddMerchantRes struct {
	// MerchantId is the ID of the newly created merchant.
	MerchantId uint `json:"merchantId" dc:"新增的商户ID"`
}

// --- 编辑商户 ---

// EditMerchantReq defines the request structure for editing an existing merchant.
type EditMerchantReq struct {
	g.Meta       `path:"/merchants/{merchantId}" method:"put" tags:"SystemMerchant" summary:"编辑商户"`
	MerchantId   uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	MerchantName string `json:"merchantName" v:"required|length:2,150|regex:^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$#商户名称不能为空|商户名称长度必须在2-150之间|商户名称只能包含字母、数字、下划线或连字符，且必须以字母或数字开头和结尾" dc:"商户名称（只能包含字母、数字、下划线或连字符）"`
	BusinessName string `json:"businessName" v:"length:0,255" dc:"公司/业务注册名称 (可选)"`
	Email        string `json:"email" v:"required|email#邮箱不能为空|邮箱格式不正确" dc:"商户邮箱"`
	Phone        string `json:"phone" v:"length:0,50" dc:"联系电话"`
	WebsiteUrl   string `json:"websiteUrl" v:"length:0,255" dc:"商户网站URL (可选)"`
	ContactEmail string `json:"contactEmail" v:"email" dc:"备用联系邮箱"`
	CallbackUrl  string `json:"callbackUrl" v:"length:0,512" dc:"回调URL"`
	Notes        string `json:"notes" v:"length:0,200" dc:"备注"`
}

// EditMerchantRes defines the response structure after editing a merchant.
type EditMerchantRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 删除商户 ---

// DeleteMerchantReq defines the request structure for deleting merchants.
type DeleteMerchantReq struct {
	g.Meta      `path:"/merchants" method:"delete" tags:"SystemMerchant" summary:"删除商户"`
	MerchantIds []uint `json:"merchantIds" v:"required#商户ID不能为空" dc:"商户ID列表"`
}

// DeleteMerchantRes defines the response structure after deleting merchants.
type DeleteMerchantRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新商户状态 ---

// UpdateMerchantStatusReq defines the request structure for updating a merchant's status.
// Consider using PATCH for partial updates.
type UpdateMerchantStatusReq struct {
	g.Meta     `path:"/merchants/{merchantId}/status" method:"put" tags:"SystemMerchant" summary:"更新商户状态"`
	MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Notes      string `json:"notes" v:"length:0,200" dc:"备注"`
	Status     int    `json:"status" v:"required#状态不能为空" dc:"状态 (-1:待审核, 0:禁用, 1:启用)"`
}

// UpdateMerchantStatusRes defines the response structure after updating a merchant's status.
type UpdateMerchantStatusRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 重置商户Google 2FA ---

// ResetMerchantGoogle2FAReq defines the request structure for resetting a merchant's Google 2FA.
type ResetMerchantGoogle2FAReq struct {
	g.Meta     `path:"/merchants/{merchantId}/google2fa" method:"put" tags:"SystemMerchant" summary:"重置商户Google 2FA"`
	MerchantId uint `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
}

// ResetMerchantGoogle2FARes defines the response structure after resetting a merchant's Google 2FA.
type ResetMerchantGoogle2FARes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 重置商户密码 ---

// ResetMerchantPasswordReq defines the request structure for resetting a merchant's password.
type ResetMerchantPasswordReq struct {
	g.Meta      `path:"/merchants/{merchantId}/password" method:"put" tags:"SystemMerchant" summary:"重置商户密码"`
	MerchantId  uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	NewPassword string `json:"newPassword" v:"required|length:6,50#新密码不能为空|密码长度必须在6-50之间" dc:"新密码"`
}

// ResetMerchantPasswordRes defines the response structure after resetting a merchant's password.
type ResetMerchantPasswordRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 生成商户API密钥 ---

// GenerateMerchantApiKeyReq defines the request structure for generating a new API key for a merchant.
type GenerateMerchantApiKeyReq struct {
	g.Meta      `path:"/merchants/{merchantId}/apikeys" method:"post" tags:"SystemMerchant" summary:"生成商户API密钥"`
	MerchantId  uint        `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Label       string      `json:"label" v:"length:0,100" dc:"标签/名称"`
	Scopes      string      `json:"scopes"  dc:"授权范围 (逗号分隔)"`
	IpWhitelist string      `json:"ipWhitelist"  dc:"IP白名单 (逗号分隔)"`
	ExpiresAt   *gtime.Time `json:"expiresAt"  dc:"过期时间 (为空表示永不过期)"`
}

// GenerateMerchantApiKeyRes defines the response structure after generating a merchant API key.
type GenerateMerchantApiKeyRes struct {
	// ApiKeyId is the ID of the newly generated API key.
	ApiKeyId uint `json:"apiKeyId" dc:"API密钥ID"`
	// ApiKey is the generated API key.
	ApiKey string `json:"apiKey" dc:"API密钥"`
	// SecretKey is the generated secret key (returned only once upon creation).
	SecretKey string `json:"secretKey" dc:"密钥 (仅在创建时返回一次)"`
}

// --- 获取商户API密钥列表 ---

// GetMerchantApiKeyListReq defines the request structure for getting the API key list for a merchant.
type GetMerchantApiKeyListReq struct {
	g.Meta `path:"/merchants/{merchantId}/apikeys" method:"get" tags:"SystemMerchant" summary:"获取商户API密钥列表"`
	common.PageRequest
	MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	ApiKey     string `json:"apiKey" dc:"API密钥 (模糊搜索)"`
	Label      string `json:"label" dc:"标签/名称 (模糊搜索)"`
	Status     string `json:"status" dc:"状态 (active-可用, revoked-已撤销, expired-已过期)"`
	DateRange  string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export     bool   `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// GetMerchantApiKeyListRes defines the response structure for getting the API key list for a merchant.
type GetMerchantApiKeyListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of API keys.
	Data []*ApiKeyInfoType `json:"data" dc:"API密钥列表数据"`
}

// --- 更新商户API密钥 ---

// UpdateMerchantApiKeyReq defines the request structure for updating a merchant's API key.
type UpdateMerchantApiKeyReq struct {
	g.Meta      `path:"/merchants/{merchantId}/apikeys/{apiKeyId}" method:"put" tags:"SystemMerchant" summary:"更新商户API密钥"`
	MerchantId  uint        `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	ApiKeyId    uint        `json:"apiKeyId" v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Label       string      `json:"label" v:"length:0,100" dc:"标签/名称"`
	Scopes      string      `json:"scopes" dc:"授权范围 (逗号分隔)"`
	IpWhitelist string      `json:"ipWhitelist" dc:"IP白名单 (逗号分隔)"`
	ExpiresAt   *gtime.Time `json:"expiresAt" dc:"过期时间 (为空表示永不过期)"`
}

// UpdateMerchantApiKeyRes defines the response structure for updating a merchant's API key.
type UpdateMerchantApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 撤销商户API密钥 ---

// RevokeMerchantApiKeyReq defines the request structure for revoking a merchant's API key.
type RevokeMerchantApiKeyReq struct {
	g.Meta   `path:"/merchants/apikeys/{apiKeyId}" method:"delete" tags:"SystemMerchant" summary:"撤销商户API密钥"`
	ApiKeyId uint `json:"apiKeyId" v:"required#API密钥ID不能为空" dc:"API密钥ID"`
}

// RevokeMerchantApiKeyRes defines the response structure after revoking a merchant's API key.
type RevokeMerchantApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}
