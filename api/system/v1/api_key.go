package v1

import (
	"admin-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 获取API密钥列表 ---

// GetApiKeyListReq defines the request structure for getting the API key list.
type GetApiKeyListReq struct {
	g.Meta `path:"/apikeys" method:"get" tags:"SystemApiKey" summary:"获取API密钥列表"`
	common.PageRequest
	ApiKeyId     uint    `json:"apiKeyId" dc:"API密钥ID (精确搜索)"`
	MerchantId   *uint   `json:"merchantId" dc:"商户ID (可选，不传则查询所有商户)"`
	MerchantName string  `json:"merchantName" dc:"商户名称 (模糊搜索)"`
	ApiKey       string  `json:"apiKey" dc:"API密钥 (模糊搜索)"`
	Label        string  `json:"label" dc:"标签/名称 (模糊搜索)"`
	Status       string  `json:"status" dc:"状态 (active-可用, revoked-已撤销, expired-已过期)"`
	Scopes       string  `json:"scopes" dc:"授权范围 (模糊搜索)"`
	IpWhitelist  string  `json:"ipWhitelist" dc:"IP白名单 (模糊搜索)"`
	DateRange    string  `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	ExpiresAt    string  `json:"expiresAt" dc:"过期时间 (格式: YYYY-MM-DD)"`
	LastUsedAt   string  `json:"lastUsedAt" dc:"最后使用时间 (格式: YYYY-MM-DD)"`
	Export       bool    `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// ApiKeyInfoType defines the structure for API key information returned in lists or details.
type ApiKeyInfoType struct {
	// ApiKeyId is the unique identifier for the API key.
	ApiKeyId uint `json:"apiKeyId" dc:"API密钥ID"`
	// MerchantId is the ID of the merchant associated with the key.
	MerchantId uint `json:"merchantId" dc:"商户ID"`
	// MerchantName is the name of the merchant.
	MerchantName string `json:"merchantName" dc:"商户名称"`
	// ApiKey is the actual API key string (masked or partial in lists).
	ApiKey string `json:"apiKey" dc:"API密钥"`
	// Label is a user-defined label for the key.
	Label string `json:"label" dc:"标签/名称"`
	// Status indicates the current status of the key (e.g., active, revoked, expired).
	Status string `json:"status" dc:"状态"`
	// Scopes defines the permissions granted to the key.
	Scopes string `json:"scopes" dc:"授权范围"`
	// IpWhitelist specifies the allowed IP addresses (comma-separated).
	IpWhitelist string `json:"ipWhitelist" dc:"IP白名单"`
	// ExpiresAt is the expiration time of the key (nil for never expires).
	ExpiresAt *gtime.Time `json:"expiresAt" dc:"过期时间"`
	// LastUsedAt is the timestamp when the key was last used.
	LastUsedAt *gtime.Time `json:"lastUsedAt" dc:"最后使用时间"`
	// CreatedAt is the timestamp when the key was created.
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// GetApiKeyListRes defines the response structure for getting the API key list.
type GetApiKeyListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of API keys.
	Data []*ApiKeyInfoType `json:"data" dc:"API密钥列表数据"`
}

// --- 更新API密钥 ---

// UpdateApiKeyReq defines the request structure for updating an API key.
type UpdateApiKeyReq struct {
	g.Meta      `path:"/apikeys/{apiKeyId}" method:"put" tags:"SystemApiKey" summary:"更新API密钥"`
	ApiKeyId    uint        `v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Label       string      `json:"label" v:"length:0,100" dc:"标签/名称"`
	Scopes      string      `json:"scopes" dc:"授权范围 (逗号分隔)"`
	IpWhitelist string      `json:"ipWhitelist" dc:"IP白名单 (逗号分隔)"`
	ExpiresAt   *gtime.Time `json:"expiresAt" dc:"过期时间 (为空表示永不过期)"`
}

// UpdateApiKeyRes defines the response structure for updating an API key.
type UpdateApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 删除API密钥 ---

// DeleteApiKeyReq defines the request structure for deleting API keys.
type DeleteApiKeyReq struct {
	g.Meta    `path:"/apikeys" method:"delete" tags:"SystemApiKey" summary:"删除API密钥"`
	ApiKeyIds []uint `json:"apiKeyIds"  v:"required#API密钥ID不能为空" dc:"API密钥ID列表"`
}

// DeleteApiKeyRes defines the response structure for deleting API keys.
type DeleteApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新API密钥状态 ---

// UpdateApiKeyStatusReq defines the request structure for updating an API key's status.
// Consider using PATCH for partial updates.
type UpdateApiKeyStatusReq struct {
	g.Meta   `path:"/apikeys/{apiKeyId}/status" method:"put" tags:"SystemApiKey" summary:"更新API密钥状态"`
	ApiKeyId uint   `json:"apiKeyId" v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Status   string `json:"status"  v:"required|in:active,revoked,expired#状态不能为空|状态值无效" dc:"状态 (active-可用, revoked-已撤销, expired-已过期)"`
}

// UpdateApiKeyStatusRes defines the response structure for updating an API key's status.
type UpdateApiKeyStatusRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}
