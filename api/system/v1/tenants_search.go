package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SearchTenantsReq defines the request for searching tenants
type SearchTenantsReq struct {
	g.<PERSON>a `path:"/tenants/search" method:"get" tags:"Tenants" summary:"Search tenants by username"`
	Q      string `json:"q" dc:"Search query (minimum 2 characters)" v:"required|min-length:2"`
	Page   int    `json:"page" dc:"Page number" d:"1" v:"min:1"`
	Limit  int    `json:"limit" dc:"Results per page" d:"20" v:"min:1|max:100"`
}

// SearchTenantsRes defines the response for searching tenants
type SearchTenantsRes struct {
	Data       []*TenantSearchItem `json:"data" dc:"Tenant search results"`
	Pagination PaginationInfo      `json:"pagination" dc:"Pagination information"`
}

// TenantSearchItem defines a single tenant search result
type TenantSearchItem struct {
	Id       uint   `json:"id" dc:"Tenant ID"`
	Username string `json:"username" dc:"Tenant username"`
}

// PaginationInfo defines pagination information
type PaginationInfo struct {
	Page    int  `json:"page" dc:"Current page"`
	Limit   int  `json:"limit" dc:"Results per page"`
	Total   int  `json:"total" dc:"Total results"`
	HasMore bool `json:"hasMore" dc:"Whether there are more results"`
}