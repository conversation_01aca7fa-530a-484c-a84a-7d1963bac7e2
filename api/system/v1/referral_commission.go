package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// --- 获取佣金记录列表 ---

// GetReferralCommissionListReq defines the request structure for querying the referral commission list.
type GetReferralCommissionListReq struct {
	g.Meta `path:"/referral-commissions" method:"get" tags:"SystemAdmin" summary:"获取佣金记录列表"`
	common.PageRequest
	// ReferrerId filters by the ID of the user who earned the commission.
	ReferrerId int64 `json:"referrerId" dc:"获得佣金的用户ID"`
	// InviteeId filters by the ID of the referred user who generated the commission.
	InviteeId int64 `json:"inviteeId" dc:"产生佣金的被推荐人用户ID"`
	// Status filters by commission status (e.g., pending, paid, cancelled).
	Status string `json:"status" dc:"佣金状态: pending-待发放, paid-已发放, cancelled-已取消"`
	// TokenId filters by the ID of the commission token.
	TokenId int `json:"tokenId" dc:"佣金代币ID"`
	// DateRange filters by date range (format: YYYY-MM-DD,YYYY-MM-DD).
	DateRange string `json:"dateRange" dc:"日期范围，格式：2025-01-01,2025-01-31"`
	// Export indicates whether to export the results (0: no, 1: yes).
	Export int `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`
	
	// 新增：三级代理模糊查询
	FirstAgentName     string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName    string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName     string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`
	
	// 新增：telegram信息查询
	TelegramId         string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername   string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName          string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// ReferralCommissionListItem defines the structure for a referral commission list item with user information.
type ReferralCommissionListItem struct {
	*entity.ReferralCommissions // Embeds the base referral commission entity
	
	// 推荐人信息
	ReferrerUsername string `json:"referrerUsername" dc:"推荐人用户名"`
	ReferrerAccount  string `json:"referrerAccount" dc:"推荐人账号"`
	
	// 被推荐人信息
	InviteeUsername string `json:"inviteeUsername" dc:"被推荐人用户名"`
	InviteeAccount  string `json:"inviteeAccount" dc:"被推荐人账号"`
	
	// 代币信息
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号"`
	
	// 推荐人的三级代理信息
	ReferrerFirstAgentName  string `json:"referrerFirstAgentName" dc:"推荐人一级代理名称"`
	ReferrerSecondAgentName string `json:"referrerSecondAgentName" dc:"推荐人二级代理名称"`
	ReferrerThirdAgentName  string `json:"referrerThirdAgentName" dc:"推荐人三级代理名称"`
	
	// 推荐人的主备份账户telegram信息
	ReferrerTelegramId       string `json:"referrerTelegramId" dc:"推荐人主Telegram ID"`
	ReferrerTelegramUsername string `json:"referrerTelegramUsername" dc:"推荐人主Telegram用户名"`
	ReferrerFirstName        string `json:"referrerFirstName" dc:"推荐人主备份账户名字"`
	
	// 被推荐人的三级代理信息
	InviteeFirstAgentName  string `json:"inviteeFirstAgentName" dc:"被推荐人一级代理名称"`
	InviteeSecondAgentName string `json:"inviteeSecondAgentName" dc:"被推荐人二级代理名称"`
	InviteeThirdAgentName  string `json:"inviteeThirdAgentName" dc:"被推荐人三级代理名称"`
	
	// 被推荐人的主备份账户telegram信息
	InviteeTelegramId       string `json:"inviteeTelegramId" dc:"被推荐人主Telegram ID"`
	InviteeTelegramUsername string `json:"inviteeTelegramUsername" dc:"被推荐人主Telegram用户名"`
	InviteeFirstName        string `json:"inviteeFirstName" dc:"被推荐人主备份账户名字"`
}

// GetReferralCommissionListRes defines the response structure for the referral commission list query.
type GetReferralCommissionListRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of referral commissions with user information.
	Data []*ReferralCommissionListItem `json:"data" dc:"佣金记录列表"`
}
