package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model" // Import model for TransactionAdminInfo

	"github.com/gogf/gf/v2/frame/g"
)

// --- Transaction API Definitions ---

// --- 后台查询交易记录列表 ---
// ListAdminTransactionsReq defines the request structure for querying the transaction list (admin).
type ListAdminTransactionsReq struct {
	g.Meta             `path:"/transactions" method:"get" tags:"SystemTransactions" summary:"查询交易记录列表(后台)"`
	common.PageRequest // Embeds common pagination request parameters.
	// Export indicates whether to export the results (0: no, 1: yes).
	Export int `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`
	// UserId filters by user ID.
	UserId uint `json:"userId" dc:"用户ID"`
	// Username filters by username (fuzzy search).
	Username string `json:"username" dc:"用户名(模糊查询)"`
	//account
	Account string `json:"account" dc:"用户账号(模糊查询)"`
	// TokenSymbol filters by token symbol.
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号"`
	// Type filters by transaction type.
	Type string `json:"type" dc:"交易类型" v:"in:deposit,withdrawal,transfe,red_packet,payment,commission,system_adjust"`
	// Status filters by transaction status.
	Status string `json:"status" dc:"交易状态"`
	//direction
	Direction string `json:"direction" dc:"资金方向" v:"in:in,out"`
	// TransactionId filters by transaction ID.
	TransactionId uint64 `json:"transactionId" dc:"交易ID"`
	// RelatedTransactionId filters by related transaction ID.
	RelatedTransactionId uint64 `json:"relatedTransactionId" dc:"关联交易ID"`
	// DateRange filters by date range (format: YYYY-MM-DD,YYYY-MM-DD).
	DateRange string `json:"dateRange" dc:"日期范围 (YYYY-MM-DD,YYYY-MM-DD)"`
	
	// 新增：三级代理模糊查询
	FirstAgentName     string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName    string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName     string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`
	
	// 新增：telegram查询条件
	TelegramId         string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername   string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName          string `json:"firstName" dc:"名字 (模糊搜索)"`
}

// ListAdminTransactionsRes defines the response structure for the transaction list query (admin).
type ListAdminTransactionsRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of transaction records. Using a DTO (TransactionAdminInfo) is good practice.
	Data []*model.TransactionAdminInfo `json:"data" dc:"交易记录列表"`
}
