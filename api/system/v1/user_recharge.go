package v1

import (
	"admin-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- User Recharge API Definitions ---

// ListUserRechargesReq defines the request structure for querying the user recharge list.
type ListUserRechargesReq struct {
	g.Meta             `path:"/user-recharges" method:"get" tags:"SystemUserRecharges" summary:"查询用户充值记录列表"`
	common.PageRequest        // Embeds common pagination request parameters.
	Export             int    `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`
	UserId             uint64 `json:"userId" dc:"用户ID"`
	Account            string `json:"account" dc:"用户账号"`
	TokenId            uint   `json:"tokenId" dc:"币种ID"`
	Symbol             string `json:"name" dc:"币种名称 (模糊查询)" `
	Chan               string `json:"chan" dc:"链名称"`
	FromAddress        string `json:"fromAddress" dc:"来源地址"`
	ToAddress          string `json:"toAddress" dc:"目标地址"`
	TxHash             string `json:"txHash" dc:"交易哈希"`
	State              uint   `json:"state" dc:"1:待确认/处理中, 2:已完成/已入账 3.撤销" v:"in:0,1,2,3"` // 0 for all
	DateRange          string `json:"dateRange" dc:"创建日期范围 (YYYY-MM-DD,YYYY-MM-DD)"`

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram查询条件
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"名字 (模糊搜索)"`
}

// UserRechargeListItem defines the structure for a single user recharge record in the list.
// Enhanced to explicitly show all important fields for better visibility
type UserRechargeListItem struct {
	UserRechargesId       uint   `json:"userRechargesId" dc:"充值记录ID"`
	UserId                uint64 `json:"userId" dc:"用户ID"`
	Account               string `json:"account" dc:"用户账号"`
	TokenId               uint   `json:"tokenId" dc:"币种ID"`
	Name                  string `json:"name" dc:"币种名称"`
	Chan                  string `json:"chan" dc:"链名称"`
	TokenContractAddress  string `json:"tokenContractAddress" dc:"代币合约地址"`
	FromAddress           string `json:"fromAddress" dc:"来源地址"`
	ToAddress             string `json:"toAddress" dc:"充值地址"`
	TxHash                string `json:"txHash" dc:"交易哈希"`
	Amount                string `json:"amount" dc:"充值金额"`
	ConversionTokenSymbol string `json:"conversionTokenSymbol" dc:"折合代币符号, 例如 USDT"`
	ConversionRate        string `json:"conversionRate" dc:"折合汇率 (充值代币与折合代币之间的汇率)"`
	ConvertedAmount       string `json:"convertedAmount" dc:"折合后的数量 (由 amount * conversion_rate 计算得出)"`
	State                 uint   `json:"state" dc:"状态: 1-待确认, 2-已完成"`
	StateText             string `json:"stateText" dc:"状态文本"`
	Confirmations         uint   `json:"confirmations" dc:"确认数"`
	Error                 string `json:"error" dc:"错误信息"`
	CreatedAt             string `json:"createdAt" dc:"创建时间"`
	CompletedAt           string `json:"completedAt" dc:"完成时间"`
	UpdatedAt             string `json:"updatedAt" dc:"更新时间"`
	// Additional computed fields
	Duration    string `json:"duration" dc:"处理时长"`
	IsConfirmed bool   `json:"isConfirmed" dc:"是否已确认"`
	// Notification fields
	NotificationSent   uint        `json:"notificationSent" dc:"是否已发送通知: 0-未发送, 1-已发送"`
	NotificationSentAt *gtime.Time `json:"notificationSentAt" dc:"通知发送时间"`
	FailureReason      string      `json:"failureReason" dc:"失败原因"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// ListUserRechargesRes defines the response structure for the user recharge list query.
type ListUserRechargesRes struct {
	Page common.PageResponse     `json:"page" dc:"分页信息"`
	Data []*UserRechargeListItem `json:"data" dc:"充值记录列表"`
}
