package v1

import (
	"admin-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 获取用户列表 ---

// GetUserListReq defines the request structure for getting the user list.
type GetUserListReq struct {
	g.Meta `path:"/users" method:"get" tags:"SystemUser" summary:"获取用户列表"`
	common.PageRequest
	Account         string  `json:"account" dc:"账号 (模糊搜索)"`
	Email           string  `json:"email" dc:"邮箱 (模糊搜索)"`
	Phone           string  `json:"phone" dc:"手机号 (模糊搜索)"`
	AccountType     *int    `json:"accountType" dc:"账户类型 (0-未知, 1-普通用户, 2-商户, 3-代理)"`
	IsStop          *bool   `json:"isStop" dc:"状态 (true-启用, false-禁用)"`
	RecommendId     *uint64 `json:"recommendId" dc:"推荐人ID"`
	DateRange       string  `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	FirstAgentName  *string `json:"firstAgentName,omitempty" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName *string `json:"secondAgentName,omitempty" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  *string `json:"thirdAgentName,omitempty" dc:"三级代理名称 (模糊搜索)"`
	Export          bool    `json:"export" dc:"是否导出 (true-导出, false-不导出)"`

	// 新增：telegram信息查询
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// UserInfoType defines the structure for user information in lists.
type UserInfoType struct {
	// Id is the unique identifier for the user.
	Id uint64 `json:"id" dc:"用户ID"`
	// Account is the user's login account name.
	Account string `json:"account" dc:"账号"`
	// Email is the user's email address.
	Email string `json:"email" dc:"邮箱"`
	// AreaCode is the user's phone area code.
	AreaCode string `json:"areaCode" dc:"电话区号"`
	// Phone is the user's phone number.
	Phone string `json:"phone" dc:"手机号"`
	// Avatar is the URL of the user's avatar image.
	Avatar string `json:"avatar" dc:"头像"`
	// AccountType is the user's account type (0-未知, 1-普通用户, 2-商户, 3-代理).
	AccountType int `json:"accountType" dc:"账户类型"`
	// InviteCode is the user's unique invitation code.
	InviteCode string `json:"inviteCode" dc:"邀请码"`
	// RecommendId is the ID of the user who referred this user.
	RecommendId uint64 `json:"recommendId" dc:"推荐人ID"`
	// RecommendAccount is the account name of the referring user.
	RecommendAccount string `json:"recommendAccount" dc:"推荐人账号"`
	// IsStop indicates if the user account is disabled (true: disabled, false: enabled).
	IsStop bool `json:"isStop" dc:"是否停用 (true-停用, false-启用)"`
	// Google2faEnabled indicates if Google 2FA is enabled for the user.
	Google2faEnabled bool `json:"google2faEnabled" dc:"是否启用谷歌2FA"`
	// RedPacketPermission indicates if the user has permission for red packets.
	RedPacketPermission bool `json:"redPacketPermission" dc:"红包权限"`
	// TransferPermission indicates if the user has permission for transfers.
	TransferPermission bool `json:"transferPermission" dc:"转账权限"`
	// WithdrawPermission indicates if the user has permission for withdrawals.
	WithdrawPermission bool `json:"withdrawPermission" dc:"提现权限"`
	// FlashTradePermission indicates if the user has permission for flash trades.
	FlashTradePermission bool `json:"flashTradePermission" dc:"闪兑权限"`
	// RechargePermission indicates if the user has permission for recharges.
	RechargePermission bool `json:"rechargePermission" dc:"充值权限"`
	// ReceivePermission indicates if the user has permission for receiving payments.
	ReceivePermission bool `json:"receivePermission" dc:"收款权限"`
	// LastLoginTime is the timestamp of the user's last login.
	LastLoginTime *gtime.Time `json:"lastLoginTime" dc:"最后登录时间"`
	// CreatedAt is the timestamp when the user account was created.
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`

	//一级代理
	FirstAgentName string `json:"firstAgentName" dc:"一级代理"`
	//二级代理
	SecondAgentName string `json:"secondAgentName" dc:"二级代理"`
	//三级代理
	ThirdAgentName string `json:"thirdAgentName" dc:"三级代理"`
	// PaymentPasswordSet indicates if the user has set a payment password.
	PaymentPasswordSet bool `json:"paymentPasswordSet" dc:"是否设置了支付密码"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`

	// 新增：资金统计字段
	TotalDepositAmount      string `json:"totalDepositAmount" dc:"用户总存款金额"`
	TotalWithdrawAmount     string `json:"totalWithdrawAmount" dc:"用户总成功取款金额"`
	TotalBettingVolume      string `json:"totalBettingVolume" dc:"用户总投注流水"`
	DepositRewardMultiplier string `json:"depositRewardMultiplier" dc:"存款奖励倍数选择"`
	WithdrawBettingVolume   string `json:"withdrawBettingVolume" dc:"用户提现需要的流水金额"`
}

// GetUserListRes defines the response structure for getting the user list.
type GetUserListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of users.
	Data []*UserInfoType `json:"data" dc:"用户列表数据"`
}

// --- 获取用户详情 ---

// GetUserReq defines the request structure for getting user details.
type GetUserReq struct {
	g.Meta `path:"/users/{id}" method:"get" tags:"SystemUser" summary:"获取用户详情"`
	Id     uint64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`
}

// UserDetailType defines the structure for user details.
type UserDetailType struct {
	UserInfoType // Embeds the basic user info.
	// Language is the user's preferred language setting.
	Language string `json:"language" dc:"语言"`
	// Reason provides the reason if the account is stopped.
	Reason string `json:"reason" dc:"停用原因"`
	// ResetPaymentPasswordPermission indicates if the user has permission to reset payment password.
	ResetPaymentPasswordPermission bool `json:"resetPaymentPasswordPermission" dc:"重置支付密码权限"`
	// UpdatedAt is the timestamp when the user account was last updated.
	UpdatedAt *gtime.Time `json:"updatedAt" dc:"更新时间"`
}

// GetUserRes defines the response structure for getting user details.
type GetUserRes struct {
	// Data contains the user details.
	Data *UserDetailType `json:"data" dc:"用户详情数据"`
}

// --- 添加用户 ---

// AddUserReq defines the request structure for adding a new user.
type AddUserReq struct {
	g.Meta                         `path:"/users" method:"post" tags:"SystemUser" summary:"添加用户"`
	Account                        string `json:"account"  v:"required|length:4,32#账号不能为空|账号长度必须在4-32之间" dc:"账号"`
	Password                       string `json:"password" v:"required|length:6,32#密码不能为空|密码长度必须在6-32之间" dc:"密码"`
	Email                          string `json:"email" v:"email#邮箱格式不正确" dc:"邮箱"`
	AreaCode                       string `json:"areaCode" d:"86" dc:"电话区号"`
	Phone                          string `json:"phone" dc:"手机号"`
	Nickname                       string `json:"nickname" dc:"昵称"`
	Avatar                         string `json:"avatar" dc:"头像"`
	InviteCode                     string `json:"inviteCode" dc:"邀请码"`
	RecommendId                    uint64 `json:"recommendId" dc:"推荐人ID"`
	Language                       string `json:"language" dc:"语言"`
	IsStop                         bool   `json:"isStop" d:"false" dc:"是否停用 (true-停用, false-启用)"`
	Reason                         string `json:"reason" dc:"停用原因"`
	RedPacketPermission            bool   `json:"redPacketPermission" d:"true" dc:"红包权限"`
	TransferPermission             bool   `json:"transferPermission" d:"true" dc:"转账权限"`
	WithdrawPermission             bool   `json:"withdrawPermission" d:"true" dc:"提现权限"`
	FlashTradePermission           bool   `json:"flashTradePermission" d:"true" dc:"闪兑权限"`
	RechargePermission             bool   `json:"rechargePermission" d:"true" dc:"充值权限"`
	ReceivePermission              bool   `json:"receivePermission" d:"true" dc:"收款权限"`
	ResetPaymentPasswordPermission bool   `json:"resetPaymentPasswordPermission" d:"false" dc:"重置支付密码权限"`
}

// AddUserRes defines the response structure after adding a new user.
type AddUserRes struct {
	// Id is the ID of the newly created user.
	Id uint64 `json:"id" dc:"新增的用户ID"`
}

// --- 编辑用户 ---

// EditUserReq defines the request structure for editing an existing user.
type EditUserReq struct {
	g.Meta                         `path:"/users/{id}" method:"put" tags:"SystemUser" summary:"编辑用户"`
	Id                             uint64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`
	Email                          string `json:"email" v:"email#邮箱格式不正确" dc:"邮箱"`
	AreaCode                       string `json:"areaCode" dc:"电话区号"`
	Phone                          string `json:"phone" dc:"手机号"`
	Nickname                       string `json:"nickname" dc:"昵称"`
	Avatar                         string `json:"avatar" dc:"头像"`
	InviteCode                     string `json:"inviteCode" dc:"邀请码"`
	Language                       string `json:"language" dc:"语言"`
	RedPacketPermission            bool   `json:"redPacketPermission" dc:"红包权限"`
	TransferPermission             bool   `json:"transferPermission" dc:"转账权限"`
	WithdrawPermission             bool   `json:"withdrawPermission" dc:"提现权限"`
	FlashTradePermission           bool   `json:"flashTradePermission" dc:"闪兑权限"`
	RechargePermission             bool   `json:"rechargePermission" dc:"充值权限"`
	ReceivePermission              bool   `json:"receivePermission" dc:"收款权限"`
	ResetPaymentPasswordPermission bool   `json:"resetPaymentPasswordPermission" dc:"重置支付密码权限"`
}

// EditUserRes defines the response structure after editing a user.
type EditUserRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 删除用户 ---

// DeleteUserReq defines the request structure for deleting users.
type DeleteUserReq struct {
	g.Meta `path:"/users" method:"delete" tags:"SystemUser" summary:"删除用户"`
	Ids    []uint64 `json:"ids" v:"required#用户ID不能为空" dc:"用户ID列表"`
}

// DeleteUserRes defines the response structure after deleting users.
type DeleteUserRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新用户状态 ---

// UpdateUserStatusReq defines the request structure for updating a user's status.
// Consider using PATCH for partial updates.
type UpdateUserStatusReq struct {
	g.Meta `path:"/users/{id}/status" method:"put" tags:"SystemUser" summary:"更新用户状态"`
	Id     uint64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`
	IsStop bool   `json:"isStop" v:"required#状态不能为空" dc:"是否停用 (true-停用, false-启用)"`
	Reason string `json:"reason" dc:"停用原因"`
}

// UpdateUserStatusRes defines the response structure after updating a user's status.
type UpdateUserStatusRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 重置用户密码 ---

// ResetUserPasswordReq defines the request structure for resetting a user's password.
type ResetUserPasswordReq struct {
	g.Meta `path:"/users/{id}/password" method:"put" tags:"SystemUser" summary:"重置用户密码"`
	Id     uint64 `json:"id" dc:"用户ID"`
}

// ResetUserPasswordRes defines the response structure after resetting a user's password.
type ResetUserPasswordRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 重置用户Google 2FA ---

// ResetUserGoogle2FAReq defines the request structure for resetting a user's Google 2FA.
type ResetUserGoogle2FAReq struct {
	g.Meta `path:"/users/{id}/google2fa" method:"put" tags:"SystemUser" summary:"重置用户Google 2FA"`
	Id     uint64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`
}

// ResetUserGoogle2FARes defines the response structure after resetting a user's Google 2FA.
type ResetUserGoogle2FARes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新用户流水要求 ---

// UpdateUserWithdrawBettingVolumeReq defines the request structure for updating a user's withdraw betting volume.
type UpdateUserWithdrawBettingVolumeReq struct {
	g.Meta                `path:"/users/{id}/withdraw-betting-volume" method:"put" tags:"SystemUser" summary:"更新用户提现流水要求"`
	Id                    uint64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`
	WithdrawBettingVolume string `json:"withdrawBettingVolume" v:"required#流水金额不能为空" dc:"用户提现需要的流水金额"`
}

// UpdateUserWithdrawBettingVolumeRes defines the response structure after updating a user's withdraw betting volume.
type UpdateUserWithdrawBettingVolumeRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}
