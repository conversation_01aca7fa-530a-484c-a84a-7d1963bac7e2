#!/bin/bash

# 测试商户名称验证规则

echo "=== 测试商户名称验证规则 ==="
echo ""

# 测试1: 包含特殊字符的商户名称（应该失败）
echo "测试1: 商户名称包含@符号 (<EMAIL>) - 应该失败"
curl -X POST http://localhost:7999/api/system/merchants \
  -H "Content-Type: application/json" \
  -d '{
    "merchantName": "<EMAIL>",
    "businessName": "Test Business",
    "email": "<EMAIL>",
    "phone": "1234567890"
  }' 2>/dev/null | jq '.'

echo ""
echo "---"
echo ""

# 测试2: 符合规范的商户名称（应该成功）
echo "测试2: 符合规范的商户名称 (merchant_123) - 应该成功"
curl -X POST http://localhost:7999/api/system/merchants \
  -H "Content-Type: application/json" \
  -d '{
    "merchantName": "merchant_123",
    "businessName": "Test Business",
    "email": "<EMAIL>",
    "phone": "1234567890"
  }' 2>/dev/null | jq '.'

echo ""
echo "---"
echo ""

# 测试3: 以下划线开头的商户名称（应该失败）
echo "测试3: 以下划线开头 (_merchant) - 应该失败"
curl -X POST http://localhost:7999/api/system/merchants \
  -H "Content-Type: application/json" \
  -d '{
    "merchantName": "_merchant",
    "businessName": "Test Business",
    "email": "<EMAIL>",
    "phone": "1234567890"
  }' 2>/dev/null | jq '.'

echo ""
echo "---"
echo ""

# 测试4: 包含连续下划线的商户名称（应该失败）
echo "测试4: 包含连续下划线 (merchant__123) - 应该失败"
curl -X POST http://localhost:7999/api/system/merchants \
  -H "Content-Type: application/json" \
  -d '{
    "merchantName": "merchant__123",
    "businessName": "Test Business",
    "email": "<EMAIL>",
    "phone": "1234567890"
  }' 2>/dev/null | jq '.'

echo ""
echo "---"
echo ""

# 测试5: 合法的商户名称示例
echo "测试5: 各种合法的商户名称示例"
for name in "merchant123" "test_merchant" "merchant-2025" "ABC123xyz"; do
  echo "  测试商户名称: $name"
  curl -s -X POST http://localhost:7999/api/system/merchants \
    -H "Content-Type: application/json" \
    -d "{
      \"merchantName\": \"$name\",
      \"businessName\": \"Test Business\",
      \"email\": \"test_${name}@example.com\",
      \"phone\": \"1234567890\"
    }" 2>/dev/null | jq -c '{code, message}'
done