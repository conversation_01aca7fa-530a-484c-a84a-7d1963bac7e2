-- ============================================================================
-- Migration: Add commission_status and betting_bonus_status to game_transaction_records
-- Description: 为游戏交易记录表添加佣金状态和反水状态字段
-- ============================================================================

-- 添加佣金处理状态字段
ALTER TABLE `game_transaction_records` 
ADD COLUMN `commission_status` enum('unprocessed','processed','not_applicable') 
COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unprocessed' 
COMMENT '佣金处理状态' AFTER `bet_id`;

-- 添加投注反水状态字段
ALTER TABLE `game_transaction_records` 
ADD COLUMN `betting_bonus_status` enum('unprocessed','processed','not_applicable') 
COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unprocessed' 
COMMENT '投注反水状态' AFTER `commission_status`;

-- 添加索引以提高查询性能
CREATE INDEX `idx_commission_status` ON `game_transaction_records` (`commission_status`);
CREATE INDEX `idx_betting_bonus_status` ON `game_transaction_records` (`betting_bonus_status`);

-- 添加复合索引用于常见的查询模式
CREATE INDEX `idx_commission_betting_status` ON `game_transaction_records` (`commission_status`, `betting_bonus_status`);
