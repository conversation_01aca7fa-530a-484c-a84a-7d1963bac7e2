-- Migration Rollback: Remove performance indexes for agent and telegram information queries
-- Created: 2025-06-18
-- Description: Removes indexes that were added to optimize agent hierarchy and telegram information queries

-- ============================================================================
-- Remove Additional Performance Indexes
-- ============================================================================

DROP INDEX IF EXISTS idx_red_packets_status_created ON red_packets;
DROP INDEX IF EXISTS idx_payment_requests_status_created ON payment_requests;
DROP INDEX IF EXISTS idx_transactions_status_created ON transactions;
DROP INDEX IF EXISTS idx_user_recharges_status_created ON user_recharges;
DROP INDEX IF EXISTS idx_user_withdraws_status_created ON user_withdraws;

-- ============================================================================
-- Remove Main Tables User ID Indexes
-- ============================================================================

DROP INDEX IF EXISTS idx_referral_commissions_invitee_id ON referral_commissions;
DROP INDEX IF EXISTS idx_referral_commissions_referrer_id ON referral_commissions;
DROP INDEX IF EXISTS idx_backup_accounts_user_id ON backup_accounts;
DROP INDEX IF EXISTS idx_login_logs_user_id ON login_logs;
DROP INDEX IF EXISTS idx_payment_requests_payer_id ON payment_requests;
DROP INDEX IF EXISTS idx_payment_requests_requester_id ON payment_requests;
DROP INDEX IF EXISTS idx_red_packets_creator_user_id ON red_packets;
DROP INDEX IF EXISTS idx_transactions_user_created ON transactions;
DROP INDEX IF EXISTS idx_transactions_user_id ON transactions;
DROP INDEX IF EXISTS idx_wallets_user_id ON wallets;
DROP INDEX IF EXISTS idx_user_addresses_user_id ON user_addresses;
DROP INDEX IF EXISTS idx_user_withdraws_user_created ON user_withdraws;
DROP INDEX IF EXISTS idx_user_withdraws_user_id ON user_withdraws;
DROP INDEX IF EXISTS idx_user_recharges_user_created ON user_recharges;
DROP INDEX IF EXISTS idx_user_recharges_user_id ON user_recharges;

-- ============================================================================
-- Remove User Backup Accounts Table Indexes
-- ============================================================================

DROP INDEX IF EXISTS idx_user_backup_accounts_first_name ON user_backup_accounts;
DROP INDEX IF EXISTS idx_user_backup_accounts_telegram_username ON user_backup_accounts;
DROP INDEX IF EXISTS idx_user_backup_accounts_telegram_id ON user_backup_accounts;
DROP INDEX IF EXISTS idx_user_backup_accounts_user_master ON user_backup_accounts;

-- ============================================================================
-- Remove Users Table Indexes
-- ============================================================================

DROP INDEX IF EXISTS idx_users_account_deleted ON users;
DROP INDEX IF EXISTS idx_users_id_deleted ON users;
DROP INDEX IF EXISTS idx_users_third_id ON users;
DROP INDEX IF EXISTS idx_users_second_id ON users;
DROP INDEX IF EXISTS idx_users_first_id ON users;
