-- Migration: Add performance indexes for agent and telegram information queries
-- Created: 2025-06-18
-- Description: Adds indexes to optimize the performance of queries that include agent hierarchy and telegram information

-- ============================================================================
-- Users Table Indexes
-- ============================================================================

-- Primary indexes for agent relationships (critical for JOIN performance)
CREATE INDEX IF NOT EXISTS idx_users_first_id ON users(first_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_users_second_id ON users(second_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_users_third_id ON users(third_id) WHERE deleted_at IS NULL;

-- Composite index for common filtering patterns
CREATE INDEX IF NOT EXISTS idx_users_id_deleted ON users(id, deleted_at);

-- Index for account lookups (used in agent name queries)
CREATE INDEX IF NOT EXISTS idx_users_account_deleted ON users(account, deleted_at);

-- ============================================================================
-- User Backup Accounts Table Indexes  
-- ============================================================================

-- Primary index for master account lookups (critical for telegram info)
CREATE INDEX IF NOT EXISTS idx_user_backup_accounts_user_master 
ON user_backup_accounts(user_id, is_master, deleted_at);

-- Indexes for telegram information searches
CREATE INDEX IF NOT EXISTS idx_user_backup_accounts_telegram_id 
ON user_backup_accounts(telegram_id) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_user_backup_accounts_telegram_username 
ON user_backup_accounts(telegram_username) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_user_backup_accounts_first_name 
ON user_backup_accounts(first_name) WHERE deleted_at IS NULL;

-- ============================================================================
-- Main Tables User ID Indexes
-- ============================================================================

-- User recharges
CREATE INDEX IF NOT EXISTS idx_user_recharges_user_id ON user_recharges(user_id);
CREATE INDEX IF NOT EXISTS idx_user_recharges_user_created 
ON user_recharges(user_id, created_at DESC);

-- User withdraws  
CREATE INDEX IF NOT EXISTS idx_user_withdraws_user_id ON user_withdraws(user_id);
CREATE INDEX IF NOT EXISTS idx_user_withdraws_user_created 
ON user_withdraws(user_id, created_at DESC);

-- User addresses
CREATE INDEX IF NOT EXISTS idx_user_addresses_user_id ON user_addresses(user_id);

-- Wallets
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id);

-- Transactions
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user_created 
ON transactions(user_id, created_at DESC);

-- Red packets
CREATE INDEX IF NOT EXISTS idx_red_packets_creator_user_id ON red_packets(creator_user_id);

-- Payment requests (dual user support)
CREATE INDEX IF NOT EXISTS idx_payment_requests_requester_id ON payment_requests(requester_user_id);
CREATE INDEX IF NOT EXISTS idx_payment_requests_payer_id ON payment_requests(payer_user_id);

-- Login logs
CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);

-- Backup accounts
CREATE INDEX IF NOT EXISTS idx_backup_accounts_user_id ON backup_accounts(user_id);

-- Referral commissions (dual user support)
CREATE INDEX IF NOT EXISTS idx_referral_commissions_referrer_id ON referral_commissions(referrer_user_id);
CREATE INDEX IF NOT EXISTS idx_referral_commissions_invitee_id ON referral_commissions(invitee_user_id);

-- ============================================================================
-- Additional Performance Indexes
-- ============================================================================

-- Composite indexes for common query patterns with status/state filtering
CREATE INDEX IF NOT EXISTS idx_user_withdraws_status_created 
ON user_withdraws(state, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_recharges_status_created 
ON user_recharges(status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_transactions_status_created 
ON transactions(status, created_at DESC);

-- Index for payment requests with status
CREATE INDEX IF NOT EXISTS idx_payment_requests_status_created 
ON payment_requests(status, created_at DESC);

-- Index for red packets with status
CREATE INDEX IF NOT EXISTS idx_red_packets_status_created 
ON red_packets(status, created_at DESC);
