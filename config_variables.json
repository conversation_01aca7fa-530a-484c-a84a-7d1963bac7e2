{"GAME_ADMIN_API_SERVER_ADDRESS": ":7999", "GAME_ADMIN_API_SERVER_OPENAPIPATH": "/api.json", "GAME_ADMIN_API_SERVER_SWAGGERPATH": "/swagger", "GAME_ADMIN_API_SERVER_LOGPATH": "logs", "GAME_ADMIN_API_SERVER_DUMPROUTERMAP": false, "GAME_ADMIN_API_DEV_MODE": true, "GAME_ADMIN_API_I18N_PATH": "manifest/i18n", "GAME_ADMIN_API_I18N_LANGUAGE": "zh-CN", "GAME_ADMIN_API_DATABASE_LOGGER_PATH": "logs/database", "GAME_ADMIN_API_DATABASE_LOGGER_LEVEL": "all", "GAME_ADMIN_API_DATABASE_LOGGER_STDOUT": true, "GAME_ADMIN_API_DATABASE_DEFAULT_LINK": "mysql:root:root@tcp(mysql:3306)/game?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s", "GAME_ADMIN_API_DATABASE_DEFAULT_DEBUG": true, "GAME_ADMIN_API_REDIS_DEFAULT_ADDRESS": "valkey:6379", "GAME_ADMIN_API_REDIS_DEFAULT_DB": 0, "GAME_ADMIN_API_REDIS_DEFAULT_PASS": "valkey_password", "GAME_ADMIN_API_REDIS_DEFAULT_IDLETIMEOUT": "20s", "GAME_ADMIN_API_LOGGER_PATH": "logs", "GAME_ADMIN_API_LOGGER_LEVEL": "all", "GAME_ADMIN_API_LOGGER_STDOUT": true, "GAME_ADMIN_API_LOGGER_ROTATESIZE": "100M", "GAME_ADMIN_API_LOGGER_ROTATEEXPIRE": "7d", "GAME_ADMIN_API_LOGGER_FORMAT": "json", "GAME_ADMIN_API_CASDOOR_SERVER_ENDPOINT": "https://sso-dev.jjpay.co", "GAME_ADMIN_API_CASDOOR_SERVER_CLIENT_ID": "80756163da773c15fa03", "GAME_ADMIN_API_CASDOOR_SERVER_CLIENT_SECRET": "0f319ddd842a7bdb25834aa85b6268f43f5ff686", "GAME_ADMIN_API_CASDOOR_SERVER_ORGANIZATION": "organization_game", "GAME_ADMIN_API_CASDOOR_SERVER_OWNER": "organization_game", "GAME_ADMIN_API_CASDOOR_SERVER_APPLICATION": "application_game", "GAME_ADMIN_API_CASDOOR_SERVER_FRONTEND_URL": "https://admin-game-sit.jjpay.co", "GAME_ADMIN_API_CASDOOR_SERVER_USER": "organization_game/game", "GAME_ADMIN_API_CASDOOR_SERVER_ROLE": "organization_game/role_game", "GAME_ADMIN_API_CASDOOR_SERVER_MODEL": "model_srgv6d", "GAME_ADMIN_API_CONSUL_ADDRESS": "consul:8500", "GAME_ADMIN_API_CONSUL_TOKEN": "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874", "GAME_ADMIN_API_CONSUL_CONFIG_PREFIX": "xpay/config", "GAME_ADMIN_API_CORS_ALLOWORIGIN": "*", "GAME_ADMIN_API_CORS_ALLOWCREDENTIALS": "true", "GAME_ADMIN_API_CORS_EXPOSEHEADERS": "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type", "GAME_ADMIN_API_CORS_MAXAGE": 86400, "GAME_ADMIN_API_CORS_ALLOWMETHODS": "GET, POST, PUT, DELETE, OPTIONS, PATCH", "GAME_ADMIN_API_CORS_ALLOWHEADERS": "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Token", "GAME_ADMIN_API_STORAGE_PROVIDER": "s3", "GAME_ADMIN_API_STORAGE_S3_ACCESSKEYID": "********************", "GAME_ADMIN_API_STORAGE_S3_SECRETACCESSKEY": "jRlPC3qDWMNvW7IX8MThWEalYSxeLupBcUKZCQHO", "GAME_ADMIN_API_STORAGE_S3_REGION": "ap-northeast-1", "GAME_ADMIN_API_STORAGE_S3_BUCKETNAME": "yescex1", "GAME_ADMIN_API_STORAGE_S3_USEPATHSTYLEENDPOINT": false}